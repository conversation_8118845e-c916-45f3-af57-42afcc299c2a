@echo off
echo ========================================
echo    ProTech System Update
echo    تحديث نظام ProTech
echo ========================================
echo.
echo Updating ProTech system on desktop...
echo جاري تحديث نظام ProTech على سطح المكتب...
echo.

copy "protech_simple_working.py" "C:\Users\<USER>\OneDrive\Desktop\protech_simple_working.py"
if %errorlevel% equ 0 (
    echo ✅ Update successful! / تم التحديث بنجاح!
    echo.
    echo The invoices report issue has been fixed.
    echo تم إصلاح مشكلة تقرير الفواتير.
    echo.
    echo You can now run the updated program from desktop.
    echo يمكنك الآن تشغيل البرنامج المحدث من سطح المكتب.
) else (
    echo ❌ Update failed! / فشل التحديث!
    echo Please check the paths and try again.
    echo يرجى التحقق من المسارات والمحاولة مرة أخرى.
)

echo.
pause
