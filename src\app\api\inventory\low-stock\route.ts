import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const threshold = parseInt(searchParams.get('threshold') || '10');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50');

    const skip = (page - 1) * limit;

    // Get products with low stock
    const [lowStockProducts, total] = await Promise.all([
      db.product.findMany({
        where: {
          AND: [
            { trackInventory: true },
            { isActive: true },
            {
              OR: [
                { currentStock: { lte: threshold } },
                {
                  AND: [
                    { minStock: { gt: 0 } },
                    { currentStock: { lte: db.product.fields.minStock } },
                  ],
                },
              ],
            },
          ],
        },
        include: {
          category: {
            select: {
              id: true,
              name: true,
            },
          },
          supplier: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true,
            },
          },
        },
        orderBy: [
          { currentStock: 'asc' },
          { name: 'asc' },
        ],
        skip,
        take: limit,
      }),
      db.product.count({
        where: {
          AND: [
            { trackInventory: true },
            { isActive: true },
            {
              OR: [
                { currentStock: { lte: threshold } },
                {
                  AND: [
                    { minStock: { gt: 0 } },
                    { currentStock: { lte: db.product.fields.minStock } },
                  ],
                },
              ],
            },
          ],
        },
      }),
    ]);

    // Calculate alert levels for each product
    const productsWithAlerts = lowStockProducts.map(product => {
      const stockPercentage = product.minStock > 0 
        ? (product.currentStock / product.minStock) * 100 
        : 0;
      
      let alertLevel: 'critical' | 'warning' | 'low' = 'low';
      
      if (product.currentStock <= 0) {
        alertLevel = 'critical';
      } else if (product.currentStock <= product.minStock * 0.5) {
        alertLevel = 'critical';
      } else if (product.currentStock <= product.minStock) {
        alertLevel = 'warning';
      }

      return {
        ...product,
        alertLevel,
        stockPercentage: Math.round(stockPercentage),
        daysUntilOutOfStock: calculateDaysUntilOutOfStock(product),
      };
    });

    const totalPages = Math.ceil(total / limit);

    // Get summary statistics
    const summary = {
      totalLowStock: total,
      critical: productsWithAlerts.filter(p => p.alertLevel === 'critical').length,
      warning: productsWithAlerts.filter(p => p.alertLevel === 'warning').length,
      low: productsWithAlerts.filter(p => p.alertLevel === 'low').length,
    };

    return NextResponse.json({
      success: true,
      data: {
        products: productsWithAlerts,
        summary,
        pagination: {
          page,
          limit,
          total,
          totalPages,
        },
      },
    });
  } catch (error) {
    console.error('Error fetching low stock products:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch low stock products',
      },
      { status: 500 }
    );
  }
}

// Helper function to calculate estimated days until out of stock
function calculateDaysUntilOutOfStock(product: any): number | null {
  // This is a simplified calculation
  // In a real system, you'd analyze historical sales data
  
  if (product.currentStock <= 0) {
    return 0;
  }

  // Get average daily usage from recent movements (simplified)
  // For now, we'll use a basic estimation
  const averageDailyUsage = 1; // This should be calculated from actual data
  
  if (averageDailyUsage <= 0) {
    return null; // Cannot calculate without usage data
  }

  return Math.floor(product.currentStock / averageDailyUsage);
}
