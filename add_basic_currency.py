#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Basic Currency Support
إضافة دعم العملات الأساسي

Basic Lebanese Pound and USD support
دعم أساسي للليرة اللبنانية والدولار
"""

import os
import shutil
from datetime import datetime

def add_basic_currency():
    """إضافة دعم العملات الأساسي"""
    try:
        print("💱 إضافة دعم العملات الأساسي")
        print("💱 Adding Basic Currency Support")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.basic_currency_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Basic currency methods
        currency_methods = '''
    def get_usd_rate(self):
        """Get USD exchange rate"""
        return 89500  # 1 USD = 89,500 LBP
    
    def format_lbp(self, amount):
        """Format Lebanese Pound"""
        return f"{amount:,.0f} ليرة لبنانية"
    
    def format_usd(self, amount):
        """Format US Dollar"""
        return f"${amount:,.2f}"
    
    def lbp_to_usd(self, lbp_amount):
        """Convert LBP to USD"""
        return lbp_amount / self.get_usd_rate()
    
    def show_currency_balance(self):
        """Show balance in both currencies"""
        try:
            self.report_title.config(text="رصيد المحل بالعملتين")
            
            # Get data
            products = self.get_real_products_data()
            sales = self.get_real_sales_data()
            customers = self.get_real_customers_data()
            
            # Calculate inventory value in LBP
            inventory_cost_lbp = 0
            inventory_value_lbp = 0
            
            for product in products:
                stock = product.get('quantity', 0)
                cost_price = product.get('base_price', 0)
                sell_price = product.get('shop_owner_price', cost_price * 1.05)
                
                inventory_cost_lbp += stock * cost_price
                inventory_value_lbp += stock * sell_price
            
            # Convert to USD
            inventory_cost_usd = self.lbp_to_usd(inventory_cost_lbp)
            inventory_value_usd = self.lbp_to_usd(inventory_value_lbp)
            
            # Calculate sales
            sales_total_lbp = sum(sale.get('total', 0) for sale in sales)
            sales_total_usd = self.lbp_to_usd(sales_total_lbp)
            
            # Calculate customer balances
            customer_debt_lbp = 0
            customer_credit_lbp = 0
            
            for customer in customers:
                balance = customer.get('balance', 0)
                if balance > 0:
                    customer_credit_lbp += balance
                else:
                    customer_debt_lbp += abs(balance)
            
            customer_debt_usd = self.lbp_to_usd(customer_debt_lbp)
            customer_credit_usd = self.lbp_to_usd(customer_credit_lbp)
            
            # Calculate total worth
            total_assets_lbp = inventory_value_lbp + customer_credit_lbp + sales_total_lbp
            total_assets_usd = self.lbp_to_usd(total_assets_lbp)
            net_worth_lbp = total_assets_lbp - customer_debt_lbp
            net_worth_usd = self.lbp_to_usd(net_worth_lbp)
            
            # Create report
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            usd_rate = self.get_usd_rate()
            
            report = "رصيد المحل بالليرة اللبنانية والدولار\\n"
            report += "="*60 + "\\n\\n"
            report += "التاريخ: " + current_time + "\\n"
            report += "سعر الصرف: 1 USD = " + f"{usd_rate:,}" + " ليرة لبنانية\\n\\n"
            
            # Inventory section
            report += "البضاعة:\\n"
            report += "-"*30 + "\\n"
            report += "عدد المنتجات: " + str(len(products)) + "\\n\\n"
            
            report += "قيمة التكلفة:\\n"
            report += "• " + self.format_lbp(inventory_cost_lbp) + "\\n"
            report += "• " + self.format_usd(inventory_cost_usd) + "\\n\\n"
            
            report += "قيمة البيع:\\n"
            report += "• " + self.format_lbp(inventory_value_lbp) + "\\n"
            report += "• " + self.format_usd(inventory_value_usd) + "\\n\\n"
            
            # Sales section
            report += "المبيعات:\\n"
            report += "-"*30 + "\\n"
            report += "عدد الفواتير: " + str(len(sales)) + "\\n\\n"
            
            report += "إجمالي المبيعات:\\n"
            report += "• " + self.format_lbp(sales_total_lbp) + "\\n"
            report += "• " + self.format_usd(sales_total_usd) + "\\n\\n"
            
            # Customers section
            report += "العملاء:\\n"
            report += "-"*30 + "\\n"
            report += "عدد العملاء: " + str(len(customers)) + "\\n\\n"
            
            report += "ديون العملاء:\\n"
            report += "• " + self.format_lbp(customer_debt_lbp) + "\\n"
            report += "• " + self.format_usd(customer_debt_usd) + "\\n\\n"
            
            # Total balance
            report += "="*60 + "\\n"
            report += "الرصيد الإجمالي:\\n"
            report += "="*60 + "\\n\\n"
            
            report += "صافي رصيد المحل:\\n"
            report += "• " + self.format_lbp(net_worth_lbp) + "\\n"
            report += "• " + self.format_usd(net_worth_usd) + "\\n\\n"
            
            # Analysis
            if net_worth_usd >= 10000:
                report += "وضع ممتاز (أكثر من 10,000 دولار)\\n"
            elif net_worth_usd >= 5000:
                report += "وضع جيد (5,000 - 10,000 دولار)\\n"
            elif net_worth_usd >= 1000:
                report += "وضع متوسط (1,000 - 5,000 دولار)\\n"
            else:
                report += "وضع يحتاج تحسين (أقل من 1,000 دولار)\\n"
            
            # Recommendations
            report += "\\nالتوصيات:\\n"
            report += "-"*30 + "\\n"
            
            if customer_debt_lbp > customer_credit_lbp:
                report += "• متابعة تحصيل ديون العملاء\\n"
            
            report += "• مراقبة سعر صرف الدولار\\n"
            report += "• تحديث الأسعار حسب سعر الصرف\\n"
            report += "• مراجعة التقرير شهرياً\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
        except Exception as e:
            print("خطأ في عرض تقرير العملات:", str(e))
            error_msg = "خطأ في تحميل تقرير العملات"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_msg)
'''
        
        # Add currency methods before the last method
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + currency_methods + content[last_method:]
        else:
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + currency_methods + content[last_method:]
        
        # Add currency button with simple text
        if "btn6 = tk.Button(sidebar, text=\"رصيد المحل\"" in content:
            btn6_pos = content.find("btn6.pack(pady=3, padx=10, fill='x')")
            if btn6_pos != -1:
                btn6_end = content.find("\\n", btn6_pos) + 1
                
                new_button = '''
            btn_curr = tk.Button(sidebar, text="رصيد عملات", 
                               font=("Arial", 10), bg='#e67e22', fg='white',
                               width=18, height=2, command=self.show_currency_balance)
            btn_curr.pack(pady=3, padx=10, fill='x')
'''
                
                content = content[:btn6_end] + new_button + content[btn6_end:]
                print("✅ تم إضافة زر رصيد عملات")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة دعم العملات الأساسي")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة دعم العملات: {e}")
        return False

def main():
    """Main function"""
    print("💱 إضافة دعم الليرة اللبنانية والدولار")
    print("💱 Adding Lebanese Pound and USD Support")
    print("="*60)
    
    if add_basic_currency():
        print("\n🎉 تم إضافة دعم العملات بنجاح!")
        
        print("\n💱 العملات المدعومة:")
        print("• 🇱🇧 الليرة اللبنانية")
        print("• 🇺🇸 الدولار الأمريكي")
        
        print("\n📊 التقرير الجديد:")
        print("• رصيد عملات")
        print("• عرض القيم بالعملتين")
        print("• تحويل تلقائي")
        print("• تحليل مالي")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. انقر على 'رصيد عملات'")
        print("4. استعرض التقرير")
        
        print("\n💡 الميزات:")
        print("• سعر صرف: 1 USD = 89,500 ليرة لبنانية")
        print("• تحويل دقيق بين العملات")
        print("• عرض واضح")
        print("• تحليل مالي")
        
    else:
        print("\n❌ فشل في إضافة دعم العملات")

if __name__ == "__main__":
    main()
