#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Smart ProTech Wrapper
الغلاف الذكي لـ ProTech

Intelligent wrapper that prevents crashes and handles errors gracefully
غلاف ذكي يمنع الأعطال ويتعامل مع الأخطاء بذكاء
"""

import sys
import os
import traceback
import json
from datetime import datetime

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class SmartWrapper:
    """Smart wrapper for ProTech"""
    
    def __init__(self):
        self.error_count = 0
        self.max_errors = 10
        self.setup_safe_environment()
    
    def setup_safe_environment(self):
        """Setup safe environment"""
        try:
            # Import safety modules
            try:
                from error_isolation import safe_execute, safe_print, safe_save
                self.safe_execute = safe_execute
                self.safe_print = safe_print
                self.safe_save = safe_save
            except:
                self.safe_execute = lambda f, *a, **k: f(*a, **k)
                self.safe_print = print
                self.safe_save = self.basic_save
            
            # Import alternative paths
            try:
                from alternative_paths import alt_manager
                self.alt_manager = alt_manager
            except:
                self.alt_manager = None
            
            # Import smart encoding
            try:
                from smart_encoding import safe_unicode_print
                self.safe_unicode_print = safe_unicode_print
            except:
                self.safe_unicode_print = print
            
        except Exception as e:
            print(f"تحذير: فشل في إعداد البيئة الآمنة: {e}")
    
    def basic_save(self, data, filename):
        """Basic save function"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            return True
        except:
            return False
    
    def handle_error(self, error, context=""):
        """Handle errors intelligently"""
        self.error_count += 1
        
        error_info = {
            "timestamp": datetime.now().isoformat(),
            "error": str(error),
            "type": type(error).__name__,
            "context": context,
            "traceback": traceback.format_exc()
        }
        
        # Log error
        self.log_error(error_info)
        
        # Try to recover
        if self.error_count < self.max_errors:
            self.safe_unicode_print(f"⚠️ خطأ تم التعامل معه: {error}")
            return True
        else:
            self.safe_unicode_print("❌ تم الوصول للحد الأقصى من الأخطاء")
            return False
    
    def log_error(self, error_info):
        """Log error information"""
        try:
            log_file = "smart_error_log.json"
            
            if os.path.exists(log_file):
                with open(log_file, 'r', encoding='utf-8') as f:
                    log_data = json.load(f)
            else:
                log_data = {"errors": []}
            
            log_data["errors"].append(error_info)
            
            # Keep only last 50 errors
            if len(log_data["errors"]) > 50:
                log_data["errors"] = log_data["errors"][-50:]
            
            with open(log_file, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
        except:
            pass
    
    def run_protech_safely(self):
        """Run ProTech safely"""
        try:
            self.safe_unicode_print("🚀 بدء تشغيل ProTech الآمن...")
            
            # Try to import and run ProTech
            try:
                # Import the main ProTech file
                import protech_simple_working
                
                # Run in safe mode
                if hasattr(protech_simple_working, 'main'):
                    self.safe_execute(protech_simple_working.main)
                else:
                    self.safe_unicode_print("⚠️ لم يتم العثور على دالة main")
                
            except Exception as e:
                if self.handle_error(e, "ProTech execution"):
                    self.safe_unicode_print("🔄 محاولة تشغيل بديل...")
                    self.run_minimal_version()
                else:
                    self.safe_unicode_print("❌ فشل في تشغيل ProTech")
        
        except Exception as e:
            self.handle_error(e, "Wrapper execution")
    
    def run_minimal_version(self):
        """Run minimal version of ProTech"""
        try:
            self.safe_unicode_print("🔧 تشغيل النسخة المبسطة...")
            
            # Create minimal GUI
            try:
                import tkinter as tk
                from tkinter import messagebox
                
                root = tk.Tk()
                root.title("ProTech - النسخة الآمنة")
                root.geometry("400x300")
                
                label = tk.Label(root, text="ProTech يعمل في الوضع الآمن", 
                               font=("Arial", 14))
                label.pack(pady=20)
                
                def safe_exit():
                    root.destroy()
                
                exit_btn = tk.Button(root, text="إغلاق", command=safe_exit)
                exit_btn.pack(pady=10)
                
                root.mainloop()
                
            except Exception as e:
                self.safe_unicode_print(f"⚠️ فشل في تشغيل الواجهة المبسطة: {e}")
                self.safe_unicode_print("✅ ProTech يعمل في وضع النص فقط")
                input("اضغط Enter للخروج...")
        
        except Exception as e:
            self.handle_error(e, "Minimal version")

def main():
    """Main function"""
    wrapper = SmartWrapper()
    wrapper.run_protech_safely()

if __name__ == "__main__":
    main()
