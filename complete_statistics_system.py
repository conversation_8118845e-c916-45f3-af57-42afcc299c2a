#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete Statistics System
إكمال نظام الإحصائيات

Complete the statistics system with remaining methods and button update
إكمال نظام الإحصائيات مع باقي الطرق وتحديث الزر
"""

import os
import shutil
from datetime import datetime

def complete_statistics_system():
    """إكمال نظام الإحصائيات"""
    try:
        print("🔧 إكمال نظام الإحصائيات المتقدم")
        print("🔧 Completing Advanced Statistics System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.complete_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Additional table methods
        additional_methods = '''
    def create_top_products_table(self):
        """إنشاء جدول أفضل المنتجات"""
        try:
            # Get data
            products = self.get_real_products_data()
            sales = self.get_real_sales_data()
            
            # Calculate product sales
            product_sales = {}
            for sale in sales:
                for item in sale.get('items', []):
                    name = item.get('name', '')
                    quantity = float(item.get('quantity', 0))
                    if name in product_sales:
                        product_sales[name] += quantity
                    else:
                        product_sales[name] = quantity
            
            # Sort products by sales
            top_products = sorted(product_sales.items(), key=lambda x: x[1], reverse=True)[:10]
            
            # Create table data
            table_data = [["Rank", "Product Name", "Units Sold", "Stock Level", "Revenue Potential"]]
            
            for i, (product_name, units_sold) in enumerate(top_products, 1):
                # Find product details
                product_info = next((p for p in products if p.get('name') == product_name), {})
                stock = product_info.get('quantity', 0)
                price = float(product_info.get('shop_owner_price', 0))
                revenue = units_sold * price
                
                table_data.append([
                    f"#{i}",
                    product_name[:20],
                    f"{units_sold:,.0f}",
                    f"{stock:,.0f}",
                    f"{revenue:,.0f} LBP"
                ])
            
            self.create_generic_table(table_data, "Top 10 Best Selling Products")
            self.current_table_data = table_data
            self.current_table_title = "Top Products"
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول أفضل المنتجات: {e}")
    
    def create_customer_analysis_table(self):
        """إنشاء جدول تحليل العملاء"""
        try:
            # Get data
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            
            # Calculate customer metrics
            customer_metrics = {}
            for sale in sales:
                customer = sale.get('customer_name', '')
                total = float(sale.get('total', 0))
                
                if customer in customer_metrics:
                    customer_metrics[customer]['total_spent'] += total
                    customer_metrics[customer]['order_count'] += 1
                else:
                    customer_metrics[customer] = {'total_spent': total, 'order_count': 1}
            
            # Create table data
            table_data = [["Customer Name", "Total Spent (LBP)", "Orders", "Avg Order", "Customer Type", "Balance"]]
            
            for customer in customers:
                name = customer.get('name', '')
                balance = float(customer.get('balance', 0))
                customer_type = customer.get('type', '')
                
                metrics = customer_metrics.get(name, {'total_spent': 0, 'order_count': 0})
                total_spent = metrics['total_spent']
                order_count = metrics['order_count']
                avg_order = total_spent / order_count if order_count > 0 else 0
                
                table_data.append([
                    name[:20],
                    f"{total_spent:,.0f}",
                    f"{order_count}",
                    f"{avg_order:,.0f}",
                    customer_type,
                    f"{balance:,.0f}"
                ])
            
            # Sort by total spent
            table_data[1:] = sorted(table_data[1:], key=lambda x: float(x[1].replace(',', '')), reverse=True)
            
            self.create_generic_table(table_data, "Customer Analysis Report")
            self.current_table_data = table_data
            self.current_table_title = "Customer Analysis"
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول تحليل العملاء: {e}")
    
    def create_sales_trends_table(self):
        """إنشاء جدول اتجاهات المبيعات"""
        try:
            # Get data
            sales = self.get_real_sales_data()
            
            # Group sales by date
            daily_sales = {}
            for sale in sales:
                date = sale.get('date', '')[:10]  # Get date part only
                total = float(sale.get('total', 0))
                
                if date in daily_sales:
                    daily_sales[date]['total'] += total
                    daily_sales[date]['count'] += 1
                else:
                    daily_sales[date] = {'total': total, 'count': 1}
            
            # Create table data
            table_data = [["Date", "Sales Count", "Total Revenue (LBP)", "Avg Sale (LBP)", "Performance"]]
            
            # Calculate average for performance comparison
            avg_daily_revenue = sum(d['total'] for d in daily_sales.values()) / len(daily_sales) if daily_sales else 0
            
            for date, data in sorted(daily_sales.items(), reverse=True)[:15]:  # Last 15 days
                total = data['total']
                count = data['count']
                avg_sale = total / count if count > 0 else 0
                
                # Performance indicator
                if total > avg_daily_revenue * 1.2:
                    performance = "🔥 Excellent"
                elif total > avg_daily_revenue:
                    performance = "✅ Good"
                elif total > avg_daily_revenue * 0.8:
                    performance = "🟡 Average"
                else:
                    performance = "🔴 Below Avg"
                
                table_data.append([
                    date,
                    f"{count}",
                    f"{total:,.0f}",
                    f"{avg_sale:,.0f}",
                    performance
                ])
            
            self.create_generic_table(table_data, "Sales Trends (Last 15 Days)")
            self.current_table_data = table_data
            self.current_table_title = "Sales Trends"
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول اتجاهات المبيعات: {e}")
    
    def create_financial_summary_table(self):
        """إنشاء جدول الملخص المالي"""
        try:
            # Get data
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Calculate financial metrics
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            
            # Customer balances
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            customer_credit = sum(abs(float(c.get('balance', 0))) for c in customers if float(c.get('balance', 0)) < 0)
            
            # Calculate costs and profits
            total_cost = 0
            for sale in sales:
                for item in sale.get('items', []):
                    item_name = item.get('name', '')
                    item_quantity = float(item.get('quantity', 0))
                    
                    for product in products:
                        if product.get('name') == item_name:
                            base_price = float(product.get('base_price', 0))
                            total_cost += item_quantity * base_price
                            break
            
            gross_profit = total_revenue - total_cost
            net_worth = inventory_value + customer_debt + gross_profit - customer_credit
            
            # Create table data
            table_data = [
                ["Financial Metric", "Amount (LBP)", "Amount (USD)", "Percentage", "Status"],
                ["Total Revenue", f"{total_revenue:,.0f}", f"${total_revenue/exchange_rate:,.2f}", "100%", "💰 Income"],
                ["Total Costs", f"{total_cost:,.0f}", f"${total_cost/exchange_rate:,.2f}", f"{(total_cost/total_revenue*100):.1f}%" if total_revenue > 0 else "0%", "💸 Expense"],
                ["Gross Profit", f"{gross_profit:,.0f}", f"${gross_profit/exchange_rate:,.2f}", f"{(gross_profit/total_revenue*100):.1f}%" if total_revenue > 0 else "0%", "📈 Profit"],
                ["Inventory Value", f"{inventory_value:,.0f}", f"${inventory_value/exchange_rate:,.2f}", f"{(inventory_value/net_worth*100):.1f}%" if net_worth > 0 else "0%", "📦 Assets"],
                ["Customer Debts", f"{customer_debt:,.0f}", f"${customer_debt/exchange_rate:,.2f}", f"{(customer_debt/net_worth*100):.1f}%" if net_worth > 0 else "0%", "📋 Receivable"],
                ["Customer Credits", f"{customer_credit:,.0f}", f"${customer_credit/exchange_rate:,.2f}", f"{(customer_credit/net_worth*100):.1f}%" if net_worth > 0 else "0%", "💳 Payable"],
                ["Net Worth", f"{net_worth:,.0f}", f"${net_worth/exchange_rate:,.2f}", "100%", "💎 Total Value"]
            ]
            
            self.create_generic_table(table_data, "Complete Financial Summary")
            self.current_table_data = table_data
            self.current_table_title = "Financial Summary"
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول الملخص المالي: {e}")
    
    def create_generic_table(self, data, title):
        """إنشاء جدول عام"""
        try:
            # Create scrollable table
            canvas = tk.Canvas(self.interactive_table_frame, bg='#ffffff')
            scrollbar = tk.Scrollbar(self.interactive_table_frame, orient="vertical", command=canvas.yview)
            table_frame = tk.Frame(canvas, bg='#ffffff')
            
            table_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=table_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            scrollbar.pack(side="right", fill="y")
            canvas.pack(side="left", fill="both", expand=True)
            
            # Table title
            title_label = tk.Label(table_frame, text=title, 
                                 font=("Arial", 12, "bold"), bg='#ffffff')
            title_label.pack(pady=10)
            
            # Create table
            for i, row in enumerate(data):
                row_frame = tk.Frame(table_frame, bg='#ecf0f1' if i % 2 == 0 else '#ffffff', relief='ridge', bd=1)
                row_frame.pack(fill='x', padx=5, pady=1)
                
                for j, cell in enumerate(row):
                    cell_bg = '#34495e' if i == 0 else row_frame['bg']
                    cell_fg = 'white' if i == 0 else 'black'
                    cell_font = ("Arial", 10, "bold") if i == 0 else ("Arial", 9)
                    
                    cell_label = tk.Label(row_frame, text=str(cell), font=cell_font,
                                        bg=cell_bg, fg=cell_fg, relief='ridge', bd=1)
                    cell_label.pack(side='left', fill='x', expand=True, padx=1, pady=1)
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجدول العام: {e}")
'''
        
        # Add the additional methods
        last_method = content.rfind("\n    def export_interactive_table(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + additional_methods + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + additional_methods + content[last_method:]
        
        # Update the statistics button
        if 'btn6 = tk.Button(sidebar, text="رصيد المحل"' in content:
            # Replace the existing button
            import re
            pattern = r'btn6 = tk\.Button\(sidebar, text="رصيد المحل"[^)]+\)'
            replacement = '''btn6 = tk.Button(sidebar, text="Advanced Stats", 
                           font=("Arial", 10), bg='#16a085', fg='white', 
                           width=18, height=2, command=self.create_advanced_statistics_page)'''
            
            content = re.sub(pattern, replacement, content, flags=re.DOTALL)
            print("✅ تم تحديث زر الإحصائيات")
        else:
            print("❌ لم يتم العثور على زر رصيد المحل")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إكمال نظام الإحصائيات المتقدم")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إكمال نظام الإحصائيات: {e}")
        return False

def main():
    """Main function"""
    print("📊 إكمال نظام الإحصائيات المتقدم لـ ProTech")
    print("📊 Completing Advanced Statistics System for ProTech")
    print("="*70)
    
    if complete_statistics_system():
        print("\n🎉 تم إكمال نظام الإحصائيات المتقدم بنجاح!")
        
        print("\n📊 النظام الكامل يشمل:")
        print("• 📈 صفحة إحصائيات متقدمة وتفاعلية")
        print("• 🎯 8 بطاقات إحصائيات سريعة ملونة")
        print("• 📋 5 أنواع جداول تفاعلية مختلفة")
        print("• 📊 تحليل الأعمال والرؤى الذكية")
        print("• 📤 تصدير البيانات إلى CSV")
        print("• 🔄 تحديث فوري للبيانات")
        print("• 🎨 تصميم احترافي مع ألوان مميزة")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Advanced Stats' في الشريط الجانبي")
        print("3. استعرض بطاقات الإحصائيات السريعة")
        print("4. اختر نوع الجدول من القائمة المنسدلة")
        print("5. انقر على 'Update Table' لتحديث البيانات")
        print("6. انقر على 'Export' لتصدير الجدول")
        print("7. اقرأ تحليل الأعمال والتوصيات")
        
        print("\n💡 الميزات المتقدمة:")
        print("• نقاط الأداء والتقييم التلقائي")
        print("• مؤشرات الصحة المالية للأعمال")
        print("• توصيات ذكية لتحسين الأداء")
        print("• عرض بالليرة والدولار")
        print("• جداول قابلة للتمرير")
        print("• تحليل اتجاهات المبيعات")
        print("• تقييم أداء المنتجات والعملاء")
        
    else:
        print("\n❌ فشل في إكمال نظام الإحصائيات المتقدم")
    
    print("\n🔧 تم الانتهاء من إكمال نظام الإحصائيات")

if __name__ == "__main__":
    main()
