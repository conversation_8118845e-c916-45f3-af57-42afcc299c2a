#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏢 برنامج ناصر النهائي للمحاسبة - النسخة المضمونة العمل
Nassar Final Accounting Program - Guaranteed Working Version

تم تطويره بواسطة Augment Agent
Developed by Augment Agent
التاريخ: 2025-06-20
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import os
import time
import threading
from datetime import datetime
import gc
import sys
import traceback

class NassarAccountingProgram:
    """برنامج ناصر النهائي للمحاسبة - النسخة المضمونة"""

    def __init__(self):
        print("🏢 تشغيل برنامج ناصر النهائي للمحاسبة...")
        print("🏢 Starting Nassar Final Accounting Program...")

        # تهيئة فورية لجميع المتغيرات لمنع AttributeError
        self.products = []
        self.customers = []
        self.suppliers = []
        self.sales = []
        
        # إعداد مسار ملف البيانات
        self.setup_data_file_path()
        
        print("✅ تم تهيئة جميع المتغيرات بنجاح")
        print("✅ All variables initialized successfully")

        # تحميل البيانات
        self.load_data()

        # إنشاء الواجهة الرئيسية
        self.create_main_window()
        
        print("🎉 تم تحميل برنامج ناصر بنجاح!")
        print("🎉 Nassar program loaded successfully!")

    def setup_data_file_path(self):
        """إعداد مسار ملف البيانات مع خيارات متعددة"""
        possible_paths = [
            # نفس مجلد البرنامج
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "protech_simple_data.json"),
            
            # مجلد البيانات في nassar program final
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\nassar program final\02_Data_Files\protech_simple_data.json",
            
            # مجلد accounting program الرئيسي
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_data.json",
            
            # مجلد Documents كخيار احتياطي
            os.path.join(os.path.expanduser("~"), "Documents", "protech_simple_data.json"),
            
            # المجلد الحالي
            "protech_simple_data.json"
        ]
        
        # البحث عن ملف البيانات الموجود
        self.data_file = None
        for path in possible_paths:
            if os.path.exists(path):
                self.data_file = path
                print(f"✅ تم العثور على ملف البيانات: {path}")
                break
        
        # إذا لم يوجد ملف، استخدم المسار الأول
        if not self.data_file:
            self.data_file = possible_paths[0]
            print(f"📁 سيتم إنشاء ملف البيانات في: {self.data_file}")

    def load_data(self):
        """تحميل البيانات من الملف أو إنشاء بيانات نموذجية"""
        try:
            if os.path.exists(self.data_file):
                print("📂 تحميل البيانات من الملف...")
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                print(f"✅ تم تحميل {len(self.products)} منتج، {len(self.customers)} عميل، {len(self.suppliers)} مورد")
                
            else:
                print("📝 إنشاء بيانات نموذجية...")
                self.create_sample_data()
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            print("🔄 إنشاء بيانات نموذجية...")
            self.create_sample_data()

    def create_sample_data(self):
        """إنشاء بيانات نموذجية شاملة"""
        print("🏗️ إنشاء البيانات النموذجية...")
        
        # الموردين
        self.suppliers = [
            {
                "id": 1,
                "name": "Tech Solutions Inc.",
                "name_ar": "شركة الحلول التقنية",
                "phone": "+966-11-123-4567",
                "email": "<EMAIL>",
                "address": "الرياض، المملكة العربية السعودية"
            },
            {
                "id": 2,
                "name": "Office World Ltd.",
                "name_ar": "شركة عالم المكاتب",
                "phone": "+966-11-234-5678",
                "email": "<EMAIL>",
                "address": "جدة، المملكة العربية السعودية"
            },
            {
                "id": 3,
                "name": "Electronics Hub",
                "name_ar": "مركز الإلكترونيات",
                "phone": "+966-11-345-6789",
                "email": "<EMAIL>",
                "address": "الدمام، المملكة العربية السعودية"
            }
        ]
        
        # المنتجات
        self.products = [
            {
                "id": 1,
                "barcode": "1234567890123",
                "name": "Business Laptop",
                "name_ar": "لابتوب الأعمال",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 1,
                "base_price": 1000.00,
                "retail_price": 1300.00,
                "wholesale_price": 1200.00,
                "distributor_price": 1150.00,
                "shop_owner_price": 1050.00,
                "stock": 45,
                "min_stock": 10,
                "unit": "piece",
                "unit_ar": "قطعة",
                "notes": "لابتوب عالي الأداء للأعمال"
            },
            {
                "id": 2,
                "barcode": "1234567890124",
                "name": "Wireless Mouse",
                "name_ar": "فأرة لاسلكية",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 2,
                "base_price": 25.00,
                "retail_price": 32.50,
                "wholesale_price": 30.00,
                "distributor_price": 28.75,
                "shop_owner_price": 26.25,
                "stock": 150,
                "min_stock": 30,
                "unit": "piece",
                "unit_ar": "قطعة",
                "notes": "فأرة لاسلكية عالية الدقة"
            },
            {
                "id": 3,
                "barcode": "1234567890125",
                "name": "Mechanical Keyboard",
                "name_ar": "لوحة مفاتيح ميكانيكية",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 3,
                "base_price": 80.00,
                "retail_price": 104.00,
                "wholesale_price": 96.00,
                "distributor_price": 92.00,
                "shop_owner_price": 84.00,
                "stock": 60,
                "min_stock": 15,
                "unit": "piece",
                "unit_ar": "قطعة",
                "notes": "لوحة مفاتيح ميكانيكية للألعاب"
            },
            {
                "id": 4,
                "barcode": "1234567890126",
                "name": "24 inch Monitor",
                "name_ar": "شاشة 24 بوصة",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 1,
                "base_price": 200.00,
                "retail_price": 260.00,
                "wholesale_price": 240.00,
                "distributor_price": 230.00,
                "shop_owner_price": 210.00,
                "stock": 30,
                "min_stock": 8,
                "unit": "piece",
                "unit_ar": "قطعة",
                "notes": "شاشة عالية الدقة 24 بوصة"
            },
            {
                "id": 5,
                "barcode": "1234567890127",
                "name": "Smartphone",
                "name_ar": "هاتف ذكي",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 2,
                "base_price": 400.00,
                "retail_price": 520.00,
                "wholesale_price": 480.00,
                "distributor_price": 460.00,
                "shop_owner_price": 420.00,
                "stock": 75,
                "min_stock": 20,
                "unit": "piece",
                "unit_ar": "قطعة",
                "notes": "هاتف ذكي متطور"
            },
            {
                "id": 6,
                "barcode": "1234567890128",
                "name": "USB Cable",
                "name_ar": "كابل USB",
                "category": "Accessories",
                "category_ar": "إكسسوارات",
                "supplier_id": 3,
                "base_price": 5.00,
                "retail_price": 6.50,
                "wholesale_price": 6.00,
                "distributor_price": 5.75,
                "shop_owner_price": 5.25,
                "stock": 200,
                "min_stock": 50,
                "unit": "piece",
                "unit_ar": "قطعة",
                "notes": "كابل USB عالي الجودة"
            },
            {
                "id": 7,
                "barcode": "*************",
                "name": "Power Bank",
                "name_ar": "بطارية محمولة",
                "category": "Accessories",
                "category_ar": "إكسسوارات",
                "supplier_id": 1,
                "base_price": 30.00,
                "retail_price": 39.00,
                "wholesale_price": 36.00,
                "distributor_price": 34.50,
                "shop_owner_price": 31.50,
                "stock": 100,
                "min_stock": 25,
                "unit": "piece",
                "unit_ar": "قطعة",
                "notes": "بطارية محمولة سعة 10000 مللي أمبير"
            }
        ]
        
        # العملاء
        self.customers = [
            {
                "id": 1,
                "name": "John Smith",
                "name_ar": "جون سميث",
                "phone": "******-1234",
                "email": "<EMAIL>",
                "address": "123 Main St, New York, USA",
                "type": "RETAIL",
                "type_ar": "تجزئة",
                "balance": 1250.00,
                "credit_limit": 5000.00
            },
            {
                "id": 2,
                "name": "ABC Corporation",
                "name_ar": "شركة ABC",
                "phone": "******-5678",
                "email": "<EMAIL>",
                "address": "456 Business Ave, Los Angeles, USA",
                "type": "WHOLESALE",
                "type_ar": "جملة",
                "balance": 8750.00,
                "credit_limit": 50000.00
            },
            {
                "id": 3,
                "name": "Ahmed Al-Rashid",
                "name_ar": "أحمد الراشد",
                "phone": "+966-50-123-4567",
                "email": "<EMAIL>",
                "address": "الرياض، المملكة العربية السعودية",
                "type": "SHOP_OWNER",
                "type_ar": "صاحب محل",
                "balance": 2500.00,
                "credit_limit": 15000.00
            }
        ]
        
        # المبيعات (فارغة في البداية)
        self.sales = []
        
        # حفظ البيانات النموذجية
        self.save_data()
        print("✅ تم إنشاء البيانات النموذجية بنجاح")

    def save_data(self):
        """حفظ البيانات في الملف"""
        try:
            # إنشاء المجلد إذا لم يكن موجوداً
            data_dir = os.path.dirname(self.data_file)
            if data_dir and not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0',
                'created_by': 'Nassar Accounting Program'
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"💾 تم حفظ البيانات في: {self.data_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            # محاولة حفظ في مكان بديل
            try:
                backup_file = os.path.join(os.path.expanduser("~"), "Documents", "protech_simple_data_backup.json")
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print(f"💾 تم حفظ البيانات في الملف الاحتياطي: {backup_file}")
            except:
                print("❌ فشل في حفظ البيانات")

    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("🏢 برنامج ناصر النهائي للمحاسبة - Nassar Final Accounting Program")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f8ff')
        
        # تكبير النافذة
        try:
            self.root.state('zoomed')
        except:
            self.root.attributes('-zoomed', True)
        
        # إعداد أيقونة النافذة (آلة حاسبة)
        try:
            # يمكن إضافة أيقونة هنا إذا كانت متوفرة
            pass
        except:
            pass
        
        # إنشاء الواجهة
        self.create_interface()
        
        # إعداد إغلاق النافذة
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # الحاوية الرئيسية
        main_container = tk.Frame(self.root, bg='#f0f8ff')
        main_container.pack(fill='both', expand=True)

        # الشريط الجانبي
        self.create_sidebar(main_container)
        
        # منطقة المحتوى
        self.content_frame = tk.Frame(main_container, bg='white', relief='sunken', bd=2)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        # عرض لوحة التحكم افتراضياً
        self.show_dashboard()

    def create_sidebar(self, parent):
        """إنشاء الشريط الجانبي"""
        self.sidebar = tk.Frame(parent, bg='#2c3e50', width=280, relief='raised', bd=2)
        self.sidebar.pack(side='left', fill='y', padx=5, pady=5)
        self.sidebar.pack_propagate(False)

        # عنوان البرنامج
        title_frame = tk.Frame(self.sidebar, bg='#34495e', relief='raised', bd=2)
        title_frame.pack(fill='x', padx=5, pady=5)
        
        title_label = tk.Label(
            title_frame,
            text="🏢 برنامج ناصر\nNassar Program",
            font=('Arial', 16, 'bold'),
            bg='#34495e',
            fg='white',
            justify='center'
        )
        title_label.pack(pady=15)

        # أزرار التنقل
        nav_buttons = [
            ("🏠 الرئيسية\nDashboard", self.show_dashboard, '#3498db'),
            ("📦 المخزون\nInventory", self.show_inventory, '#2ecc71'),
            ("👥 العملاء\nCustomers", self.show_customers, '#e74c3c'),
            ("🏢 الموردين\nSuppliers", self.show_suppliers, '#f39c12'),
            ("💰 المبيعات\nSales", self.show_sales, '#9b59b6'),
            ("📊 التقارير\nReports", self.show_reports, '#1abc9c'),
            ("⚙️ الإعدادات\nSettings", self.show_settings, '#95a5a6')
        ]

        for text, command, color in nav_buttons:
            btn = tk.Button(
                self.sidebar,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=12,
                cursor='hand2',
                anchor='center'
            )
            btn.pack(fill='x', padx=10, pady=5)
            
            # تأثيرات التمرير
            def on_enter(event, btn=btn, hover_color=self.darken_color(color)):
                btn.config(bg=hover_color)
            
            def on_leave(event, btn=btn, original_color=color):
                btn.config(bg=original_color)
            
            btn.bind('<Enter>', on_enter)
            btn.bind('<Leave>', on_leave)

        # معلومات النظام
        info_frame = tk.Frame(self.sidebar, bg='#2c3e50')
        info_frame.pack(side='bottom', fill='x', padx=5, pady=5)
        
        info_label = tk.Label(
            info_frame,
            text=f"📅 {datetime.now().strftime('%Y-%m-%d')}\n🕐 {datetime.now().strftime('%H:%M')}",
            font=('Arial', 10),
            bg='#2c3e50',
            fg='#bdc3c7',
            justify='center'
        )
        info_label.pack(pady=10)

    def darken_color(self, color):
        """تغميق اللون للتأثيرات"""
        color_map = {
            '#3498db': '#2980b9',
            '#2ecc71': '#27ae60',
            '#e74c3c': '#c0392b',
            '#f39c12': '#e67e22',
            '#9b59b6': '#8e44ad',
            '#1abc9c': '#16a085',
            '#95a5a6': '#7f8c8d'
        }
        return color_map.get(color, color)

    def clear_content(self):
        """مسح محتوى المنطقة الرئيسية"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        """عرض لوحة التحكم الرئيسية"""
        self.clear_content()

        # العنوان الرئيسي
        title_frame = tk.Frame(self.content_frame, bg='white')
        title_frame.pack(fill='x', pady=20)

        title_label = tk.Label(
            title_frame,
            text="🏠 لوحة التحكم الرئيسية\nMain Dashboard",
            font=('Arial', 24, 'bold'),
            bg='white',
            fg='#2c3e50',
            justify='center'
        )
        title_label.pack()

        # إطار الإحصائيات
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(pady=30)

        # الإحصائيات
        stats = [
            ("📦 المنتجات\nProducts", len(self.products), '#3498db'),
            ("👥 العملاء\nCustomers", len(self.customers), '#2ecc71'),
            ("🏢 الموردين\nSuppliers", len(self.suppliers), '#e74c3c'),
            ("💰 المبيعات\nSales", len(self.sales), '#f39c12')
        ]

        for i, (text, count, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
            stat_frame.grid(row=0, column=i, padx=20, pady=10, sticky='ew')

            # العدد
            count_label = tk.Label(
                stat_frame,
                text=str(count),
                font=('Arial', 36, 'bold'),
                bg=color,
                fg='white'
            )
            count_label.pack(pady=15)

            # النص
            text_label = tk.Label(
                stat_frame,
                text=text,
                font=('Arial', 14, 'bold'),
                bg=color,
                fg='white',
                justify='center'
            )
            text_label.pack(pady=(0, 15))

        # معلومات إضافية
        info_frame = tk.Frame(self.content_frame, bg='#ecf0f1', relief='sunken', bd=2)
        info_frame.pack(fill='x', padx=20, pady=20)

        welcome_text = f"""
🎉 مرحباً بك في برنامج ناصر النهائي للمحاسبة
Welcome to Nassar Final Accounting Program

📊 إحصائيات سريعة:
• إجمالي المنتجات: {len(self.products)} منتج
• إجمالي العملاء: {len(self.customers)} عميل
• إجمالي الموردين: {len(self.suppliers)} مورد
• إجمالي المبيعات: {len(self.sales)} فاتورة

💡 نصائح للاستخدام:
• استخدم القائمة الجانبية للتنقل بين الصفحات
• اضغط على زر "إعادة تحميل البيانات" إذا لم تظهر البيانات
• جميع البيانات محفوظة تلقائياً

🔄 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        info_label = tk.Label(
            info_frame,
            text=welcome_text,
            font=('Arial', 12),
            bg='#ecf0f1',
            fg='#2c3e50',
            justify='right',
            anchor='e'
        )
        info_label.pack(pady=20, padx=20)

    def show_inventory(self):
        """عرض صفحة المخزون"""
        self.clear_content()

        # العنوان
        title_frame = tk.Frame(self.content_frame, bg='white')
        title_frame.pack(fill='x', pady=10)

        title_label = tk.Label(
            title_frame,
            text="📦 إدارة المخزون\nInventory Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50',
            justify='center'
        )
        title_label.pack()

        # أزرار التحكم
        control_frame = tk.Frame(self.content_frame, bg='white')
        control_frame.pack(fill='x', pady=10)

        # زر إعادة تحميل البيانات (الأحمر المهم)
        reload_btn = tk.Button(
            control_frame,
            text="🔄 إعادة تحميل البيانات\nForce Reload Data",
            command=self.force_reload_data,
            font=('Arial', 14, 'bold'),
            bg='#dc2626',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        reload_btn.pack(side='left', padx=10)

        # زر إضافة منتج
        add_btn = tk.Button(
            control_frame,
            text="➕ إضافة منتج\nAdd Product",
            command=self.add_product,
            font=('Arial', 12, 'bold'),
            bg='#059669',
            fg='white',
            relief='raised',
            bd=3,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        add_btn.pack(side='left', padx=10)

        # جدول المنتجات
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # أعمدة الجدول
        columns = ('ID', 'Barcode', 'Name', 'Category', 'Base Price', 'Retail Price', 'Stock', 'Min Stock', 'Unit')

        # إنشاء Treeview
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # تعيين عناوين الأعمدة
        for col in columns:
            self.inventory_tree.heading(col, text=col, anchor='center')
            self.inventory_tree.column(col, width=120, anchor='center')

        # إضافة البيانات
        self.refresh_inventory_table()

        # شريط التمرير
        scrollbar_y = ttk.Scrollbar(table_frame, orient='vertical', command=self.inventory_tree.yview)
        scrollbar_x = ttk.Scrollbar(table_frame, orient='horizontal', command=self.inventory_tree.xview)

        self.inventory_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)

        # ترتيب العناصر
        self.inventory_tree.pack(side='left', fill='both', expand=True)
        scrollbar_y.pack(side='right', fill='y')
        scrollbar_x.pack(side='bottom', fill='x')

    def refresh_inventory_table(self):
        """تحديث جدول المخزون"""
        # مسح البيانات الحالية
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # إضافة البيانات الجديدة
        for product in self.products:
            values = (
                product.get('id', ''),
                product.get('barcode', ''),
                f"{product.get('name', '')} | {product.get('name_ar', '')}",
                f"{product.get('category', '')} | {product.get('category_ar', '')}",
                f"${product.get('base_price', 0):.2f}",
                f"${product.get('retail_price', 0):.2f}",
                product.get('stock', 0),
                product.get('min_stock', 0),
                f"{product.get('unit', '')} | {product.get('unit_ar', '')}"
            )
            self.inventory_tree.insert('', 'end', values=values)

    def force_reload_data(self):
        """إعادة تحميل البيانات بالقوة"""
        try:
            print("🔄 إعادة تحميل البيانات بالقوة...")

            # مسح البيانات الحالية
            self.products = []
            self.customers = []
            self.suppliers = []
            self.sales = []

            # إعادة تحميل من الملف
            self.load_data()

            # تحديث الجدول إذا كان موجوداً
            if hasattr(self, 'inventory_tree'):
                self.refresh_inventory_table()

            # رسالة نجاح
            messagebox.showinfo(
                "إعادة تحميل البيانات\nData Reload",
                f"✅ تم إعادة تحميل البيانات بنجاح!\n✅ Data reloaded successfully!\n\n"
                f"📊 المنتجات: {len(self.products)}\n📊 Products: {len(self.products)}\n"
                f"👥 العملاء: {len(self.customers)}\n👥 Customers: {len(self.customers)}\n"
                f"🏢 الموردين: {len(self.suppliers)}\n🏢 Suppliers: {len(self.suppliers)}\n"
                f"💰 المبيعات: {len(self.sales)}\n💰 Sales: {len(self.sales)}"
            )

            print("✅ تم إعادة تحميل البيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعادة تحميل البيانات: {e}")
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في إعادة تحميل البيانات\nFailed to reload data:\n{str(e)}"
            )

    def add_product(self):
        """إضافة منتج جديد"""
        messagebox.showinfo(
            "قريباً\nComing Soon",
            "ميزة إضافة المنتجات ستكون متوفرة قريباً\nAdd product feature coming soon"
        )

    def show_customers(self):
        """عرض صفحة العملاء"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء\nCustomer Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # جدول العملاء
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('ID', 'Name', 'Phone', 'Email', 'Type', 'Balance', 'Credit Limit')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col, anchor='center')
            tree.column(col, width=150, anchor='center')

        # إضافة العملاء
        for customer in self.customers:
            tree.insert('', 'end', values=(
                customer.get('id', ''),
                f"{customer.get('name', '')} | {customer.get('name_ar', '')}",
                customer.get('phone', ''),
                customer.get('email', ''),
                f"{customer.get('type', '')} | {customer.get('type_ar', '')}",
                f"${customer.get('balance', 0):.2f}",
                f"${customer.get('credit_limit', 0):.2f}"
            ))

        tree.pack(fill='both', expand=True)

    def show_suppliers(self):
        """عرض صفحة الموردين"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="🏢 إدارة الموردين\nSupplier Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # جدول الموردين
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('ID', 'Name', 'Phone', 'Email', 'Address')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col, anchor='center')
            tree.column(col, width=200, anchor='center')

        # إضافة الموردين
        for supplier in self.suppliers:
            tree.insert('', 'end', values=(
                supplier.get('id', ''),
                f"{supplier.get('name', '')} | {supplier.get('name_ar', '')}",
                supplier.get('phone', ''),
                supplier.get('email', ''),
                supplier.get('address', '')
            ))

        tree.pack(fill='both', expand=True)

    def show_sales(self):
        """عرض صفحة المبيعات"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات\nSales Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        info_label = tk.Label(
            self.content_frame,
            text="قريباً: نظام المبيعات المتقدم\nComing Soon: Advanced Sales System\n\n"
                 "سيتضمن:\n• إنشاء فواتير المبيعات\n• تتبع المدفوعات\n• حساب الأسعار التلقائي\n• طباعة الفواتير",
            font=('Arial', 16),
            bg='white',
            fg='#7f8c8d',
            justify='center'
        )
        info_label.pack(pady=100)

    def show_reports(self):
        """عرض صفحة التقارير"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="📊 التقارير والإحصائيات\nReports & Statistics",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        info_label = tk.Label(
            self.content_frame,
            text="قريباً: تقارير مفصلة\nComing Soon: Detailed Reports\n\n"
                 "سيتضمن:\n• تقارير المبيعات\n• تقارير المخزون\n• تقارير الأرباح\n• إحصائيات العملاء",
            font=('Arial', 16),
            bg='white',
            fg='#7f8c8d',
            justify='center'
        )
        info_label.pack(pady=100)

    def show_settings(self):
        """عرض صفحة الإعدادات"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="⚙️ إعدادات النظام\nSystem Settings",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # معلومات النظام
        info_frame = tk.Frame(self.content_frame, bg='#ecf0f1', relief='sunken', bd=2)
        info_frame.pack(fill='x', padx=20, pady=20)

        system_info = f"""
📋 معلومات النظام:
• اسم البرنامج: برنامج ناصر النهائي للمحاسبة
• الإصدار: 1.0
• المطور: Augment Agent
• التاريخ: 2025-06-20

📁 مسار ملف البيانات:
{self.data_file}

📊 إحصائيات البيانات:
• المنتجات: {len(self.products)}
• العملاء: {len(self.customers)}
• الموردين: {len(self.suppliers)}
• المبيعات: {len(self.sales)}

🔄 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        info_label = tk.Label(
            info_frame,
            text=system_info,
            font=('Arial', 12),
            bg='#ecf0f1',
            fg='#2c3e50',
            justify='right',
            anchor='e'
        )
        info_label.pack(pady=20, padx=20)

        # أزرار الإعدادات
        buttons_frame = tk.Frame(self.content_frame, bg='white')
        buttons_frame.pack(pady=20)

        backup_btn = tk.Button(
            buttons_frame,
            text="💾 إنشاء نسخة احتياطية\nCreate Backup",
            command=self.create_backup,
            font=('Arial', 12, 'bold'),
            bg='#3498db',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        backup_btn.pack(side='left', padx=10)

        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ البيانات\nSave Data",
            command=self.save_data,
            font=('Arial', 12, 'bold'),
            bg='#2ecc71',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        save_btn.pack(side='left', padx=10)

    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"protech_backup_{timestamp}.json"

            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'backup_created': datetime.now().isoformat(),
                'version': '1.0'
            }

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            messagebox.showinfo(
                "نسخة احتياطية\nBackup",
                f"✅ تم إنشاء النسخة الاحتياطية بنجاح!\n✅ Backup created successfully!\n\n"
                f"📁 الملف: {backup_file}"
            )

        except Exception as e:
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في إنشاء النسخة الاحتياطية\nFailed to create backup:\n{str(e)}"
            )

    def on_closing(self):
        """التعامل مع إغلاق النافذة"""
        try:
            print("💾 حفظ البيانات قبل الإغلاق...")
            self.save_data()
            print("✅ تم حفظ البيانات بنجاح")
            print("👋 إغلاق برنامج ناصر...")
        except Exception as e:
            print(f"⚠️ خطأ في حفظ البيانات: {e}")
        finally:
            self.root.destroy()

    def run(self):
        """تشغيل البرنامج"""
        try:
            print("🚀 تشغيل واجهة برنامج ناصر...")
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            traceback.print_exc()

def main():
    """الدالة الرئيسية"""
    try:
        print("🏢 بدء تشغيل برنامج ناصر النهائي للمحاسبة...")
        print("🏢 Starting Nassar Final Accounting Program...")

        app = NassarAccountingProgram()
        app.run()

    except Exception as e:
        print(f"❌ خطأ في بدء التشغيل: {e}")
        print(f"❌ Startup error: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
