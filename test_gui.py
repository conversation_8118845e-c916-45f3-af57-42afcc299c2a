#!/usr/bin/env python3
"""
Simple GUI test to verify tkinter is working
"""

import tkinter as tk
from tkinter import messagebox
import sys

def test_gui():
    """Test basic GUI functionality"""
    try:
        # Create main window
        root = tk.Tk()
        root.title("ProTech GUI Test")
        root.geometry("600x400")
        root.configure(bg='white')
        
        # Center the window
        root.update_idletasks()
        x = (root.winfo_screenwidth() // 2) - (600 // 2)
        y = (root.winfo_screenheight() // 2) - (400 // 2)
        root.geometry(f"600x400+{x}+{y}")
        
        # Header
        header_frame = tk.Frame(root, bg='#3b82f6', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(
            header_frame,
            text="🚀 ProTech GUI Test Window",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#3b82f6'
        ).pack(expand=True)
        
        # Content
        content_frame = tk.Frame(root, bg='white')
        content_frame.pack(fill='both', expand=True, padx=20, pady=20)
        
        # Test message
        tk.Label(
            content_frame,
            text="✅ GUI System is Working!",
            font=('Arial', 16, 'bold'),
            fg='#10b981',
            bg='white'
        ).pack(pady=20)
        
        tk.Label(
            content_frame,
            text="This confirms that tkinter GUI is functioning properly.",
            font=('Arial', 12),
            fg='#374151',
            bg='white'
        ).pack(pady=10)
        
        tk.Label(
            content_frame,
            text="ProTech application should be working similarly.",
            font=('Arial', 12),
            fg='#374151',
            bg='white'
        ).pack(pady=10)
        
        # Buttons
        button_frame = tk.Frame(content_frame, bg='white')
        button_frame.pack(pady=30)
        
        tk.Button(
            button_frame,
            text="✅ Test Successful",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            command=lambda: messagebox.showinfo("Success", "GUI Test Passed!"),
            cursor='hand2',
            width=15
        ).pack(side='left', padx=10)
        
        tk.Button(
            button_frame,
            text="❌ Close Test",
            font=('Arial', 12, 'bold'),
            bg='#ef4444',
            fg='white',
            command=root.destroy,
            cursor='hand2',
            width=15
        ).pack(side='left', padx=10)
        
        # Status
        tk.Label(
            content_frame,
            text="If you can see this window, ProTech should work too!",
            font=('Arial', 10),
            fg='#6b7280',
            bg='white'
        ).pack(pady=20)
        
        # Force window to front
        root.lift()
        root.attributes('-topmost', True)
        root.after_idle(lambda: root.attributes('-topmost', False))
        
        print("✅ GUI Test window created and should be visible")
        print("📍 Look for 'ProTech GUI Test Window' on your screen")
        
        # Start the GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ GUI Test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🧪 Starting GUI Test...")
    test_gui()
    print("🧪 GUI Test completed")
