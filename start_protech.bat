@echo off
title ProTech Accounting System - Starting...
color 0A

echo.
echo ============================================================
echo    🚀 تشغيل نظام ProTech للمحاسبة
echo    🚀 Starting ProTech Accounting System
echo ============================================================
echo.

echo [1/3] فحص Python / Checking Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ Python غير موجود / Python not found
    pause
    exit /b 1
)

echo ✅ Python موجود / Python found

echo.
echo [2/3] فحص Flask / Checking Flask...
python -c "import flask; print('Flask version:', flask.__version__)"
if %errorlevel% neq 0 (
    echo ⚠️ تثبيت Flask / Installing Flask...
    pip install flask
)

echo ✅ Flask جاهز / Flask ready

echo.
echo [3/3] بدء التطبيق / Starting application...
echo.
echo ┌─────────────────────────────────────────────────────────┐
echo │  🎉 نظام ProTech للمحاسبة                              │
echo │  🎉 ProTech Accounting System                           │
echo │                                                         │
echo │  🌐 الرابط / URL: http://localhost:5000                │
echo │  📱 متوافق مع الجوال / Mobile Compatible               │
echo │  🌍 يدعم العربية والإنجليزية / Arabic & English       │
echo │                                                         │
echo │  اضغط Ctrl+C لإيقاف الخادم / Press Ctrl+C to stop     │
echo └─────────────────────────────────────────────────────────┘
echo.

python app.py

echo.
echo 🛑 تم إيقاف الخادم / Server stopped
pause
