#!/usr/bin/env python3
"""
Fix Syntax Error in ProTech
إصلاح خطأ التركيب في ProTech

Fix the NameError: name 'self' is not defined error
إصلاح خطأ: name 'self' is not defined
"""

import os
import re
import shutil
from datetime import datetime

def backup_file():
    """Create backup of current file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.syntax_fix_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def fix_self_load_data_error():
    """Fix the self.load_data() error by removing it from wrong places"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        new_lines = []
        fixed_count = 0
        
        for i, line in enumerate(lines):
            # Check if this line contains self.load_data() in wrong context
            if 'self.load_data()' in line:
                # Check the context - it should be inside a method (indented properly)
                # Look at previous lines to see if we're inside a class but outside a method
                
                # Get the indentation level
                indent_level = len(line) - len(line.lstrip())
                
                # Check if we're in a class but not in a method
                in_class = False
                in_method = False
                
                # Look back to find context
                for j in range(i-1, max(0, i-20), -1):
                    prev_line = lines[j].strip()
                    if prev_line.startswith('class '):
                        in_class = True
                    elif prev_line.startswith('def ') and 'self' in prev_line:
                        in_method = True
                        break
                    elif prev_line.startswith('def ') and 'self' not in prev_line:
                        break
                
                # If we're in a class but not in a method, this is wrong
                if in_class and not in_method and indent_level < 8:
                    print(f"🔧 إزالة self.load_data() من السطر {i+1}: {line.strip()}")
                    fixed_count += 1
                    continue  # Skip this line
            
            new_lines.append(line)
        
        # Write the fixed file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.writelines(new_lines)
        
        print(f"✅ تم إصلاح {fixed_count} خطأ في self.load_data()")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح self.load_data(): {e}")
        return False

def add_proper_load_data_call():
    """Add proper load_data call in __init__ method"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the __init__ method and add load_data call properly
        init_pattern = r'(def __init__\(self.*?\):.*?)(self\.root\.mainloop\(\)|if __name__|def\s+\w+)'
        
        def add_load_call(match):
            init_content = match.group(1)
            next_part = match.group(2)
            
            # Check if load_data is already called properly
            if 'self.load_data()' not in init_content:
                # Add load_data call before the end of __init__
                lines = init_content.split('\n')
                
                # Find a good place to insert (before mainloop or at the end)
                insert_index = len(lines) - 1
                
                # Insert the load_data call
                lines.insert(insert_index, '')
                lines.insert(insert_index + 1, '        # Load existing data')
                lines.insert(insert_index + 2, '        try:')
                lines.insert(insert_index + 3, '            self.load_data()')
                lines.insert(insert_index + 4, '            print("✅ تم تحميل البيانات")')
                lines.insert(insert_index + 5, '        except Exception as e:')
                lines.insert(insert_index + 6, '            print(f"⚠️ خطأ في تحميل البيانات: {e}")')
                lines.insert(insert_index + 7, '            # Initialize empty data')
                lines.insert(insert_index + 8, '            self.suppliers = []')
                lines.insert(insert_index + 9, '            self.products = []')
                lines.insert(insert_index + 10, '            self.customers = []')
                lines.insert(insert_index + 11, '            self.sales = []')
                
                init_content = '\n'.join(lines)
                print("✅ تم إضافة استدعاء load_data في __init__")
            
            return init_content + next_part
        
        # Apply the fix
        content = re.sub(init_pattern, add_load_call, content, flags=re.DOTALL)
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة load_data: {e}")
        return False

def ensure_load_data_method_exists():
    """Ensure load_data method exists and is properly defined"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if load_data method exists
        if 'def load_data(self):' not in content:
            # Add a simple load_data method
            load_data_method = '''
    def load_data(self):
        """Load data from JSON file"""
        try:
            data_file = getattr(self, 'data_file', 'protech_simple_data.json')
            
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                print(f"📥 تم تحميل البيانات: {len(self.suppliers)} موردين، {len(self.products)} منتجات، {len(self.customers)} عملاء")
                return True
            else:
                print("📝 ملف البيانات غير موجود، سيتم إنشاؤه")
                self.suppliers = []
                self.products = []
                self.customers = []
                self.sales = []
                return True
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            return False
'''
            
            # Add the method before save_data or at the end of class
            if 'def save_data(self):' in content:
                content = content.replace('def save_data(self):', load_data_method + '\n    def save_data(self):')
            else:
                # Add before the last method or end of class
                content = content.replace('if __name__ == "__main__":', load_data_method + '\nif __name__ == "__main__":')
            
            with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إضافة دالة load_data")
        else:
            print("✅ دالة load_data موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص load_data: {e}")
        return False

def test_file_syntax():
    """Test if the file has correct syntax"""
    try:
        import py_compile
        py_compile.compile('protech_simple_working.py', doraise=True)
        print("✅ الملف يعمل بدون أخطاء تركيبية")
        return True
    except Exception as e:
        print(f"❌ خطأ في تركيب الملف: {e}")
        return False

def create_simple_launcher():
    """Create a simple launcher script"""
    launcher_content = '''#!/usr/bin/env python3
"""
ProTech Simple Launcher
مشغل ProTech البسيط

Simple launcher for ProTech that handles errors gracefully
مشغل بسيط لـ ProTech يتعامل مع الأخطاء بلطف
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def launch_protech():
    """Launch ProTech with error handling"""
    try:
        print("🚀 تشغيل ProTech...")
        
        # Check if the main file exists
        if not os.path.exists('protech_simple_working.py'):
            messagebox.showerror("خطأ", "ملف ProTech غير موجود!")
            return False
        
        # Try to run ProTech
        process = subprocess.Popen([sys.executable, 'protech_simple_working.py'])
        print("✅ تم تشغيل ProTech بنجاح")
        return True
        
    except Exception as e:
        error_msg = f"خطأ في تشغيل ProTech:\\n{str(e)}"
        print(f"❌ {error_msg}")
        
        # Show error dialog
        root = tk.Tk()
        root.withdraw()
        messagebox.showerror("خطأ في التشغيل", error_msg)
        root.destroy()
        
        return False

if __name__ == "__main__":
    launch_protech()
'''
    
    with open('launch_protech.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل بسيط: launch_protech.py")

def main():
    """Main function"""
    print("🔧 إصلاح خطأ التركيب في ProTech")
    print("🔧 Fixing Syntax Error in ProTech")
    print()
    
    try:
        # Step 1: Create backup
        backup_file()
        
        # Step 2: Fix self.load_data() errors
        print("🔧 إصلاح أخطاء self.load_data()...")
        if fix_self_load_data_error():
            print("✅ تم إصلاح أخطاء self.load_data()")
        
        # Step 3: Ensure load_data method exists
        print("📝 فحص وجود دالة load_data...")
        if ensure_load_data_method_exists():
            print("✅ دالة load_data جاهزة")
        
        # Step 4: Add proper load_data call in __init__
        print("🔧 إضافة استدعاء load_data في __init__...")
        if add_proper_load_data_call():
            print("✅ تم إضافة استدعاء load_data")
        
        # Step 5: Test syntax
        print("🧪 اختبار تركيب الملف...")
        if test_file_syntax():
            print("✅ الملف جاهز للتشغيل")
        
        # Step 6: Create simple launcher
        print("🚀 إنشاء مشغل بسيط...")
        create_simple_launcher()
        
        print("\n" + "="*60)
        print("✅ تم إصلاح خطأ التركيب بنجاح!")
        print("✅ Syntax error fixed successfully!")
        print("="*60)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إزالة self.load_data() من الأماكن الخاطئة")
        print("• إضافة استدعاء load_data في __init__ بشكل صحيح")
        print("• إضافة معالجة أخطاء شاملة")
        print("• إنشاء مشغل بسيط كبديل")
        
        print("\n🚀 طرق التشغيل:")
        print("1. النقر المزدوج على protech_simple_working.py")
        print("2. تشغيل launch_protech.py كبديل")
        print("3. من سطر الأوامر: python protech_simple_working.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح: {e}")
        return False

if __name__ == "__main__":
    main()
