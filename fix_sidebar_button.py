#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Sidebar Button
إصلاح زر الشريط الجانبي

Fix the sidebar button to use table reports
إصلاح زر الشريط الجانبي لاستخدام تقارير الجداول
"""

import os
import shutil
from datetime import datetime

def fix_sidebar_button():
    """إصلاح زر الشريط الجانبي"""
    try:
        print("🔧 إصلاح زر الشريط الجانبي")
        print("🔧 Fixing Sidebar Button")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.fix_sidebar_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the sidebar button with "تقرير شامل"
        if 'btn5 = tk.Button(sidebar, text="تقرير شامل"' in content:
            # Find the complete button definition
            start_pos = content.find('btn5 = tk.Button(sidebar, text="تقرير شامل"')
            if start_pos != -1:
                # Find the end of this button definition (look for the next line that starts with something else)
                lines = content[start_pos:].split('\n')
                button_lines = []
                for i, line in enumerate(lines):
                    button_lines.append(line)
                    if line.strip().endswith(')') and 'btn5' in line:
                        break
                    elif i > 0 and not line.strip().startswith('font=') and not line.strip().startswith('bg=') and not line.strip().startswith('fg=') and not line.strip().startswith('width=') and not line.strip().startswith('height=') and not line.strip().startswith('command=') and line.strip() and not line.strip().startswith('relief='):
                        break
                
                old_button = '\n'.join(button_lines[:-1]) if len(button_lines) > 1 else button_lines[0]
                
                # Create new button
                new_button = '''btn5 = tk.Button(sidebar, text="Table Reports", 
                           font=("Arial", 10), bg='#9b59b6', fg='white', 
                           width=18, height=2, command=self.create_reports_table_page)'''
                
                content = content.replace(old_button, new_button)
                print("✅ تم تحديث زر الشريط الجانبي")
            else:
                print("❌ لم يتم العثور على بداية تعريف الزر")
                return False
        else:
            print("❌ لم يتم العثور على زر 'تقرير شامل'")
            
            # Try to find any btn5 in sidebar
            import re
            sidebar_btn5_pattern = r'btn5 = tk\.Button\(sidebar[^)]+\)'
            matches = re.findall(sidebar_btn5_pattern, content, re.DOTALL)
            
            if matches:
                print(f"🔍 وجد btn5 في الشريط الجانبي: {matches[0][:100]}...")
                old_button = matches[0]
                new_button = '''btn5 = tk.Button(sidebar, text="Table Reports", 
                           font=("Arial", 10), bg='#9b59b6', fg='white', 
                           width=18, height=2, command=self.create_reports_table_page)'''
                
                content = content.replace(old_button, new_button)
                print("✅ تم تحديث زر الشريط الجانبي")
            else:
                print("❌ لم يتم العثور على btn5 في الشريط الجانبي")
                return False
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح زر الشريط الجانبي")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح زر الشريط الجانبي: {e}")
        return False

def verify_final_changes():
    """التحقق النهائي من التغييرات"""
    try:
        print("\n🔍 التحقق النهائي من التغييرات")
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('Table Reports في الزر', 'Table Reports' in content),
            ('create_reports_table_page في الاستدعاء', 'command=self.create_reports_table_page' in content),
            ('تعريف create_reports_table_page', 'def create_reports_table_page(self):' in content),
            ('تعريف show_products_in_table', 'def show_products_in_table(self):' in content),
            ('تعريف show_customers_in_table', 'def show_customers_in_table(self):' in content),
            ('تعريف show_sales_in_table', 'def show_sales_in_table(self):' in content),
            ('تعريف show_financial_in_table', 'def show_financial_in_table(self):' in content),
            ('تعريف show_profits_in_table', 'def show_profits_in_table(self):' in content),
            ('تعريف export_current_table', 'def export_current_table(self):' in content)
        ]
        
        all_good = True
        for check_name, result in checks:
            if result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        if all_good:
            print("\n🎉 جميع التحققات نجحت! النظام جاهز للاستخدام")
        else:
            print("\n⚠️ بعض التحققات فشلت")
        
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ في التحقق النهائي: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح زر الشريط الجانبي في ProTech")
    print("🔧 Fixing Sidebar Button in ProTech")
    print("="*60)
    
    if fix_sidebar_button():
        print("\n🎉 تم إصلاح زر الشريط الجانبي بنجاح!")
        
        # Verify all changes
        if verify_final_changes():
            print("\n📊 النظام الآن يحتوي على:")
            print("• زر 'Table Reports' في الشريط الجانبي")
            print("• صفحة تقارير متقدمة مع 5 تقارير")
            print("• جداول منسقة للبيانات")
            print("• إمكانية التصدير")
            
            print("\n🎯 كيفية الاستخدام:")
            print("1. افتح برنامج ProTech")
            print("2. ابحث عن زر 'Table Reports' في الشريط الجانبي")
            print("3. انقر على الزر")
            print("4. اختر التقرير المطلوب:")
            print("   • Products Table - جدول المنتجات")
            print("   • Customers Table - جدول العملاء")
            print("   • Sales Table - جدول المبيعات")
            print("   • Financial Report - التقرير المالي")
            print("   • Profits Table - جدول الأرباح")
            print("5. انقر على 'Export CSV' لتصدير البيانات")
            
            print("\n💡 الميزات:")
            print("• جداول منسقة مع أعمدة محاذية")
            print("• شريط تمرير للجداول الطويلة")
            print("• عرض بالليرة والدولار")
            print("• إحصائيات وإجماليات")
            print("• تصدير سهل للملفات")
        
    else:
        print("\n❌ فشل في إصلاح زر الشريط الجانبي")
    
    print("\n🔧 تم الانتهاء من الإصلاح")

if __name__ == "__main__":
    main()
