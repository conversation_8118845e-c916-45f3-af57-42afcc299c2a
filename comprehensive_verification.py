#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Verification for ProTech
التحقق الشامل من ProTech

Comprehensive verification of all fixes and improvements made to ProTech
تحقق شامل من جميع الإصلاحات والتحسينات المطبقة على ProTech
"""

import os
import json
import subprocess
import sys
import time
from datetime import datetime

class ProTechComprehensiveVerification:
    """Comprehensive verification for ProTech system"""
    
    def __init__(self):
        self.data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        self.main_file = "protech_simple_working.py"
        self.data_file = "protech_simple_data.json"
        self.verification_results = {
            'code_integrity': {},
            'data_integrity': {},
            'functionality': {},
            'performance': {},
            'arabic_encoding': {},
            'save_system': {}
        }
    
    def verify_code_integrity(self):
        """Verify code integrity after all fixes"""
        try:
            print("🔍 تحقق من سلامة الكود بعد الإصلاحات...")
            
            code_path = os.path.join(self.data_dir, self.main_file)
            
            if not os.path.exists(code_path):
                print("❌ ملف البرنامج غير موجود!")
                return False
            
            # Test 1: Syntax check
            try:
                result = subprocess.run([sys.executable, '-m', 'py_compile', code_path], 
                                      capture_output=True, text=True)
                syntax_ok = result.returncode == 0
                
                if syntax_ok:
                    print("✅ فحص التركيب: نجح")
                else:
                    print(f"❌ فحص التركيب: فشل - {result.stderr}")
                
            except Exception as e:
                print(f"❌ خطأ في فحص التركيب: {e}")
                syntax_ok = False
            
            # Test 2: File size check
            file_size = os.path.getsize(code_path)
            size_reasonable = 500000 <= file_size <= 2000000  # 500KB to 2MB
            
            if size_reasonable:
                print(f"✅ حجم الملف مناسب: {file_size/1024:.1f} KB")
            else:
                print(f"⚠️ حجم الملف غير عادي: {file_size/1024:.1f} KB")
            
            # Test 3: Encoding check
            try:
                with open(code_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                encoding_ok = True
                print("✅ ترميز الملف سليم")
            except UnicodeDecodeError:
                print("❌ مشكلة في ترميز الملف")
                encoding_ok = False
            
            self.verification_results['code_integrity'] = {
                'syntax_ok': syntax_ok,
                'size_reasonable': size_reasonable,
                'encoding_ok': encoding_ok,
                'file_size': file_size
            }
            
            return syntax_ok and encoding_ok
            
        except Exception as e:
            print(f"❌ خطأ في تحقق سلامة الكود: {e}")
            return False
    
    def verify_data_integrity(self):
        """Verify data integrity after encoding fixes"""
        try:
            print("📊 تحقق من سلامة البيانات...")
            
            data_path = os.path.join(self.data_dir, self.data_file)
            
            if not os.path.exists(data_path):
                print("❌ ملف البيانات غير موجود!")
                return False
            
            # Test 1: JSON validity
            try:
                with open(data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                json_valid = True
                print("✅ ملف JSON سليم")
            except json.JSONDecodeError as e:
                print(f"❌ ملف JSON تالف: {e}")
                json_valid = False
                return False
            
            # Test 2: Required structure
            required_keys = ['suppliers', 'products', 'customers', 'sales']
            structure_ok = all(key in data for key in required_keys)
            
            if structure_ok:
                print("✅ هيكل البيانات سليم")
            else:
                missing = [key for key in required_keys if key not in data]
                print(f"❌ مفاتيح مفقودة: {missing}")
            
            # Test 3: Data types
            types_ok = all(isinstance(data[key], list) for key in required_keys if key in data)
            
            if types_ok:
                print("✅ أنواع البيانات صحيحة")
            else:
                print("❌ أنواع البيانات غير صحيحة")
            
            # Test 4: Record counts
            record_counts = {key: len(data[key]) for key in required_keys if key in data}
            print(f"📊 عدد السجلات: {record_counts}")
            
            self.verification_results['data_integrity'] = {
                'json_valid': json_valid,
                'structure_ok': structure_ok,
                'types_ok': types_ok,
                'record_counts': record_counts
            }
            
            return json_valid and structure_ok and types_ok
            
        except Exception as e:
            print(f"❌ خطأ في تحقق سلامة البيانات: {e}")
            return False
    
    def verify_arabic_encoding(self):
        """Verify Arabic encoding fixes"""
        try:
            print("🔤 تحقق من إصلاح ترميز النص العربي...")
            
            data_path = os.path.join(self.data_dir, self.data_file)
            
            with open(data_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Test Arabic text in different sections
            arabic_tests = []
            
            # Test suppliers
            if data.get('suppliers') and len(data['suppliers']) > 0:
                supplier_name = data['suppliers'][0].get('name', '')
                arabic_tests.append(('مورد', supplier_name))
            
            # Test products
            if data.get('products') and len(data['products']) > 0:
                product_name = data['products'][0].get('name', '')
                arabic_tests.append(('منتج', product_name))
            
            # Test customers
            if data.get('customers') and len(data['customers']) > 0:
                customer_name = data['customers'][0].get('name', '')
                arabic_tests.append(('عميل', customer_name))
            
            arabic_ok = True
            for expected, actual in arabic_tests:
                if expected in actual:
                    print(f"✅ النص العربي صحيح: {actual}")
                else:
                    print(f"⚠️ النص قد لا يكون عربي: {actual}")
                    arabic_ok = False
            
            # Test encoding consistency
            try:
                json_str = json.dumps(data, ensure_ascii=False, indent=2)
                encoding_consistent = True
                print("✅ ترميز JSON متسق")
            except Exception as e:
                print(f"❌ مشكلة في ترميز JSON: {e}")
                encoding_consistent = False
            
            self.verification_results['arabic_encoding'] = {
                'arabic_text_ok': arabic_ok,
                'encoding_consistent': encoding_consistent
            }
            
            return arabic_ok and encoding_consistent
            
        except Exception as e:
            print(f"❌ خطأ في تحقق ترميز النص العربي: {e}")
            return False
    
    def verify_program_functionality(self):
        """Verify program functionality"""
        try:
            print("🚀 تحقق من وظائف البرنامج...")
            
            code_path = os.path.join(self.data_dir, self.main_file)
            
            # Test 1: Program starts without errors
            try:
                # Start program in background
                process = subprocess.Popen([sys.executable, code_path], 
                                         cwd=self.data_dir,
                                         stdout=subprocess.PIPE, 
                                         stderr=subprocess.PIPE)
                
                # Wait a bit for startup
                time.sleep(3)
                
                # Check if process is still running (good sign)
                if process.poll() is None:
                    print("✅ البرنامج يبدأ بدون أخطاء")
                    startup_ok = True
                    
                    # Terminate the test process
                    process.terminate()
                    try:
                        process.wait(timeout=5)
                    except subprocess.TimeoutExpired:
                        process.kill()
                else:
                    # Process ended, check for errors
                    stdout, stderr = process.communicate()
                    if stderr:
                        print(f"❌ البرنامج فشل في البدء: {stderr.decode('utf-8', errors='ignore')}")
                    else:
                        print("⚠️ البرنامج انتهى بسرعة (قد يكون طبيعي)")
                    startup_ok = False
                
            except Exception as e:
                print(f"❌ خطأ في اختبار بدء البرنامج: {e}")
                startup_ok = False
            
            # Test 2: Import test
            try:
                # Test if we can import the main components
                test_code = f"""
import sys
sys.path.append(r'{self.data_dir}')
try:
    import tkinter as tk
    print("tkinter import: OK")
    
    # Test basic functionality without running GUI
    root = tk.Tk()
    root.withdraw()  # Hide window
    root.destroy()
    print("tkinter basic test: OK")
except Exception as e:
    print(f"tkinter test failed: {{e}}")
"""
                
                result = subprocess.run([sys.executable, '-c', test_code], 
                                      capture_output=True, text=True)
                
                if 'tkinter basic test: OK' in result.stdout:
                    print("✅ مكونات البرنامج الأساسية تعمل")
                    components_ok = True
                else:
                    print("❌ مشكلة في مكونات البرنامج الأساسية")
                    components_ok = False
                
            except Exception as e:
                print(f"❌ خطأ في اختبار المكونات: {e}")
                components_ok = False
            
            self.verification_results['functionality'] = {
                'startup_ok': startup_ok,
                'components_ok': components_ok
            }
            
            return startup_ok and components_ok
            
        except Exception as e:
            print(f"❌ خطأ في تحقق الوظائف: {e}")
            return False
    
    def verify_save_system(self):
        """Verify save system functionality"""
        try:
            print("💾 تحقق من نظام الحفظ...")
            
            data_path = os.path.join(self.data_dir, self.data_file)
            
            # Test 1: File is writable
            try:
                with open(data_path, 'a', encoding='utf-8') as f:
                    pass
                writable = True
                print("✅ ملف البيانات قابل للكتابة")
            except Exception as e:
                print(f"❌ ملف البيانات غير قابل للكتابة: {e}")
                writable = False
            
            # Test 2: Backup files exist
            backup_patterns = ['*backup*', '*emergency*']
            backup_count = 0
            
            for pattern in backup_patterns:
                import glob
                backups = glob.glob(os.path.join(self.data_dir, f"*{pattern}*"))
                backup_count += len(backups)
            
            backups_exist = backup_count > 0
            if backups_exist:
                print(f"✅ نسخ احتياطية موجودة: {backup_count}")
            else:
                print("⚠️ لا توجد نسخ احتياطية")
            
            # Test 3: Temp file creation
            try:
                temp_path = os.path.join(self.data_dir, "verification_test.tmp")
                with open(temp_path, 'w', encoding='utf-8') as f:
                    f.write("test")
                
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    temp_creation_ok = True
                    print("✅ إنشاء ملفات مؤقتة يعمل")
                else:
                    temp_creation_ok = False
                    print("❌ مشكلة في إنشاء ملفات مؤقتة")
                    
            except Exception as e:
                print(f"❌ خطأ في اختبار الملفات المؤقتة: {e}")
                temp_creation_ok = False
            
            self.verification_results['save_system'] = {
                'writable': writable,
                'backups_exist': backups_exist,
                'backup_count': backup_count,
                'temp_creation_ok': temp_creation_ok
            }
            
            return writable and temp_creation_ok
            
        except Exception as e:
            print(f"❌ خطأ في تحقق نظام الحفظ: {e}")
            return False
    
    def run_comprehensive_verification(self):
        """Run comprehensive verification of all fixes"""
        try:
            print("🔍 بدء التحقق الشامل من جميع الإصلاحات")
            print("🔍 Starting Comprehensive Verification of All Fixes")
            print("="*60)
            
            # Run all verifications
            verifications = [
                ("سلامة الكود", self.verify_code_integrity),
                ("سلامة البيانات", self.verify_data_integrity),
                ("ترميز النص العربي", self.verify_arabic_encoding),
                ("وظائف البرنامج", self.verify_program_functionality),
                ("نظام الحفظ", self.verify_save_system),
            ]
            
            successful_verifications = 0
            total_verifications = len(verifications)
            
            for name, verification_func in verifications:
                print(f"\n🔍 تحقق من {name}...")
                if verification_func():
                    successful_verifications += 1
                    print(f"✅ {name}: تم التحقق بنجاح")
                else:
                    print(f"❌ {name}: فشل التحقق")
            
            # Generate summary
            success_rate = (successful_verifications / total_verifications) * 100
            
            print("\n" + "="*60)
            print(f"📊 ملخص التحقق الشامل:")
            print(f"✅ تحققات ناجحة: {successful_verifications}/{total_verifications}")
            print(f"📈 معدل النجاح: {success_rate:.1f}%")
            
            if success_rate >= 80:
                print("\n🎉 ممتاز! جميع الإصلاحات تعمل بشكل صحيح")
                print("🎉 Excellent! All fixes are working correctly")
                status = "excellent"
            elif success_rate >= 60:
                print("\n✅ جيد! معظم الإصلاحات تعمل بشكل صحيح")
                print("✅ Good! Most fixes are working correctly")
                status = "good"
            else:
                print("\n⚠️ يحتاج مراجعة! بعض الإصلاحات لا تعمل")
                print("⚠️ Needs review! Some fixes are not working")
                status = "needs_review"
            
            # Save verification report
            self.save_verification_report(success_rate, status)
            
            print("="*60)
            
            return success_rate >= 60
            
        except Exception as e:
            print(f"❌ خطأ في التحقق الشامل: {e}")
            return False
    
    def save_verification_report(self, success_rate, status):
        """Save verification report"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"protech_verification_report_{timestamp}.json"
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'success_rate': success_rate,
                'status': status,
                'verification_results': self.verification_results,
                'summary': {
                    'code_integrity_ok': all(self.verification_results.get('code_integrity', {}).values()),
                    'data_integrity_ok': all(self.verification_results.get('data_integrity', {}).values()),
                    'arabic_encoding_ok': all(self.verification_results.get('arabic_encoding', {}).values()),
                    'functionality_ok': all(self.verification_results.get('functionality', {}).values()),
                    'save_system_ok': all(self.verification_results.get('save_system', {}).values())
                }
            }
            
            report_path = os.path.join(self.data_dir, report_file)
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"📄 تم حفظ تقرير التحقق: {report_file}")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ التقرير: {e}")

def main():
    """Main verification function"""
    verifier = ProTechComprehensiveVerification()
    success = verifier.run_comprehensive_verification()
    
    if success:
        print("\n🎉 التحقق الشامل مكتمل بنجاح!")
        print("🎉 Comprehensive verification completed successfully!")
        print("\n🚀 ProTech جاهز للاستخدام مع جميع الإصلاحات!")
        print("🚀 ProTech is ready to use with all fixes!")
    else:
        print("\n⚠️ التحقق الشامل مكتمل مع مشاكل")
        print("⚠️ Comprehensive verification completed with issues")
    
    return success

if __name__ == "__main__":
    main()
