#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Multi-Currency Report
إضافة التقرير متعدد العملات

Add Lebanese Pound and USD currency support to store balance report
إضافة دعم الليرة اللبنانية والدولار الأمريكي لتقرير رصيد المحل
"""

import os
import shutil
from datetime import datetime

def add_multi_currency_support():
    """إضافة دعم العملات المتعددة"""
    try:
        print("💱 إضافة دعم العملات المتعددة للتقرير")
        print("💱 Adding Multi-Currency Support to Report")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.currency_support_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add currency conversion methods
        currency_methods = '''
    def get_exchange_rates(self):
        """الحصول على أسعار الصرف"""
        # أسعار الصرف الافتراضية (يمكن تحديثها)
        return {
            'USD_TO_LBP': 89500,  # دولار إلى ليرة لبنانية
            'LBP_TO_USD': 1/89500  # ليرة لبنانية إلى دولار
        }
    
    def format_currency(self, amount, currency='LBP'):
        """تنسيق العملة"""
        if currency == 'LBP':
            return f"{amount:,.0f} ل.ل"
        elif currency == 'USD':
            return f"${amount:,.2f}"
        else:
            return f"{amount:,.2f}"
    
    def convert_currency(self, amount, from_currency='LBP', to_currency='USD'):
        """تحويل العملة"""
        rates = self.get_exchange_rates()
        
        if from_currency == 'LBP' and to_currency == 'USD':
            return amount * rates['LBP_TO_USD']
        elif from_currency == 'USD' and to_currency == 'LBP':
            return amount * rates['USD_TO_LBP']
        else:
            return amount
    
    def show_store_balance_multi_currency(self):
        """عرض تقرير رصيد المحل بالعملات المتعددة"""
        try:
            self.report_title.config(text="رصيد المحل - عملات متعددة")
            
            # حساب رصيد البضاعة
            products = getattr(self, 'products', [])
            sales = getattr(self, 'sales', [])
            customers = getattr(self, 'customers', [])
            
            # أسعار الصرف
            rates = self.get_exchange_rates()
            usd_to_lbp = rates['USD_TO_LBP']
            lbp_to_usd = rates['LBP_TO_USD']
            
            # حساب قيمة البضاعة الإجمالية
            total_inventory_value_lbp = 0
            total_inventory_cost_lbp = 0
            categories_value = {}
            
            for product in products:
                stock = product.get('quantity', 0)
                base_price = product.get('base_price', 0)
                selling_price = product.get('shop_owner_price', base_price * 1.05)
                category = product.get('category', 'غير مصنف')
                
                # قيمة البضاعة بالليرة اللبنانية
                cost_value_lbp = stock * base_price
                total_inventory_cost_lbp += cost_value_lbp
                
                selling_value_lbp = stock * selling_price
                total_inventory_value_lbp += selling_value_lbp
                
                # تصنيف حسب الفئات
                if category not in categories_value:
                    categories_value[category] = {'cost_lbp': 0, 'selling_lbp': 0, 'quantity': 0}
                categories_value[category]['cost_lbp'] += cost_value_lbp
                categories_value[category]['selling_lbp'] += selling_value_lbp
                categories_value[category]['quantity'] += stock
            
            # تحويل إلى الدولار
            total_inventory_cost_usd = total_inventory_cost_lbp * lbp_to_usd
            total_inventory_value_usd = total_inventory_value_lbp * lbp_to_usd
            
            # حساب الأموال
            total_sales_revenue_lbp = sum(sale.get('total', 0) for sale in sales)
            total_sales_cost_lbp = 0
            
            # حساب تكلفة المبيعات
            for sale in sales:
                for item in sale.get('items', []):
                    for product in products:
                        if product.get('name') == item.get('name'):
                            cost_price = product.get('base_price', 0)
                            quantity_sold = item.get('quantity', 0)
                            total_sales_cost_lbp += cost_price * quantity_sold
                            break
            
            # تحويل المبيعات إلى الدولار
            total_sales_revenue_usd = total_sales_revenue_lbp * lbp_to_usd
            total_sales_cost_usd = total_sales_cost_lbp * lbp_to_usd
            
            # صافي الربح
            net_profit_lbp = total_sales_revenue_lbp - total_sales_cost_lbp
            net_profit_usd = net_profit_lbp * lbp_to_usd
            
            # حساب أرصدة العملاء
            total_customer_debt_lbp = 0
            total_customer_credit_lbp = 0
            
            for customer in customers:
                balance = customer.get('balance', 0)
                if balance > 0:
                    total_customer_credit_lbp += balance
                else:
                    total_customer_debt_lbp += abs(balance)
            
            # تحويل أرصدة العملاء إلى الدولار
            total_customer_debt_usd = total_customer_debt_lbp * lbp_to_usd
            total_customer_credit_usd = total_customer_credit_lbp * lbp_to_usd
            
            # إنشاء التقرير
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            report = "رصيد المحل - عملات متعددة\\n"
            report += "="*70 + "\\n\\n"
            report += "التاريخ: " + current_time + "\\n"
            report += "سعر الصرف: 1 USD = " + f"{usd_to_lbp:,.0f}" + " ل.ل\\n\\n"
            
            # قسم البضاعة
            report += "📦 رصيد البضاعة:\\n"
            report += "-"*50 + "\\n"
            report += "إجمالي المنتجات: " + str(len(products)) + " صنف\\n\\n"
            
            report += "قيمة البضاعة (سعر التكلفة):\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(total_inventory_cost_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(total_inventory_cost_usd, 'USD') + "\\n\\n"
            
            report += "قيمة البضاعة (سعر البيع):\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(total_inventory_value_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(total_inventory_value_usd, 'USD') + "\\n\\n"
            
            profit_lbp = total_inventory_value_lbp - total_inventory_cost_lbp
            profit_usd = total_inventory_value_usd - total_inventory_cost_usd
            report += "الربح المتوقع من البضاعة:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(profit_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(profit_usd, 'USD') + "\\n\\n"
            
            # تفصيل حسب الفئات
            if categories_value:
                report += "تفصيل البضاعة حسب الفئات:\\n"
                for category, values in categories_value.items():
                    cost_usd = values['cost_lbp'] * lbp_to_usd
                    selling_usd = values['selling_lbp'] * lbp_to_usd
                    profit_cat_lbp = values['selling_lbp'] - values['cost_lbp']
                    profit_cat_usd = selling_usd - cost_usd
                    
                    report += "• " + category + ":\\n"
                    report += "  الكمية: " + str(values['quantity']) + "\\n"
                    report += "  قيمة التكلفة: " + self.format_currency(values['cost_lbp'], 'LBP') + " / " + self.format_currency(cost_usd, 'USD') + "\\n"
                    report += "  قيمة البيع: " + self.format_currency(values['selling_lbp'], 'LBP') + " / " + self.format_currency(selling_usd, 'USD') + "\\n"
                    report += "  الربح المتوقع: " + self.format_currency(profit_cat_lbp, 'LBP') + " / " + self.format_currency(profit_cat_usd, 'USD') + "\\n\\n"
            
            # قسم الأموال
            report += "💰 رصيد الأموال:\\n"
            report += "-"*50 + "\\n"
            
            report += "إجمالي إيرادات المبيعات:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(total_sales_revenue_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(total_sales_revenue_usd, 'USD') + "\\n\\n"
            
            report += "إجمالي تكلفة المبيعات:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(total_sales_cost_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(total_sales_cost_usd, 'USD') + "\\n\\n"
            
            report += "صافي الربح المحقق:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(net_profit_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(net_profit_usd, 'USD') + "\\n\\n"
            
            # أرصدة العملاء
            report += "👥 أرصدة العملاء:\\n"
            report += "-"*50 + "\\n"
            
            report += "إجمالي ديون العملاء:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(total_customer_debt_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(total_customer_debt_usd, 'USD') + "\\n\\n"
            
            report += "إجمالي أرصدة العملاء:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(total_customer_credit_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(total_customer_credit_usd, 'USD') + "\\n\\n"
            
            net_customer_balance_lbp = total_customer_credit_lbp - total_customer_debt_lbp
            net_customer_balance_usd = total_customer_credit_usd - total_customer_debt_usd
            report += "صافي أرصدة العملاء:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(net_customer_balance_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(net_customer_balance_usd, 'USD') + "\\n\\n"
            
            # الرصيد الإجمالي للمحل
            report += "="*70 + "\\n"
            report += "📊 الرصيد الإجمالي للمحل:\\n"
            report += "="*70 + "\\n"
            
            total_assets_lbp = total_inventory_value_lbp + total_customer_credit_lbp
            total_assets_usd = total_inventory_value_usd + total_customer_credit_usd
            net_worth_lbp = total_assets_lbp - total_customer_debt_lbp + net_profit_lbp
            net_worth_usd = total_assets_usd - total_customer_debt_usd + net_profit_usd
            
            report += "إجمالي الأصول:\\n"
            report += "• قيمة البضاعة: " + self.format_currency(total_inventory_value_lbp, 'LBP') + " / " + self.format_currency(total_inventory_value_usd, 'USD') + "\\n"
            report += "• أرصدة العملاء: " + self.format_currency(total_customer_credit_lbp, 'LBP') + " / " + self.format_currency(total_customer_credit_usd, 'USD') + "\\n"
            report += "• الأرباح المحققة: " + self.format_currency(net_profit_lbp, 'LBP') + " / " + self.format_currency(net_profit_usd, 'USD') + "\\n"
            
            total_with_profit_lbp = total_assets_lbp + net_profit_lbp
            total_with_profit_usd = total_assets_usd + net_profit_usd
            report += "إجمالي الأصول: " + self.format_currency(total_with_profit_lbp, 'LBP') + " / " + self.format_currency(total_with_profit_usd, 'USD') + "\\n\\n"
            
            report += "إجمالي الخصوم:\\n"
            report += "• ديون العملاء: " + self.format_currency(total_customer_debt_lbp, 'LBP') + " / " + self.format_currency(total_customer_debt_usd, 'USD') + "\\n\\n"
            
            report += "صافي رصيد المحل:\\n"
            report += "• بالليرة اللبنانية: " + self.format_currency(net_worth_lbp, 'LBP') + "\\n"
            report += "• بالدولار الأمريكي: " + self.format_currency(net_worth_usd, 'USD') + "\\n\\n"
            
            # تحليل الأداء
            if total_inventory_cost_lbp > 0:
                inventory_margin = ((total_inventory_value_lbp - total_inventory_cost_lbp) / total_inventory_cost_lbp) * 100
                report += "📈 تحليل الأداء:\\n"
                report += "-"*50 + "\\n"
                report += "هامش ربح البضاعة: " + str(round(inventory_margin, 1)) + "%\\n"
                
                if total_sales_revenue_lbp > 0:
                    sales_margin = (net_profit_lbp / total_sales_revenue_lbp) * 100
                    report += "هامش ربح المبيعات: " + str(round(sales_margin, 1)) + "%\\n"
                
                turnover_ratio = total_sales_cost_lbp / total_inventory_cost_lbp if total_inventory_cost_lbp > 0 else 0
                report += "معدل دوران المخزون: " + str(round(turnover_ratio, 2)) + "\\n\\n"
            
            # مقارنة العملات
            report += "💱 مقارنة العملات:\\n"
            report += "-"*50 + "\\n"
            report += "سعر الصرف المستخدم: 1 USD = " + f"{usd_to_lbp:,.0f}" + " ل.ل\\n"
            
            if net_worth_usd >= 1000:
                report += "رصيد المحل بالدولار مناسب للاستثمار\\n"
            elif net_worth_usd >= 100:
                report += "رصيد المحل بالدولار متوسط\\n"
            else:
                report += "رصيد المحل بالدولار يحتاج تحسين\\n"
            
            # توصيات
            report += "\\n💡 التوصيات:\\n"
            report += "-"*50 + "\\n"
            
            if total_customer_debt_lbp > total_customer_credit_lbp:
                report += "• متابعة تحصيل ديون العملاء\\n"
            
            if net_worth_usd < 1000:
                report += "• العمل على زيادة رأس المال بالدولار\\n"
            
            report += "• مراقبة أسعار الصرف يومياً\\n"
            report += "• تحديث أسعار المنتجات حسب سعر الصرف\\n"
            report += "• الاحتفاظ بجزء من الأرباح بالدولار\\n"
            report += "• مراجعة التقرير أسبوعياً\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
        except Exception as e:
            print("خطأ في عرض تقرير العملات المتعددة:", str(e))
            error_report = "خطأ في تحميل تقرير العملات المتعددة\\n"
            error_report += "تأكد من وجود البيانات وأعد المحاولة"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_report)
'''
        
        # Add the currency methods before the last method
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + currency_methods + content[last_method:]
        else:
            # If fallback method not found, add before any method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + currency_methods + content[last_method:]
        
        # Add new button for multi-currency report
        if "btn6 = tk.Button(sidebar, text=\"رصيد المحل\"" in content:
            # Find the position after btn6
            btn6_pos = content.find("btn6.pack(pady=3, padx=10, fill='x')")
            if btn6_pos != -1:
                btn6_end = content.find("\n", btn6_pos) + 1
                
                # Add new button
                new_button = '''
            btn7 = tk.Button(sidebar, text="رصيد المحل (عملات)", 
                           font=("Arial", 10), bg='#e67e22', fg='white',
                           width=18, height=2, command=self.show_store_balance_multi_currency)
            btn7.pack(pady=3, padx=10, fill='x')
'''
                
                content = content[:btn6_end] + new_button + content[btn6_end:]
                print("✅ تم إضافة زر رصيد المحل (عملات)")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة دعم العملات المتعددة")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة دعم العملات المتعددة: {e}")
        return False
