#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Safe Performance Optimizer for ProTech
محسن الأداء الآمن لـ ProTech

Safely optimize ProTech performance without causing crashes
تحسين أداء ProTech بأمان دون التسبب في أعطال
"""

import os
import shutil
import json
import time
from datetime import datetime

class SafePerformanceOptimizer:
    """Safe performance optimizer for ProTech"""
    
    def __init__(self):
        self.main_file = "protech_simple_working.py"
        self.data_file = "protech_simple_data.json"
        self.backup_created = False
        self.optimizations_applied = []
    
    def create_safety_backup(self):
        """Create comprehensive safety backup"""
        try:
            print("💾 إنشاء نسخة احتياطية آمنة...")
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_dir = f"safety_backup_{timestamp}"
            
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            # Backup main files
            important_files = [
                self.main_file,
                self.data_file,
                "protech_config.json",
                "robust_save_function.py"
            ]
            
            for file in important_files:
                if os.path.exists(file):
                    shutil.copy2(file, os.path.join(backup_dir, file))
                    print(f"✅ نسخ احتياطي: {file}")
            
            self.backup_created = True
            print(f"✅ تم إنشاء النسخة الاحتياطية: {backup_dir}")
            return backup_dir
            
        except Exception as e:
            print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
            return None
    
    def test_current_functionality(self):
        """Test current functionality before optimization"""
        try:
            print("🧪 اختبار الوظائف الحالية...")
            
            # Test 1: File compilation
            import subprocess
            import sys
            
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.main_file], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print("❌ البرنامج لا يُجمع حالياً")
                return False
            
            print("✅ تجميع البرنامج: نجح")
            
            # Test 2: Data file integrity
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    json.load(f)
                print("✅ ملف البيانات: سليم")
            else:
                print("⚠️ ملف البيانات غير موجود")
            
            # Test 3: File permissions
            if os.access(self.main_file, os.R_OK | os.W_OK):
                print("✅ صلاحيات الملف: متاحة")
            else:
                print("❌ صلاحيات الملف: غير متاحة")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في اختبار الوظائف: {e}")
            return False
    
    def optimize_imports(self):
        """Optimize imports safely"""
        try:
            print("📦 تحسين الاستيراد...")
            
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add performance imports at the top
            performance_imports = """
# Performance optimization imports
import gc
import threading
from functools import lru_cache
import weakref

"""
            
            # Check if already optimized
            if "Performance optimization imports" in content:
                print("✅ الاستيراد محسن مسبقاً")
                return True
            
            # Find the right place to insert
            lines = content.split('\n')
            insert_pos = 0
            
            for i, line in enumerate(lines):
                if line.strip().startswith('import ') or line.strip().startswith('from '):
                    insert_pos = i
                elif line.strip() and not line.strip().startswith('#'):
                    break
            
            # Insert performance imports
            lines.insert(insert_pos, performance_imports)
            
            # Write back
            with open(self.main_file, 'w', encoding='utf-8') as f:
                f.write('\n'.join(lines))
            
            self.optimizations_applied.append("تحسين الاستيراد")
            print("✅ تم تحسين الاستيراد")
            return True
            
        except Exception as e:
            print(f"❌ فشل في تحسين الاستيراد: {e}")
            return False
    
    def add_memory_optimization(self):
        """Add memory optimization functions"""
        try:
            print("🧠 إضافة تحسين الذاكرة...")
            
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Memory optimization functions
            memory_functions = '''
# Memory optimization functions
def optimize_memory():
    """Optimize memory usage"""
    try:
        gc.collect()
        return True
    except:
        return False

@lru_cache(maxsize=128)
def get_cached_data(key):
    """Get cached data with LRU cache"""
    return key

def cleanup_memory():
    """Clean up memory periodically"""
    try:
        gc.collect()
        return True
    except:
        return False

'''
            
            # Check if already added
            if "optimize_memory()" in content:
                print("✅ تحسين الذاكرة موجود مسبقاً")
                return True
            
            # Find class definition to insert before it
            class_pos = content.find("class ")
            if class_pos == -1:
                # Insert at end of imports
                import_end = content.rfind("import ")
                if import_end != -1:
                    next_line = content.find('\n', import_end)
                    content = content[:next_line] + '\n' + memory_functions + content[next_line:]
                else:
                    content = memory_functions + '\n' + content
            else:
                content = content[:class_pos] + memory_functions + '\n' + content[class_pos:]
            
            # Write back
            with open(self.main_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.optimizations_applied.append("تحسين الذاكرة")
            print("✅ تم إضافة تحسين الذاكرة")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إضافة تحسين الذاكرة: {e}")
            return False
    
    def optimize_data_loading(self):
        """Optimize data loading performance"""
        try:
            print("📊 تحسين تحميل البيانات...")
            
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add lazy loading function
            lazy_loading_code = '''
    def load_data_optimized(self):
        """Optimized data loading with caching"""
        try:
            if hasattr(self, '_data_cache') and self._data_cache:
                return self._data_cache
            
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Cache the data
                self._data_cache = data
                
                # Load into instance variables
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                return data
            else:
                return self.create_empty_data()
                
        except Exception as e:
            print(f"خطأ في تحميل البيانات المحسن: {e}")
            return self.create_empty_data()
    
    def create_empty_data(self):
        """Create empty data structure"""
        empty_data = {
            'suppliers': [],
            'products': [],
            'customers': [],
            'sales': []
        }
        self._data_cache = empty_data
        return empty_data

'''
            
            # Check if already optimized
            if "load_data_optimized" in content:
                print("✅ تحميل البيانات محسن مسبقاً")
                return True
            
            # Find class definition and add method
            class_start = content.find("class ")
            if class_start != -1:
                # Find the end of __init__ method
                init_start = content.find("def __init__", class_start)
                if init_start != -1:
                    # Find next method or end of class
                    next_method = content.find("\n    def ", init_start + 1)
                    if next_method != -1:
                        content = content[:next_method] + '\n' + lazy_loading_code + content[next_method:]
                    else:
                        # Add at end of class
                        class_end = content.find("\nclass ", class_start + 1)
                        if class_end == -1:
                            class_end = len(content)
                        content = content[:class_end] + '\n' + lazy_loading_code + content[class_end:]
            
            # Write back
            with open(self.main_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.optimizations_applied.append("تحسين تحميل البيانات")
            print("✅ تم تحسين تحميل البيانات")
            return True
            
        except Exception as e:
            print(f"❌ فشل في تحسين تحميل البيانات: {e}")
            return False
    
    def add_performance_monitoring(self):
        """Add performance monitoring"""
        try:
            print("📈 إضافة مراقبة الأداء...")
            
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Performance monitoring code
            monitoring_code = '''
    def start_performance_monitoring(self):
        """Start performance monitoring"""
        try:
            self.performance_data = {
                'start_time': time.time(),
                'memory_usage': [],
                'operation_times': {}
            }
            
            # Start monitoring thread
            if not hasattr(self, 'monitor_thread'):
                self.monitor_thread = threading.Thread(target=self.monitor_performance, daemon=True)
                self.monitor_thread.start()
            
            return True
        except Exception as e:
            print(f"خطأ في بدء مراقبة الأداء: {e}")
            return False
    
    def monitor_performance(self):
        """Monitor performance in background"""
        try:
            while True:
                if hasattr(self, 'performance_data'):
                    # Simple memory monitoring
                    try:
                        import psutil
                        process = psutil.Process()
                        memory_mb = process.memory_info().rss / 1024 / 1024
                        self.performance_data['memory_usage'].append({
                            'time': time.time(),
                            'memory_mb': memory_mb
                        })
                        
                        # Keep only last 100 entries
                        if len(self.performance_data['memory_usage']) > 100:
                            self.performance_data['memory_usage'] = self.performance_data['memory_usage'][-100:]
                    except:
                        pass
                
                time.sleep(30)  # Monitor every 30 seconds
        except:
            pass

'''
            
            # Check if already added
            if "start_performance_monitoring" in content:
                print("✅ مراقبة الأداء موجودة مسبقاً")
                return True
            
            # Add to class
            class_start = content.find("class ")
            if class_start != -1:
                # Find a good place to insert
                init_end = content.find("def __init__", class_start)
                if init_end != -1:
                    method_end = content.find("\n    def ", init_end + 1)
                    if method_end == -1:
                        method_end = len(content)
                    content = content[:method_end] + '\n' + monitoring_code + content[method_end:]
            
            # Write back
            with open(self.main_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.optimizations_applied.append("مراقبة الأداء")
            print("✅ تم إضافة مراقبة الأداء")
            return True
            
        except Exception as e:
            print(f"❌ فشل في إضافة مراقبة الأداء: {e}")
            return False
    
    def test_after_optimization(self):
        """Test functionality after optimization"""
        try:
            print("🧪 اختبار البرنامج بعد التحسين...")
            
            # Test compilation
            import subprocess
            import sys
            
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.main_file], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print("❌ البرنامج لا يُجمع بعد التحسين!")
                print(f"الخطأ: {result.stderr}")
                return False
            
            print("✅ تجميع البرنامج بعد التحسين: نجح")
            
            # Test basic functionality
            try:
                # Try to import and basic check
                with open(self.main_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Check for syntax issues
                compile(content, self.main_file, 'exec')
                print("✅ فحص التركيب: نجح")
                
            except SyntaxError as e:
                print(f"❌ خطأ تركيب: {e}")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
    
    def run_safe_optimization(self):
        """Run safe optimization process"""
        try:
            print("🚀 بدء تحسين الأداء الآمن لـ ProTech")
            print("🚀 Starting Safe Performance Optimization for ProTech")
            print("="*60)
            
            # Step 1: Create safety backup
            backup_dir = self.create_safety_backup()
            if not backup_dir:
                print("❌ فشل في إنشاء النسخة الاحتياطية - توقف التحسين")
                return False
            
            # Step 2: Test current functionality
            if not self.test_current_functionality():
                print("❌ البرنامج لا يعمل حالياً - توقف التحسين")
                return False
            
            # Step 3: Apply optimizations one by one
            optimizations = [
                ("تحسين الاستيراد", self.optimize_imports),
                ("تحسين الذاكرة", self.add_memory_optimization),
                ("تحسين تحميل البيانات", self.optimize_data_loading),
                ("مراقبة الأداء", self.add_performance_monitoring)
            ]
            
            for opt_name, opt_func in optimizations:
                print(f"\n🔧 تطبيق: {opt_name}...")
                
                if opt_func():
                    # Test after each optimization
                    if self.test_after_optimization():
                        print(f"✅ {opt_name}: نجح")
                    else:
                        print(f"❌ {opt_name}: تسبب في مشكلة - استعادة النسخة الاحتياطية")
                        # Restore from backup
                        shutil.copy2(os.path.join(backup_dir, self.main_file), self.main_file)
                        return False
                else:
                    print(f"⚠️ {opt_name}: تم تخطيه")
            
            # Final test
            print("\n🧪 اختبار نهائي شامل...")
            if self.test_after_optimization():
                print("✅ جميع التحسينات نجحت!")
                
                # Generate report
                self.generate_optimization_report()
                
                return True
            else:
                print("❌ فشل الاختبار النهائي")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في التحسين الآمن: {e}")
            return False
    
    def generate_optimization_report(self):
        """Generate optimization report"""
        try:
            report = {
                'timestamp': datetime.now().isoformat(),
                'optimizations_applied': self.optimizations_applied,
                'backup_created': self.backup_created,
                'status': 'success'
            }
            
            report_file = f"optimization_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"\n📄 تقرير التحسين: {report_file}")
            print(f"🔧 تحسينات مطبقة: {len(self.optimizations_applied)}")
            for opt in self.optimizations_applied:
                print(f"  ✅ {opt}")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء التقرير: {e}")

def main():
    """Main function"""
    optimizer = SafePerformanceOptimizer()
    success = optimizer.run_safe_optimization()
    
    if success:
        print("\n🎉 تم تحسين الأداء بنجاح دون أعطال!")
        print("🎉 Performance optimized successfully without crashes!")
    else:
        print("\n❌ فشل في تحسين الأداء أو تم منع الأعطال")
        print("❌ Performance optimization failed or crashes prevented")
    
    return success

if __name__ == "__main__":
    main()
