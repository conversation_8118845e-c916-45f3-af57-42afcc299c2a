#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Auto Save System for ProTech
نظام الحفظ التلقائي لـ ProTech

Automatic saving after every operation and data persistence
حفظ تلقائي بعد كل عملية واستمرارية البيانات
"""

import os
import json
import shutil
from datetime import datetime

def create_auto_save_system():
    """Create comprehensive auto-save system for ProTech"""
    try:
        print("🔄 إنشاء نظام الحفظ التلقائي الشامل")
        print("🔄 Creating Comprehensive Auto-Save System")
        print("="*60)

        # Find ProTech file
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")

        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False

        # Create backup
        backup_name = f"{protech_file}.auto_save_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")

        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Auto-save system code
        auto_save_code = '''
# ===== AUTO SAVE SYSTEM - START =====
# نظام الحفظ التلقائي - البداية

import threading
import time
from functools import wraps

class AutoSaveManager:
    """Comprehensive auto-save manager"""

    def __init__(self, app_instance):
        self.app = app_instance
        self.save_enabled = True
        self.last_save_time = time.time()
        self.save_queue = []
        self.setup_auto_save()

    def setup_auto_save(self):
        """Setup auto-save system"""
        try:
            # Override data modification methods
            self.wrap_data_methods()

            # Setup window close handler
            if hasattr(self.app, 'root'):
                self.app.root.protocol("WM_DELETE_WINDOW", self.on_close)

            print("✅ تم تفعيل نظام الحفظ التلقائي")

        except Exception as e:
            print(f"❌ خطأ في إعداد الحفظ التلقائي: {e}")

    def wrap_data_methods(self):
        """Wrap all data modification methods with auto-save"""
        try:
            # Methods that modify data
            methods_to_wrap = [
                'add_supplier', 'edit_supplier', 'delete_supplier',
                'add_product', 'edit_product', 'delete_product',
                'add_customer', 'edit_customer', 'delete_customer',
                'add_sale', 'edit_sale', 'delete_sale',
                'save_invoice', 'update_inventory'
            ]

            for method_name in methods_to_wrap:
                if hasattr(self.app, method_name):
                    original_method = getattr(self.app, method_name)
                    wrapped_method = self.auto_save_wrapper(original_method, method_name)
                    setattr(self.app, method_name, wrapped_method)

        except Exception as e:
            print(f"❌ خطأ في تغليف الطرق: {e}")

    def auto_save_wrapper(self, original_method, method_name):
        """Wrapper that adds auto-save to any method"""
        @wraps(original_method)
        def wrapper(*args, **kwargs):
            try:
                # Execute original method
                result = original_method(*args, **kwargs)

                # Auto-save after successful operation
                self.trigger_save(f"بعد {method_name}")

                return result

            except Exception as e:
                print(f"❌ خطأ في {method_name}: {e}")
                raise

        return wrapper

    def trigger_save(self, reason="تلقائي"):
        """Trigger immediate save"""
        try:
            if self.save_enabled and hasattr(self.app, 'save_data'):
                success = self.app.save_data()
                if success:
                    current_time = datetime.now().strftime('%H:%M:%S')
                    print(f"💾 حفظ تلقائي {reason}: {current_time}")
                    self.last_save_time = time.time()
                else:
                    print(f"⚠️ فشل الحفظ التلقائي {reason}")

        except Exception as e:
            print(f"❌ خطأ في الحفظ التلقائي: {e}")

    def on_close(self):
        """Handle window close with final save"""
        try:
            print("🔄 حفظ نهائي قبل الإغلاق...")
            self.trigger_save("قبل الإغلاق")

            # Small delay to ensure save completes
            time.sleep(0.5)

            # Close the application
            if hasattr(self.app, 'root'):
                self.app.root.destroy()

        except Exception as e:
            print(f"❌ خطأ في الإغلاق: {e}")
            # Force close if error
            try:
                self.app.root.destroy()
            except:
                pass

def enhanced_save_data(self):
    """Enhanced save_data with better reliability"""
    try:
        # Prepare comprehensive data
        data = {
            'suppliers': getattr(self, 'suppliers', []),
            'products': getattr(self, 'products', []),
            'customers': getattr(self, 'customers', []),
            'sales': getattr(self, 'sales', []),
            'settings': getattr(self, 'settings', {}),
            'last_saved': datetime.now().isoformat(),
            'version': '1.0'
        }

        # Validate data before saving
        total_items = (len(data['suppliers']) + len(data['products']) +
                      len(data['customers']) + len(data['sales']))

        # Save to main file with atomic write
        temp_file = f"{self.data_file}.tmp"

        try:
            # Write to temporary file first
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

            # Atomic move to final location
            if os.path.exists(temp_file):
                shutil.move(temp_file, self.data_file)

            # Create timestamped backup
            backup_file = f"{self.data_file}.autosave_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(self.data_file, backup_file)

            # Clean old auto-save backups (keep last 10)
            self.cleanup_autosave_backups()

            return True

        except Exception as e:
            print(f"❌ خطأ في الحفظ: {e}")

            # Clean up temp file if it exists
            if os.path.exists(temp_file):
                try:
                    os.remove(temp_file)
                except:
                    pass

            return False

    except Exception as e:
        print(f"❌ خطأ عام في الحفظ: {e}")
        return False

def cleanup_autosave_backups(self):
    """Clean up old auto-save backup files"""
    try:
        import glob

        # Find all auto-save backups
        backup_pattern = f"{self.data_file}.autosave_*"
        backup_files = glob.glob(backup_pattern)

        if len(backup_files) > 10:
            # Sort by modification time and keep only the 10 newest
            backup_files.sort(key=os.path.getmtime, reverse=True)
            for old_backup in backup_files[10:]:
                try:
                    os.remove(old_backup)
                except:
                    pass

    except Exception as e:
        print(f"تحذير: مشكلة في تنظيف النسخ الاحتياطية: {e}")

def enhanced_load_data(self):
    """Enhanced load_data with better error handling and display"""
    try:
        if os.path.exists(self.data_file):
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Load all data with defaults
            self.suppliers = data.get('suppliers', [])
            self.products = data.get('products', [])
            self.customers = data.get('customers', [])
            self.sales = data.get('sales', [])
            self.settings = data.get('settings', {})

            # Display loaded data summary
            total_items = len(self.suppliers) + len(self.products) + len(self.customers) + len(self.sales)

            if total_items > 0:
                print(f"📊 تم تحميل البيانات:")
                print(f"  🏢 الموردين: {len(self.suppliers)}")
                print(f"  📦 المنتجات: {len(self.products)}")
                print(f"  👥 العملاء: {len(self.customers)}")
                print(f"  💰 المبيعات: {len(self.sales)}")
                print(f"  📊 إجمالي: {total_items} عنصر")

                # Refresh all displays
                self.refresh_all_displays()
            else:
                print("📝 بدء جلسة جديدة - لا توجد بيانات محفوظة")
        else:
            # Initialize empty data
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            self.settings = {}
            print("📝 إنشاء ملف بيانات جديد")

    except Exception as e:
        print(f"❌ خطأ في تحميل البيانات: {e}")
        # Initialize with empty data on error
        self.suppliers = []
        self.products = []
        self.customers = []
        self.sales = []
        self.settings = {}

def refresh_all_displays(self):
    """Refresh all data displays in the UI"""
    try:
        # Refresh supplier display
        if hasattr(self, 'supplier_tree') and self.supplier_tree:
            self.load_suppliers_to_tree()

        # Refresh product display
        if hasattr(self, 'product_tree') and self.product_tree:
            self.load_products_to_tree()

        # Refresh customer display
        if hasattr(self, 'customer_tree') and self.customer_tree:
            self.load_customers_to_tree()

        # Refresh sales display
        if hasattr(self, 'sales_tree') and self.sales_tree:
            self.load_sales_to_tree()

        # Update any combo boxes or dropdowns
        self.update_all_comboboxes()

    except Exception as e:
        print(f"تحذير: مشكلة في تحديث العروض: {e}")

def update_all_comboboxes(self):
    """Update all comboboxes with current data"""
    try:
        # Update product comboboxes
        if hasattr(self, 'product_combo'):
            product_names = [p.get('name', '') for p in self.products]
            self.product_combo['values'] = product_names

        # Update customer comboboxes
        if hasattr(self, 'customer_combo'):
            customer_names = [c.get('name', '') for c in self.customers]
            self.customer_combo['values'] = customer_names

        # Update supplier comboboxes
        if hasattr(self, 'supplier_combo'):
            supplier_names = [s.get('name', '') for s in self.suppliers]
            self.supplier_combo['values'] = supplier_names

    except Exception as e:
        print(f"تحذير: مشكلة في تحديث القوائم المنسدلة: {e}")

# ===== AUTO SAVE SYSTEM - END =====
# نظام الحفظ التلقائي - النهاية

'''