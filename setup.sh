#!/bin/bash

echo "========================================"
echo "ProTech Accounting System Setup"
echo "========================================"
echo

echo "Checking Node.js installation..."
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed or not in PATH."
    echo "Please install Node.js from https://nodejs.org/"
    exit 1
fi

echo "Node.js found!"
node --version

echo
echo "Installing dependencies..."
npm install

if [ $? -ne 0 ]; then
    echo
    echo "Failed to install dependencies. Trying with legacy peer deps..."
    npm install --legacy-peer-deps
fi

echo
echo "Setting up environment variables..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "Environment file created. Please update .env with your database credentials."
else
    echo "Environment file already exists."
fi

echo
echo "Setup completed!"
echo
echo "Next steps:"
echo "1. Update .env file with your PostgreSQL database credentials"
echo "2. Run: npm run db:push (to create database tables)"
echo "3. Run: npm run dev (to start the development server)"
echo
