@echo off
chcp 65001 > nul
title برنامج ناصر للمحاسبة - Nassar Accounting Program

echo.
echo ════════════════════════════════════════════════════════════
echo    🏢 برنامج ناصر للمحاسبة - Nassar Accounting Program
echo ════════════════════════════════════════════════════════════
echo.

cd /d "%~dp0"

echo 🔍 فحص الملفات المطلوبة...
echo Checking required files...

if not exist "START_NASSAR_PROGRAM.py" (
    echo ❌ ملف المشغل الرئيسي غير موجود!
    echo ❌ Main launcher file not found!
    pause
    exit /b 1
)

if not exist "01_Main_Program\protech_simple_working.py" (
    echo ❌ ملف البرنامج الرئيسي غير موجود!
    echo ❌ Main program file not found!
    pause
    exit /b 1
)

echo ✅ جميع الملفات موجودة
echo ✅ All files found

echo.
echo 🚀 تشغيل برنامج ناصر للمحاسبة...
echo 🚀 Starting Nassar Accounting Program...
echo.

python START_NASSAR_PROGRAM.py

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ حدث خطأ في تشغيل البرنامج
    echo ❌ Error occurred while running the program
    echo.
    echo 🔧 جاري المحاولة مع Python3...
    echo 🔧 Trying with Python3...
    python3 START_NASSAR_PROGRAM.py
)

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ فشل في تشغيل البرنامج
    echo ❌ Failed to run the program
    echo.
    echo 💡 تأكد من تثبيت Python على النظام
    echo 💡 Make sure Python is installed on the system
    echo.
    echo 📋 للمساعدة، راجع ملف Quick_Start_Guide.txt
    echo 📋 For help, check Quick_Start_Guide.txt
)

echo.
echo 📝 تم إغلاق البرنامج
echo 📝 Program closed
pause
