@echo off
title ProTech Accounting - Enhanced Python Flask Server
color 0A

echo.
echo ============================================================
echo    ProTech Accounting System - Enhanced Version
echo    نظام ProTech للمحاسبة - النسخة المحسنة
echo ============================================================
echo.

echo [1/4] Checking Python installation...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ ERROR: Python not found
    echo Please install Python from https://python.org/
    pause
    exit /b 1
)

echo ✅ Python found:
python --version

echo.
echo [2/4] Checking Flask installation...
python -c "import flask; print('Flask version:', flask.__version__)" >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️  Flask not found. Installing...
    pip install flask
    if %errorlevel% neq 0 (
        echo ❌ Failed to install Flask
        pause
        exit /b 1
    )
    echo ✅ Flask installed successfully
) else (
    echo ✅ Flask is available
)

echo.
echo [3/4] Checking project files...
if not exist "app.py" (
    echo ❌ ERROR: app.py not found
    echo Please ensure you're in the correct directory
    pause
    exit /b 1
)
echo ✅ Project files found

echo.
echo [4/4] Starting enhanced Flask application...
echo.
echo ┌─────────────────────────────────────────────────────────┐
echo │  🚀 ProTech Accounting System - Enhanced                │
echo │                                                         │
echo │  🐍 Python Flask Server (Enhanced)                     │
echo │  🌐 Main URL: http://localhost:5000                     │
echo │  🧪 Test Page: http://localhost:5000/test               │
echo │  📊 Dashboard: http://localhost:5000                    │
echo │  📦 Inventory: http://localhost:5000/inventory          │
echo │  👥 Customers: http://localhost:5000/customers          │
echo │  💰 Sales: http://localhost:5000/sales                  │
echo │  🏢 Suppliers: http://localhost:5000/suppliers          │
echo │  📈 Reports: http://localhost:5000/reports              │
echo │                                                         │
echo │  ✨ Features: Bilingual, RTL Support, Enhanced APIs    │
echo │  📱 Responsive Design for All Devices                  │
echo │  ⚡ Performance Monitoring Enabled                     │
echo │                                                         │
echo │  Press Ctrl+C to stop the server                       │
echo └─────────────────────────────────────────────────────────┘
echo.

REM Start the enhanced Flask application
python app.py

echo.
echo 🛑 Server stopped.
echo Thank you for using ProTech Accounting System!
echo شكراً لاستخدام نظام ProTech للمحاسبة!
pause
