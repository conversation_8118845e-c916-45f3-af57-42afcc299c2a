#!/usr/bin/env python3
"""
ProTech Database Manager - Advanced Persistent Storage System
مدير قاعدة البيانات ProTech - نظام التخزين الدائم المتقدم

Advanced database management with SQLite backend and JSON compatibility
إدارة قاعدة بيانات متقدمة مع SQLite والتوافق مع JSON
"""

import sqlite3
import json
import os
import shutil
import threading
import time
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import hashlib
import gzip
import pickle

class ProTechDatabaseManager:
    """Advanced database manager with persistent storage and optimization"""
    
    def __init__(self, db_path: str = "protech_database.db", json_backup_path: str = "protech_simple_data.json"):
        """Initialize database manager with SQLite and JSON backup"""
        self.db_path = db_path
        self.json_backup_path = json_backup_path
        self.backup_dir = "database_backups"
        self.archive_dir = "database_archives"
        
        # Performance settings
        self.auto_backup_interval = 300  # 5 minutes
        self.auto_vacuum_interval = 3600  # 1 hour
        self.max_backup_files = 50
        self.compression_enabled = True
        
        # Thread safety
        self.db_lock = threading.RLock()
        self.backup_lock = threading.Lock()
        
        # Performance monitoring
        self.query_stats = {
            'total_queries': 0,
            'slow_queries': 0,
            'failed_queries': 0,
            'cache_hits': 0,
            'cache_misses': 0
        }
        
        # Query cache
        self.query_cache = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Initialize database
        self.setup_logging()
        self.create_directories()
        self.init_database()
        self.start_background_tasks()
        
        print("✅ تم تهيئة مدير قاعدة البيانات المتقدم")
        print("✅ Advanced database manager initialized")

    def setup_logging(self):
        """Setup logging for database operations"""
        try:
            if not os.path.exists('logs'):
                os.makedirs('logs')
            
            # Database logger
            self.db_logger = logging.getLogger('ProTech.Database')
            self.db_logger.setLevel(logging.INFO)
            
            # File handler
            db_handler = logging.FileHandler('logs/database.log', encoding='utf-8')
            db_handler.setLevel(logging.INFO)
            
            # Formatter
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            db_handler.setFormatter(formatter)
            
            self.db_logger.addHandler(db_handler)
            
        except Exception as e:
            print(f"خطأ في تهيئة نظام السجلات: {e}")

    def create_directories(self):
        """Create necessary directories for database operations"""
        directories = [self.backup_dir, self.archive_dir, 'logs', 'temp']
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 تم إنشاء مجلد: {directory}")

    def init_database(self):
        """Initialize SQLite database with optimized schema"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path, timeout=30.0)
                conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging for better performance
                conn.execute("PRAGMA synchronous=NORMAL")  # Balance between safety and speed
                conn.execute("PRAGMA cache_size=10000")  # Increase cache size
                conn.execute("PRAGMA temp_store=MEMORY")  # Store temp tables in memory
                conn.execute("PRAGMA mmap_size=268435456")  # 256MB memory map
                
                cursor = conn.cursor()
                
                # Create tables with optimized schema
                self.create_tables(cursor)
                self.create_indexes(cursor)
                self.create_triggers(cursor)
                
                conn.commit()
                conn.close()
                
                print("✅ تم تهيئة قاعدة البيانات بنجاح")
                self.db_logger.info("Database initialized successfully")
                
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            self.db_logger.error(f"Database initialization error: {e}")

    def create_tables(self, cursor):
        """Create optimized database tables"""
        
        # Suppliers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                name_ar TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                contact_person TEXT,
                category TEXT,
                rating INTEGER DEFAULT 5,
                payment_terms TEXT DEFAULT '30 يوم',
                tax_number TEXT,
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_order_date TIMESTAMP,
                total_orders INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0
            )
        """)
        
        # Products table with enhanced fields
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE NOT NULL,
                name TEXT NOT NULL,
                name_ar TEXT,
                description TEXT,
                category TEXT NOT NULL,
                supplier_id INTEGER,
                base_price REAL NOT NULL,
                cost_price REAL,
                retail_price REAL,
                wholesale_price REAL,
                distributor_price REAL,
                shop_owner_price REAL,
                stock INTEGER DEFAULT 0,
                min_stock INTEGER DEFAULT 0,
                max_stock INTEGER DEFAULT 1000,
                unit TEXT DEFAULT 'قطعة',
                unit_weight REAL,
                unit_dimensions TEXT,
                location TEXT,
                expiry_date DATE,
                batch_number TEXT,
                is_active BOOLEAN DEFAULT 1,
                tax_rate REAL DEFAULT 0.15,
                discount_rate REAL DEFAULT 0,
                image_path TEXT,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_sold_date TIMESTAMP,
                total_sold INTEGER DEFAULT 0,
                total_revenue REAL DEFAULT 0,
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        """)
        
        # Customers table with enhanced tracking
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                name TEXT NOT NULL,
                name_ar TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                city TEXT,
                country TEXT DEFAULT 'Saudi Arabia',
                customer_type TEXT CHECK(customer_type IN ('RETAIL', 'WHOLESALE', 'SHOP_OWNER', 'AUTHORIZED_DISTRIBUTOR')) DEFAULT 'RETAIL',
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                payment_terms TEXT DEFAULT 'نقدي',
                tax_number TEXT,
                discount_rate REAL DEFAULT 0,
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_purchase_date TIMESTAMP,
                total_purchases INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                loyalty_points INTEGER DEFAULT 0
            )
        """)
        
        # Sales/Invoices table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sales (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                customer_name TEXT,
                customer_phone TEXT,
                customer_type TEXT,
                sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                due_date TIMESTAMP,
                subtotal REAL NOT NULL,
                tax_amount REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                balance_amount REAL DEFAULT 0,
                payment_status TEXT CHECK(payment_status IN ('PAID', 'PARTIAL', 'UNPAID', 'OVERDUE')) DEFAULT 'UNPAID',
                payment_method TEXT DEFAULT 'نقدي',
                currency TEXT DEFAULT 'SAR',
                exchange_rate REAL DEFAULT 1.0,
                sales_person TEXT,
                notes TEXT,
                is_cancelled BOOLEAN DEFAULT 0,
                cancelled_reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)
        
        # Sale items table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS sale_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                product_barcode TEXT,
                product_name TEXT,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                discount_rate REAL DEFAULT 0,
                discount_amount REAL DEFAULT 0,
                tax_rate REAL DEFAULT 0.15,
                tax_amount REAL DEFAULT 0,
                line_total REAL NOT NULL,
                cost_price REAL,
                profit_amount REAL,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)
        
        # Inventory movements table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS inventory_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT CHECK(movement_type IN ('IN', 'OUT', 'ADJUSTMENT', 'TRANSFER')) NOT NULL,
                quantity REAL NOT NULL,
                old_stock REAL,
                new_stock REAL,
                unit_cost REAL,
                total_cost REAL,
                reference_type TEXT,
                reference_id INTEGER,
                reference_number TEXT,
                reason TEXT,
                notes TEXT,
                created_by TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)
        
        # Payments table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                sale_id INTEGER,
                customer_id INTEGER,
                payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                amount REAL NOT NULL,
                payment_method TEXT DEFAULT 'نقدي',
                reference_number TEXT,
                bank_name TEXT,
                check_number TEXT,
                check_date DATE,
                notes TEXT,
                is_cancelled BOOLEAN DEFAULT 0,
                cancelled_reason TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (sale_id) REFERENCES sales (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)
        
        # System settings table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                setting_key TEXT UNIQUE NOT NULL,
                setting_value TEXT,
                setting_type TEXT DEFAULT 'string',
                description TEXT,
                category TEXT DEFAULT 'general',
                is_system BOOLEAN DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Audit log table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS audit_log (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                record_id INTEGER,
                action TEXT CHECK(action IN ('INSERT', 'UPDATE', 'DELETE')) NOT NULL,
                old_values TEXT,
                new_values TEXT,
                user_name TEXT,
                ip_address TEXT,
                user_agent TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)

    def create_indexes(self, cursor):
        """Create database indexes for better performance"""
        indexes = [
            # Products indexes
            "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
            "CREATE INDEX IF NOT EXISTS idx_products_supplier ON products(supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock)",
            
            # Customers indexes
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)",
            "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",
            "CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(customer_type)",
            "CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active)",
            
            # Sales indexes
            "CREATE INDEX IF NOT EXISTS idx_sales_invoice ON sales(invoice_number)",
            "CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)",
            "CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(payment_status)",
            
            # Sale items indexes
            "CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id)",
            "CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id)",
            
            # Inventory movements indexes
            "CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory_movements(product_id)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_type ON inventory_movements(movement_type)",
            "CREATE INDEX IF NOT EXISTS idx_inventory_date ON inventory_movements(created_at)",
            
            # Payments indexes
            "CREATE INDEX IF NOT EXISTS idx_payments_sale ON payments(sale_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_customer ON payments(customer_id)",
            "CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)",
            
            # Audit log indexes
            "CREATE INDEX IF NOT EXISTS idx_audit_table ON audit_log(table_name)",
            "CREATE INDEX IF NOT EXISTS idx_audit_record ON audit_log(record_id)",
            "CREATE INDEX IF NOT EXISTS idx_audit_date ON audit_log(created_at)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.db_logger.warning(f"Index creation warning: {e}")

    def create_triggers(self, cursor):
        """Create database triggers for automatic updates"""
        
        # Update timestamps trigger for products
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
            AFTER UPDATE ON products
            BEGIN
                UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        # Update timestamps trigger for customers
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_customers_timestamp 
            AFTER UPDATE ON customers
            BEGIN
                UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        # Update timestamps trigger for suppliers
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS update_suppliers_timestamp 
            AFTER UPDATE ON suppliers
            BEGIN
                UPDATE suppliers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
        """)
        
        # Inventory movement trigger
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS inventory_movement_trigger
            AFTER INSERT ON sale_items
            BEGIN
                INSERT INTO inventory_movements (
                    product_id, movement_type, quantity, old_stock, new_stock,
                    reference_type, reference_id, reason
                ) VALUES (
                    NEW.product_id, 'OUT', NEW.quantity,
                    (SELECT stock FROM products WHERE id = NEW.product_id),
                    (SELECT stock FROM products WHERE id = NEW.product_id) - NEW.quantity,
                    'SALE', NEW.sale_id, 'Sale transaction'
                );
                
                UPDATE products 
                SET stock = stock - NEW.quantity,
                    last_sold_date = CURRENT_TIMESTAMP,
                    total_sold = total_sold + NEW.quantity,
                    total_revenue = total_revenue + NEW.line_total
                WHERE id = NEW.product_id;
            END
        """)

    def start_background_tasks(self):
        """Start background database maintenance tasks"""
        try:
            # Auto backup thread
            self.backup_thread = threading.Thread(target=self.auto_backup_worker, daemon=True)
            self.backup_thread.start()
            
            # Auto vacuum thread
            self.vacuum_thread = threading.Thread(target=self.auto_vacuum_worker, daemon=True)
            self.vacuum_thread.start()
            
            # Cache cleanup thread
            self.cache_cleanup_thread = threading.Thread(target=self.cache_cleanup_worker, daemon=True)
            self.cache_cleanup_thread.start()
            
            print("✅ تم تشغيل مهام الصيانة التلقائية")
            
        except Exception as e:
            print(f"خطأ في تشغيل المهام التلقائية: {e}")
            self.db_logger.error(f"Background tasks error: {e}")

    def auto_backup_worker(self):
        """Background worker for automatic backups"""
        while True:
            try:
                time.sleep(self.auto_backup_interval)
                self.create_backup()
                self.cleanup_old_backups()
            except Exception as e:
                self.db_logger.error(f"Auto backup error: {e}")

    def auto_vacuum_worker(self):
        """Background worker for database optimization"""
        while True:
            try:
                time.sleep(self.auto_vacuum_interval)
                self.vacuum_database()
                self.analyze_database()
            except Exception as e:
                self.db_logger.error(f"Auto vacuum error: {e}")

    def cache_cleanup_worker(self):
        """Background worker for cache cleanup"""
        while True:
            try:
                time.sleep(300)  # Clean cache every 5 minutes
                self.cleanup_cache()
            except Exception as e:
                self.db_logger.error(f"Cache cleanup error: {e}")

    def execute_query(self, query: str, params: tuple = (), fetch_one: bool = False, fetch_all: bool = True) -> Any:
        """Execute database query with error handling and caching"""
        try:
            self.query_stats['total_queries'] += 1
            start_time = time.time()
            
            # Check cache for SELECT queries
            if query.strip().upper().startswith('SELECT'):
                cache_key = hashlib.md5(f"{query}{params}".encode()).hexdigest()
                if cache_key in self.query_cache:
                    cache_entry = self.query_cache[cache_key]
                    if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                        self.query_stats['cache_hits'] += 1
                        return cache_entry['result']
                
                self.query_stats['cache_misses'] += 1
            
            with self.db_lock:
                conn = sqlite3.connect(self.db_path, timeout=30.0)
                conn.row_factory = sqlite3.Row  # Enable column access by name
                cursor = conn.cursor()
                
                cursor.execute(query, params)
                
                if fetch_one:
                    result = cursor.fetchone()
                elif fetch_all and query.strip().upper().startswith('SELECT'):
                    result = cursor.fetchall()
                else:
                    result = cursor.rowcount
                
                conn.commit()
                conn.close()
                
                # Cache SELECT results
                if query.strip().upper().startswith('SELECT') and (fetch_one or fetch_all):
                    cache_key = hashlib.md5(f"{query}{params}".encode()).hexdigest()
                    self.query_cache[cache_key] = {
                        'result': result,
                        'timestamp': time.time()
                    }
                
                # Log slow queries
                execution_time = time.time() - start_time
                if execution_time > 1.0:  # Queries taking more than 1 second
                    self.query_stats['slow_queries'] += 1
                    self.db_logger.warning(f"Slow query ({execution_time:.2f}s): {query[:100]}...")
                
                return result
                
        except Exception as e:
            self.query_stats['failed_queries'] += 1
            self.db_logger.error(f"Query execution error: {e}\nQuery: {query}\nParams: {params}")
            raise e

    def create_backup(self, backup_name: str = None) -> str:
        """Create database backup with compression"""
        try:
            with self.backup_lock:
                if backup_name is None:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_name = f"protech_backup_{timestamp}"
                
                backup_path = os.path.join(self.backup_dir, f"{backup_name}.db")
                
                # Create SQLite backup
                with self.db_lock:
                    source_conn = sqlite3.connect(self.db_path)
                    backup_conn = sqlite3.connect(backup_path)
                    source_conn.backup(backup_conn)
                    source_conn.close()
                    backup_conn.close()
                
                # Create compressed backup
                if self.compression_enabled:
                    compressed_path = f"{backup_path}.gz"
                    with open(backup_path, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    os.remove(backup_path)  # Remove uncompressed version
                    backup_path = compressed_path
                
                # Export to JSON for compatibility
                self.export_to_json()
                
                self.db_logger.info(f"Database backup created: {backup_path}")
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
                
                return backup_path
                
        except Exception as e:
            self.db_logger.error(f"Backup creation error: {e}")
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            raise e

    def export_to_json(self, output_path: str = None) -> str:
        """Export database to JSON format for compatibility"""
        try:
            if output_path is None:
                output_path = self.json_backup_path

            # Export all tables to JSON
            data = {
                'suppliers': self.get_all_suppliers(),
                'products': self.get_all_products(),
                'customers': self.get_all_customers(),
                'sales': self.get_all_sales(),
                'export_timestamp': datetime.now().isoformat(),
                'database_version': '2.0',
                'total_records': 0
            }

            # Calculate total records
            data['total_records'] = (
                len(data['suppliers']) +
                len(data['products']) +
                len(data['customers']) +
                len(data['sales'])
            )

            # Write to JSON file
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            self.db_logger.info(f"Database exported to JSON: {output_path}")
            print(f"✅ تم تصدير قاعدة البيانات إلى JSON: {output_path}")

            return output_path

        except Exception as e:
            self.db_logger.error(f"JSON export error: {e}")
            print(f"❌ خطأ في تصدير JSON: {e}")
            raise e

    def import_from_json(self, json_path: str = None) -> bool:
        """Import data from JSON file to database"""
        try:
            if json_path is None:
                json_path = self.json_backup_path

            if not os.path.exists(json_path):
                print(f"❌ ملف JSON غير موجود: {json_path}")
                return False

            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Import suppliers
            if 'suppliers' in data:
                for supplier in data['suppliers']:
                    try:
                        self.insert_supplier(supplier)
                    except Exception as e:
                        print(f"تحذير: فشل في استيراد مورد {supplier.get('name', 'Unknown')}: {e}")

            # Import products
            if 'products' in data:
                for product in data['products']:
                    try:
                        self.insert_product(product)
                    except Exception as e:
                        print(f"تحذير: فشل في استيراد منتج {product.get('name', 'Unknown')}: {e}")

            # Import customers
            if 'customers' in data:
                for customer in data['customers']:
                    try:
                        self.insert_customer(customer)
                    except Exception as e:
                        print(f"تحذير: فشل في استيراد عميل {customer.get('name', 'Unknown')}: {e}")

            # Import sales
            if 'sales' in data:
                for sale in data['sales']:
                    try:
                        self.insert_sale(sale)
                    except Exception as e:
                        print(f"تحذير: فشل في استيراد فاتورة {sale.get('invoice_number', 'Unknown')}: {e}")

            self.db_logger.info(f"Data imported from JSON: {json_path}")
            print(f"✅ تم استيراد البيانات من JSON: {json_path}")

            return True

        except Exception as e:
            self.db_logger.error(f"JSON import error: {e}")
            print(f"❌ خطأ في استيراد JSON: {e}")
            return False

    def vacuum_database(self):
        """Optimize database by running VACUUM"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                conn.execute("VACUUM")
                conn.close()

            self.db_logger.info("Database vacuum completed")
            print("✅ تم تحسين قاعدة البيانات")

        except Exception as e:
            self.db_logger.error(f"Database vacuum error: {e}")

    def analyze_database(self):
        """Analyze database for query optimization"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_path)
                conn.execute("ANALYZE")
                conn.close()

            self.db_logger.info("Database analysis completed")

        except Exception as e:
            self.db_logger.error(f"Database analysis error: {e}")

    def cleanup_cache(self):
        """Clean expired cache entries"""
        try:
            current_time = time.time()
            expired_keys = []

            for key, entry in self.query_cache.items():
                if current_time - entry['timestamp'] > self.cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self.query_cache[key]

            if expired_keys:
                print(f"🧹 تم تنظيف {len(expired_keys)} عنصر من الذاكرة المؤقتة")

        except Exception as e:
            self.db_logger.error(f"Cache cleanup error: {e}")

    def cleanup_old_backups(self):
        """Remove old backup files to save space"""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith('protech_backup_') and (file.endswith('.db') or file.endswith('.db.gz')):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getctime(file_path)))

            # Sort by creation time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # Remove old backups
            if len(backup_files) > self.max_backup_files:
                for file_path, _ in backup_files[self.max_backup_files:]:
                    os.remove(file_path)
                    print(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {os.path.basename(file_path)}")

        except Exception as e:
            self.db_logger.error(f"Backup cleanup error: {e}")

    def get_database_stats(self) -> Dict[str, Any]:
        """Get comprehensive database statistics"""
        try:
            stats = {
                'database_size': os.path.getsize(self.db_path) if os.path.exists(self.db_path) else 0,
                'table_counts': {},
                'query_stats': self.query_stats.copy(),
                'cache_stats': {
                    'cache_size': len(self.query_cache),
                    'cache_hit_rate': 0
                },
                'last_backup': None,
                'backup_count': 0
            }

            # Get table counts
            tables = ['suppliers', 'products', 'customers', 'sales', 'sale_items', 'inventory_movements', 'payments']
            for table in tables:
                try:
                    result = self.execute_query(f"SELECT COUNT(*) as count FROM {table}", fetch_one=True)
                    stats['table_counts'][table] = result['count'] if result else 0
                except:
                    stats['table_counts'][table] = 0

            # Calculate cache hit rate
            total_cache_requests = self.query_stats['cache_hits'] + self.query_stats['cache_misses']
            if total_cache_requests > 0:
                stats['cache_stats']['cache_hit_rate'] = self.query_stats['cache_hits'] / total_cache_requests

            # Get backup info
            if os.path.exists(self.backup_dir):
                backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith('protech_backup_')]
                stats['backup_count'] = len(backup_files)
                if backup_files:
                    latest_backup = max(backup_files, key=lambda f: os.path.getctime(os.path.join(self.backup_dir, f)))
                    stats['last_backup'] = latest_backup

            return stats

        except Exception as e:
            self.db_logger.error(f"Database stats error: {e}")
            return {}

    # Data access methods
    def get_all_suppliers(self) -> List[Dict]:
        """Get all suppliers from database"""
        try:
            result = self.execute_query("SELECT * FROM suppliers ORDER BY name")
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.db_logger.error(f"Get suppliers error: {e}")
            return []

    def get_all_products(self) -> List[Dict]:
        """Get all products from database"""
        try:
            result = self.execute_query("""
                SELECT p.*, s.name as supplier_name
                FROM products p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                ORDER BY p.name
            """)
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.db_logger.error(f"Get products error: {e}")
            return []

    def get_all_customers(self) -> List[Dict]:
        """Get all customers from database"""
        try:
            result = self.execute_query("SELECT * FROM customers ORDER BY name")
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.db_logger.error(f"Get customers error: {e}")
            return []

    def get_all_sales(self) -> List[Dict]:
        """Get all sales from database"""
        try:
            result = self.execute_query("""
                SELECT s.*, c.name as customer_name
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                ORDER BY s.sale_date DESC
            """)
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.db_logger.error(f"Get sales error: {e}")
            return []

    def insert_supplier(self, supplier_data: Dict) -> int:
        """Insert new supplier"""
        try:
            query = """
                INSERT INTO suppliers (name, name_ar, phone, email, address, contact_person,
                                     category, rating, payment_terms, tax_number, credit_limit,
                                     is_active, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                supplier_data.get('name'),
                supplier_data.get('name_ar'),
                supplier_data.get('phone'),
                supplier_data.get('email'),
                supplier_data.get('address'),
                supplier_data.get('contact_person'),
                supplier_data.get('category'),
                supplier_data.get('rating', 5),
                supplier_data.get('payment_terms', '30 يوم'),
                supplier_data.get('tax_number'),
                supplier_data.get('credit_limit', 0),
                supplier_data.get('active', True),
                supplier_data.get('notes')
            )

            result = self.execute_query(query, params, fetch_all=False)
            self.db_logger.info(f"Supplier inserted: {supplier_data.get('name')}")
            return result

        except Exception as e:
            self.db_logger.error(f"Insert supplier error: {e}")
            raise e

    def insert_product(self, product_data: Dict) -> int:
        """Insert new product"""
        try:
            query = """
                INSERT INTO products (barcode, name, name_ar, description, category, supplier_id,
                                    base_price, cost_price, retail_price, wholesale_price,
                                    distributor_price, shop_owner_price, stock, min_stock,
                                    max_stock, unit, is_active, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                product_data.get('barcode'),
                product_data.get('name'),
                product_data.get('name_ar'),
                product_data.get('description'),
                product_data.get('category'),
                product_data.get('supplier_id'),
                product_data.get('price', product_data.get('base_price', 0)),
                product_data.get('cost', product_data.get('cost_price', 0)),
                product_data.get('retail_price'),
                product_data.get('wholesale_price'),
                product_data.get('distributor_price'),
                product_data.get('shop_owner_price'),
                product_data.get('stock', 0),
                product_data.get('min_stock', 0),
                product_data.get('max_stock', 1000),
                product_data.get('unit', 'قطعة'),
                product_data.get('active', True),
                product_data.get('notes')
            )

            result = self.execute_query(query, params, fetch_all=False)
            self.db_logger.info(f"Product inserted: {product_data.get('name')}")
            return result

        except Exception as e:
            self.db_logger.error(f"Insert product error: {e}")
            raise e

    def insert_customer(self, customer_data: Dict) -> int:
        """Insert new customer"""
        try:
            query = """
                INSERT INTO customers (code, name, name_ar, email, phone, address, customer_type,
                                     credit_limit, current_balance, payment_terms, tax_number,
                                     discount_rate, is_active, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                customer_data.get('code'),
                customer_data.get('name'),
                customer_data.get('name_ar'),
                customer_data.get('email'),
                customer_data.get('phone'),
                customer_data.get('address'),
                customer_data.get('type', customer_data.get('customer_type', 'RETAIL')),
                customer_data.get('credit_limit', 0),
                customer_data.get('balance', customer_data.get('current_balance', 0)),
                customer_data.get('payment_terms', 'نقدي'),
                customer_data.get('tax_number'),
                customer_data.get('discount_rate', 0),
                customer_data.get('active', True),
                customer_data.get('notes')
            )

            result = self.execute_query(query, params, fetch_all=False)
            self.db_logger.info(f"Customer inserted: {customer_data.get('name')}")
            return result

        except Exception as e:
            self.db_logger.error(f"Insert customer error: {e}")
            raise e

    def insert_sale(self, sale_data: Dict) -> int:
        """Insert new sale with items"""
        try:
            # Insert main sale record
            sale_query = """
                INSERT INTO sales (invoice_number, customer_id, customer_name, customer_phone,
                                 customer_type, subtotal, tax_amount, discount_amount, total_amount,
                                 paid_amount, balance_amount, payment_status, payment_method, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            sale_params = (
                sale_data.get('invoice_number', sale_data.get('invoice')),
                sale_data.get('customer_id'),
                sale_data.get('customer_name'),
                sale_data.get('customer_phone'),
                sale_data.get('customer_type'),
                sale_data.get('subtotal', sale_data.get('total', 0)),
                sale_data.get('tax_amount', 0),
                sale_data.get('discount_amount', 0),
                sale_data.get('total_amount', sale_data.get('total', 0)),
                sale_data.get('paid_amount', sale_data.get('payment_amount', 0)),
                sale_data.get('balance_amount', sale_data.get('balance', 0)),
                sale_data.get('payment_status', 'UNPAID'),
                sale_data.get('payment_method', 'نقدي'),
                sale_data.get('notes')
            )

            sale_id = self.execute_query(sale_query, sale_params, fetch_all=False)

            # Insert sale items if available
            if 'items' in sale_data and sale_data['items']:
                for item in sale_data['items']:
                    self.insert_sale_item(sale_id, item)

            self.db_logger.info(f"Sale inserted: {sale_data.get('invoice_number')}")
            return sale_id

        except Exception as e:
            self.db_logger.error(f"Insert sale error: {e}")
            raise e

    def insert_sale_item(self, sale_id: int, item_data: Dict) -> int:
        """Insert sale item"""
        try:
            query = """
                INSERT INTO sale_items (sale_id, product_id, product_barcode, product_name,
                                      quantity, unit_price, discount_rate, discount_amount,
                                      tax_rate, tax_amount, line_total, cost_price, profit_amount)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            # Calculate values
            quantity = float(item_data.get('quantity', 0))
            unit_price = float(item_data.get('price', item_data.get('unit_price', 0)))
            line_total = quantity * unit_price

            params = (
                sale_id,
                item_data.get('product_id'),
                item_data.get('barcode'),
                item_data.get('name'),
                quantity,
                unit_price,
                item_data.get('discount_rate', 0),
                item_data.get('discount_amount', 0),
                item_data.get('tax_rate', 0.15),
                item_data.get('tax_amount', 0),
                line_total,
                item_data.get('base_price', item_data.get('cost_price', 0)),
                line_total - (quantity * float(item_data.get('base_price', 0)))
            )

            result = self.execute_query(query, params, fetch_all=False)
            return result

        except Exception as e:
            self.db_logger.error(f"Insert sale item error: {e}")
            raise e

    def search_products(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Search products with advanced filtering"""
        try:
            query = """
                SELECT p.*, s.name as supplier_name
                FROM products p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE (p.name LIKE ? OR p.name_ar LIKE ? OR p.barcode LIKE ? OR p.category LIKE ?)
                AND p.is_active = 1
                ORDER BY p.name
                LIMIT ?
            """
            search_pattern = f"%{search_term}%"
            params = (search_pattern, search_pattern, search_pattern, search_pattern, limit)

            result = self.execute_query(query, params)
            return [dict(row) for row in result] if result else []

        except Exception as e:
            self.db_logger.error(f"Search products error: {e}")
            return []

    def get_low_stock_products(self, threshold: int = None) -> List[Dict]:
        """Get products with low stock"""
        try:
            if threshold is None:
                query = "SELECT * FROM products WHERE stock <= min_stock AND is_active = 1 ORDER BY stock"
                params = ()
            else:
                query = "SELECT * FROM products WHERE stock <= ? AND is_active = 1 ORDER BY stock"
                params = (threshold,)

            result = self.execute_query(query, params)
            return [dict(row) for row in result] if result else []

        except Exception as e:
            self.db_logger.error(f"Get low stock products error: {e}")
            return []

    def get_product_by_barcode(self, barcode: str) -> Optional[Dict]:
        """Get product by barcode"""
        try:
            query = "SELECT * FROM products WHERE barcode = ? AND is_active = 1"
            result = self.execute_query(query, (barcode,), fetch_one=True)
            return dict(result) if result else None

        except Exception as e:
            self.db_logger.error(f"Get product by barcode error: {e}")
            return None

    def update_product_stock(self, product_id: int, new_stock: int, reason: str = "Manual adjustment") -> bool:
        """Update product stock with inventory tracking"""
        try:
            # Get current stock
            current_product = self.execute_query("SELECT stock FROM products WHERE id = ?", (product_id,), fetch_one=True)
            if not current_product:
                return False

            old_stock = current_product['stock']
            quantity_change = new_stock - old_stock

            # Update product stock
            update_query = "UPDATE products SET stock = ? WHERE id = ?"
            self.execute_query(update_query, (new_stock, product_id), fetch_all=False)

            # Record inventory movement
            movement_query = """
                INSERT INTO inventory_movements (product_id, movement_type, quantity, old_stock, new_stock, reason)
                VALUES (?, ?, ?, ?, ?, ?)
            """
            movement_type = 'IN' if quantity_change > 0 else 'OUT' if quantity_change < 0 else 'ADJUSTMENT'
            movement_params = (product_id, movement_type, abs(quantity_change), old_stock, new_stock, reason)
            self.execute_query(movement_query, movement_params, fetch_all=False)

            self.db_logger.info(f"Product stock updated: ID {product_id}, {old_stock} -> {new_stock}")
            return True

        except Exception as e:
            self.db_logger.error(f"Update product stock error: {e}")
            return False

    def get_sales_report(self, start_date: str = None, end_date: str = None) -> Dict[str, Any]:
        """Generate comprehensive sales report"""
        try:
            # Base query conditions
            where_conditions = ["1=1"]
            params = []

            if start_date:
                where_conditions.append("DATE(sale_date) >= ?")
                params.append(start_date)

            if end_date:
                where_conditions.append("DATE(sale_date) <= ?")
                params.append(end_date)

            where_clause = " AND ".join(where_conditions)

            # Total sales
            total_sales_query = f"""
                SELECT COUNT(*) as count, SUM(total_amount) as total, SUM(paid_amount) as paid
                FROM sales WHERE {where_clause}
            """
            total_sales = self.execute_query(total_sales_query, params, fetch_one=True)

            # Sales by status
            status_query = f"""
                SELECT payment_status, COUNT(*) as count, SUM(total_amount) as total
                FROM sales WHERE {where_clause}
                GROUP BY payment_status
            """
            sales_by_status = self.execute_query(status_query, params)

            # Top products
            top_products_query = f"""
                SELECT si.product_name, SUM(si.quantity) as total_quantity, SUM(si.line_total) as total_revenue
                FROM sale_items si
                JOIN sales s ON si.sale_id = s.id
                WHERE {where_clause}
                GROUP BY si.product_name
                ORDER BY total_revenue DESC
                LIMIT 10
            """
            top_products = self.execute_query(top_products_query, params)

            # Daily sales
            daily_sales_query = f"""
                SELECT DATE(sale_date) as sale_date, COUNT(*) as count, SUM(total_amount) as total
                FROM sales WHERE {where_clause}
                GROUP BY DATE(sale_date)
                ORDER BY sale_date DESC
            """
            daily_sales = self.execute_query(daily_sales_query, params)

            report = {
                'period': {
                    'start_date': start_date,
                    'end_date': end_date
                },
                'summary': {
                    'total_sales': total_sales['count'] if total_sales else 0,
                    'total_amount': total_sales['total'] if total_sales else 0,
                    'total_paid': total_sales['paid'] if total_sales else 0,
                    'outstanding_balance': (total_sales['total'] - total_sales['paid']) if total_sales else 0
                },
                'sales_by_status': [dict(row) for row in sales_by_status] if sales_by_status else [],
                'top_products': [dict(row) for row in top_products] if top_products else [],
                'daily_sales': [dict(row) for row in daily_sales] if daily_sales else [],
                'generated_at': datetime.now().isoformat()
            }

            return report

        except Exception as e:
            self.db_logger.error(f"Sales report error: {e}")
            return {}

    def close(self):
        """Close database connections and cleanup"""
        try:
            # Clear cache
            self.query_cache.clear()

            # Final backup
            self.create_backup("final_backup")

            print("✅ تم إغلاق مدير قاعدة البيانات")
            self.db_logger.info("Database manager closed")

        except Exception as e:
            self.db_logger.error(f"Database close error: {e}")

# Example usage and testing
if __name__ == "__main__":
    print("🧪 اختبار مدير قاعدة البيانات المتقدم")
    print("🧪 Testing Advanced Database Manager")

    try:
        # Initialize database manager
        db_manager = ProTechDatabaseManager()

        # Test database operations
        print("\n📊 إحصائيات قاعدة البيانات:")
        stats = db_manager.get_database_stats()
        for key, value in stats.items():
            print(f"  {key}: {value}")

        # Test backup
        backup_path = db_manager.create_backup("test_backup")
        print(f"\n💾 تم إنشاء نسخة احتياطية: {backup_path}")

        # Test JSON export
        json_path = db_manager.export_to_json("test_export.json")
        print(f"\n📄 تم تصدير JSON: {json_path}")

        # Test import from existing JSON
        if os.path.exists("protech_simple_data.json"):
            print("\n📥 استيراد البيانات من JSON...")
            success = db_manager.import_from_json("protech_simple_data.json")
            if success:
                print("✅ تم استيراد البيانات بنجاح")

                # Show updated stats
                print("\n📊 إحصائيات بعد الاستيراد:")
                new_stats = db_manager.get_database_stats()
                for key, value in new_stats.items():
                    if key == 'table_counts':
                        print(f"  {key}:")
                        for table, count in value.items():
                            print(f"    {table}: {count}")
                    else:
                        print(f"  {key}: {value}")

        # Close database
        db_manager.close()

        print("\n✅ اكتمل اختبار قاعدة البيانات بنجاح!")

    except Exception as e:
        print(f"\n❌ خطأ في اختبار قاعدة البيانات: {e}")
        import traceback
        traceback.print_exc()
