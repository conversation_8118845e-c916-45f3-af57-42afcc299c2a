# ProTech Accounting System - Installation Guide

This guide will help you set up the ProTech Accounting System on your local machine or server.

## Prerequisites

Before installing the application, ensure you have the following installed:

### Required Software

1. **Node.js** (v18 or higher)
   - Download from: https://nodejs.org/
   - Verify installation: `node --version`

2. **PostgreSQL** (v12 or higher)
   - Download from: https://www.postgresql.org/download/
   - Verify installation: `psql --version`

3. **Git** (for cloning the repository)
   - Download from: https://git-scm.com/

### Optional Tools

- **pgAdmin** or **DBeaver** for database management
- **VS Code** or your preferred code editor

## Installation Steps

### Step 1: Clone the Repository

```bash
git clone <repository-url>
cd protech-accounting
```

### Step 2: Automated Setup (Recommended)

#### For Windows:
```cmd
setup.bat
```

#### For Linux/macOS:
```bash
chmod +x setup.sh
./setup.sh
```

### Step 3: Manual Setup (Alternative)

If the automated setup doesn't work, follow these manual steps:

#### 3.1 Install Dependencies
```bash
npm install
```

If you encounter dependency conflicts, try:
```bash
npm install --legacy-peer-deps
```

#### 3.2 Environment Configuration
```bash
cp .env.example .env
```

Edit the `.env` file with your configuration:

```env
# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/protech_accounting?schema=public"

# Application Settings
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"
JWT_SECRET="your-jwt-secret-here"

# Other settings...
```

### Step 4: Database Setup

#### 4.1 Create Database
Connect to PostgreSQL and create the database:

```sql
CREATE DATABASE protech_accounting;
CREATE USER protech_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE protech_accounting TO protech_user;
```

#### 4.2 Initialize Database Schema
```bash
npx prisma generate
npx prisma db push
```

#### 4.3 Seed Database (Optional)
```bash
npx prisma db seed
```

### Step 5: Start the Application

#### Development Mode
```bash
npm run dev
```

#### Production Mode
```bash
npm run build
npm start
```

The application will be available at: http://localhost:3000

## Default Login Credentials

After seeding the database, you can log in with:

- **Email**: <EMAIL>
- **Password**: admin123

**Important**: Change these credentials immediately after first login.

## Troubleshooting

### Common Issues

#### 1. Node.js Not Found
- Ensure Node.js is installed and added to your system PATH
- Restart your terminal/command prompt after installation

#### 2. Database Connection Error
- Verify PostgreSQL is running
- Check database credentials in `.env` file
- Ensure the database exists and user has proper permissions

#### 3. Port Already in Use
- Change the port in your `.env` file:
  ```env
  PORT=3001
  ```
- Or kill the process using port 3000:
  ```bash
  # Windows
  netstat -ano | findstr :3000
  taskkill /PID <PID> /F
  
  # Linux/macOS
  lsof -ti:3000 | xargs kill -9
  ```

#### 4. Dependency Installation Issues
- Clear npm cache: `npm cache clean --force`
- Delete `node_modules` and `package-lock.json`, then reinstall:
  ```bash
  rm -rf node_modules package-lock.json
  npm install
  ```

#### 5. Prisma Issues
- Reset Prisma client: `npx prisma generate`
- Reset database: `npx prisma db push --force-reset`

### Getting Help

If you encounter issues not covered here:

1. Check the application logs in the terminal
2. Verify all prerequisites are properly installed
3. Ensure your `.env` file is correctly configured
4. Try the manual setup steps if automated setup fails

## Next Steps

After successful installation:

1. **Change Default Credentials**: Update the admin password
2. **Configure Company Settings**: Update company information in settings
3. **Add Initial Data**: Create your first products, customers, and suppliers
4. **Explore Features**: Familiarize yourself with the system modules

## System Requirements

### Minimum Requirements
- **RAM**: 4GB
- **Storage**: 2GB free space
- **CPU**: Dual-core processor
- **OS**: Windows 10, macOS 10.15, or Linux (Ubuntu 18.04+)

### Recommended Requirements
- **RAM**: 8GB or more
- **Storage**: 10GB free space
- **CPU**: Quad-core processor
- **OS**: Latest versions of supported operating systems

## Security Considerations

1. **Change Default Passwords**: Update all default credentials
2. **Environment Variables**: Keep `.env` file secure and never commit it to version control
3. **Database Security**: Use strong passwords and limit database access
4. **HTTPS**: Use HTTPS in production environments
5. **Regular Updates**: Keep dependencies updated for security patches

## Backup and Maintenance

### Database Backup
```bash
pg_dump protech_accounting > backup.sql
```

### Database Restore
```bash
psql protech_accounting < backup.sql
```

### Regular Maintenance
- Monitor disk space usage
- Regular database backups
- Keep system dependencies updated
- Monitor application logs for errors

## Support

For technical support or questions:
- Check the README.md file for additional information
- Review the troubleshooting section above
- Contact the development team

---

**Note**: This installation guide assumes a development/testing environment. For production deployment, additional security and performance configurations may be required.
