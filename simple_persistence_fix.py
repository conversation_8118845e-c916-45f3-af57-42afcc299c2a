#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Persistence Fix
إصلاح بسيط لاستمرارية البيانات

Simple and effective fix for data persistence issue
إصلاح بسيط وفعال لمشكلة استمرارية البيانات
"""

import os
import shutil
from datetime import datetime

def create_simple_persistence_fix():
    """Create a simple persistence fix by adding save buttons and auto-save"""
    try:
        print("🔧 إنشاء إصلاح بسيط لاستمرارية البيانات")
        print("🔧 Creating Simple Persistence Fix")
        print("="*50)
        
        # Find ProTech file
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.simple_persistence_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple fix: Add save button and auto-save
        simple_fix = '''
# ===== SIMPLE PERSISTENCE FIX - START =====
# إصلاح بسيط لاستمرارية البيانات

import atexit

def setup_auto_save(app_instance):
    """Setup simple auto-save"""
    try:
        # Save on exit
        atexit.register(lambda: app_instance.save_data() if hasattr(app_instance, 'save_data') else None)
        
        # Override window close
        if hasattr(app_instance, 'root'):
            original_destroy = app_instance.root.destroy
            def safe_destroy():
                try:
                    if hasattr(app_instance, 'save_data'):
                        app_instance.save_data()
                        print("✅ تم حفظ البيانات")
                except:
                    pass
                original_destroy()
            app_instance.root.destroy = safe_destroy
            
    except Exception as e:
        print(f"تحذير: {e}")

# ===== SIMPLE PERSISTENCE FIX - END =====

'''
        
        # Add the fix after double click fix
        if "DOUBLE CLICK FIX - END" in content:
            insert_pos = content.find("DOUBLE CLICK FIX - END") + len("DOUBLE CLICK FIX - END")
            content = content[:insert_pos] + simple_fix + content[insert_pos:]
        else:
            content = simple_fix + content
        
        # Add setup call in __init__ if class exists
        if "def __init__(self" in content:
            init_end = content.find("self.load_data()")
            if init_end != -1:
                init_end = content.find("\n", init_end)
                setup_call = "\n        setup_auto_save(self)"
                content = content[:init_end] + setup_call + content[init_end:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة إصلاح بسيط لاستمرارية البيانات")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح البسيط: {e}")
        return False

def create_manual_save_button():
    """Create a manual save button version"""
    try:
        print("\n🔘 إنشاء نسخة مع زر حفظ يدوي...")
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        
        # Create a simple save button script
        save_button_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Manual Save
حفظ يدوي لـ ProTech

Manual save functionality for ProTech
وظيفة الحفظ اليدوي لـ ProTech
"""

import tkinter as tk
from tkinter import messagebox
import json
import os
from datetime import datetime

def manual_save():
    """Manual save function"""
    try:
        data_file = "protech_simple_data.json"
        
        if os.path.exists(data_file):
            # Create backup
            backup_file = f"{data_file}.manual_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            import shutil
            shutil.copy2(data_file, backup_file)
            
            messagebox.showinfo("حفظ", f"تم حفظ البيانات\\nنسخة احتياطية: {backup_file}")
        else:
            messagebox.showwarning("تحذير", "لم يتم العثور على ملف البيانات")
    
    except Exception as e:
        messagebox.showerror("خطأ", f"فشل في الحفظ: {e}")

def create_save_window():
    """Create save window"""
    root = tk.Tk()
    root.title("حفظ بيانات ProTech")
    root.geometry("300x150")
    root.resizable(False, False)
    
    # Center window
    root.eval('tk::PlaceWindow . center')
    
    label = tk.Label(root, text="حفظ بيانات ProTech", font=("Arial", 14))
    label.pack(pady=20)
    
    save_btn = tk.Button(root, text="حفظ البيانات", command=manual_save,
                        font=("Arial", 12), bg="#4CAF50", fg="white",
                        width=15, height=2)
    save_btn.pack(pady=10)
    
    close_btn = tk.Button(root, text="إغلاق", command=root.destroy,
                         font=("Arial", 10), width=10)
    close_btn.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    create_save_window()
'''
        
        save_script_path = os.path.join(desktop_path, "حفظ_بيانات_ProTech.py")
        
        with open(save_script_path, 'w', encoding='utf-8') as f:
            f.write(save_button_script)
        
        print(f"✅ تم إنشاء أداة الحفظ اليدوي: {os.path.basename(save_script_path)}")
        return save_script_path
        
    except Exception as e:
        print(f"❌ فشل في إنشاء أداة الحفظ اليدوي: {e}")
        return None

def create_data_backup_tool():
    """Create comprehensive data backup tool"""
    try:
        print("\n💾 إنشاء أداة النسخ الاحتياطي الشاملة...")
        
        backup_tool = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Data Backup Tool
أداة النسخ الاحتياطي لبيانات ProTech
"""

import os
import json
import shutil
import glob
from datetime import datetime

def create_comprehensive_backup():
    """Create comprehensive backup of all ProTech data"""
    try:
        print("💾 إنشاء نسخة احتياطية شاملة...")
        
        # Create backup directory
        backup_dir = f"ProTech_Backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(backup_dir, exist_ok=True)
        
        # Files to backup
        files_to_backup = [
            "protech_simple_data.json",
            "protech_config.json",
            "protech_settings.json",
            "protech_simple_working.py"
        ]
        
        backed_up_files = []
        
        for file in files_to_backup:
            if os.path.exists(file):
                try:
                    dest_path = os.path.join(backup_dir, file)
                    shutil.copy2(file, dest_path)
                    
                    size = os.path.getsize(dest_path) / 1024
                    backed_up_files.append((file, size))
                    print(f"✅ {file}: {size:.1f} KB")
                    
                except Exception as e:
                    print(f"❌ {file}: فشل - {e}")
        
        # Backup existing backup files
        backup_files = glob.glob("*.backup_*") + glob.glob("*_backup_*")
        
        if backup_files:
            backup_subdir = os.path.join(backup_dir, "previous_backups")
            os.makedirs(backup_subdir, exist_ok=True)
            
            for backup_file in backup_files:
                try:
                    dest_path = os.path.join(backup_subdir, backup_file)
                    shutil.copy2(backup_file, dest_path)
                except:
                    pass
        
        # Create backup info file
        backup_info = {
            "backup_date": datetime.now().isoformat(),
            "files_backed_up": [{"file": f, "size_kb": s} for f, s in backed_up_files],
            "total_files": len(backed_up_files),
            "backup_directory": backup_dir
        }
        
        info_file = os.path.join(backup_dir, "backup_info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump(backup_info, f, ensure_ascii=False, indent=2)
        
        print(f"\\n✅ تم إنشاء النسخة الاحتياطية: {backup_dir}")
        print(f"📊 ملفات محفوظة: {len(backed_up_files)}")
        
        return backup_dir
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def restore_from_backup():
    """Restore data from backup"""
    try:
        print("\\n🔄 استعادة البيانات من النسخة الاحتياطية...")
        
        # Find backup directories
        backup_dirs = [d for d in os.listdir('.') if d.startswith('ProTech_Backup_')]
        
        if not backup_dirs:
            print("❌ لم يتم العثور على مجلدات نسخ احتياطية")
            return False
        
        # Sort by creation time (newest first)
        backup_dirs.sort(reverse=True)
        
        print(f"📋 تم العثور على {len(backup_dirs)} نسخة احتياطية:")
        for i, backup_dir in enumerate(backup_dirs[:5], 1):
            print(f"  {i}. {backup_dir}")
        
        # Use the newest backup
        latest_backup = backup_dirs[0]
        backup_path = os.path.join(latest_backup, "protech_simple_data.json")
        
        if os.path.exists(backup_path):
            # Create backup of current data
            current_data = "protech_simple_data.json"
            if os.path.exists(current_data):
                current_backup = f"{current_data}.before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(current_data, current_backup)
                print(f"💾 نسخة احتياطية من البيانات الحالية: {current_backup}")
            
            # Restore data
            shutil.copy2(backup_path, current_data)
            print(f"✅ تم استعادة البيانات من: {latest_backup}")
            
            # Check restored data
            with open(current_data, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            total_items = (len(data.get('products', [])) + 
                         len(data.get('customers', [])) + 
                         len(data.get('suppliers', [])) + 
                         len(data.get('sales', [])))
            
            print(f"📊 البيانات المستعادة:")
            print(f"  📦 المنتجات: {len(data.get('products', []))}")
            print(f"  👥 العملاء: {len(data.get('customers', []))}")
            print(f"  🏢 الموردين: {len(data.get('suppliers', []))}")
            print(f"  💰 المبيعات: {len(data.get('sales', []))}")
            print(f"  📊 إجمالي: {total_items} عنصر")
            
            return True
        else:
            print(f"❌ لم يتم العثور على ملف البيانات في: {latest_backup}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاستعادة: {e}")
        return False

def main():
    """Main function"""
    print("💾 أداة النسخ الاحتياطي الشاملة لـ ProTech")
    print("💾 ProTech Comprehensive Backup Tool")
    print("="*60)
    
    print("\\n1. إنشاء نسخة احتياطية")
    backup_dir = create_comprehensive_backup()
    
    if backup_dir:
        print("\\n2. اختبار الاستعادة")
        if restore_from_backup():
            print("\\n🎉 تم اختبار النسخ الاحتياطي والاستعادة بنجاح!")
        else:
            print("\\n⚠️ مشكلة في اختبار الاستعادة")
    
    input("\\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        backup_tool_path = os.path.join(desktop_path, "نسخ_احتياطي_ProTech.py")
        
        with open(backup_tool_path, 'w', encoding='utf-8') as f:
            f.write(backup_tool)
        
        print(f"✅ تم إنشاء أداة النسخ الاحتياطي: {os.path.basename(backup_tool_path)}")
        return backup_tool_path
        
    except Exception as e:
        print(f"❌ فشل في إنشاء أداة النسخ الاحتياطي: {e}")
        return None

def main():
    """Main function"""
    print("🔧 إصلاح بسيط لمشكلة استمرارية البيانات")
    print("🔧 Simple Fix for Data Persistence Issue")
    print("="*60)
    
    print("\n💡 المشكلة:")
    print("البيانات تختفي عند إغلاق وإعادة فتح البرنامج")
    
    print("\n🔧 الحلول المطبقة:")
    
    created_items = []
    
    # Apply simple persistence fix
    if create_simple_persistence_fix():
        created_items.append("إصلاح بسيط لاستمرارية البيانات")
    
    # Create manual save button
    manual_save = create_manual_save_button()
    if manual_save:
        created_items.append("أداة الحفظ اليدوي")
    
    # Create backup tool
    backup_tool = create_data_backup_tool()
    if backup_tool:
        created_items.append("أداة النسخ الاحتياطي الشاملة")
    
    # Summary
    print("\n" + "="*60)
    print("📊 ملخص الحلول:")
    
    if created_items:
        print(f"✅ تم إنشاء {len(created_items)} حل:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم إنشاء أي حلول")
    
    print("\n🎯 كيفية الاستخدام:")
    
    print("\n1️⃣ الحفظ التلقائي:")
    print("• البرنامج الآن يحفظ تلقائياً عند الإغلاق")
    print("• لا حاجة لخطوات إضافية")
    
    print("\n2️⃣ الحفظ اليدوي:")
    print("• شغل 'حفظ_بيانات_ProTech.py' لحفظ البيانات يدوياً")
    print("• استخدم هذا قبل إغلاق البرنامج للأمان")
    
    print("\n3️⃣ النسخ الاحتياطية:")
    print("• شغل 'نسخ_احتياطي_ProTech.py' لإنشاء نسخ احتياطية")
    print("• شغل 'استعادة_بيانات_ProTech.py' لاستعادة البيانات")
    
    print("\n💡 التوصيات:")
    print("1. استخدم الحفظ اليدوي قبل إغلاق البرنامج")
    print("2. أنشئ نسخة احتياطية بانتظام")
    print("3. تحقق من وجود البيانات بعد إعادة التشغيل")
    
    print("\n🎉 تم إنشاء حلول متعددة لمشكلة استمرارية البيانات!")
    
    if len(created_items) >= 2:
        print("✅ لديك الآن حلول متعددة لحماية البيانات")
    else:
        print("⚠️ قد تحتاج استخدام الحلول اليدوية")

if __name__ == "__main__":
    main()
