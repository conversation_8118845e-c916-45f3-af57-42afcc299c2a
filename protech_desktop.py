#!/usr/bin/env python3
"""
ProTech Accounting System - Desktop Application
نظام ProTech للمحاسبة - تطبيق سطح المكتب
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import webbrowser
import subprocess
import sys
import os
import time
from datetime import datetime

class ProTechDesktopApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام ProTech للمحاسبة - ProTech Accounting System")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f8ff')
        
        # Set window icon (if available)
        try:
            self.root.iconbitmap('icon.ico')
        except:
            pass
        
        # Variables
        self.server_process = None
        self.server_running = False
        
        # Create GUI
        self.create_widgets()
        
        # Center window
        self.center_window()
        
    def center_window(self):
        """Center the window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_widgets(self):
        """Create all GUI widgets"""
        
        # Header Frame
        header_frame = tk.Frame(self.root, bg='#2563eb', height=100)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame, 
            text="🎉 نظام ProTech للمحاسبة",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#2563eb'
        )
        title_label.pack(pady=10)
        
        subtitle_label = tk.Label(
            header_frame,
            text="ProTech Accounting System - Desktop Application",
            font=('Arial', 12),
            fg='#bfdbfe',
            bg='#2563eb'
        )
        subtitle_label.pack()
        
        # Main Content Frame
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Status Frame
        status_frame = tk.LabelFrame(main_frame, text="حالة النظام / System Status", font=('Arial', 12, 'bold'), bg='#f0f8ff')
        status_frame.pack(fill='x', pady=5)
        
        self.status_label = tk.Label(
            status_frame,
            text="⚪ النظام متوقف / System Stopped",
            font=('Arial', 11),
            fg='#dc2626',
            bg='#f0f8ff'
        )
        self.status_label.pack(pady=5)
        
        # Control Buttons Frame
        control_frame = tk.LabelFrame(main_frame, text="التحكم في النظام / System Control", font=('Arial', 12, 'bold'), bg='#f0f8ff')
        control_frame.pack(fill='x', pady=5)
        
        button_frame = tk.Frame(control_frame, bg='#f0f8ff')
        button_frame.pack(pady=10)
        
        # Start Button
        self.start_btn = tk.Button(
            button_frame,
            text="🚀 تشغيل النظام\nStart System",
            font=('Arial', 11, 'bold'),
            bg='#16a34a',
            fg='white',
            width=15,
            height=3,
            command=self.start_server
        )
        self.start_btn.pack(side='left', padx=5)
        
        # Stop Button
        self.stop_btn = tk.Button(
            button_frame,
            text="🛑 إيقاف النظام\nStop System",
            font=('Arial', 11, 'bold'),
            bg='#dc2626',
            fg='white',
            width=15,
            height=3,
            command=self.stop_server,
            state='disabled'
        )
        self.stop_btn.pack(side='left', padx=5)
        
        # Open Browser Button
        self.browser_btn = tk.Button(
            button_frame,
            text="🌐 فتح المتصفح\nOpen Browser",
            font=('Arial', 11, 'bold'),
            bg='#2563eb',
            fg='white',
            width=15,
            height=3,
            command=self.open_browser,
            state='disabled'
        )
        self.browser_btn.pack(side='left', padx=5)
        
        # Information Frame
        info_frame = tk.LabelFrame(main_frame, text="معلومات النظام / System Information", font=('Arial', 12, 'bold'), bg='#f0f8ff')
        info_frame.pack(fill='both', expand=True, pady=5)
        
        # Create notebook for tabs
        notebook = ttk.Notebook(info_frame)
        notebook.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Info Tab
        info_tab = tk.Frame(notebook, bg='#f0f8ff')
        notebook.add(info_tab, text="معلومات / Info")
        
        info_text = tk.Text(info_tab, wrap='word', font=('Arial', 10), bg='white', fg='black')
        info_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        info_content = """
🎉 مرحباً بك في نظام ProTech للمحاسبة
Welcome to ProTech Accounting System

📋 المميزات / Features:
✅ إدارة المخزون / Inventory Management
✅ إدارة العملاء / Customer Management  
✅ إدارة الموردين / Supplier Management
✅ إدارة المبيعات / Sales Management
✅ التقارير والتحليلات / Reports & Analytics
✅ دعم ثنائي اللغة / Bilingual Support
✅ واجهة سطح مكتب / Desktop Interface

🌐 الروابط / URLs:
📊 لوحة التحكم / Dashboard: http://localhost:5000
📦 المخزون / Inventory: http://localhost:5000/inventory
👥 العملاء / Customers: http://localhost:5000/customers
🏢 الموردين / Suppliers: http://localhost:5000/suppliers
💰 المبيعات / Sales: http://localhost:5000/sales
📈 التقارير / Reports: http://localhost:5000/reports

🔧 التشغيل / Operation:
1. اضغط "تشغيل النظام" لبدء الخادم
2. اضغط "فتح المتصفح" للوصول للتطبيق
3. استخدم النظام من خلال المتصفح
4. اضغط "إيقاف النظام" عند الانتهاء

📞 الدعم / Support:
📧 البريد الإلكتروني / Email: <EMAIL>
🌐 الموقع / Website: www.protech.com
📱 الهاتف / Phone: +966-11-123-4567
        """
        
        info_text.insert('1.0', info_content)
        info_text.config(state='disabled')
        
        # Log Tab
        log_tab = tk.Frame(notebook, bg='#f0f8ff')
        notebook.add(log_tab, text="سجل النشاط / Activity Log")
        
        self.log_text = scrolledtext.ScrolledText(log_tab, wrap='word', font=('Consolas', 9), bg='black', fg='#00ff00')
        self.log_text.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Add initial log entry
        self.add_log("🎉 تم تشغيل تطبيق سطح المكتب / Desktop application started")
        self.add_log(f"🕒 الوقت / Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        self.add_log("📋 جاهز للاستخدام / Ready for use")
        
        # Footer
        footer_frame = tk.Frame(self.root, bg='#374151', height=40)
        footer_frame.pack(fill='x', side='bottom')
        footer_frame.pack_propagate(False)
        
        footer_label = tk.Label(
            footer_frame,
            text="© 2024 ProTech Accounting System | نظام ProTech للمحاسبة",
            font=('Arial', 9),
            fg='white',
            bg='#374151'
        )
        footer_label.pack(pady=10)
    
    def add_log(self, message):
        """Add message to log"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert('end', log_entry)
        self.log_text.see('end')
        self.root.update()
    
    def start_server(self):
        """Start the Flask server"""
        try:
            self.add_log("🚀 بدء تشغيل الخادم... / Starting server...")
            
            # Start server in a separate thread
            server_thread = threading.Thread(target=self.run_server, daemon=True)
            server_thread.start()
            
            # Wait a moment for server to start
            self.root.after(3000, self.check_server_status)
            
        except Exception as e:
            self.add_log(f"❌ خطأ في بدء الخادم / Server start error: {str(e)}")
            messagebox.showerror("خطأ / Error", f"فشل في بدء الخادم:\n{str(e)}")
    
    def run_server(self):
        """Run the Flask server"""
        try:
            # Run the basic Flask app
            cmd = [sys.executable, "basic_flask.py"]
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
            
        except Exception as e:
            self.add_log(f"❌ خطأ في تشغيل الخادم / Server run error: {str(e)}")
    
    def check_server_status(self):
        """Check if server is running"""
        try:
            import urllib.request
            urllib.request.urlopen('http://localhost:5000', timeout=5)
            
            # Server is running
            self.server_running = True
            self.status_label.config(
                text="🟢 النظام يعمل / System Running",
                fg='#16a34a'
            )
            
            self.start_btn.config(state='disabled')
            self.stop_btn.config(state='normal')
            self.browser_btn.config(state='normal')
            
            self.add_log("✅ الخادم يعمل بنجاح / Server running successfully")
            self.add_log("🌐 متاح على / Available at: http://localhost:5000")
            
            messagebox.showinfo(
                "نجح التشغيل / Success",
                "تم تشغيل النظام بنجاح!\nيمكنك الآن فتح المتصفح للوصول للتطبيق.\n\nSystem started successfully!\nYou can now open the browser to access the application."
            )
            
        except:
            # Server not ready yet, try again
            self.root.after(2000, self.check_server_status)
    
    def stop_server(self):
        """Stop the Flask server"""
        try:
            if self.server_process:
                self.server_process.terminate()
                self.server_process = None
            
            self.server_running = False
            self.status_label.config(
                text="⚪ النظام متوقف / System Stopped",
                fg='#dc2626'
            )
            
            self.start_btn.config(state='normal')
            self.stop_btn.config(state='disabled')
            self.browser_btn.config(state='disabled')
            
            self.add_log("🛑 تم إيقاف الخادم / Server stopped")
            
            messagebox.showinfo(
                "تم الإيقاف / Stopped",
                "تم إيقاف النظام بنجاح.\n\nSystem stopped successfully."
            )
            
        except Exception as e:
            self.add_log(f"❌ خطأ في إيقاف الخادم / Server stop error: {str(e)}")
            messagebox.showerror("خطأ / Error", f"فشل في إيقاف الخادم:\n{str(e)}")
    
    def open_browser(self):
        """Open browser to access the application"""
        try:
            webbrowser.open('http://localhost:5000')
            self.add_log("🌐 تم فتح المتصفح / Browser opened")
            
        except Exception as e:
            self.add_log(f"❌ خطأ في فتح المتصفح / Browser open error: {str(e)}")
            messagebox.showerror("خطأ / Error", f"فشل في فتح المتصفح:\n{str(e)}")
    
    def on_closing(self):
        """Handle window closing"""
        if self.server_running:
            result = messagebox.askyesno(
                "تأكيد الإغلاق / Confirm Close",
                "النظام لا يزال يعمل. هل تريد إيقافه وإغلاق التطبيق؟\n\nSystem is still running. Do you want to stop it and close the application?"
            )
            if result:
                self.stop_server()
                time.sleep(1)
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """Run the desktop application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main function"""
    print("🚀 Starting ProTech Desktop Application...")
    print("🖥️ تشغيل تطبيق ProTech لسطح المكتب...")
    
    try:
        app = ProTechDesktopApp()
        app.run()
    except Exception as e:
        print(f"❌ Error starting desktop application: {e}")
        input("Press Enter to exit...")

if __name__ == '__main__':
    main()
