#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Restored Version - ProTech
إصلاح النسخة المستعادة - ProTech
"""

import os
import re

def fix_unicode_issues():
    """Fix Unicode issues in restored version"""
    try:
        print("🔧 إصلاح مشاكل الترميز في النسخة المستعادة...")
        
        # Read the file
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix problematic Unicode sequences
        fixes = [
            (r'\\U0001f680[^"]*', 'تشغيل ProTech'),
            (r'\\u274c[^"]*', 'خطأ'),
            (r'\\u2705[^"]*', 'نجح'),
            (r'print\("\\U0001f680[^"]*"\)', 'print("تشغيل ProTech...")'),
            (r'print\(f"\\u274c[^"]*"\)', 'print("خطأ في التشغيل")'),
        ]
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content)
        
        # Fix specific problematic lines
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # Fix main function print statements
            if 'print(' in line and ('\\U0001f680' in line or '\\u274c' in line):
                if 'بدء تشغيل نظام ProTech' in line:
                    line = '    print("تشغيل ProTech...")'
                elif 'خطأ في بدء التشغيل' in line:
                    line = '    print("خطأ في التشغيل:", str(e))'
            
            fixed_lines.append(line)
        
        # Write fixed content
        fixed_content = '\n'.join(fixed_lines)
        
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("✅ تم إصلاح مشاكل الترميز")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def test_fixed_version():
    """Test the fixed version"""
    try:
        import subprocess
        import sys
        
        print("🧪 اختبار النسخة المصلحة...")
        
        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التركيب نجح")
            return True
        else:
            print(f"❌ خطأ في التركيب: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_safe_launcher_948():
    """Create safe launcher for 9:48 version"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech 9:48 Safe Launcher
مشغل ProTech الآمن - نسخة 9:48
"""

import os
import sys
import subprocess

def main():
    """Launch ProTech 9:48 version safely"""
    try:
        print("تشغيل ProTech - نسخة 9:48...")
        
        # Set UTF-8 encoding
        if sys.platform == 'win32':
            os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Check file exists
        if not os.path.exists('protech_simple_working.py'):
            print("❌ ملف ProTech غير موجود!")
            input("اضغط Enter للخروج...")
            return
        
        # Launch with proper encoding
        result = subprocess.run([
            sys.executable, 'protech_simple_working.py'
        ], env=os.environ.copy())
        
        if result.returncode != 0:
            print("خطأ في التشغيل")
            input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
    
    with open('launch_protech_948.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل آمن للنسخة 9:48: launch_protech_948.py")

def main():
    """Main function"""
    print("🔄 إصلاح النسخة المستعادة من الساعة 9:48")
    print("🔄 Fix Restored Version from 9:48")
    print("=" * 60)
    
    try:
        # Step 1: Fix Unicode issues
        if fix_unicode_issues():
            print("✅ تم إصلاح مشاكل الترميز")
        
        # Step 2: Test fixed version
        if test_fixed_version():
            print("✅ الاختبار نجح")
        
        # Step 3: Create safe launcher
        create_safe_launcher_948()
        
        print("\n" + "=" * 60)
        print("✅ تم إصلاح النسخة المستعادة بنجاح!")
        print("✅ Restored version fixed successfully!")
        print("=" * 60)
        
        print("\n🎯 تم استعادة البرنامج كما كان عند الساعة 9:48")
        print("• تم إصلاح مشاكل الترميز")
        print("• تم إنشاء مشغل آمن")
        print("• البرنامج جاهز للتشغيل")
        
        print("\n🚀 الآن يمكنك:")
        print("1. تشغيل launch_protech_948.py")
        print("2. النقر المزدوج على protech_simple_working.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
