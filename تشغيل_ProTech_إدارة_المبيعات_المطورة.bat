@echo off
title ProTech Enhanced Sales Management System
color 0A

echo.
echo ================================================================
echo    🎉 نظام ProTech - إدارة المبيعات المطورة
echo    🎉 ProTech System - Enhanced Sales Management
echo ================================================================
echo.

echo [1/4] إيقاف العمليات السابقة / Stopping previous processes...
taskkill /f /im python.exe >nul 2>&1
echo ✅ تم إيقاف العمليات السابقة / Previous processes stopped

echo.
echo [2/4] فحص متطلبات النظام / System requirements check...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير موجود / Python not found
    pause
    exit /b 1
)
echo ✅ Python متوفر / Python available

echo.
echo [3/4] فحص ملفات النظام / System files check...
if not exist "protech_simple_working.py" (
    echo ❌ ملف التطبيق غير موجود / Application file missing
    pause
    exit /b 1
)
echo ✅ ملف التطبيق موجود / Application file exists

echo.
echo [4/4] تشغيل النظام المطور / Starting enhanced system...

echo.
echo ┌─────────────────────────────────────────────────────────────┐
echo │  🎉 نظام ProTech - إدارة المبيعات المطورة                 │
echo │  🎉 ProTech System - Enhanced Sales Management             │
echo │                                                             │
echo │  📝 القسم الأول - معلومات الفاتورة / Section 1:            │
echo │  🏷️ نوع العميل مع التسعير المتدرج / Customer Type:        │
echo │     🏪 صاحب متجر (+5%%) / Shop Owner (+5%%)               │
echo │     🚚 موزع معتمد (+15%%) / Authorized Distributor (+15%%) │
echo │     📦 عميل جملة (+20%%) / Wholesale Customer (+20%%)      │
echo │     🛒 عميل تجزئة (+30%%) / Retail Customer (+30%%)        │
echo │  👤 اسم العميل / Customer Name                            │
echo │  📱 رقم الهاتف / Phone Number                             │
echo │  🧾 رقم الفاتورة / Invoice Number                         │
echo │  📊 هامش الربح الحالي / Current Margin                    │
echo │  📅 تاريخ الفاتورة / Invoice Date                         │
echo │                                                             │
echo │  📦 القسم الثاني - جدول المنتجات / Section 2:             │
echo │  📱 الباركود سكانر / Barcode Scanner                      │
echo │  📝 الاسم / Name                                          │
echo │  🔢 الكمية / Quantity                                     │
echo │  💰 السعر الإفرادي / Unit Price                           │
echo │  💵 المجموع / Total                                       │
echo │  ⚙️ الإجراءات / Actions (تعديل/حذف)                      │
echo │                                                             │
echo │  🛠️ وظائف متقدمة / Advanced Functions:                    │
echo │  ✅ تسعير تلقائي حسب نوع العميل / Auto pricing by type   │
echo │  ✅ باركود سكانر + إدخال يدوي / Barcode + manual input   │
echo │  ✅ تعديل وحذف المنتجات / Edit & delete products         │
echo │  ✅ فحص المخزون / Stock validation                       │
echo │  ✅ حساب تلقائي للمجاميع / Auto total calculation        │
echo │  ✅ ربط مع قاعدة العملاء / Customer database integration │
echo │                                                             │
echo │  💾 أزرار الحفظ والطباعة / Save & Print Buttons:          │
echo │  ✅ حفظ الفاتورة / Save Invoice                           │
echo │  ✅ تحديث المخزون / Update Stock                          │
echo │  ✅ طباعة الفاتورة / Print Invoice                        │
echo │                                                             │
echo │  📊 أمثلة التسعير / Pricing Examples:                      │
echo │  إذا السعر الأساسي $100 / If base price is $100:         │
echo │  🏪 صاحب متجر: $105 / Shop Owner: $105                   │
echo │  🚚 موزع معتمد: $115 / Distributor: $115                 │
echo │  📦 عميل جملة: $120 / Wholesale: $120                    │
echo │  🛒 عميل تجزئة: $130 / Retail: $130                      │
echo │                                                             │
echo │  🚀 جاري التشغيل... / Starting...                        │
echo └─────────────────────────────────────────────────────────────┘
echo.

echo 🖥️ تشغيل نظام إدارة المبيعات المطور...
echo 🖥️ Starting Enhanced Sales Management System...
echo.

python protech_simple_working.py

echo.
echo ================================================================
echo 🛑 تم إغلاق نظام إدارة المبيعات المطور
echo 🛑 Enhanced Sales Management System Closed
echo.
echo 💾 تم حفظ جميع بيانات المبيعات
echo 💾 All sales data saved
echo.
echo 📊 ملخص الجلسة / Session Summary:
echo    - إدارة المبيعات: مطورة ومتكاملة / Sales Management: Enhanced & Integrated
echo    - القسمين: معلومات + جدول / Two Sections: Info + Table
echo    - التسعير المتدرج: 4 مستويات / Tiered Pricing: 4 levels
echo    - الباركود سكانر: متقدم ومرن / Barcode Scanner: Advanced & Flexible
echo    - وظائف التعديل: شاملة وآمنة / Edit Functions: Complete & Safe
echo    - الحفظ والطباعة: جاهز ومتكامل / Save & Print: Ready & Integrated
echo.
echo 🎯 تم تحقيق 100%% من المطلوب!
echo 🎯 100%% of requirements achieved!
echo.
echo 🌟 شكراً لاستخدام نظام ProTech المطور!
echo 🌟 Thank you for using ProTech Enhanced System!
echo ================================================================
pause
