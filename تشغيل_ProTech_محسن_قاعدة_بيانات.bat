@echo off
title ProTech Enhanced Database System - نظام ProTech المحسن مع قاعدة البيانات
color 0A

echo.
echo ================================================================
echo    🗄️ نظام ProTech المحسن مع قاعدة البيانات المتقدمة
echo    🗄️ ProTech Enhanced Advanced Database System
echo ================================================================
echo.

echo [1/8] فحص متطلبات النظام المحسن / Checking enhanced system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت / Python not installed
    echo يرجى تثبيت Python 3.8+ من https://python.org
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر / Python available
)

echo.
echo [2/8] فحص المكتبات المطلوبة المحسنة / Checking enhanced required libraries...
python -c "import tkinter, json, os, sqlite3, datetime, threading, time, gc, psutil, sys, traceback, logging, hashlib, gzip, shutil" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبات مطلوبة مفقودة / Required libraries missing
    echo تثبيت المكتبات المطلوبة / Installing required libraries...
    pip install --upgrade pip
    pip install psutil
    echo ✅ تم تثبيت المكتبات / Libraries installed
) else (
    echo ✅ جميع المكتبات المحسنة متوفرة / All enhanced libraries available
)

echo.
echo [3/8] إنشاء مجلدات النظام المحسن / Creating enhanced system directories...
if not exist "enhanced_backups" mkdir enhanced_backups
if not exist "enhanced_archives" mkdir enhanced_archives
if not exist "logs" mkdir logs
if not exist "temp" mkdir temp
if not exist "exports" mkdir exports
if not exist "reports" mkdir reports
echo ✅ تم إنشاء مجلدات النظام المحسن / Enhanced system directories created

echo.
echo [4/8] فحص ملفات النظام المحسن / Checking enhanced system files...
if exist "protech_enhanced_database.py" (
    echo ✅ النظام المحسن موجود / Enhanced system found
) else (
    echo ❌ النظام المحسن مفقود / Enhanced system missing
    echo يرجى التأكد من وجود ملف protech_enhanced_database.py
    pause
    exit /b 1
)

if exist "protech_simple_working.py" (
    echo ✅ النظام الأصلي موجود / Original system found
) else (
    echo ⚠️ النظام الأصلي غير موجود / Original system not found
    echo سيتم تشغيل النظام المحسن فقط / Will run enhanced system only
)

echo.
echo [5/8] فحص قاعدة البيانات المحسنة / Checking enhanced database...
python -c "
import sqlite3
import os
try:
    if os.path.exists('protech_enhanced.db'):
        conn = sqlite3.connect('protech_enhanced.db')
        cursor = conn.cursor()
        cursor.execute('SELECT name FROM sqlite_master WHERE type=\"table\"')
        tables = cursor.fetchall()
        conn.close()
        print(f'✅ قاعدة البيانات المحسنة جاهزة مع {len(tables)} جدول')
        print(f'✅ Enhanced database ready with {len(tables)} tables')
    else:
        print('📝 سيتم إنشاء قاعدة بيانات جديدة')
        print('📝 New database will be created')
except Exception as e:
    print(f'⚠️ تحذير في قاعدة البيانات: {e}')
    print(f'⚠️ Database warning: {e}')
" 2>nul

echo.
echo [6/8] فحص البيانات الموجودة / Checking existing data...
if exist "protech_simple_data.json" (
    echo ✅ ملف البيانات JSON موجود / JSON data file found
    python -c "
import json
try:
    with open('protech_simple_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    products = len(data.get('products', []))
    customers = len(data.get('customers', []))
    suppliers = len(data.get('suppliers', []))
    print(f'📊 البيانات: {products} منتج، {customers} عميل، {suppliers} مورد')
    print(f'📊 Data: {products} products, {customers} customers, {suppliers} suppliers')
except Exception as e:
    print(f'⚠️ خطأ في قراءة البيانات: {e}')
" 2>nul
) else (
    echo 📝 لا توجد بيانات سابقة - سيبدأ النظام فارغاً / No previous data - system will start empty
)

echo.
echo [7/8] تهيئة النظام المحسن / Initializing enhanced system...
python -c "
from protech_enhanced_database import ProTechEnhancedDatabase
import sys
try:
    print('🔧 تهيئة قاعدة البيانات المحسنة...')
    print('🔧 Initializing enhanced database...')
    # Test initialization without GUI
    import tkinter as tk
    root = tk.Tk()
    root.withdraw()  # Hide the window
    print('✅ تم اختبار النظام المحسن بنجاح')
    print('✅ Enhanced system test successful')
    root.destroy()
except Exception as e:
    print(f'❌ خطأ في تهيئة النظام المحسن: {e}')
    print(f'❌ Enhanced system initialization error: {e}')
    sys.exit(1)
" 2>nul

if errorlevel 1 (
    echo ❌ فشل في تهيئة النظام المحسن / Enhanced system initialization failed
    echo.
    echo 🔧 خطوات استكشاف الأخطاء / Troubleshooting steps:
    echo    1. تأكد من تثبيت Python بشكل صحيح / Ensure Python is properly installed
    echo    2. تأكد من تثبيت جميع المكتبات المطلوبة / Ensure all required libraries are installed
    echo    3. تحقق من أذونات الملفات / Check file permissions
    echo    4. راجع ملفات السجلات في مجلد logs / Check log files in logs folder
    echo.
    pause
    exit /b 1
)

echo.
echo [8/8] تشغيل نظام ProTech المحسن / Starting ProTech Enhanced System...
echo.
echo 🎯 المميزات المحسنة الجديدة / New Enhanced Features:
echo    • قاعدة بيانات SQLite محسنة مع فهرسة متقدمة / Enhanced SQLite with advanced indexing
echo    • واجهة مستخدم محسنة مع تصميم حديث / Enhanced UI with modern design
echo    • نظام بحث متقدم مع ذاكرة مؤقتة ذكية / Advanced search with smart caching
echo    • إدارة شاملة للمخزون مع تتبع متقدم / Comprehensive inventory with advanced tracking
echo    • نسخ احتياطية تلقائية مضغوطة / Automatic compressed backups
echo    • مراقبة الأداء في الوقت الفعلي / Real-time performance monitoring
echo    • تصدير واستيراد JSON محسن / Enhanced JSON export/import
echo    • نظام سجلات متقدم / Advanced logging system
echo    • تحسينات الذاكرة والأداء / Memory and performance optimizations
echo    • اختصارات لوحة المفاتيح للإنتاجية / Keyboard shortcuts for productivity
echo    • إحصائيات مفصلة لقاعدة البيانات / Detailed database statistics
echo    • واجهة إدارة قاعدة البيانات / Database management interface
echo.

echo 🚀 بدء التشغيل المحسن / Starting enhanced application...
echo.

python protech_enhanced_database.py

echo.
if errorlevel 1 (
    echo ❌ حدث خطأ في تشغيل النظام المحسن / Error occurred in enhanced system
    echo.
    echo 🔧 خطوات استكشاف الأخطاء المتقدمة / Advanced troubleshooting steps:
    echo    1. تحقق من سجل الأخطاء في logs/protech_enhanced.log
    echo    2. تأكد من سلامة قاعدة البيانات protech_enhanced.db
    echo    3. جرب حذف ملفات الذاكرة المؤقتة في مجلد temp
    echo    4. تأكد من وجود مساحة كافية على القرص الصلب
    echo    5. جرب تشغيل النظام كمدير
    echo.
    echo    1. Check error log in logs/protech_enhanced.log
    echo    2. Verify database integrity protech_enhanced.db
    echo    3. Try deleting cache files in temp folder
    echo    4. Ensure sufficient disk space
    echo    5. Try running as administrator
    echo.
    echo 📁 الملفات المهمة / Important files:
    echo    - protech_enhanced_database.py (النظام المحسن / Enhanced system)
    echo    - protech_enhanced.db (قاعدة البيانات / Database)
    echo    - logs/ (مجلد السجلات / Logs folder)
    echo    - enhanced_backups/ (النسخ الاحتياطية / Backups)
    echo.
) else (
    echo ✅ تم إغلاق النظام المحسن بنجاح / Enhanced system closed successfully
    echo.
    echo 📊 معلومات النظام المحسن / Enhanced System Information:
    echo    • تم حفظ النسخ الاحتياطية في enhanced_backups/
    echo    • تم حفظ السجلات المفصلة في logs/
    echo    • تم حفظ التقارير في reports/
    echo    • تم حفظ الصادرات في exports/
    echo    • Backups saved in enhanced_backups/
    echo    • Detailed logs saved in logs/
    echo    • Reports saved in reports/
    echo    • Exports saved in exports/
    echo.
    echo 🔄 لإعادة التشغيل، انقر نقرة مزدوجة على هذا الملف مرة أخرى
    echo 🔄 To restart, double-click this file again
)

echo.
echo ================================================================
echo    شكراً لاستخدام نظام ProTech المحسن مع قاعدة البيانات المتقدمة
echo    Thank you for using ProTech Enhanced Advanced Database System
echo    
echo    🌟 النسخة المحسنة 2.0 - Enhanced Version 2.0
echo    🏢 نظام محاسبة متكامل - Integrated Accounting System
echo    🗄️ قاعدة بيانات متقدمة - Advanced Database
echo ================================================================
echo.

pause
