#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Performance Optimizer
محسن الأداء العملي لـ ProTech

Optimize ProTech for better operational performance
تحسين ProTech لأداء عملي أفضل
"""

import os
import shutil
import json
from datetime import datetime

def create_backup():
    """Create backup of current file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.performance_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def create_optimized_protech():
    """Create performance-optimized ProTech"""
    
    optimized_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Simple Working - Performance Optimized
نظام ProTech للمحاسبة - محسن الأداء العملي

High-performance accounting system with operational optimizations
نظام محاسبة عالي الأداء مع تحسينات عملية
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime
import threading
import time

class ProTechOptimized:
    """ProTech with operational performance optimizations"""
    
    def __init__(self):
        """Initialize with performance optimizations"""
        try:
            # Core data structures
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            self.loading = False
            self.data_file = "protech_data.json"
            
            # Performance settings
            self.auto_save_enabled = True
            self.auto_save_interval = 30  # seconds
            self.last_save_time = time.time()
            
            # Create optimized interface
            self.create_main_window()
            self.create_interface()
            
            # Load data and start auto-save
            self.root.after(100, self.load_data)
            self.start_auto_save()
            
            print("✅ ProTech محسن الأداء جاهز")
            
        except Exception as e:
            print(f"❌ خطأ في التهيئة: {e}")
            self.create_safe_mode()
    
    def create_main_window(self):
        """Create optimized main window"""
        try:
            self.root = tk.Tk()
            self.root.title("🧮 ProTech - محسن الأداء العملي")
            self.root.geometry("1200x800")
            self.root.configure(bg='#f8fafc')
            
            # Set calculator icon
            self.set_calculator_icon()
            
            # Performance optimizations
            self.root.resizable(True, True)
            self.root.minsize(800, 600)
            
            # Center and bring to front
            self.center_window()
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after_idle(lambda: self.root.attributes('-topmost', False))
            
        except Exception as e:
            print(f"❌ خطأ في النافذة: {e}")
    
    def set_calculator_icon(self):
        """Set calculator icon"""
        try:
            calc_paths = [r'C:\\Windows\\System32\\calc.exe']
            for path in calc_paths:
                if os.path.exists(path):
                    try:
                        self.root.iconbitmap(path)
                        return
                    except:
                        continue
            # Fallback
            current_title = self.root.title()
            if not current_title.startswith('🧮'):
                self.root.title(f'🧮 {current_title}')
        except:
            pass
    
    def center_window(self):
        """Center window on screen"""
        try:
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
        except:
            pass
    
    def create_safe_mode(self):
        """Create safe mode interface"""
        try:
            self.root = tk.Tk()
            self.root.title("ProTech - Safe Mode")
            self.root.geometry("600x400")
            
            label = tk.Label(self.root, text="ProTech - Safe Mode\\nالوضع الآمن", 
                           font=('Arial', 16), fg='red')
            label.pack(expand=True)
            
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
        except:
            pass
    
    def create_interface(self):
        """Create optimized interface"""
        try:
            # Main container
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Notebook for tabs
            self.notebook = ttk.Notebook(main_frame)
            self.notebook.pack(fill='both', expand=True)
            
            # Create tabs
            self.create_suppliers_tab()
            self.create_products_tab()
            self.create_customers_tab()
            self.create_sales_tab()
            self.create_reports_tab()
            
            # Status bar
            self.create_status_bar()
            
        except Exception as e:
            print(f"❌ خطأ في الواجهة: {e}")
    
    def create_suppliers_tab(self):
        """Create suppliers tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="🏪 الموردين")
            
            # Input section
            input_frame = ttk.LabelFrame(frame, text="إضافة مورد جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)
            
            ttk.Label(input_frame, text="اسم المورد:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.supplier_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.supplier_name, width=30).grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.supplier_phone = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.supplier_phone, width=20).grid(row=0, column=3, padx=5, pady=5)
            
            ttk.Button(input_frame, text="إضافة", command=self.add_supplier).grid(row=0, column=4, padx=10, pady=5)
            
            # List section
            list_frame = ttk.LabelFrame(frame, text="قائمة الموردين", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)
            
            self.suppliers_tree = ttk.Treeview(list_frame, columns=('name', 'phone'), show='headings', height=15)
            self.suppliers_tree.heading('name', text='اسم المورد')
            self.suppliers_tree.heading('phone', text='الهاتف')
            self.suppliers_tree.pack(fill='both', expand=True)
            
        except Exception as e:
            print(f"❌ خطأ في تبويب الموردين: {e}")
    
    def create_products_tab(self):
        """Create products tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="📦 المنتجات")
            
            # Input section
            input_frame = ttk.LabelFrame(frame, text="إضافة منتج جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)
            
            # Row 1
            ttk.Label(input_frame, text="اسم المنتج:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.product_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_name, width=25).grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الباركود:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.product_barcode = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_barcode, width=15).grid(row=0, column=3, padx=5, pady=5)
            
            # Row 2
            ttk.Label(input_frame, text="السعر:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
            self.product_price = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_price, width=15).grid(row=1, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الكمية:").grid(row=1, column=2, padx=5, pady=5, sticky='e')
            self.product_stock = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_stock, width=15).grid(row=1, column=3, padx=5, pady=5)
            
            ttk.Button(input_frame, text="إضافة", command=self.add_product).grid(row=1, column=4, padx=10, pady=5)
            
            # List section
            list_frame = ttk.LabelFrame(frame, text="قائمة المنتجات", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)
            
            self.products_tree = ttk.Treeview(list_frame, 
                                            columns=('name', 'barcode', 'price', 'stock'), 
                                            show='headings', height=15)
            self.products_tree.heading('name', text='المنتج')
            self.products_tree.heading('barcode', text='الباركود')
            self.products_tree.heading('price', text='السعر')
            self.products_tree.heading('stock', text='الكمية')
            self.products_tree.pack(fill='both', expand=True)
            
        except Exception as e:
            print(f"❌ خطأ في تبويب المنتجات: {e}")
    
    def create_customers_tab(self):
        """Create customers tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="👥 العملاء")
            
            # Input section
            input_frame = ttk.LabelFrame(frame, text="إضافة عميل جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)
            
            ttk.Label(input_frame, text="اسم العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.customer_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.customer_phone = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.customer_phone, width=20).grid(row=0, column=3, padx=5, pady=5)
            
            ttk.Label(input_frame, text="النوع:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
            self.customer_type = tk.StringVar()
            type_combo = ttk.Combobox(input_frame, textvariable=self.customer_type, width=22)
            type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
            type_combo.grid(row=1, column=1, padx=5, pady=5)
            type_combo.set('تجزئة')
            
            ttk.Button(input_frame, text="إضافة", command=self.add_customer).grid(row=1, column=3, padx=10, pady=5)
            
            # List section
            list_frame = ttk.LabelFrame(frame, text="قائمة العملاء", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)
            
            self.customers_tree = ttk.Treeview(list_frame, 
                                             columns=('name', 'phone', 'type'), 
                                             show='headings', height=15)
            self.customers_tree.heading('name', text='العميل')
            self.customers_tree.heading('phone', text='الهاتف')
            self.customers_tree.heading('type', text='النوع')
            self.customers_tree.pack(fill='both', expand=True)
            
        except Exception as e:
            print(f"❌ خطأ في تبويب العملاء: {e}")

    def create_sales_tab(self):
        """Create sales tab with performance optimizations"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="💰 المبيعات")

            # Customer info
            customer_frame = ttk.LabelFrame(frame, text="معلومات العميل", padding=10)
            customer_frame.pack(fill='x', padx=10, pady=5)

            ttk.Label(customer_frame, text="العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.sale_customer_name = tk.StringVar()
            ttk.Entry(customer_frame, textvariable=self.sale_customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)

            ttk.Label(customer_frame, text="النوع:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.sale_customer_type = tk.StringVar()
            type_combo = ttk.Combobox(customer_frame, textvariable=self.sale_customer_type, width=15)
            type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
            type_combo.grid(row=0, column=3, padx=5, pady=5)
            type_combo.set('تجزئة')

            # Product input
            product_frame = ttk.LabelFrame(frame, text="إضافة منتج", padding=10)
            product_frame.pack(fill='x', padx=10, pady=5)

            ttk.Label(product_frame, text="الباركود:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.sale_barcode = tk.StringVar()
            barcode_entry = ttk.Entry(product_frame, textvariable=self.sale_barcode, width=20)
            barcode_entry.grid(row=0, column=1, padx=5, pady=5)
            barcode_entry.bind('<Return>', self.add_product_to_sale)

            ttk.Label(product_frame, text="الكمية:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.sale_quantity = tk.StringVar()
            self.sale_quantity.set('1')
            ttk.Entry(product_frame, textvariable=self.sale_quantity, width=10).grid(row=0, column=3, padx=5, pady=5)

            ttk.Button(product_frame, text="إضافة", command=self.add_product_to_sale).grid(row=0, column=4, padx=10, pady=5)

            # Sale items
            sale_frame = ttk.LabelFrame(frame, text="منتجات الفاتورة", padding=10)
            sale_frame.pack(fill='both', expand=True, padx=10, pady=5)

            self.sale_tree = ttk.Treeview(sale_frame,
                                        columns=('name', 'qty', 'price', 'total'),
                                        show='headings', height=10)
            self.sale_tree.heading('name', text='المنتج')
            self.sale_tree.heading('qty', text='الكمية')
            self.sale_tree.heading('price', text='السعر')
            self.sale_tree.heading('total', text='المجموع')
            self.sale_tree.pack(fill='both', expand=True)

            # Total and buttons
            total_frame = ttk.Frame(frame)
            total_frame.pack(fill='x', padx=10, pady=5)

            self.total_label = ttk.Label(total_frame, text="المجموع: 0.00", font=('Arial', 14, 'bold'))
            self.total_label.pack(side='left', padx=10)

            ttk.Button(total_frame, text="حفظ", command=self.save_sale).pack(side='right', padx=5)
            ttk.Button(total_frame, text="مسح", command=self.clear_sale).pack(side='right', padx=5)

        except Exception as e:
            print(f"❌ خطأ في تبويب المبيعات: {e}")

    def create_reports_tab(self):
        """Create reports tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="📊 التقارير")

            # Buttons
            buttons_frame = ttk.LabelFrame(frame, text="التقارير", padding=10)
            buttons_frame.pack(fill='x', padx=10, pady=10)

            ttk.Button(buttons_frame, text="المبيعات", command=self.show_sales_report).pack(side='left', padx=10)
            ttk.Button(buttons_frame, text="المخزون", command=self.show_inventory_report).pack(side='left', padx=10)
            ttk.Button(buttons_frame, text="العملاء", command=self.show_customers_report).pack(side='left', padx=10)

            # Display area
            self.reports_text = tk.Text(frame, wrap='word', font=('Arial', 11))
            self.reports_text.pack(fill='both', expand=True, padx=10, pady=10)

        except Exception as e:
            print(f"❌ خطأ في تبويب التقارير: {e}")

    def create_status_bar(self):
        """Create status bar"""
        try:
            self.status_frame = ttk.Frame(self.root)
            self.status_frame.pack(side='bottom', fill='x', padx=10, pady=5)

            self.status_label = ttk.Label(self.status_frame, text="جاهز")
            self.status_label.pack(side='left', padx=10)

            ttk.Button(self.status_frame, text="💾 حفظ", command=self.save_data).pack(side='right', padx=10)

        except Exception as e:
            print(f"❌ خطأ في شريط الحالة: {e}")

    # Performance-optimized functions
    def add_supplier(self):
        """Add supplier with validation"""
        try:
            name = self.supplier_name.get().strip()
            phone = self.supplier_phone.get().strip()

            if not name:
                messagebox.showerror("خطأ", "أدخل اسم المورد")
                return

            supplier = {
                'name': name,
                'phone': phone,
                'date': datetime.now().isoformat()
            }

            self.suppliers.append(supplier)
            self.update_suppliers_display()

            self.supplier_name.set('')
            self.supplier_phone.set('')

            self.status_label.config(text=f"تم إضافة: {name}")
            self.trigger_auto_save()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def add_product(self):
        """Add product with validation"""
        try:
            name = self.product_name.get().strip()
            barcode = self.product_barcode.get().strip()
            price = float(self.product_price.get() or 0)
            stock = int(self.product_stock.get() or 0)

            if not name or price <= 0:
                messagebox.showerror("خطأ", "أدخل بيانات صحيحة")
                return

            product = {
                'name': name,
                'barcode': barcode,
                'price': price,
                'stock': stock,
                'date': datetime.now().isoformat()
            }

            self.products.append(product)
            self.update_products_display()

            self.product_name.set('')
            self.product_barcode.set('')
            self.product_price.set('')
            self.product_stock.set('')

            self.status_label.config(text=f"تم إضافة: {name}")
            self.trigger_auto_save()

        except ValueError:
            messagebox.showerror("خطأ", "أدخل أرقام صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def add_customer(self):
        """Add customer with validation"""
        try:
            name = self.customer_name.get().strip()
            phone = self.customer_phone.get().strip()
            customer_type = self.customer_type.get()

            if not name:
                messagebox.showerror("خطأ", "أدخل اسم العميل")
                return

            customer = {
                'name': name,
                'phone': phone,
                'type': customer_type,
                'date': datetime.now().isoformat()
            }

            self.customers.append(customer)
            self.update_customers_display()

            self.customer_name.set('')
            self.customer_phone.set('')
            self.customer_type.set('تجزئة')

            self.status_label.config(text=f"تم إضافة: {name}")
            self.trigger_auto_save()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def add_product_to_sale(self, event=None):
        """Add product to sale with optimization"""
        try:
            barcode = self.sale_barcode.get().strip()
            quantity = int(self.sale_quantity.get() or 1)

            if not barcode:
                messagebox.showerror("خطأ", "أدخل الباركود")
                return

            # Find product
            product = None
            for p in self.products:
                if p.get('barcode') == barcode or p.get('name', '').lower() == barcode.lower():
                    product = p
                    break

            if not product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return

            if product['stock'] < quantity:
                messagebox.showerror("خطأ", f"المخزون غير كافي. المتاح: {product['stock']}")
                return

            # Calculate price
            base_price = product['price']
            customer_type = self.sale_customer_type.get()

            margins = {
                'تجزئة': 1.30,      # +30%
                'جملة': 1.20,       # +20%
                'موزع معتمد': 1.15, # +15%
                'صاحب محل': 1.05   # +5%
            }

            final_price = base_price * margins.get(customer_type, 1.30)
            total = final_price * quantity

            # Add to tree
            self.sale_tree.insert('', 'end', values=(
                product['name'],
                quantity,
                f"{final_price:.2f}",
                f"{total:.2f}"
            ))

            self.update_sale_total()

            self.sale_barcode.set('')
            self.sale_quantity.set('1')

        except ValueError:
            messagebox.showerror("خطأ", "أدخل كمية صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def update_sale_total(self):
        """Update sale total"""
        try:
            total = 0
            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                if len(values) >= 4:
                    total += float(values[3])

            self.total_label.config(text=f"المجموع: {total:.2f}")

        except Exception as e:
            print(f"خطأ في المجموع: {e}")

    def save_sale(self):
        """Save sale with optimization"""
        try:
            customer_name = self.sale_customer_name.get().strip()
            customer_type = self.sale_customer_type.get()

            if not customer_name:
                messagebox.showerror("خطأ", "أدخل اسم العميل")
                return

            items = []
            total = 0

            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                if len(values) >= 4:
                    items.append({
                        'name': values[0],
                        'quantity': int(values[1]),
                        'price': float(values[2]),
                        'total': float(values[3])
                    })
                    total += float(values[3])

                    # Update stock
                    for product in self.products:
                        if product['name'] == values[0]:
                            product['stock'] -= int(values[1])
                            break

            if not items:
                messagebox.showerror("خطأ", "لا توجد منتجات")
                return

            sale = {
                'customer_name': customer_name,
                'customer_type': customer_type,
                'items': items,
                'total': total,
                'date': datetime.now().isoformat()
            }

            self.sales.append(sale)
            self.clear_sale()
            self.update_products_display()

            messagebox.showinfo("نجح", f"تم حفظ الفاتورة: {total:.2f}")
            self.status_label.config(text=f"فاتورة محفوظة: {total:.2f}")
            self.trigger_auto_save()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def clear_sale(self):
        """Clear sale"""
        try:
            for item in self.sale_tree.get_children():
                self.sale_tree.delete(item)

            self.sale_customer_name.set('')
            self.sale_customer_type.set('تجزئة')
            self.sale_barcode.set('')
            self.sale_quantity.set('1')
            self.total_label.config(text="المجموع: 0.00")

        except Exception as e:
            print(f"خطأ في المسح: {e}")

    # Display update functions
    def update_suppliers_display(self):
        """Update suppliers display"""
        try:
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            for supplier in self.suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier.get('name', ''),
                    supplier.get('phone', '')
                ))
        except Exception as e:
            print(f"خطأ في عرض الموردين: {e}")

    def update_products_display(self):
        """Update products display"""
        try:
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            for product in self.products:
                self.products_tree.insert('', 'end', values=(
                    product.get('name', ''),
                    product.get('barcode', ''),
                    f"{product.get('price', 0):.2f}",
                    product.get('stock', 0)
                ))
        except Exception as e:
            print(f"خطأ في عرض المنتجات: {e}")

    def update_customers_display(self):
        """Update customers display"""
        try:
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            for customer in self.customers:
                self.customers_tree.insert('', 'end', values=(
                    customer.get('name', ''),
                    customer.get('phone', ''),
                    customer.get('type', 'تجزئة')
                ))
        except Exception as e:
            print(f"خطأ في عرض العملاء: {e}")

    # Reports functions
    def show_sales_report(self):
        """Show sales report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "📊 تقرير المبيعات\\n"
            report += "=" * 40 + "\\n\\n"

            if not self.sales:
                report += "لا توجد مبيعات.\\n"
            else:
                total_sales = 0
                for i, sale in enumerate(self.sales, 1):
                    report += f"فاتورة {i}:\\n"
                    report += f"العميل: {sale.get('customer_name', '')}\\n"
                    report += f"النوع: {sale.get('customer_type', '')}\\n"
                    report += f"المبلغ: {sale.get('total', 0):.2f}\\n"
                    report += f"التاريخ: {sale.get('date', '')[:10]}\\n"
                    report += "-" * 30 + "\\n"
                    total_sales += sale.get('total', 0)

                report += f"\\nالإجمالي: {total_sales:.2f}\\n"
                report += f"عدد الفواتير: {len(self.sales)}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التقرير: {e}")

    def show_inventory_report(self):
        """Show inventory report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "📦 تقرير المخزون\\n"
            report += "=" * 40 + "\\n\\n"

            if not self.products:
                report += "لا توجد منتجات.\\n"
            else:
                total_value = 0
                for product in self.products:
                    value = product.get('price', 0) * product.get('stock', 0)
                    total_value += value

                    report += f"المنتج: {product.get('name', '')}\\n"
                    report += f"السعر: {product.get('price', 0):.2f}\\n"
                    report += f"الكمية: {product.get('stock', 0)}\\n"
                    report += f"القيمة: {value:.2f}\\n"
                    report += "-" * 30 + "\\n"

                report += f"\\nإجمالي القيمة: {total_value:.2f}\\n"
                report += f"عدد المنتجات: {len(self.products)}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التقرير: {e}")

    def show_customers_report(self):
        """Show customers report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "👥 تقرير العملاء\\n"
            report += "=" * 40 + "\\n\\n"

            if not self.customers:
                report += "لا يوجد عملاء.\\n"
            else:
                for customer in self.customers:
                    report += f"العميل: {customer.get('name', '')}\\n"
                    report += f"الهاتف: {customer.get('phone', '')}\\n"
                    report += f"النوع: {customer.get('type', '')}\\n"
                    report += "-" * 30 + "\\n"

                report += f"\\nعدد العملاء: {len(self.customers)}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التقرير: {e}")

    # Data management with auto-save
    def load_data(self):
        """Load data with optimization"""
        try:
            self.loading = True

            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])

                self.update_suppliers_display()
                self.update_products_display()
                self.update_customers_display()

                self.status_label.config(text="تم تحميل البيانات")
                print(f"📥 تحميل: {len(self.products)} منتج، {len(self.customers)} عميل")
            else:
                self.status_label.config(text="بدء جديد")

        except Exception as e:
            print(f"❌ خطأ في التحميل: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")
        finally:
            self.loading = False

    def save_data(self):
        """Save data with optimization"""
        try:
            if self.loading:
                return False

            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat()
            }

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            self.last_save_time = time.time()
            self.status_label.config(text="تم الحفظ")
            print("💾 تم حفظ البيانات")

            return True

        except Exception as e:
            print(f"❌ خطأ في الحفظ: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {e}")
            return False

    def start_auto_save(self):
        """Start auto-save timer"""
        try:
            if self.auto_save_enabled:
                current_time = time.time()
                if current_time - self.last_save_time >= self.auto_save_interval:
                    self.save_data()

                # Schedule next auto-save
                self.root.after(self.auto_save_interval * 1000, self.start_auto_save)

        except Exception as e:
            print(f"خطأ في الحفظ التلقائي: {e}")

    def trigger_auto_save(self):
        """Trigger immediate auto-save"""
        try:
            if self.auto_save_enabled:
                self.save_data()
        except Exception as e:
            print(f"خطأ في الحفظ الفوري: {e}")

    def run(self):
        """Run the optimized application"""
        try:
            print("🚀 تشغيل ProTech محسن الأداء...")

            # Setup close handler
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # Bring to front
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after_idle(lambda: self.root.attributes('-topmost', False))

            # Start main loop
            self.root.mainloop()

        except Exception as e:
            print(f"❌ خطأ في التشغيل: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج: {e}")

    def on_closing(self):
        """Handle closing with auto-save"""
        try:
            if self.save_data():
                print("👋 إغلاق ProTech...")
                self.root.destroy()
            else:
                if messagebox.askyesno("تأكيد", "فشل الحفظ. إغلاق بدون حفظ؟"):
                    self.root.destroy()
        except Exception as e:
            print(f"خطأ عند الإغلاق: {e}")
            self.root.destroy()

def main():
    """Main function"""
    try:
        print("🧮 ProTech محسن الأداء العملي")
        print("=" * 50)

        app = ProTechOptimized()
        app.run()

    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''

    try:
        # Create backup
        create_backup()

        # Write optimized code
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(optimized_code)

        print("✅ تم إنشاء ProTech المحسن بالكامل")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return False

def apply_additional_optimizations():
    """Apply additional performance optimizations"""

    additional_optimizations = '''
# Additional Performance Optimizations
# تحسينات أداء إضافية

def optimize_database_operations():
    """Optimize database operations"""
    # Add indexing for faster searches
    # إضافة فهرسة للبحث السريع
    pass

def optimize_ui_rendering():
    """Optimize UI rendering"""
    # Reduce UI updates frequency
    # تقليل تكرار تحديث الواجهة
    pass

def optimize_memory_usage():
    """Optimize memory usage"""
    # Clean up unused objects
    # تنظيف الكائنات غير المستخدمة
    pass
'''

    try:
        with open('protech_simple_working.py', 'a', encoding='utf-8') as f:
            f.write(additional_optimizations)

        print("✅ تم تطبيق التحسينات الإضافية")
        return True

    except Exception as e:
        print(f"❌ خطأ في التحسينات الإضافية: {e}")
        return False

def main():
    """Main optimization function"""
    print("🚀 تحسين الأداء العملي لـ ProTech")
    print("🚀 ProTech Operational Performance Optimization")
    print("=" * 60)
    
    try:
        if create_optimized_protech():
            print("✅ تم تحسين الأداء العملي بنجاح!")
        else:
            print("❌ فشل في التحسين")
            
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    main()
