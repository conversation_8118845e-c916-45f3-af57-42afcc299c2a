#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess

def run_protech_desktop():
    """Run ProTech from Desktop location"""
    desktop_path = r"c:\Users\<USER>\OneDrive\Desktop\accounting program"
    protech_file = os.path.join(desktop_path, "protech_simple_working.py")
    
    print("🚀 تشغيل ProTech من Desktop...")
    print("🚀 Running ProTech from Desktop...")
    
    if os.path.exists(protech_file):
        print(f"✅ تم العثور على الملف: {protech_file}")
        print(f"✅ File found: {protech_file}")
        
        # Change to desktop directory
        os.chdir(desktop_path)
        print(f"📁 تم تغيير المجلد إلى: {desktop_path}")
        print(f"📁 Changed directory to: {desktop_path}")
        
        # Run the program
        try:
            subprocess.run([sys.executable, "protech_simple_working.py"], check=True)
        except subprocess.CalledProcessError as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            print(f"❌ Error running program: {e}")
        except Exception as e:
            print(f"❌ خطأ عام: {e}")
            print(f"❌ General error: {e}")
    else:
        print(f"❌ لم يتم العثور على الملف: {protech_file}")
        print(f"❌ File not found: {protech_file}")

if __name__ == "__main__":
    run_protech_desktop()
