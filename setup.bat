@echo off
echo ========================================
echo ProTech Accounting System Setup
echo ========================================
echo.

echo Checking Node.js installation...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Node.js is not installed or not in PATH.
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo Node.js found!
node --version

echo.
echo Installing dependencies...
npm install

if %errorlevel% neq 0 (
    echo.
    echo Failed to install dependencies. Trying with legacy peer deps...
    npm install --legacy-peer-deps
)

echo.
echo Setting up environment variables...
if not exist .env (
    copy .env.example .env
    echo Environment file created. Please update .env with your database credentials.
) else (
    echo Environment file already exists.
)

echo.
echo Setup completed!
echo.
echo Next steps:
echo 1. Update .env file with your PostgreSQL database credentials
echo 2. Run: npm run db:push (to create database tables)
echo 3. Run: npm run dev (to start the development server)
echo.
pause
