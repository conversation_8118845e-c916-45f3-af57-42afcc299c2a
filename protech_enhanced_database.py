#!/usr/bin/env python3
"""
ProTech Enhanced Database System - تطوير قاعدة البيانات المتقدمة
نظام ProTech مع قاعدة بيانات محسنة وتخزين دائم

Enhanced version of ProTech with advanced SQLite database integration
نسخة محسنة من ProTech مع تكامل قاعدة بيانات SQLite متقدمة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sqlite3
from datetime import datetime, timedelta
import threading
import time
from functools import lru_cache
import gc
import psutil
import sys
import traceback
import logging
import hashlib
import gzip
import shutil
from typing import Dict, List, Any, Optional

class ProTechEnhancedDatabase:
    """Enhanced ProTech with advanced SQLite database integration"""

    def __init__(self):
        print("🚀 تشغيل نظام ProTech المحسن مع قاعدة البيانات المتقدمة...")
        print("🚀 Starting ProTech Enhanced Database System...")

        # Initialize timing
        self.startup_start_time = time.time()
        
        # Performance optimization flags
        self.loading = True
        self.cache_enabled = True
        self.auto_save_enabled = True
        self.last_save_time = time.time()

        # Enhanced performance monitoring
        self.performance_stats = {
            'startup_time': time.time(),
            'memory_usage': [],
            'operation_times': {},
            'error_count': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'button_clicks': {},
            'button_response_times': {},
            'ui_response_times': [],
            'data_load_times': [],
            'search_times': [],
            'database_operations': 0,
            'memory_optimizations': 0,
            'cache_cleanups': 0,
            'sql_queries': 0,
            'backup_operations': 0
        }

        # Advanced database settings
        self.db_path = "protech_enhanced.db"
        self.json_backup_path = "protech_simple_data.json"
        self.backup_dir = "enhanced_backups"
        self.archive_dir = "enhanced_archives"
        
        # Memory optimization settings
        self.memory_optimization_enabled = True
        self.auto_cleanup_interval = 180  # 3 minutes
        self.last_cleanup_time = time.time()
        self.max_memory_usage = 400  # MB

        # Advanced caching settings
        self.query_cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.max_cache_size = 1000

        # Database connection pool
        self.db_lock = threading.RLock()
        self.connection_pool = []
        self.max_connections = 5

        # Setup enhanced logging
        self.setup_enhanced_logging()

        # Initialize advanced database
        self.init_enhanced_database()

        # Initialize caches
        self.init_advanced_caches()

        # Setup button optimization
        self.setup_button_optimization()

        # Initialize main window
        self.root = tk.Tk()
        self.root.title("نظام ProTech المحسن مع قاعدة البيانات - ProTech Enhanced Database System")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f8ff')
        self.root.state('zoomed')

        # Enhanced UI optimizations
        self.root.option_add('*tearOff', False)
        self.root.option_add('*highlightThickness', 0)
        self.root.option_add('*borderWidth', 1)
        self.root.resizable(True, True)

        # Setup window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Setup keyboard shortcuts
        self.setup_keyboard_shortcuts()

        # Create enhanced interface
        self.create_enhanced_interface()

        # Load data from database
        self.load_data_from_database()

        # Start background tasks
        self.start_enhanced_background_tasks()

        self.loading = False

        # Record startup time
        startup_duration = time.time() - self.startup_start_time
        self.performance_stats['startup_time'] = startup_duration

        print(f"✅ تم تحميل النظام المحسن بنجاح في {startup_duration:.2f} ثانية!")
        print(f"✅ Enhanced system loaded successfully in {startup_duration:.2f} seconds!")

        # Start performance monitoring
        self.start_performance_monitoring()

    def setup_enhanced_logging(self):
        """Setup enhanced logging system"""
        try:
            # Create logs directory
            if not os.path.exists('logs'):
                os.makedirs('logs')
            
            # Setup multiple loggers
            log_format = '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
            
            # Main logger
            self.logger = logging.getLogger('ProTechEnhanced')
            self.logger.setLevel(logging.INFO)
            
            # File handlers
            main_handler = logging.FileHandler('logs/protech_enhanced.log', encoding='utf-8')
            main_handler.setLevel(logging.INFO)
            main_handler.setFormatter(logging.Formatter(log_format))
            
            error_handler = logging.FileHandler('logs/protech_errors.log', encoding='utf-8')
            error_handler.setLevel(logging.ERROR)
            error_handler.setFormatter(logging.Formatter(log_format))
            
            db_handler = logging.FileHandler('logs/database_operations.log', encoding='utf-8')
            db_handler.setLevel(logging.INFO)
            db_handler.setFormatter(logging.Formatter(log_format))
            
            # Console handler
            console_handler = logging.StreamHandler()
            console_handler.setLevel(logging.WARNING)
            console_handler.setFormatter(logging.Formatter('%(levelname)s - %(message)s'))
            
            # Add handlers
            self.logger.addHandler(main_handler)
            self.logger.addHandler(error_handler)
            self.logger.addHandler(db_handler)
            self.logger.addHandler(console_handler)
            
            # Database logger
            self.db_logger = logging.getLogger('ProTechEnhanced.Database')
            self.db_logger.setLevel(logging.INFO)
            self.db_logger.addHandler(db_handler)
            
            print("✅ تم تهيئة نظام السجلات المحسن")
            
        except Exception as e:
            print(f"خطأ في تهيئة نظام السجلات: {e}")

    def init_enhanced_database(self):
        """Initialize enhanced SQLite database"""
        try:
            # Create backup directories
            for directory in [self.backup_dir, self.archive_dir, 'logs', 'temp']:
                if not os.path.exists(directory):
                    os.makedirs(directory)
            
            # Initialize database connection
            with self.db_lock:
                conn = sqlite3.connect(self.db_path, timeout=30.0, check_same_thread=False)
                
                # Enhanced SQLite optimizations
                conn.execute("PRAGMA journal_mode=WAL")  # Write-Ahead Logging
                conn.execute("PRAGMA synchronous=NORMAL")  # Balance safety and speed
                conn.execute("PRAGMA cache_size=20000")  # 20MB cache
                conn.execute("PRAGMA temp_store=MEMORY")  # Memory temp storage
                conn.execute("PRAGMA mmap_size=268435456")  # 256MB memory map
                conn.execute("PRAGMA optimize")  # Auto-optimize
                
                cursor = conn.cursor()
                
                # Create enhanced tables
                self.create_enhanced_tables(cursor)
                self.create_enhanced_indexes(cursor)
                self.create_enhanced_triggers(cursor)
                self.create_enhanced_views(cursor)
                
                conn.commit()
                conn.close()
                
                print("✅ تم تهيئة قاعدة البيانات المحسنة بنجاح")
                self.logger.info("Enhanced database initialized successfully")
                
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            self.logger.error(f"Database initialization error: {e}")
            raise e

    def create_enhanced_tables(self, cursor):
        """Create enhanced database tables with comprehensive schema"""
        
        # Enhanced suppliers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                name TEXT NOT NULL,
                name_ar TEXT,
                phone TEXT,
                email TEXT,
                address TEXT,
                city TEXT,
                country TEXT DEFAULT 'Saudi Arabia',
                contact_person TEXT,
                contact_phone TEXT,
                category TEXT,
                rating INTEGER DEFAULT 5 CHECK(rating >= 1 AND rating <= 5),
                payment_terms TEXT DEFAULT '30 يوم',
                tax_number TEXT,
                commercial_register TEXT,
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                currency TEXT DEFAULT 'SAR',
                bank_account TEXT,
                bank_name TEXT,
                is_active BOOLEAN DEFAULT 1,
                notes TEXT,
                tags TEXT,
                website TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                last_order_date TIMESTAMP,
                total_orders INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                last_payment_date TIMESTAMP,
                discount_rate REAL DEFAULT 0
            )
        """)
        
        # Enhanced products table with comprehensive fields
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                barcode TEXT UNIQUE NOT NULL,
                internal_code TEXT,
                name TEXT NOT NULL,
                name_ar TEXT,
                description TEXT,
                description_ar TEXT,
                category TEXT NOT NULL,
                subcategory TEXT,
                brand TEXT,
                model TEXT,
                supplier_id INTEGER,
                manufacturer TEXT,
                country_of_origin TEXT,
                
                -- Pricing structure
                base_price REAL NOT NULL DEFAULT 0,
                cost_price REAL DEFAULT 0,
                retail_price REAL,
                wholesale_price REAL,
                distributor_price REAL,
                shop_owner_price REAL,
                vip_price REAL,
                
                -- Pricing margins (percentages)
                retail_margin REAL DEFAULT 30,
                wholesale_margin REAL DEFAULT 20,
                distributor_margin REAL DEFAULT 15,
                shop_owner_margin REAL DEFAULT 5,
                
                -- Inventory management
                stock INTEGER DEFAULT 0,
                min_stock INTEGER DEFAULT 0,
                max_stock INTEGER DEFAULT 1000,
                reorder_point INTEGER DEFAULT 10,
                reorder_quantity INTEGER DEFAULT 50,
                
                -- Physical properties
                unit TEXT DEFAULT 'قطعة',
                unit_weight REAL,
                unit_volume REAL,
                unit_dimensions TEXT,
                package_size INTEGER DEFAULT 1,
                
                -- Location and tracking
                location TEXT,
                shelf_location TEXT,
                warehouse_section TEXT,
                
                -- Dates and expiry
                expiry_date DATE,
                manufacture_date DATE,
                batch_number TEXT,
                serial_number TEXT,
                
                -- Status and flags
                is_active BOOLEAN DEFAULT 1,
                is_featured BOOLEAN DEFAULT 0,
                is_on_sale BOOLEAN DEFAULT 0,
                requires_prescription BOOLEAN DEFAULT 0,
                is_fragile BOOLEAN DEFAULT 0,
                is_perishable BOOLEAN DEFAULT 0,
                
                -- Financial
                tax_rate REAL DEFAULT 0.15,
                discount_rate REAL DEFAULT 0,
                commission_rate REAL DEFAULT 0,
                
                -- Media and documentation
                image_path TEXT,
                image_url TEXT,
                datasheet_path TEXT,
                manual_path TEXT,
                
                -- Additional information
                notes TEXT,
                tags TEXT,
                keywords TEXT,
                
                -- Audit fields
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                updated_by TEXT,
                
                -- Sales tracking
                last_sold_date TIMESTAMP,
                total_sold INTEGER DEFAULT 0,
                total_revenue REAL DEFAULT 0,
                last_purchase_date TIMESTAMP,
                last_purchase_price REAL,
                
                -- Quality and compliance
                quality_grade TEXT,
                certification TEXT,
                compliance_notes TEXT,
                
                FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
            )
        """)
        
        # Enhanced customers table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                name TEXT NOT NULL,
                name_ar TEXT,
                email TEXT,
                phone TEXT,
                mobile TEXT,
                whatsapp TEXT,
                address TEXT,
                address_ar TEXT,
                city TEXT,
                state TEXT,
                postal_code TEXT,
                country TEXT DEFAULT 'Saudi Arabia',
                
                -- Customer classification
                customer_type TEXT CHECK(customer_type IN ('RETAIL', 'WHOLESALE', 'SHOP_OWNER', 'AUTHORIZED_DISTRIBUTOR', 'VIP', 'CORPORATE')) DEFAULT 'RETAIL',
                customer_category TEXT,
                customer_segment TEXT,
                
                -- Financial information
                credit_limit REAL DEFAULT 0,
                current_balance REAL DEFAULT 0,
                payment_terms TEXT DEFAULT 'نقدي',
                currency TEXT DEFAULT 'SAR',
                
                -- Tax and legal
                tax_number TEXT,
                commercial_register TEXT,
                
                -- Pricing and discounts
                discount_rate REAL DEFAULT 0,
                special_pricing BOOLEAN DEFAULT 0,
                price_list TEXT,
                
                -- Contact preferences
                preferred_contact_method TEXT DEFAULT 'phone',
                language_preference TEXT DEFAULT 'ar',
                
                -- Status and flags
                is_active BOOLEAN DEFAULT 1,
                is_vip BOOLEAN DEFAULT 0,
                is_blacklisted BOOLEAN DEFAULT 0,
                email_verified BOOLEAN DEFAULT 0,
                phone_verified BOOLEAN DEFAULT 0,
                
                -- Additional information
                notes TEXT,
                tags TEXT,
                source TEXT,
                referral_source TEXT,
                
                -- Audit fields
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                created_by TEXT,
                updated_by TEXT,
                
                -- Purchase history
                last_purchase_date TIMESTAMP,
                total_purchases INTEGER DEFAULT 0,
                total_amount REAL DEFAULT 0,
                average_order_value REAL DEFAULT 0,
                
                -- Loyalty program
                loyalty_points INTEGER DEFAULT 0,
                loyalty_tier TEXT DEFAULT 'Bronze',
                loyalty_expiry_date DATE,
                
                -- Communication history
                last_contact_date TIMESTAMP,
                last_email_date TIMESTAMP,
                last_sms_date TIMESTAMP,
                
                -- Geographic and demographic
                birth_date DATE,
                gender TEXT,
                occupation TEXT,
                company_name TEXT,
                company_position TEXT
            )
        """)

        print("✅ تم إنشاء الجداول المحسنة")

    def create_enhanced_indexes(self, cursor):
        """Create comprehensive indexes for optimal performance"""
        indexes = [
            # Suppliers indexes
            "CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(code)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_name ON suppliers(name)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_category ON suppliers(category)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_active ON suppliers(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_rating ON suppliers(rating)",
            
            # Products indexes - comprehensive coverage
            "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)",
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
            "CREATE INDEX IF NOT EXISTS idx_products_supplier ON products(supplier_id)",
            "CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock)",
            "CREATE INDEX IF NOT EXISTS idx_products_price ON products(base_price)",
            "CREATE INDEX IF NOT EXISTS idx_products_featured ON products(is_featured)",
            "CREATE INDEX IF NOT EXISTS idx_products_sale ON products(is_on_sale)",
            "CREATE INDEX IF NOT EXISTS idx_products_brand ON products(brand)",
            "CREATE INDEX IF NOT EXISTS idx_products_expiry ON products(expiry_date)",
            
            # Composite indexes for common queries
            "CREATE INDEX IF NOT EXISTS idx_products_category_active ON products(category, is_active)",
            "CREATE INDEX IF NOT EXISTS idx_products_supplier_active ON products(supplier_id, is_active)",
            "CREATE INDEX IF NOT EXISTS idx_products_stock_min ON products(stock, min_stock)",
            "CREATE INDEX IF NOT EXISTS idx_products_price_category ON products(base_price, category)",
            
            # Customers indexes
            "CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(code)",
            "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)",
            "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",
            "CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)",
            "CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(customer_type)",
            "CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active)",
            "CREATE INDEX IF NOT EXISTS idx_customers_vip ON customers(is_vip)",
            "CREATE INDEX IF NOT EXISTS idx_customers_city ON customers(city)",
            "CREATE INDEX IF NOT EXISTS idx_customers_loyalty ON customers(loyalty_tier)",
            
            # Full-text search indexes
            "CREATE INDEX IF NOT EXISTS idx_products_search ON products(name, name_ar, description, keywords)",
            "CREATE INDEX IF NOT EXISTS idx_customers_search ON customers(name, name_ar, email, phone)",
            "CREATE INDEX IF NOT EXISTS idx_suppliers_search ON suppliers(name, name_ar, contact_person)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except Exception as e:
                self.logger.warning(f"Index creation warning: {e}")
        
        print("✅ تم إنشاء الفهارس المحسنة")

    def create_enhanced_triggers(self, cursor):
        """Create advanced database triggers"""
        
        # Auto-update timestamps
        timestamp_triggers = [
            """
            CREATE TRIGGER IF NOT EXISTS update_suppliers_timestamp 
            AFTER UPDATE ON suppliers
            BEGIN
                UPDATE suppliers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
            """,
            """
            CREATE TRIGGER IF NOT EXISTS update_products_timestamp 
            AFTER UPDATE ON products
            BEGIN
                UPDATE products SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
            """,
            """
            CREATE TRIGGER IF NOT EXISTS update_customers_timestamp 
            AFTER UPDATE ON customers
            BEGIN
                UPDATE customers SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
            END
            """
        ]
        
        # Auto-calculate pricing based on margins
        pricing_trigger = """
            CREATE TRIGGER IF NOT EXISTS auto_calculate_prices
            AFTER UPDATE OF base_price, retail_margin, wholesale_margin, distributor_margin, shop_owner_margin ON products
            BEGIN
                UPDATE products SET 
                    retail_price = NEW.base_price * (1 + NEW.retail_margin / 100),
                    wholesale_price = NEW.base_price * (1 + NEW.wholesale_margin / 100),
                    distributor_price = NEW.base_price * (1 + NEW.distributor_margin / 100),
                    shop_owner_price = NEW.base_price * (1 + NEW.shop_owner_margin / 100)
                WHERE id = NEW.id;
            END
        """
        
        # Stock validation trigger
        stock_validation_trigger = """
            CREATE TRIGGER IF NOT EXISTS validate_stock_levels
            BEFORE UPDATE OF stock ON products
            BEGIN
                SELECT CASE
                    WHEN NEW.stock < 0 THEN
                        RAISE(ABORT, 'Stock cannot be negative')
                    WHEN NEW.stock < NEW.min_stock THEN
                        -- Log low stock warning (would need external logging)
                        1
                END;
            END
        """
        
        all_triggers = timestamp_triggers + [pricing_trigger, stock_validation_trigger]
        
        for trigger_sql in all_triggers:
            try:
                cursor.execute(trigger_sql)
            except Exception as e:
                self.logger.warning(f"Trigger creation warning: {e}")
        
        print("✅ تم إنشاء المشغلات المحسنة")

    def create_enhanced_views(self, cursor):
        """Create useful database views for reporting"""
        
        views = [
            # Low stock products view
            """
            CREATE VIEW IF NOT EXISTS low_stock_products AS
            SELECT 
                p.*,
                s.name as supplier_name,
                (p.min_stock - p.stock) as shortage_quantity,
                (p.min_stock - p.stock) * p.base_price as shortage_value
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.stock <= p.min_stock AND p.is_active = 1
            """,
            
            # Product summary view
            """
            CREATE VIEW IF NOT EXISTS product_summary AS
            SELECT 
                p.*,
                s.name as supplier_name,
                s.phone as supplier_phone,
                CASE 
                    WHEN p.stock <= 0 THEN 'Out of Stock'
                    WHEN p.stock <= p.min_stock THEN 'Low Stock'
                    WHEN p.stock >= p.max_stock THEN 'Overstock'
                    ELSE 'Normal'
                END as stock_status,
                (p.stock * p.base_price) as inventory_value
            FROM products p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            """,
            
            # Customer summary view
            """
            CREATE VIEW IF NOT EXISTS customer_summary AS
            SELECT 
                c.*,
                CASE 
                    WHEN c.current_balance > 0 THEN 'Credit'
                    WHEN c.current_balance < 0 THEN 'Debt'
                    ELSE 'Balanced'
                END as balance_status,
                CASE 
                    WHEN c.is_vip = 1 THEN 'VIP'
                    WHEN c.total_amount > 10000 THEN 'High Value'
                    WHEN c.total_amount > 5000 THEN 'Medium Value'
                    ELSE 'Regular'
                END as customer_value_segment
            FROM customers c
            """
        ]
        
        for view_sql in views:
            try:
                cursor.execute(view_sql)
            except Exception as e:
                self.logger.warning(f"View creation warning: {e}")
        
        print("✅ تم إنشاء العروض المحسنة")

    def init_advanced_caches(self):
        """Initialize advanced caching system"""
        self._product_cache = {}
        self._customer_cache = {}
        self._supplier_cache = {}
        self._search_cache = {}
        self._query_cache = {}
        self._report_cache = {}
        
        # Cache metadata
        self._cache_metadata = {
            'product': {'hits': 0, 'misses': 0, 'last_cleanup': time.time()},
            'customer': {'hits': 0, 'misses': 0, 'last_cleanup': time.time()},
            'supplier': {'hits': 0, 'misses': 0, 'last_cleanup': time.time()},
            'search': {'hits': 0, 'misses': 0, 'last_cleanup': time.time()},
            'query': {'hits': 0, 'misses': 0, 'last_cleanup': time.time()},
            'report': {'hits': 0, 'misses': 0, 'last_cleanup': time.time()}
        }
        
        print("✅ تم تهيئة نظام التخزين المؤقت المتقدم")

    def setup_button_optimization(self):
        """Setup enhanced button optimization"""
        self.button_styles = {
            'primary': {
                'bg': '#059669', 'fg': 'white', 'hover_bg': '#047857', 'active_bg': '#065f46',
                'font': ('Arial', 11, 'bold'), 'relief': 'raised', 'bd': 2, 'cursor': 'hand2'
            },
            'secondary': {
                'bg': '#3b82f6', 'fg': 'white', 'hover_bg': '#2563eb', 'active_bg': '#1d4ed8',
                'font': ('Arial', 11, 'bold'), 'relief': 'raised', 'bd': 2, 'cursor': 'hand2'
            },
            'success': {
                'bg': '#10b981', 'fg': 'white', 'hover_bg': '#059669', 'active_bg': '#047857',
                'font': ('Arial', 11, 'bold'), 'relief': 'raised', 'bd': 2, 'cursor': 'hand2'
            },
            'warning': {
                'bg': '#f59e0b', 'fg': 'white', 'hover_bg': '#d97706', 'active_bg': '#b45309',
                'font': ('Arial', 11, 'bold'), 'relief': 'raised', 'bd': 2, 'cursor': 'hand2'
            },
            'danger': {
                'bg': '#ef4444', 'fg': 'white', 'hover_bg': '#dc2626', 'active_bg': '#b91c1c',
                'font': ('Arial', 11, 'bold'), 'relief': 'raised', 'bd': 2, 'cursor': 'hand2'
            }
        }
        
        self.button_cache = {}
        print("✅ تم تهيئة تحسين الأزرار")

    def execute_query(self, query: str, params: tuple = (), fetch_one: bool = False, fetch_all: bool = True) -> Any:
        """Execute database query with advanced caching and error handling"""
        try:
            self.performance_stats['sql_queries'] += 1
            start_time = time.time()

            # Check cache for SELECT queries
            if query.strip().upper().startswith('SELECT'):
                cache_key = hashlib.md5(f"{query}{params}".encode()).hexdigest()
                if cache_key in self._query_cache:
                    cache_entry = self._query_cache[cache_key]
                    if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                        self.performance_stats['cache_hits'] += 1
                        self._cache_metadata['query']['hits'] += 1
                        return cache_entry['result']

                self.performance_stats['cache_misses'] += 1
                self._cache_metadata['query']['misses'] += 1

            with self.db_lock:
                conn = sqlite3.connect(self.db_path, timeout=30.0)
                conn.row_factory = sqlite3.Row  # Enable column access by name
                cursor = conn.cursor()

                cursor.execute(query, params)

                if fetch_one:
                    result = cursor.fetchone()
                elif fetch_all and query.strip().upper().startswith('SELECT'):
                    result = cursor.fetchall()
                else:
                    result = cursor.rowcount

                conn.commit()
                conn.close()

                # Cache SELECT results
                if query.strip().upper().startswith('SELECT') and (fetch_one or fetch_all):
                    cache_key = hashlib.md5(f"{query}{params}".encode()).hexdigest()
                    self._query_cache[cache_key] = {
                        'result': result,
                        'timestamp': time.time()
                    }

                    # Limit cache size
                    if len(self._query_cache) > self.max_cache_size:
                        self.cleanup_query_cache()

                # Log slow queries
                execution_time = time.time() - start_time
                if execution_time > 0.5:  # Queries taking more than 0.5 seconds
                    self.logger.warning(f"Slow query ({execution_time:.2f}s): {query[:100]}...")

                return result

        except Exception as e:
            self.logger.error(f"Query execution error: {e}\nQuery: {query}\nParams: {params}")
            raise e

    def cleanup_query_cache(self):
        """Clean up old query cache entries"""
        try:
            current_time = time.time()
            expired_keys = []

            for key, entry in self._query_cache.items():
                if current_time - entry['timestamp'] > self.cache_ttl:
                    expired_keys.append(key)

            for key in expired_keys:
                del self._query_cache[key]

            # If still too large, remove oldest entries
            if len(self._query_cache) > self.max_cache_size:
                sorted_items = sorted(self._query_cache.items(), key=lambda x: x[1]['timestamp'])
                for key, _ in sorted_items[:len(self._query_cache) - self.max_cache_size]:
                    del self._query_cache[key]

            self.performance_stats['cache_cleanups'] += 1

        except Exception as e:
            self.logger.error(f"Cache cleanup error: {e}")

    def load_data_from_database(self):
        """Load data from enhanced database"""
        try:
            print("📥 تحميل البيانات من قاعدة البيانات المحسنة...")
            start_time = time.time()

            # Load suppliers
            self.suppliers = self.get_all_suppliers()

            # Load products with enhanced data
            self.products = self.get_all_products()

            # Load customers
            self.customers = self.get_all_customers()

            # Load sales (if sales table exists)
            try:
                self.sales = self.get_all_sales()
            except:
                self.sales = []

            load_time = time.time() - start_time
            self.performance_stats['data_load_times'].append(load_time)

            print(f"✅ تم تحميل {len(self.products)} منتج، {len(self.customers)} عميل، {len(self.suppliers)} مورد في {load_time:.2f} ثانية")
            self.logger.info(f"Data loaded: {len(self.products)} products, {len(self.customers)} customers, {len(self.suppliers)} suppliers")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            self.logger.error(f"Data loading error: {e}")
            # Initialize empty data if loading fails
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []

    def get_all_suppliers(self) -> List[Dict]:
        """Get all suppliers from database"""
        try:
            result = self.execute_query("SELECT * FROM suppliers ORDER BY name")
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.logger.error(f"Get suppliers error: {e}")
            return []

    def get_all_products(self) -> List[Dict]:
        """Get all products from database with supplier information"""
        try:
            query = """
                SELECT p.*, s.name as supplier_name, s.phone as supplier_phone
                FROM products p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                ORDER BY p.name
            """
            result = self.execute_query(query)
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.logger.error(f"Get products error: {e}")
            return []

    def get_all_customers(self) -> List[Dict]:
        """Get all customers from database"""
        try:
            result = self.execute_query("SELECT * FROM customers ORDER BY name")
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.logger.error(f"Get customers error: {e}")
            return []

    def get_all_sales(self) -> List[Dict]:
        """Get all sales from database (if table exists)"""
        try:
            # Check if sales table exists
            check_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='sales'"
            table_exists = self.execute_query(check_query, fetch_one=True)

            if not table_exists:
                return []

            query = """
                SELECT s.*, c.name as customer_name
                FROM sales s
                LEFT JOIN customers c ON s.customer_id = c.id
                ORDER BY s.created_at DESC
            """
            result = self.execute_query(query)
            return [dict(row) for row in result] if result else []
        except Exception as e:
            self.logger.error(f"Get sales error: {e}")
            return []

    def search_products(self, search_term: str, limit: int = 100) -> List[Dict]:
        """Advanced product search with caching"""
        try:
            # Check cache first
            cache_key = f"search_products_{search_term}_{limit}"
            if cache_key in self._search_cache:
                cache_entry = self._search_cache[cache_key]
                if time.time() - cache_entry['timestamp'] < self.cache_ttl:
                    self._cache_metadata['search']['hits'] += 1
                    return cache_entry['result']

            self._cache_metadata['search']['misses'] += 1

            query = """
                SELECT p.*, s.name as supplier_name
                FROM products p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE (p.name LIKE ? OR p.name_ar LIKE ? OR p.barcode LIKE ?
                       OR p.category LIKE ? OR p.brand LIKE ? OR p.keywords LIKE ?)
                AND p.is_active = 1
                ORDER BY
                    CASE
                        WHEN p.barcode = ? THEN 1
                        WHEN p.name LIKE ? THEN 2
                        WHEN p.name_ar LIKE ? THEN 3
                        ELSE 4
                    END,
                    p.name
                LIMIT ?
            """

            search_pattern = f"%{search_term}%"
            exact_pattern = f"{search_term}%"
            params = (search_pattern, search_pattern, search_pattern, search_pattern,
                     search_pattern, search_pattern, search_term, exact_pattern, exact_pattern, limit)

            result = self.execute_query(query, params)
            products = [dict(row) for row in result] if result else []

            # Cache the result
            self._search_cache[cache_key] = {
                'result': products,
                'timestamp': time.time()
            }

            return products

        except Exception as e:
            self.logger.error(f"Search products error: {e}")
            return []

    def get_low_stock_products(self, threshold: int = None) -> List[Dict]:
        """Get products with low stock using database view"""
        try:
            if threshold is None:
                query = "SELECT * FROM low_stock_products ORDER BY shortage_quantity DESC"
                params = ()
            else:
                query = """
                    SELECT p.*, s.name as supplier_name,
                           (? - p.stock) as shortage_quantity,
                           (? - p.stock) * p.base_price as shortage_value
                    FROM products p
                    LEFT JOIN suppliers s ON p.supplier_id = s.id
                    WHERE p.stock <= ? AND p.is_active = 1
                    ORDER BY p.stock
                """
                params = (threshold, threshold, threshold)

            result = self.execute_query(query, params)
            return [dict(row) for row in result] if result else []

        except Exception as e:
            self.logger.error(f"Get low stock products error: {e}")
            return []

    def insert_product(self, product_data: Dict) -> int:
        """Insert new product with comprehensive data"""
        try:
            query = """
                INSERT INTO products (
                    barcode, internal_code, name, name_ar, description, description_ar,
                    category, subcategory, brand, model, supplier_id, manufacturer,
                    base_price, cost_price, retail_margin, wholesale_margin,
                    distributor_margin, shop_owner_margin, stock, min_stock, max_stock,
                    unit, unit_weight, location, is_active, notes, tags, keywords,
                    created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                product_data.get('barcode'),
                product_data.get('internal_code'),
                product_data.get('name'),
                product_data.get('name_ar'),
                product_data.get('description'),
                product_data.get('description_ar'),
                product_data.get('category'),
                product_data.get('subcategory'),
                product_data.get('brand'),
                product_data.get('model'),
                product_data.get('supplier_id'),
                product_data.get('manufacturer'),
                product_data.get('price', product_data.get('base_price', 0)),
                product_data.get('cost', product_data.get('cost_price', 0)),
                product_data.get('retail_margin', 30),
                product_data.get('wholesale_margin', 20),
                product_data.get('distributor_margin', 15),
                product_data.get('shop_owner_margin', 5),
                product_data.get('stock', 0),
                product_data.get('min_stock', 0),
                product_data.get('max_stock', 1000),
                product_data.get('unit', 'قطعة'),
                product_data.get('unit_weight'),
                product_data.get('location'),
                product_data.get('active', True),
                product_data.get('notes'),
                product_data.get('tags'),
                product_data.get('keywords'),
                'system'  # created_by
            )

            result = self.execute_query(query, params, fetch_all=False)
            self.logger.info(f"Product inserted: {product_data.get('name')}")

            # Clear relevant caches
            self.clear_product_caches()

            return result

        except Exception as e:
            self.logger.error(f"Insert product error: {e}")
            raise e

    def insert_customer(self, customer_data: Dict) -> int:
        """Insert new customer with comprehensive data"""
        try:
            query = """
                INSERT INTO customers (
                    code, name, name_ar, email, phone, mobile, address, city,
                    customer_type, credit_limit, current_balance, payment_terms,
                    discount_rate, is_active, notes, tags, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                customer_data.get('code'),
                customer_data.get('name'),
                customer_data.get('name_ar'),
                customer_data.get('email'),
                customer_data.get('phone'),
                customer_data.get('mobile'),
                customer_data.get('address'),
                customer_data.get('city'),
                customer_data.get('type', customer_data.get('customer_type', 'RETAIL')),
                customer_data.get('credit_limit', 0),
                customer_data.get('balance', customer_data.get('current_balance', 0)),
                customer_data.get('payment_terms', 'نقدي'),
                customer_data.get('discount_rate', 0),
                customer_data.get('active', True),
                customer_data.get('notes'),
                customer_data.get('tags'),
                'system'  # created_by
            )

            result = self.execute_query(query, params, fetch_all=False)
            self.logger.info(f"Customer inserted: {customer_data.get('name')}")

            # Clear relevant caches
            self.clear_customer_caches()

            return result

        except Exception as e:
            self.logger.error(f"Insert customer error: {e}")
            raise e

    def insert_supplier(self, supplier_data: Dict) -> int:
        """Insert new supplier with comprehensive data"""
        try:
            query = """
                INSERT INTO suppliers (
                    code, name, name_ar, phone, email, address, city,
                    contact_person, category, rating, payment_terms,
                    credit_limit, is_active, notes, created_by
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            params = (
                supplier_data.get('code'),
                supplier_data.get('name'),
                supplier_data.get('name_ar'),
                supplier_data.get('phone'),
                supplier_data.get('email'),
                supplier_data.get('address'),
                supplier_data.get('city'),
                supplier_data.get('contact_person'),
                supplier_data.get('category'),
                supplier_data.get('rating', 5),
                supplier_data.get('payment_terms', '30 يوم'),
                supplier_data.get('credit_limit', 0),
                supplier_data.get('active', True),
                supplier_data.get('notes'),
                'system'  # created_by
            )

            result = self.execute_query(query, params, fetch_all=False)
            self.logger.info(f"Supplier inserted: {supplier_data.get('name')}")

            # Clear relevant caches
            self.clear_supplier_caches()

            return result

        except Exception as e:
            self.logger.error(f"Insert supplier error: {e}")
            raise e

    def clear_product_caches(self):
        """Clear product-related caches"""
        self._product_cache.clear()
        self._search_cache.clear()
        # Clear query cache entries related to products
        keys_to_remove = [k for k in self._query_cache.keys() if 'products' in str(k)]
        for key in keys_to_remove:
            del self._query_cache[key]

    def clear_customer_caches(self):
        """Clear customer-related caches"""
        self._customer_cache.clear()
        # Clear query cache entries related to customers
        keys_to_remove = [k for k in self._query_cache.keys() if 'customers' in str(k)]
        for key in keys_to_remove:
            del self._query_cache[key]

    def clear_supplier_caches(self):
        """Clear supplier-related caches"""
        self._supplier_cache.clear()
        # Clear query cache entries related to suppliers
        keys_to_remove = [k for k in self._query_cache.keys() if 'suppliers' in str(k)]
        for key in keys_to_remove:
            del self._query_cache[key]

    def create_enhanced_interface(self):
        """Create enhanced interface with database features"""
        # Header with database status
        header_frame = tk.Frame(self.root, bg='#1e40af', height=100)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Title with database indicator
        title_label = tk.Label(
            header_frame,
            text="🗄️ نظام ProTech المحسن مع قاعدة البيانات المتقدمة",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#1e40af'
        )
        title_label.pack(side='left', padx=20, pady=20)

        # Database status indicator
        self.db_status_label = tk.Label(
            header_frame,
            text="🟢 قاعدة البيانات: متصلة ومحسنة",
            font=('Arial', 12, 'bold'),
            fg='#10b981',
            bg='#1e40af'
        )
        self.db_status_label.pack(side='right', padx=20, pady=20)

        # Main container
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Enhanced navigation panel
        nav_frame = tk.Frame(main_frame, bg='#3b82f6', width=280)
        nav_frame.pack(side='left', fill='y', padx=(0, 10))
        nav_frame.pack_propagate(False)

        # Navigation title
        nav_title = tk.Label(
            nav_frame,
            text="📋 القوائم الرئيسية المحسنة\nEnhanced Main Menus",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='#3b82f6'
        )
        nav_title.pack(pady=20)

        # Enhanced navigation buttons with database features
        nav_buttons = [
            ("🏠 لوحة التحكم المحسنة\nEnhanced Dashboard", self.show_enhanced_dashboard, 'primary'),
            ("📦 إدارة المخزون المتقدمة\nAdvanced Inventory", self.show_enhanced_inventory, 'secondary'),
            ("👥 إدارة العملاء\nCustomer Management", self.show_enhanced_customers, 'secondary'),
            ("🏢 إدارة الموردين\nSupplier Management", self.show_enhanced_suppliers, 'secondary'),
            ("💰 إدارة المبيعات\nSales Management", self.show_enhanced_sales, 'warning'),
            ("📊 التقارير المتقدمة\nAdvanced Reports", self.show_enhanced_reports, 'info'),
            ("🗄️ إدارة قاعدة البيانات\nDatabase Management", self.show_database_management, 'success'),
            ("⚙️ الإعدادات المحسنة\nEnhanced Settings", self.show_enhanced_settings, 'neutral')
        ]

        for text, command, style in nav_buttons:
            btn = self.create_enhanced_button(
                nav_frame, text, command, style=style, width=22, height=3
            )
            btn.pack(pady=8, padx=15, fill='x')

        # Content area with enhanced styling
        self.content_frame = tk.Frame(main_frame, bg='white', relief='ridge', bd=2)
        self.content_frame.pack(side='right', fill='both', expand=True)

        # Enhanced status bar with database statistics
        status_frame = tk.Frame(self.root, bg='#374151', height=40)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # Status information
        self.status_label = tk.Label(
            status_frame,
            text="جاهز - قاعدة البيانات محسنة / Ready - Enhanced Database",
            font=('Arial', 10, 'bold'),
            fg='white',
            bg='#374151'
        )
        self.status_label.pack(side='left', padx=15, pady=8)

        # Performance stats
        self.perf_label = tk.Label(
            status_frame,
            text="الأداء: ممتاز | Performance: Excellent",
            font=('Arial', 10),
            fg='#10b981',
            bg='#374151'
        )
        self.perf_label.pack(side='right', padx=15, pady=8)

        # Show enhanced dashboard by default
        self.show_enhanced_dashboard()

    def create_enhanced_button(self, parent, text, command, style='primary', width=None, height=None, **kwargs):
        """Create enhanced button with advanced styling"""
        try:
            style_config = self.button_styles.get(style, self.button_styles['primary'])

            button_config = {
                'text': text,
                'command': command,
                'font': style_config['font'],
                'bg': style_config['bg'],
                'fg': style_config['fg'],
                'relief': style_config['relief'],
                'bd': style_config['bd'],
                'cursor': style_config['cursor'],
                'activebackground': style_config['active_bg'],
                'activeforeground': 'white'
            }

            if width:
                button_config['width'] = width
            if height:
                button_config['height'] = height

            button_config.update(kwargs)
            button = tk.Button(parent, **button_config)

            # Add hover effects
            def on_enter(event):
                button.config(bg=style_config['hover_bg'])

            def on_leave(event):
                button.config(bg=style_config['bg'])

            button.bind('<Enter>', on_enter)
            button.bind('<Leave>', on_leave)

            return button

        except Exception as e:
            self.logger.error(f"Enhanced button creation error: {e}")
            return tk.Button(parent, text=text, command=command)

    def clear_content(self):
        """Clear content area"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_enhanced_dashboard(self):
        """Show enhanced dashboard with comprehensive database statistics"""
        self.clear_content()

        # Title
        title_label = tk.Label(
            self.content_frame,
            text="📊 لوحة التحكم المحسنة مع إحصائيات قاعدة البيانات\nEnhanced Dashboard with Database Statistics",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Create scrollable frame for dashboard content
        canvas = tk.Canvas(self.content_frame, bg='white')
        scrollbar = ttk.Scrollbar(self.content_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='white')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Main statistics cards
        stats_frame = tk.Frame(scrollable_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)

        # Get database statistics
        try:
            total_products = len(self.products)
            total_customers = len(self.customers)
            total_suppliers = len(self.suppliers)
            total_sales = len(self.sales)

            # Enhanced stat cards with better styling
            stat_cards = [
                ("📦", "المنتجات\nProducts", total_products, "#3b82f6", "منتج نشط"),
                ("👥", "العملاء\nCustomers", total_customers, "#10b981", "عميل مسجل"),
                ("🏢", "الموردين\nSuppliers", total_suppliers, "#06b6d4", "مورد معتمد"),
                ("🧾", "المبيعات\nSales", total_sales, "#f59e0b", "فاتورة مبيعات")
            ]

            for i, (icon, title, value, color, subtitle) in enumerate(stat_cards):
                card_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
                card_frame.pack(side='left', fill='both', expand=True, padx=8, pady=5)

                # Icon
                icon_label = tk.Label(card_frame, text=icon, font=('Arial', 32), fg='white', bg=color)
                icon_label.pack(pady=(15, 5))

                # Title
                title_label = tk.Label(card_frame, text=title, font=('Arial', 12, 'bold'), fg='white', bg=color)
                title_label.pack()

                # Value
                value_label = tk.Label(card_frame, text=str(value), font=('Arial', 24, 'bold'), fg='white', bg=color)
                value_label.pack(pady=5)

                # Subtitle
                subtitle_label = tk.Label(card_frame, text=subtitle, font=('Arial', 9), fg='white', bg=color)
                subtitle_label.pack(pady=(0, 15))

        except Exception as e:
            error_label = tk.Label(
                scrollable_frame,
                text=f"خطأ في تحميل الإحصائيات: {str(e)}",
                font=('Arial', 12),
                fg='red',
                bg='white'
            )
            error_label.pack(pady=20)

        # Database performance section
        perf_frame = tk.LabelFrame(
            scrollable_frame,
            text="📈 إحصائيات الأداء المحسنة / Enhanced Performance Statistics",
            font=('Arial', 14, 'bold'),
            bg='white',
            fg='#1f2937'
        )
        perf_frame.pack(fill='x', padx=20, pady=20)

        try:
            # Performance metrics
            cache_hit_rate = 0
            if self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'] > 0:
                cache_hit_rate = (self.performance_stats['cache_hits'] /
                                (self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'])) * 100

            perf_metrics = [
                f"🔍 إجمالي الاستعلامات / Total Queries: {self.performance_stats['sql_queries']}",
                f"⚡ معدل نجاح الذاكرة المؤقتة / Cache Hit Rate: {cache_hit_rate:.1f}%",
                f"🧹 عمليات تنظيف الذاكرة / Cache Cleanups: {self.performance_stats['cache_cleanups']}",
                f"💾 عمليات قاعدة البيانات / Database Operations: {self.performance_stats['database_operations']}",
                f"⏱️ وقت بدء التشغيل / Startup Time: {self.performance_stats['startup_time']:.2f}s",
                f"🔄 تحسينات الذاكرة / Memory Optimizations: {self.performance_stats['memory_optimizations']}"
            ]

            for metric in perf_metrics:
                metric_label = tk.Label(perf_frame, text=metric, font=('Arial', 11), bg='white', fg='#374151')
                metric_label.pack(anchor='w', padx=15, pady=3)

        except Exception as e:
            error_label = tk.Label(
                perf_frame,
                text=f"خطأ في تحميل إحصائيات الأداء: {str(e)}",
                font=('Arial', 11),
                fg='red',
                bg='white'
            )
            error_label.pack(pady=10)

        # Quick actions section
        actions_frame = tk.LabelFrame(
            scrollable_frame,
            text="⚡ الإجراءات السريعة / Quick Actions",
            font=('Arial', 14, 'bold'),
            bg='white',
            fg='#1f2937'
        )
        actions_frame.pack(fill='x', padx=20, pady=20)

        # Quick action buttons
        quick_actions = [
            ("🔍 البحث السريع", self.quick_search, 'primary'),
            ("📦 إضافة منتج", self.quick_add_product, 'success'),
            ("👤 إضافة عميل", self.quick_add_customer, 'secondary'),
            ("💾 نسخة احتياطية", self.create_backup, 'warning'),
            ("📊 تقرير سريع", self.quick_report, 'info')
        ]

        actions_button_frame = tk.Frame(actions_frame, bg='white')
        actions_button_frame.pack(fill='x', padx=10, pady=10)

        for text, command, style in quick_actions:
            btn = self.create_enhanced_button(
                actions_button_frame, text, command, style=style, width=15, height=2
            )
            btn.pack(side='left', padx=5, pady=5)

        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Update status
        self.status_label.config(text="لوحة التحكم المحسنة - جاهزة / Enhanced Dashboard - Ready")

    def show_enhanced_inventory(self):
        """Show enhanced inventory management"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="📦 إدارة المخزون المتقدمة مع قاعدة البيانات\nAdvanced Inventory with Database",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Enhanced search and filter frame
        search_frame = tk.Frame(self.content_frame, bg='white')
        search_frame.pack(fill='x', padx=20, pady=10)

        # Search controls
        tk.Label(search_frame, text="🔍 البحث المتقدم:", font=('Arial', 12, 'bold'), bg='white').pack(side='left', padx=5)

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, font=('Arial', 12), width=30)
        search_entry.pack(side='left', padx=5)
        search_entry.bind('<KeyRelease>', self.on_enhanced_search)

        # Filter controls
        tk.Label(search_frame, text="فئة:", font=('Arial', 10), bg='white').pack(side='left', padx=(20, 5))
        self.category_filter = ttk.Combobox(search_frame, width=15, state='readonly')
        self.category_filter.pack(side='left', padx=5)
        self.category_filter.bind('<<ComboboxSelected>>', self.on_category_filter)

        # Action buttons
        btn_frame = tk.Frame(search_frame, bg='white')
        btn_frame.pack(side='right', padx=10)

        refresh_btn = self.create_enhanced_button(
            btn_frame, "🔄 تحديث", self.refresh_enhanced_inventory, 'primary', width=12
        )
        refresh_btn.pack(side='left', padx=2)

        add_btn = self.create_enhanced_button(
            btn_frame, "➕ إضافة منتج", self.add_enhanced_product, 'success', width=12
        )
        add_btn.pack(side='left', padx=2)

        # Enhanced products table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Define enhanced columns
        columns = ('ID', 'Barcode', 'Name', 'Category', 'Brand', 'Supplier', 'Stock', 'Min Stock', 'Price', 'Status')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # Configure enhanced columns
        column_configs = {
            'ID': {'width': 50, 'anchor': 'center'},
            'Barcode': {'width': 120, 'anchor': 'center'},
            'Name': {'width': 200, 'anchor': 'w'},
            'Category': {'width': 120, 'anchor': 'center'},
            'Brand': {'width': 100, 'anchor': 'center'},
            'Supplier': {'width': 150, 'anchor': 'w'},
            'Stock': {'width': 80, 'anchor': 'center'},
            'Min Stock': {'width': 80, 'anchor': 'center'},
            'Price': {'width': 100, 'anchor': 'e'},
            'Status': {'width': 100, 'anchor': 'center'}
        }

        for col in columns:
            self.products_tree.heading(col, text=col)
            config = column_configs.get(col, {'width': 100, 'anchor': 'center'})
            self.products_tree.column(col, width=config['width'], anchor=config['anchor'])

        # Enhanced scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.products_tree.xview)
        self.products_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack table and scrollbars
        self.products_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Load enhanced inventory data
        self.refresh_enhanced_inventory()
        self.populate_category_filter()

        # Update status
        self.status_label.config(text="إدارة المخزون المتقدمة - جاهزة / Advanced Inventory - Ready")

    def on_enhanced_search(self, event=None):
        """Handle enhanced product search with database"""
        try:
            search_term = self.search_var.get().strip()
            start_time = time.time()

            if search_term:
                # Use database search with advanced features
                products = self.search_products(search_term, limit=500)
                self.performance_stats['search_times'].append(time.time() - start_time)
            else:
                # Show all products
                products = self.products

            # Apply category filter if selected
            category = self.category_filter.get()
            if category and category != "جميع الفئات":
                products = [p for p in products if p.get('category') == category]

            # Update tree
            self.update_enhanced_products_tree(products)

            # Update status
            self.status_label.config(text=f"تم العثور على {len(products)} منتج / Found {len(products)} products")

        except Exception as e:
            self.logger.error(f"Enhanced search error: {e}")
            messagebox.showerror("خطأ في البحث", f"حدث خطأ أثناء البحث:\n{str(e)}")

    def on_category_filter(self, event=None):
        """Handle category filter change"""
        self.on_enhanced_search()

    def refresh_enhanced_inventory(self):
        """Refresh inventory from enhanced database"""
        try:
            start_time = time.time()

            # Reload products from database
            self.products = self.get_all_products()

            # Update tree
            self.update_enhanced_products_tree(self.products)

            # Update performance stats
            load_time = time.time() - start_time
            self.performance_stats['data_load_times'].append(load_time)

            # Update status
            self.status_label.config(text=f"تم تحديث {len(self.products)} منتج في {load_time:.2f}s")

            self.logger.info(f"Inventory refreshed: {len(self.products)} products in {load_time:.2f}s")

        except Exception as e:
            self.logger.error(f"Refresh inventory error: {e}")
            messagebox.showerror("خطأ", f"فشل في تحديث المخزون:\n{str(e)}")

    def populate_category_filter(self):
        """Populate category filter with available categories"""
        try:
            # Get unique categories from products
            categories = set()
            for product in self.products:
                if product.get('category'):
                    categories.add(product['category'])

            # Sort categories
            sorted_categories = sorted(list(categories))

            # Add "All Categories" option
            filter_options = ["جميع الفئات"] + sorted_categories

            # Update combobox
            self.category_filter['values'] = filter_options
            self.category_filter.set("جميع الفئات")

        except Exception as e:
            self.logger.error(f"Populate category filter error: {e}")

    def update_enhanced_products_tree(self, products):
        """Update products tree with enhanced data"""
        try:
            # Clear existing items
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # Add products with enhanced information
            for product in products:
                stock = product.get('stock', 0)
                min_stock = product.get('min_stock', 0)

                # Determine status with enhanced logic
                if stock <= 0:
                    status = "❌ نفد"
                elif stock <= min_stock:
                    status = "⚠️ منخفض"
                elif stock >= product.get('max_stock', 1000):
                    status = "📈 مرتفع"
                else:
                    status = "✅ جيد"

                # Format price with currency
                price = product.get('base_price', 0)
                formatted_price = f"{price:.2f} ر.س"

                values = (
                    product.get('id', ''),
                    product.get('barcode', ''),
                    product.get('name', ''),
                    product.get('category', ''),
                    product.get('brand', ''),
                    product.get('supplier_name', ''),
                    stock,
                    min_stock,
                    formatted_price,
                    status
                )

                # Insert item with color coding
                item_id = self.products_tree.insert('', 'end', values=values)

                # Color coding based on stock status
                if stock <= 0:
                    self.products_tree.set(item_id, 'Status', '❌ نفد')
                elif stock <= min_stock:
                    self.products_tree.set(item_id, 'Status', '⚠️ منخفض')

        except Exception as e:
            self.logger.error(f"Update products tree error: {e}")

    def add_enhanced_product(self):
        """Add new product with enhanced form"""
        try:
            # Create enhanced product dialog
            dialog = tk.Toplevel(self.root)
            dialog.title("إضافة منتج جديد - Add New Product")
            dialog.geometry("800x700")
            dialog.configure(bg='white')
            dialog.transient(self.root)
            dialog.grab_set()

            # Center the dialog
            dialog.update_idletasks()
            x = (dialog.winfo_screenwidth() // 2) - (800 // 2)
            y = (dialog.winfo_screenheight() // 2) - (700 // 2)
            dialog.geometry(f"800x700+{x}+{y}")

            # Create scrollable form
            canvas = tk.Canvas(dialog, bg='white')
            scrollbar = ttk.Scrollbar(dialog, orient="vertical", command=canvas.yview)
            scrollable_frame = tk.Frame(canvas, bg='white')

            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )

            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)

            # Form title
            title_label = tk.Label(
                scrollable_frame,
                text="📦 إضافة منتج جديد - Add New Product",
                font=('Arial', 16, 'bold'),
                fg='#1f2937',
                bg='white'
            )
            title_label.pack(pady=20)

            # Form fields
            fields = {}

            # Basic Information Section
            basic_frame = tk.LabelFrame(scrollable_frame, text="المعلومات الأساسية / Basic Information",
                                      font=('Arial', 12, 'bold'), bg='white')
            basic_frame.pack(fill='x', padx=20, pady=10)

            basic_fields = [
                ('barcode', 'الباركود / Barcode *', True),
                ('name', 'اسم المنتج / Product Name *', True),
                ('name_ar', 'الاسم بالعربية / Arabic Name', False),
                ('category', 'الفئة / Category *', True),
                ('brand', 'العلامة التجارية / Brand', False),
                ('description', 'الوصف / Description', False)
            ]

            for field_name, label_text, required in basic_fields:
                field_frame = tk.Frame(basic_frame, bg='white')
                field_frame.pack(fill='x', padx=10, pady=5)

                label = tk.Label(field_frame, text=label_text, font=('Arial', 10, 'bold'),
                               bg='white', width=25, anchor='w')
                label.pack(side='left')

                if field_name == 'description':
                    entry = tk.Text(field_frame, height=3, width=40, font=('Arial', 10))
                else:
                    entry = tk.Entry(field_frame, font=('Arial', 10), width=40)
                entry.pack(side='left', padx=10)
                fields[field_name] = entry

            # Pricing Section
            pricing_frame = tk.LabelFrame(scrollable_frame, text="معلومات التسعير / Pricing Information",
                                        font=('Arial', 12, 'bold'), bg='white')
            pricing_frame.pack(fill='x', padx=20, pady=10)

            pricing_fields = [
                ('base_price', 'السعر الأساسي / Base Price *', True),
                ('cost_price', 'سعر التكلفة / Cost Price', False),
                ('retail_margin', 'هامش التجزئة % / Retail Margin %', False),
                ('wholesale_margin', 'هامش الجملة % / Wholesale Margin %', False)
            ]

            for field_name, label_text, required in pricing_fields:
                field_frame = tk.Frame(pricing_frame, bg='white')
                field_frame.pack(fill='x', padx=10, pady=5)

                label = tk.Label(field_frame, text=label_text, font=('Arial', 10, 'bold'),
                               bg='white', width=25, anchor='w')
                label.pack(side='left')

                entry = tk.Entry(field_frame, font=('Arial', 10), width=40)
                entry.pack(side='left', padx=10)
                fields[field_name] = entry

            # Inventory Section
            inventory_frame = tk.LabelFrame(scrollable_frame, text="معلومات المخزون / Inventory Information",
                                          font=('Arial', 12, 'bold'), bg='white')
            inventory_frame.pack(fill='x', padx=20, pady=10)

            inventory_fields = [
                ('stock', 'الكمية الحالية / Current Stock', False),
                ('min_stock', 'الحد الأدنى / Minimum Stock', False),
                ('max_stock', 'الحد الأقصى / Maximum Stock', False),
                ('unit', 'الوحدة / Unit', False)
            ]

            for field_name, label_text, required in inventory_fields:
                field_frame = tk.Frame(inventory_frame, bg='white')
                field_frame.pack(fill='x', padx=10, pady=5)

                label = tk.Label(field_frame, text=label_text, font=('Arial', 10, 'bold'),
                               bg='white', width=25, anchor='w')
                label.pack(side='left')

                entry = tk.Entry(field_frame, font=('Arial', 10), width=40)
                entry.pack(side='left', padx=10)
                fields[field_name] = entry

            # Supplier Selection
            supplier_frame = tk.LabelFrame(scrollable_frame, text="معلومات المورد / Supplier Information",
                                         font=('Arial', 12, 'bold'), bg='white')
            supplier_frame.pack(fill='x', padx=20, pady=10)

            supplier_field_frame = tk.Frame(supplier_frame, bg='white')
            supplier_field_frame.pack(fill='x', padx=10, pady=5)

            supplier_label = tk.Label(supplier_field_frame, text="المورد / Supplier",
                                    font=('Arial', 10, 'bold'), bg='white', width=25, anchor='w')
            supplier_label.pack(side='left')

            supplier_combo = ttk.Combobox(supplier_field_frame, width=37, state='readonly')
            supplier_combo.pack(side='left', padx=10)

            # Populate suppliers
            supplier_options = [""] + [f"{s.get('name', '')} - {s.get('id', '')}" for s in self.suppliers]
            supplier_combo['values'] = supplier_options
            fields['supplier'] = supplier_combo

            # Buttons frame
            buttons_frame = tk.Frame(scrollable_frame, bg='white')
            buttons_frame.pack(fill='x', padx=20, pady=20)

            def save_enhanced_product():
                try:
                    # Validate required fields
                    required_fields = ['barcode', 'name', 'category', 'base_price']
                    for field in required_fields:
                        value = fields[field].get() if hasattr(fields[field], 'get') else fields[field].get('1.0', 'end-1c')
                        if not value.strip():
                            messagebox.showerror("خطأ", f"الحقل {field} مطلوب")
                            return

                    # Prepare product data
                    product_data = {}

                    # Get basic fields
                    for field_name in ['barcode', 'name', 'name_ar', 'category', 'brand']:
                        if field_name in fields:
                            product_data[field_name] = fields[field_name].get().strip()

                    # Get description
                    if 'description' in fields:
                        product_data['description'] = fields['description'].get('1.0', 'end-1c').strip()

                    # Get numeric fields
                    numeric_fields = ['base_price', 'cost_price', 'retail_margin', 'wholesale_margin',
                                    'stock', 'min_stock', 'max_stock']
                    for field_name in numeric_fields:
                        if field_name in fields:
                            value = fields[field_name].get().strip()
                            if value:
                                try:
                                    product_data[field_name] = float(value)
                                except ValueError:
                                    messagebox.showerror("خطأ", f"قيمة غير صحيحة في حقل {field_name}")
                                    return

                    # Get unit
                    product_data['unit'] = fields.get('unit', tk.StringVar()).get().strip() or 'قطعة'

                    # Get supplier ID
                    supplier_selection = fields['supplier'].get()
                    if supplier_selection:
                        try:
                            supplier_id = supplier_selection.split(' - ')[-1]
                            product_data['supplier_id'] = int(supplier_id)
                        except:
                            pass

                    # Set default values
                    product_data.setdefault('stock', 0)
                    product_data.setdefault('min_stock', 0)
                    product_data.setdefault('max_stock', 1000)
                    product_data.setdefault('retail_margin', 30)
                    product_data.setdefault('wholesale_margin', 20)

                    # Insert product into database
                    product_id = self.insert_product(product_data)

                    # Refresh inventory
                    self.refresh_enhanced_inventory()

                    # Close dialog
                    dialog.destroy()

                    # Show success message
                    messagebox.showinfo("نجح", f"تم إضافة المنتج بنجاح\nProduct ID: {product_id}")

                except Exception as e:
                    self.logger.error(f"Save enhanced product error: {e}")
                    messagebox.showerror("خطأ", f"فشل في حفظ المنتج:\n{str(e)}")

            # Save and Cancel buttons
            save_btn = self.create_enhanced_button(
                buttons_frame, "💾 حفظ المنتج", save_enhanced_product, 'success', width=15
            )
            save_btn.pack(side='left', padx=10)

            cancel_btn = self.create_enhanced_button(
                buttons_frame, "❌ إلغاء", dialog.destroy, 'danger', width=15
            )
            cancel_btn.pack(side='left', padx=10)

            # Pack canvas and scrollbar
            canvas.pack(side="left", fill="both", expand=True)
            scrollbar.pack(side="right", fill="y")

        except Exception as e:
            self.logger.error(f"Add enhanced product error: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح نموذج إضافة المنتج:\n{str(e)}")

    def show_enhanced_customers(self):
        """Show enhanced customer management"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء المحسنة / Enhanced Customer Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Customer management interface will be implemented here
        placeholder_label = tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير - إدارة العملاء المحسنة\n🚧 Under Development - Enhanced Customer Management",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        )
        placeholder_label.pack(pady=50)

    def show_enhanced_suppliers(self):
        """Show enhanced supplier management"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="🏢 إدارة الموردين المحسنة / Enhanced Supplier Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Supplier management interface will be implemented here
        placeholder_label = tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير - إدارة الموردين المحسنة\n🚧 Under Development - Enhanced Supplier Management",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        )
        placeholder_label.pack(pady=50)

    def show_enhanced_sales(self):
        """Show enhanced sales management"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات المحسنة / Enhanced Sales Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Sales management interface will be implemented here
        placeholder_label = tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير - إدارة المبيعات المحسنة\n🚧 Under Development - Enhanced Sales Management",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        )
        placeholder_label.pack(pady=50)

    def show_enhanced_reports(self):
        """Show enhanced reports"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="📊 التقارير المتقدمة / Advanced Reports",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Reports interface will be implemented here
        placeholder_label = tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير - التقارير المتقدمة\n🚧 Under Development - Advanced Reports",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        )
        placeholder_label.pack(pady=50)

    def show_database_management(self):
        """Show database management interface"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="🗄️ إدارة قاعدة البيانات المتقدمة / Advanced Database Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Database operations frame
        ops_frame = tk.LabelFrame(
            self.content_frame,
            text="عمليات قاعدة البيانات / Database Operations",
            font=('Arial', 14, 'bold'),
            bg='white'
        )
        ops_frame.pack(fill='x', padx=20, pady=10)

        # Database operation buttons
        db_buttons = [
            ("💾 نسخة احتياطية", self.create_enhanced_backup, 'primary'),
            ("📤 تصدير JSON", self.export_to_json, 'secondary'),
            ("📥 استيراد JSON", self.import_from_json, 'warning'),
            ("🔧 تحسين قاعدة البيانات", self.optimize_database, 'success'),
            ("📊 إحصائيات مفصلة", self.show_detailed_database_stats, 'info')
        ]

        buttons_frame = tk.Frame(ops_frame, bg='white')
        buttons_frame.pack(fill='x', padx=10, pady=10)

        for text, command, style in db_buttons:
            btn = self.create_enhanced_button(
                buttons_frame, text, command, style=style, width=18, height=2
            )
            btn.pack(side='left', padx=5, pady=5)

        # Database statistics frame
        stats_frame = tk.LabelFrame(
            self.content_frame,
            text="إحصائيات قاعدة البيانات / Database Statistics",
            font=('Arial', 14, 'bold'),
            bg='white'
        )
        stats_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create text widget for statistics
        self.db_stats_text = tk.Text(stats_frame, font=('Arial', 11), bg='#f8fafc', wrap='word')
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient='vertical', command=self.db_stats_text.yview)
        self.db_stats_text.configure(yscrollcommand=stats_scrollbar.set)

        self.db_stats_text.pack(side='left', fill='both', expand=True, padx=10, pady=10)
        stats_scrollbar.pack(side='right', fill='y', pady=10)

        # Load and display database statistics
        self.refresh_database_stats()

    def show_enhanced_settings(self):
        """Show enhanced settings"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="⚙️ الإعدادات المحسنة / Enhanced Settings",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)

        # Settings interface will be implemented here
        placeholder_label = tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير - الإعدادات المحسنة\n🚧 Under Development - Enhanced Settings",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        )
        placeholder_label.pack(pady=50)

    def create_enhanced_backup(self):
        """Create enhanced database backup"""
        try:
            # Show progress dialog
            progress_dialog = tk.Toplevel(self.root)
            progress_dialog.title("إنشاء نسخة احتياطية")
            progress_dialog.geometry("400x150")
            progress_dialog.configure(bg='white')
            progress_dialog.transient(self.root)
            progress_dialog.grab_set()

            # Center dialog
            progress_dialog.update_idletasks()
            x = (progress_dialog.winfo_screenwidth() // 2) - (200)
            y = (progress_dialog.winfo_screenheight() // 2) - (75)
            progress_dialog.geometry(f"400x150+{x}+{y}")

            tk.Label(progress_dialog, text="جاري إنشاء النسخة الاحتياطية...",
                    font=('Arial', 12), bg='white').pack(pady=20)

            progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill='x')
            progress_bar.start()

            def create_backup_thread():
                try:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_name = f"protech_enhanced_backup_{timestamp}"

                    # Create backup directory if not exists
                    if not os.path.exists(self.backup_dir):
                        os.makedirs(self.backup_dir)

                    # Create database backup
                    backup_path = os.path.join(self.backup_dir, f"{backup_name}.db")
                    shutil.copy2(self.db_path, backup_path)

                    # Create compressed backup
                    compressed_path = f"{backup_path}.gz"
                    with open(backup_path, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    os.remove(backup_path)  # Remove uncompressed version

                    # Export to JSON as well
                    json_backup_path = os.path.join(self.backup_dir, f"{backup_name}.json")
                    self.export_to_json_file(json_backup_path)

                    self.performance_stats['backup_operations'] += 1

                    # Close progress dialog and show success
                    progress_dialog.after(0, lambda: [
                        progress_dialog.destroy(),
                        messagebox.showinfo("نجح", f"تم إنشاء النسخة الاحتياطية بنجاح:\n{compressed_path}")
                    ])

                except Exception as e:
                    progress_dialog.after(0, lambda: [
                        progress_dialog.destroy(),
                        messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")
                    ])

            # Start backup in separate thread
            backup_thread = threading.Thread(target=create_backup_thread, daemon=True)
            backup_thread.start()

        except Exception as e:
            self.logger.error(f"Enhanced backup error: {e}")
            messagebox.showerror("خطأ", f"فشل في بدء عملية النسخ الاحتياطي:\n{str(e)}")

    def export_to_json(self):
        """Export database to JSON file"""
        try:
            # Ask user for file location
            file_path = filedialog.asksaveasfilename(
                title="تصدير إلى JSON",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                self.export_to_json_file(file_path)
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى:\n{file_path}")

        except Exception as e:
            self.logger.error(f"Export to JSON error: {e}")
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات:\n{str(e)}")

    def export_to_json_file(self, file_path):
        """Export database to specific JSON file"""
        try:
            # Prepare data for export
            export_data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'export_timestamp': datetime.now().isoformat(),
                'database_version': '2.0_enhanced',
                'total_records': len(self.suppliers) + len(self.products) + len(self.customers) + len(self.sales)
            }

            # Write to JSON file
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)

            self.logger.info(f"Database exported to JSON: {file_path}")

        except Exception as e:
            self.logger.error(f"Export to JSON file error: {e}")
            raise e

    def import_from_json(self):
        """Import data from JSON file"""
        try:
            # Ask user for file location
            file_path = filedialog.askopenfilename(
                title="استيراد من JSON",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                # Confirm import
                result = messagebox.askyesno(
                    "تأكيد الاستيراد",
                    "هل أنت متأكد من استيراد البيانات؟\nسيتم دمج البيانات الجديدة مع البيانات الحالية."
                )

                if result:
                    self.import_from_json_file(file_path)
                    messagebox.showinfo("نجح", "تم استيراد البيانات بنجاح")

                    # Refresh all data
                    self.load_data_from_database()

                    # Refresh current view if it's inventory
                    if hasattr(self, 'products_tree'):
                        self.refresh_enhanced_inventory()

        except Exception as e:
            self.logger.error(f"Import from JSON error: {e}")
            messagebox.showerror("خطأ", f"فشل في استيراد البيانات:\n{str(e)}")

    def import_from_json_file(self, file_path):
        """Import data from specific JSON file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)

            # Import suppliers
            if 'suppliers' in data:
                for supplier in data['suppliers']:
                    try:
                        self.insert_supplier(supplier)
                    except Exception as e:
                        self.logger.warning(f"Failed to import supplier {supplier.get('name', 'Unknown')}: {e}")

            # Import products
            if 'products' in data:
                for product in data['products']:
                    try:
                        self.insert_product(product)
                    except Exception as e:
                        self.logger.warning(f"Failed to import product {product.get('name', 'Unknown')}: {e}")

            # Import customers
            if 'customers' in data:
                for customer in data['customers']:
                    try:
                        self.insert_customer(customer)
                    except Exception as e:
                        self.logger.warning(f"Failed to import customer {customer.get('name', 'Unknown')}: {e}")

            self.logger.info(f"Data imported from JSON: {file_path}")

        except Exception as e:
            self.logger.error(f"Import from JSON file error: {e}")
            raise e

    def optimize_database(self):
        """Optimize database performance"""
        try:
            # Show progress dialog
            progress_dialog = tk.Toplevel(self.root)
            progress_dialog.title("تحسين قاعدة البيانات")
            progress_dialog.geometry("400x150")
            progress_dialog.configure(bg='white')
            progress_dialog.transient(self.root)
            progress_dialog.grab_set()

            # Center dialog
            progress_dialog.update_idletasks()
            x = (progress_dialog.winfo_screenwidth() // 2) - (200)
            y = (progress_dialog.winfo_screenheight() // 2) - (75)
            progress_dialog.geometry(f"400x150+{x}+{y}")

            tk.Label(progress_dialog, text="جاري تحسين قاعدة البيانات...",
                    font=('Arial', 12), bg='white').pack(pady=20)

            progress_bar = ttk.Progressbar(progress_dialog, mode='indeterminate')
            progress_bar.pack(pady=10, padx=20, fill='x')
            progress_bar.start()

            def optimize_thread():
                try:
                    with self.db_lock:
                        conn = sqlite3.connect(self.db_path)

                        # Run VACUUM to optimize database
                        conn.execute("VACUUM")

                        # Run ANALYZE to update statistics
                        conn.execute("ANALYZE")

                        # Optimize specific tables
                        conn.execute("REINDEX")

                        conn.close()

                    # Clear caches
                    self.cleanup_query_cache()
                    self.clear_product_caches()
                    self.clear_customer_caches()
                    self.clear_supplier_caches()

                    # Force garbage collection
                    gc.collect()

                    self.performance_stats['memory_optimizations'] += 1

                    # Close progress dialog and show success
                    progress_dialog.after(0, lambda: [
                        progress_dialog.destroy(),
                        messagebox.showinfo("نجح", "تم تحسين قاعدة البيانات بنجاح")
                    ])

                except Exception as e:
                    progress_dialog.after(0, lambda: [
                        progress_dialog.destroy(),
                        messagebox.showerror("خطأ", f"فشل في تحسين قاعدة البيانات:\n{str(e)}")
                    ])

            # Start optimization in separate thread
            optimize_thread_obj = threading.Thread(target=optimize_thread, daemon=True)
            optimize_thread_obj.start()

        except Exception as e:
            self.logger.error(f"Database optimization error: {e}")
            messagebox.showerror("خطأ", f"فشل في بدء تحسين قاعدة البيانات:\n{str(e)}")

    def show_detailed_database_stats(self):
        """Show detailed database statistics in a new window"""
        try:
            # Create statistics window
            stats_window = tk.Toplevel(self.root)
            stats_window.title("إحصائيات قاعدة البيانات المفصلة")
            stats_window.geometry("800x600")
            stats_window.configure(bg='white')
            stats_window.transient(self.root)

            # Center window
            stats_window.update_idletasks()
            x = (stats_window.winfo_screenwidth() // 2) - (400)
            y = (stats_window.winfo_screenheight() // 2) - (300)
            stats_window.geometry(f"800x600+{x}+{y}")

            # Create text widget with scrollbar
            text_frame = tk.Frame(stats_window, bg='white')
            text_frame.pack(fill='both', expand=True, padx=20, pady=20)

            text_widget = tk.Text(text_frame, font=('Courier', 10), bg='#f8fafc', wrap='word')
            scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            # Get comprehensive statistics
            stats_text = self.get_comprehensive_database_stats()
            text_widget.insert('1.0', stats_text)
            text_widget.config(state='disabled')

            # Pack widgets
            text_widget.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')

        except Exception as e:
            self.logger.error(f"Show detailed database stats error: {e}")
            messagebox.showerror("خطأ", f"فشل في عرض الإحصائيات:\n{str(e)}")

    def get_comprehensive_database_stats(self):
        """Get comprehensive database statistics"""
        try:
            stats_text = "📊 إحصائيات قاعدة البيانات المفصلة\n"
            stats_text += "=" * 60 + "\n\n"

            # Database file information
            if os.path.exists(self.db_path):
                db_size = os.path.getsize(self.db_path)
                stats_text += f"📁 معلومات الملف:\n"
                stats_text += f"   المسار: {self.db_path}\n"
                stats_text += f"   الحجم: {db_size:,} بايت ({db_size/(1024*1024):.2f} MB)\n"
                stats_text += f"   آخر تعديل: {datetime.fromtimestamp(os.path.getmtime(self.db_path))}\n\n"

            # Table counts
            stats_text += f"📋 عدد السجلات:\n"
            stats_text += f"   المنتجات: {len(self.products):,}\n"
            stats_text += f"   العملاء: {len(self.customers):,}\n"
            stats_text += f"   الموردين: {len(self.suppliers):,}\n"
            stats_text += f"   المبيعات: {len(self.sales):,}\n"
            stats_text += f"   الإجمالي: {len(self.products) + len(self.customers) + len(self.suppliers) + len(self.sales):,}\n\n"

            # Performance statistics
            stats_text += f"⚡ إحصائيات الأداء:\n"
            stats_text += f"   استعلامات SQL: {self.performance_stats['sql_queries']:,}\n"
            stats_text += f"   نجاح الذاكرة المؤقتة: {self.performance_stats['cache_hits']:,}\n"
            stats_text += f"   فشل الذاكرة المؤقتة: {self.performance_stats['cache_misses']:,}\n"

            if self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'] > 0:
                hit_rate = (self.performance_stats['cache_hits'] /
                          (self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'])) * 100
                stats_text += f"   معدل نجاح الذاكرة المؤقتة: {hit_rate:.1f}%\n"

            stats_text += f"   تنظيف الذاكرة المؤقتة: {self.performance_stats['cache_cleanups']:,}\n"
            stats_text += f"   تحسينات الذاكرة: {self.performance_stats['memory_optimizations']:,}\n"
            stats_text += f"   عمليات النسخ الاحتياطي: {self.performance_stats['backup_operations']:,}\n\n"

            # Cache information
            stats_text += f"💾 معلومات الذاكرة المؤقتة:\n"
            stats_text += f"   حجم ذاكرة الاستعلامات: {len(self._query_cache):,}\n"
            stats_text += f"   حجم ذاكرة البحث: {len(self._search_cache):,}\n"
            stats_text += f"   حجم ذاكرة المنتجات: {len(self._product_cache):,}\n"
            stats_text += f"   حجم ذاكرة العملاء: {len(self._customer_cache):,}\n"
            stats_text += f"   حجم ذاكرة الموردين: {len(self._supplier_cache):,}\n\n"

            # System information
            try:
                process = psutil.Process()
                memory_info = process.memory_info()
                stats_text += f"🖥️ معلومات النظام:\n"
                stats_text += f"   استخدام الذاكرة: {memory_info.rss / (1024*1024):.1f} MB\n"
                stats_text += f"   استخدام المعالج: {process.cpu_percent():.1f}%\n"
                stats_text += f"   عدد الخيوط: {process.num_threads()}\n\n"
            except:
                pass

            # Timing information
            if self.performance_stats['data_load_times']:
                avg_load_time = sum(self.performance_stats['data_load_times']) / len(self.performance_stats['data_load_times'])
                stats_text += f"⏱️ أوقات العمليات:\n"
                stats_text += f"   وقت بدء التشغيل: {self.performance_stats['startup_time']:.2f}s\n"
                stats_text += f"   متوسط وقت تحميل البيانات: {avg_load_time:.2f}s\n"
                stats_text += f"   عدد عمليات التحميل: {len(self.performance_stats['data_load_times'])}\n\n"

            stats_text += f"📅 تم إنشاء التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"

            return stats_text

        except Exception as e:
            self.logger.error(f"Get comprehensive database stats error: {e}")
            return f"خطأ في إنشاء الإحصائيات: {str(e)}"

    def refresh_database_stats(self):
        """Refresh database statistics display"""
        try:
            if hasattr(self, 'db_stats_text'):
                stats_text = self.get_comprehensive_database_stats()
                self.db_stats_text.delete('1.0', 'end')
                self.db_stats_text.insert('1.0', stats_text)

        except Exception as e:
            self.logger.error(f"Refresh database stats error: {e}")

    # Quick action methods
    def quick_search(self):
        """Quick search dialog"""
        search_term = tk.simpledialog.askstring("البحث السريع", "أدخل كلمة البحث:")
        if search_term:
            self.show_enhanced_inventory()
            if hasattr(self, 'search_var'):
                self.search_var.set(search_term)
                self.on_enhanced_search()

    def quick_add_product(self):
        """Quick add product"""
        self.show_enhanced_inventory()
        self.add_enhanced_product()

    def quick_add_customer(self):
        """Quick add customer"""
        messagebox.showinfo("قريباً", "إضافة العملاء السريعة قيد التطوير")

    def create_backup(self):
        """Quick backup"""
        self.create_enhanced_backup()

    def quick_report(self):
        """Quick report"""
        self.show_detailed_database_stats()

    def setup_keyboard_shortcuts(self):
        """Setup keyboard shortcuts for enhanced productivity"""
        try:
            # Bind keyboard shortcuts
            self.root.bind('<Control-s>', lambda e: self.create_backup())
            self.root.bind('<Control-f>', lambda e: self.quick_search())
            self.root.bind('<Control-n>', lambda e: self.quick_add_product())
            self.root.bind('<F5>', lambda e: self.refresh_current_view())
            self.root.bind('<Control-q>', lambda e: self.on_closing())

            # Function key shortcuts
            self.root.bind('<F1>', lambda e: self.show_enhanced_dashboard())
            self.root.bind('<F2>', lambda e: self.show_enhanced_inventory())
            self.root.bind('<F3>', lambda e: self.show_enhanced_customers())
            self.root.bind('<F4>', lambda e: self.show_enhanced_suppliers())

        except Exception as e:
            self.logger.error(f"Setup keyboard shortcuts error: {e}")

    def refresh_current_view(self):
        """Refresh current view"""
        try:
            if hasattr(self, 'products_tree'):
                self.refresh_enhanced_inventory()
            elif hasattr(self, 'db_stats_text'):
                self.refresh_database_stats()
        except Exception as e:
            self.logger.error(f"Refresh current view error: {e}")

    def start_enhanced_background_tasks(self):
        """Start enhanced background tasks"""
        try:
            # Auto-save thread
            if self.auto_save_enabled:
                self.auto_save_thread = threading.Thread(target=self.enhanced_auto_save_worker, daemon=True)
                self.auto_save_thread.start()

            # Cache cleanup thread
            if self.cache_enabled:
                self.cache_cleanup_thread = threading.Thread(target=self.enhanced_cache_cleanup_worker, daemon=True)
                self.cache_cleanup_thread.start()

            # Performance monitoring thread
            self.perf_monitor_thread = threading.Thread(target=self.performance_monitor_worker, daemon=True)
            self.perf_monitor_thread.start()

            print("✅ تم تشغيل المهام الخلفية المحسنة")

        except Exception as e:
            self.logger.error(f"Start enhanced background tasks error: {e}")

    def enhanced_auto_save_worker(self):
        """Enhanced auto-save worker"""
        while True:
            try:
                time.sleep(60)  # Auto-save every minute
                if not self.loading and time.time() - self.last_save_time > 60:
                    self.create_enhanced_backup()
                    self.last_save_time = time.time()
            except Exception as e:
                self.logger.error(f"Enhanced auto-save error: {e}")

    def enhanced_cache_cleanup_worker(self):
        """Enhanced cache cleanup worker"""
        while True:
            try:
                time.sleep(self.auto_cleanup_interval)

                # Clean query cache
                self.cleanup_query_cache()

                # Clean search cache
                current_time = time.time()
                expired_keys = []
                for key, entry in self._search_cache.items():
                    if current_time - entry['timestamp'] > self.cache_ttl:
                        expired_keys.append(key)

                for key in expired_keys:
                    del self._search_cache[key]

                # Force garbage collection
                gc.collect()

                self.performance_stats['cache_cleanups'] += 1

            except Exception as e:
                self.logger.error(f"Enhanced cache cleanup error: {e}")

    def performance_monitor_worker(self):
        """Performance monitoring worker"""
        while True:
            try:
                time.sleep(30)  # Monitor every 30 seconds

                # Monitor memory usage
                try:
                    process = psutil.Process()
                    memory_mb = process.memory_info().rss / (1024 * 1024)
                    self.performance_stats['memory_usage'].append(memory_mb)

                    # Keep only last 100 measurements
                    if len(self.performance_stats['memory_usage']) > 100:
                        self.performance_stats['memory_usage'] = self.performance_stats['memory_usage'][-100:]

                    # Check if memory usage is too high
                    if memory_mb > self.max_memory_usage:
                        self.logger.warning(f"High memory usage: {memory_mb:.1f}MB")
                        # Trigger cleanup
                        self.cleanup_query_cache()
                        gc.collect()
                        self.performance_stats['memory_optimizations'] += 1

                except:
                    pass

                # Update performance label if exists
                if hasattr(self, 'perf_label'):
                    try:
                        cache_hit_rate = 0
                        if self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'] > 0:
                            cache_hit_rate = (self.performance_stats['cache_hits'] /
                                            (self.performance_stats['cache_hits'] + self.performance_stats['cache_misses'])) * 100

                        perf_text = f"الأداء: {cache_hit_rate:.1f}% | ذاكرة: {memory_mb:.0f}MB"
                        self.root.after(0, lambda: self.perf_label.config(text=perf_text))
                    except:
                        pass

            except Exception as e:
                self.logger.error(f"Performance monitor error: {e}")

    def start_performance_monitoring(self):
        """Start performance monitoring"""
        try:
            # Log startup completion
            self.logger.info(f"ProTech Enhanced Database started successfully in {self.performance_stats['startup_time']:.2f}s")

            # Update status
            if hasattr(self, 'status_label'):
                self.status_label.config(text=f"جاهز - تم التحميل في {self.performance_stats['startup_time']:.2f}s")

        except Exception as e:
            self.logger.error(f"Start performance monitoring error: {e}")

    def on_closing(self):
        """Handle application closing with enhanced cleanup"""
        try:
            print("🔄 إغلاق النظام المحسن...")

            # Create final backup
            try:
                self.create_enhanced_backup()
            except:
                pass

            # Log final statistics
            self.logger.info("Application closing - Final statistics:")
            self.logger.info(f"SQL Queries: {self.performance_stats['sql_queries']}")
            self.logger.info(f"Cache Hits: {self.performance_stats['cache_hits']}")
            self.logger.info(f"Cache Misses: {self.performance_stats['cache_misses']}")
            self.logger.info(f"Backup Operations: {self.performance_stats['backup_operations']}")

            # Clear all caches
            self.clear_product_caches()
            self.clear_customer_caches()
            self.clear_supplier_caches()

            # Force garbage collection
            gc.collect()

            # Destroy window
            self.root.destroy()

            print("✅ تم إغلاق النظام المحسن بنجاح")

        except Exception as e:
            self.logger.error(f"Application closing error: {e}")
            self.root.destroy()

    def run(self):
        """Run the enhanced application"""
        try:
            print("🚀 تشغيل النظام المحسن...")
            self.root.mainloop()
        except Exception as e:
            self.logger.error(f"Application run error: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{str(e)}")

def main():
    """Main function for enhanced ProTech"""
    print("🖥️ تشغيل نظام ProTech المحسن مع قاعدة البيانات المتقدمة...")
    print("🖥️ Starting ProTech Enhanced with Advanced Database...")

    try:
        app = ProTechEnhancedDatabase()
        app.run()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        import traceback
        traceback.print_exc()
        messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق:\n{str(e)}")

if __name__ == '__main__':
    main()
