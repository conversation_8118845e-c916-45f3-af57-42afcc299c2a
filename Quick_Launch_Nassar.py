#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 Quick Launch - Nassar Accounting Program
تشغيل سريع - برنامج ناصر للمحاسبة

مشغل سريع بدون رسائل ترحيب
Quick launcher without welcome messages
"""

import os
import sys
import subprocess

def quick_launch():
    """Quick launch without dialogs"""
    try:
        # Get the parent directory (nassar program final)
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        
        # Path to main program
        main_program = os.path.join(parent_dir, "01_Main_Program", "protech_simple_working.py")
        data_file = os.path.join(parent_dir, "02_Data_Files", "protech_simple_data.json")
        
        print("🚀 تشغيل سريع لبرنامج ناصر...")
        print("🚀 Quick launching Nassar Program...")
        
        if not os.path.exists(main_program):
            print(f"❌ البرنامج غير موجود: {main_program}")
            print(f"❌ Program not found: {main_program}")
            input("Press Enter to exit...")
            return
        
        # Change to main program directory
        main_program_dir = os.path.dirname(main_program)
        os.chdir(main_program_dir)
        
        # Copy data file if exists
        if os.path.exists(data_file):
            import shutil
            target_data = os.path.join(main_program_dir, "protech_simple_data.json")
            shutil.copy2(data_file, target_data)
        
        # Launch
        subprocess.run([sys.executable, "protech_simple_working.py"])
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        print(f"❌ Error: {e}")
        input("Press Enter to exit...")

if __name__ == "__main__":
    quick_launch()
