#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏢 مشغل نظام ناصر الكامل للمحاسبة
NASSAR COMPLETE SYSTEM LAUNCHER

المشغل الرئيسي الشامل لجميع مكونات نظام ناصر
Main comprehensive launcher for all Nassar system components

تم تطويره بواسطة Augment Agent
Developed by Augment Agent
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox, ttk
from datetime import datetime

class NassarCompleteLauncher:
    def __init__(self):
        self.base_path = os.path.dirname(os.path.abspath(__file__))
        self.setup_paths()
        
    def setup_paths(self):
        """إعداد مسارات النظام"""
        self.paths = {
            'main_programs': os.path.join(self.base_path, "01_Main_Programs"),
            'launchers': os.path.join(self.base_path, "02_Launchers"),
            'data_files': os.path.join(self.base_path, "03_Data_Files"),
            'tools': os.path.join(self.base_path, "04_Tools_Utilities"),
            'backups': os.path.join(self.base_path, "05_Backups"),
            'docs': os.path.join(self.base_path, "06_Documentation")
        }
        
    def create_main_window(self):
        """إنشاء النافذة الرئيسية"""
        self.root = tk.Tk()
        self.root.title("🏢 نظام ناصر الكامل للمحاسبة - Nassar Complete Accounting System")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f8ff')
        
        # محاولة تكبير النافذة
        try:
            self.root.state('zoomed')
        except:
            self.root.attributes('-zoomed', True)
        
        self.create_interface()
        
    def create_interface(self):
        """إنشاء واجهة المستخدم"""
        # العنوان الرئيسي
        title_frame = tk.Frame(self.root, bg='#2c3e50', relief='raised', bd=3)
        title_frame.pack(fill='x', pady=5)
        
        title_label = tk.Label(
            title_frame,
            text="🏢 نظام ناصر الكامل للمحاسبة\nNASSAR COMPLETE ACCOUNTING SYSTEM",
            font=('Arial', 20, 'bold'),
            bg='#2c3e50',
            fg='white',
            justify='center'
        )
        title_label.pack(pady=20)
        
        # معلومات النظام
        info_frame = tk.Frame(self.root, bg='#ecf0f1', relief='sunken', bd=2)
        info_frame.pack(fill='x', padx=10, pady=5)
        
        info_text = f"""
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d')} | 🕐 الوقت: {datetime.now().strftime('%H:%M:%S')}
📁 مسار النظام: {self.base_path}
🔧 المطور: Augment Agent | 📋 الإصدار: 1.0 النهائي
        """
        
        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=('Arial', 10),
            bg='#ecf0f1',
            fg='#2c3e50',
            justify='center'
        )
        info_label.pack(pady=10)
        
        # الحاوية الرئيسية
        main_container = tk.Frame(self.root, bg='#f0f8ff')
        main_container.pack(fill='both', expand=True, padx=10, pady=5)
        
        # قسم البرامج الرئيسية
        self.create_main_programs_section(main_container)
        
        # قسم المشغلات
        self.create_launchers_section(main_container)
        
        # قسم الأدوات
        self.create_tools_section(main_container)
        
        # قسم الوثائق
        self.create_docs_section(main_container)
        
    def create_main_programs_section(self, parent):
        """قسم البرامج الرئيسية"""
        section_frame = tk.LabelFrame(
            parent,
            text="🏢 البرامج الرئيسية / Main Programs",
            font=('Arial', 14, 'bold'),
            bg='#f0f8ff',
            fg='#2c3e50',
            relief='raised',
            bd=2
        )
        section_frame.pack(fill='x', pady=5)
        
        buttons_frame = tk.Frame(section_frame, bg='#f0f8ff')
        buttons_frame.pack(pady=10)
        
        # أزرار البرامج الرئيسية
        programs = [
            ("🏢 برنامج ناصر النهائي\nNassar Final Program", self.launch_nassar_final, '#2ecc71'),
            ("🖥️ مشغل سطح المكتب\nDesktop Launcher", self.launch_desktop, '#3498db'),
            ("📊 لوحة التحكم\nControl Panel", self.show_control_panel, '#9b59b6')
        ]
        
        for i, (text, command, color) in enumerate(programs):
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=15,
                cursor='hand2',
                width=20,
                height=3
            )
            btn.grid(row=0, column=i, padx=10, pady=5)
            
    def create_launchers_section(self, parent):
        """قسم المشغلات"""
        section_frame = tk.LabelFrame(
            parent,
            text="🚀 المشغلات المتخصصة / Specialized Launchers",
            font=('Arial', 14, 'bold'),
            bg='#f0f8ff',
            fg='#2c3e50',
            relief='raised',
            bd=2
        )
        section_frame.pack(fill='x', pady=5)
        
        buttons_frame = tk.Frame(section_frame, bg='#f0f8ff')
        buttons_frame.pack(pady=10)
        
        # أزرار المشغلات
        launchers = [
            ("🔒 المشغل المضمون\nGuaranteed Launcher", self.launch_guaranteed, '#e74c3c'),
            ("🚨 المشغل الطارئ\nEmergency Launcher", self.launch_emergency, '#f39c12'),
            ("⚡ التشغيل السريع\nQuick Launch", self.launch_quick, '#1abc9c')
        ]
        
        for i, (text, command, color) in enumerate(launchers):
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=15,
                cursor='hand2',
                width=20,
                height=3
            )
            btn.grid(row=0, column=i, padx=10, pady=5)
            
    def create_tools_section(self, parent):
        """قسم الأدوات"""
        section_frame = tk.LabelFrame(
            parent,
            text="🛠️ الأدوات المساعدة / Utility Tools",
            font=('Arial', 14, 'bold'),
            bg='#f0f8ff',
            fg='#2c3e50',
            relief='raised',
            bd=2
        )
        section_frame.pack(fill='x', pady=5)
        
        buttons_frame = tk.Frame(section_frame, bg='#f0f8ff')
        buttons_frame.pack(pady=10)
        
        # أزرار الأدوات
        tools = [
            ("💾 أداة النسخ الاحتياطي\nBackup Tool", self.launch_backup_tool, '#34495e'),
            ("🔍 أداة فحص البيانات\nData Checker", self.launch_data_checker, '#8e44ad'),
            ("📁 مدير الملفات\nFile Manager", self.open_file_manager, '#95a5a6')
        ]
        
        for i, (text, command, color) in enumerate(tools):
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=15,
                cursor='hand2',
                width=20,
                height=3
            )
            btn.grid(row=0, column=i, padx=10, pady=5)
            
    def create_docs_section(self, parent):
        """قسم الوثائق"""
        section_frame = tk.LabelFrame(
            parent,
            text="📚 الوثائق والمساعدة / Documentation & Help",
            font=('Arial', 14, 'bold'),
            bg='#f0f8ff',
            fg='#2c3e50',
            relief='raised',
            bd=2
        )
        section_frame.pack(fill='x', pady=5)
        
        buttons_frame = tk.Frame(section_frame, bg='#f0f8ff')
        buttons_frame.pack(pady=10)
        
        # أزرار الوثائق
        docs = [
            ("📖 دليل المستخدم\nUser Manual", self.open_user_manual, '#16a085'),
            ("🚀 دليل البدء السريع\nQuick Start Guide", self.open_quick_guide, '#27ae60'),
            ("ℹ️ معلومات النظام\nSystem Info", self.show_system_info, '#2980b9')
        ]
        
        for i, (text, command, color) in enumerate(docs):
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=15,
                cursor='hand2',
                width=20,
                height=3
            )
            btn.grid(row=0, column=i, padx=10, pady=5)
        
        # زر الخروج
        exit_btn = tk.Button(
            section_frame,
            text="❌ إغلاق النظام\nClose System",
            command=self.root.quit,
            font=('Arial', 14, 'bold'),
            bg='#c0392b',
            fg='white',
            relief='raised',
            bd=3,
            padx=30,
            pady=10,
            cursor='hand2'
        )
        exit_btn.pack(pady=10)
        
    # وظائف البرامج الرئيسية
    def launch_nassar_final(self):
        """تشغيل برنامج ناصر النهائي"""
        program_path = os.path.join(self.paths['main_programs'], "NASSAR_PROGRAM_FINAL_WORKING.py")
        self.run_program(program_path, "برنامج ناصر النهائي")
        
    def launch_desktop(self):
        """تشغيل مشغل سطح المكتب"""
        program_path = os.path.join(self.paths['main_programs'], "LAUNCH_PROTECH_DESKTOP.py")
        self.run_program(program_path, "مشغل سطح المكتب")
        
    def show_control_panel(self):
        """عرض لوحة التحكم"""
        messagebox.showinfo(
            "لوحة التحكم\nControl Panel",
            "قريباً: لوحة تحكم متقدمة لإدارة النظام\nComing Soon: Advanced control panel for system management"
        )
        
    # وظائف المشغلات
    def launch_guaranteed(self):
        """تشغيل المشغل المضمون"""
        launcher_path = os.path.join(self.paths['launchers'], "GUARANTEED_LAUNCHER.py")
        self.run_program(launcher_path, "المشغل المضمون")
        
    def launch_emergency(self):
        """تشغيل المشغل الطارئ"""
        launcher_path = os.path.join(self.paths['launchers'], "EMERGENCY_FIX_LAUNCHER.py")
        self.run_program(launcher_path, "المشغل الطارئ")
        
    def launch_quick(self):
        """التشغيل السريع"""
        launcher_path = os.path.join(self.paths['launchers'], "LAUNCH_NASSAR_FINAL.py")
        self.run_program(launcher_path, "التشغيل السريع")
        
    # وظائف الأدوات
    def launch_backup_tool(self):
        """تشغيل أداة النسخ الاحتياطي"""
        tool_path = os.path.join(self.paths['tools'], "Data_Backup_Tool.py")
        self.run_program(tool_path, "أداة النسخ الاحتياطي")
        
    def launch_data_checker(self):
        """تشغيل أداة فحص البيانات"""
        tool_path = os.path.join(self.paths['tools'], "Data_Checker_Tool.py")
        self.run_program(tool_path, "أداة فحص البيانات")
        
    def open_file_manager(self):
        """فتح مدير الملفات"""
        try:
            os.startfile(self.base_path)
        except:
            messagebox.showerror("خطأ", f"فشل في فتح مدير الملفات\nFailed to open file manager")
            
    # وظائف الوثائق
    def open_user_manual(self):
        """فتح دليل المستخدم"""
        manual_path = os.path.join(self.paths['docs'], "README_Nassar_Program.md")
        if os.path.exists(manual_path):
            try:
                os.startfile(manual_path)
            except:
                messagebox.showinfo("دليل المستخدم", f"يمكنك العثور على دليل المستخدم في:\n{manual_path}")
        else:
            messagebox.showinfo("دليل المستخدم", "دليل المستخدم غير متوفر حالياً")
            
    def open_quick_guide(self):
        """فتح دليل البدء السريع"""
        guide_path = os.path.join(self.paths['docs'], "Quick_Start_Guide.txt")
        if os.path.exists(guide_path):
            try:
                os.startfile(guide_path)
            except:
                messagebox.showinfo("دليل البدء السريع", f"يمكنك العثور على دليل البدء السريع في:\n{guide_path}")
        else:
            messagebox.showinfo("دليل البدء السريع", "دليل البدء السريع غير متوفر حالياً")
            
    def show_system_info(self):
        """عرض معلومات النظام"""
        info = f"""
🏢 نظام ناصر الكامل للمحاسبة
NASSAR COMPLETE ACCOUNTING SYSTEM

📋 معلومات النظام:
• الاسم: نظام ناصر الكامل للمحاسبة
• الإصدار: 1.0 النهائي
• المطور: Augment Agent
• التاريخ: 2025-06-20

📁 مسارات النظام:
• المجلد الرئيسي: {self.base_path}
• البرامج الرئيسية: {self.paths['main_programs']}
• المشغلات: {self.paths['launchers']}
• ملفات البيانات: {self.paths['data_files']}
• الأدوات المساعدة: {self.paths['tools']}
• النسخ الاحتياطية: {self.paths['backups']}
• الوثائق: {self.paths['docs']}

🎯 الميزات:
• إدارة المخزون مع الباركود
• إدارة العملاء والموردين
• نظام المبيعات والفواتير
• التقارير والإحصائيات
• واجهة ثنائية اللغة
• حماية كاملة للبيانات
• أدوات مساعدة متقدمة

🔄 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        messagebox.showinfo("معلومات النظام\nSystem Information", info)
        
    def run_program(self, program_path, program_name):
        """تشغيل برنامج معين"""
        try:
            if os.path.exists(program_path):
                print(f"🚀 تشغيل {program_name}...")
                subprocess.Popen([sys.executable, program_path])
                print(f"✅ تم تشغيل {program_name} بنجاح")
            else:
                messagebox.showerror(
                    "خطأ\nError",
                    f"لم يتم العثور على {program_name}\n{program_name} not found:\n{program_path}"
                )
        except Exception as e:
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في تشغيل {program_name}\nFailed to launch {program_name}:\n{str(e)}"
            )
            
    def run(self):
        """تشغيل المشغل الرئيسي"""
        self.create_main_window()
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🏢 تشغيل نظام ناصر الكامل للمحاسبة...")
    print("🏢 Starting Nassar Complete Accounting System...")
    
    try:
        launcher = NassarCompleteLauncher()
        launcher.run()
    except Exception as e:
        print(f"❌ خطأ في تشغيل النظام: {e}")
        messagebox.showerror("خطأ\nError", f"فشل في تشغيل النظام\nFailed to start system:\n{str(e)}")

if __name__ == "__main__":
    main()
