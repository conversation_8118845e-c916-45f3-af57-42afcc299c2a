#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import subprocess
from datetime import datetime

def ensure_data_file():
    """Ensure data file exists with proper content"""
    data_file = "protech_simple_data.json"
    
    print("🔍 Checking data file...")
    
    if not os.path.exists(data_file):
        print("⚠️ Data file not found, creating...")
        create_sample_data_file()
        return True
    
    try:
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            
        products_count = len(data.get('products', []))
        customers_count = len(data.get('customers', []))
        suppliers_count = len(data.get('suppliers', []))
        
        print(f"✅ Data file found: {products_count} products, {customers_count} customers, {suppliers_count} suppliers")
        
        if products_count == 0 and customers_count == 0 and suppliers_count == 0:
            print("⚠️ Data file is empty, recreating...")
            create_sample_data_file()
            return True
            
        return True
        
    except Exception as e:
        print(f"❌ Error reading data file: {e}")
        print("🔄 Recreating data file...")
        create_sample_data_file()
        return True

def create_sample_data_file():
    """Create sample data file"""
    sample_data = {
        "suppliers": [
            {
                "id": 1,
                "name": "Tech Solutions Inc.",
                "name_ar": "شركة الحلول التقنية",
                "phone": "+966-11-123-4567",
                "email": "<EMAIL>",
                "address": "الرياض، المملكة العربية السعودية",
                "contact_person": "أحمد محمد",
                "active": True
            },
            {
                "id": 2,
                "name": "Office World Ltd.",
                "name_ar": "شركة عالم المكاتب",
                "phone": "+966-11-234-5678",
                "email": "<EMAIL>",
                "address": "جدة، المملكة العربية السعودية",
                "contact_person": "سارة أحمد",
                "active": True
            },
            {
                "id": 3,
                "name": "Electronics Hub",
                "name_ar": "مركز الإلكترونيات",
                "phone": "+966-11-345-6789",
                "email": "<EMAIL>",
                "address": "الدمام، المملكة العربية السعودية",
                "contact_person": "محمد علي",
                "active": True
            }
        ],
        "products": [
            {
                "id": 1,
                "barcode": "1234567890123",
                "name": "Business Laptop",
                "name_ar": "لابتوب الأعمال",
                "category": "Electronics",
                "supplier_id": 1,
                "price": 1200,
                "cost": 950,
                "stock": 45,
                "min_stock": 10,
                "unit": "قطعة",
                "unit_price": 1200,
                "quantity_per_box": 1,
                "notes": ""
            },
            {
                "id": 2,
                "barcode": "1234567890124",
                "name": "Wireless Mouse",
                "name_ar": "فأرة لاسلكية",
                "category": "Electronics",
                "supplier_id": 2,
                "price": 35,
                "cost": 25,
                "stock": 150,
                "min_stock": 30,
                "unit": "قطعة",
                "unit_price": 35,
                "quantity_per_box": 1,
                "notes": ""
            },
            {
                "id": 3,
                "barcode": "1234567890125",
                "name": "Mechanical Keyboard",
                "name_ar": "لوحة مفاتيح ميكانيكية",
                "category": "Electronics",
                "supplier_id": 3,
                "price": 120,
                "cost": 85,
                "stock": 60,
                "min_stock": 15,
                "unit": "قطعة",
                "unit_price": 120,
                "quantity_per_box": 1,
                "notes": ""
            },
            {
                "id": 4,
                "barcode": "1234567890126",
                "name": "24 Monitor",
                "name_ar": "شاشة 24 بوصة",
                "category": "Electronics",
                "supplier_id": 1,
                "price": 280,
                "cost": 220,
                "stock": 35,
                "min_stock": 8,
                "unit": "قطعة",
                "unit_price": 280,
                "quantity_per_box": 1,
                "notes": ""
            },
            {
                "id": 5,
                "barcode": "1234567890127",
                "name": "Smartphone",
                "name_ar": "هاتف ذكي",
                "category": "Electronics",
                "supplier_id": 2,
                "price": 550,
                "cost": 400,
                "stock": 5,
                "min_stock": 10,
                "unit": "قطعة",
                "unit_price": 550,
                "quantity_per_box": 1,
                "notes": ""
            },
            {
                "id": 6,
                "barcode": "123025",
                "name": "يسبس",
                "name_ar": "يسبس",
                "category": "Electronics",
                "supplier_id": 1,
                "price": 0.0,
                "cost": 0.0,
                "stock": 0,
                "min_stock": 5,
                "unit": "قطعة",
                "unit_price": 0.0,
                "quantity_per_box": 1,
                "notes": ""
            }
        ],
        "customers": [
            {
                "id": 1,
                "code": "CUST001",
                "name": "John Smith",
                "name_ar": "جون سميث",
                "email": "<EMAIL>",
                "phone": "******-1234",
                "balance": 1250,
                "type": "RETAIL"
            },
            {
                "id": 2,
                "code": "CUST002",
                "name": "ABC Corporation",
                "name_ar": "شركة ABC",
                "email": "<EMAIL>",
                "phone": "******-5678",
                "balance": 8750,
                "type": "WHOLESALE"
            },
            {
                "id": 3,
                "code": "CUST003",
                "name": "Ahmed Al-Rashid",
                "name_ar": "أحمد الراشد",
                "email": "<EMAIL>",
                "phone": "+966-50-123-4567",
                "balance": 2500,
                "type": "SHOP_OWNER"
            }
        ],
        "sales": [
            {
                "id": 1,
                "invoice": "INV-2024-001",
                "customer_id": 2,
                "date": "2024-01-15",
                "total": 2760,
                "paid": 2760,
                "balance": 0,
                "status": "PAID"
            }
        ],
        "last_updated": datetime.now().isoformat()
    }
    
    try:
        with open("protech_simple_data.json", 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2, default=str)
        print("✅ Sample data file created successfully")
        return True
    except Exception as e:
        print(f"❌ Error creating sample data file: {e}")
        return False

def run_protech():
    """Run ProTech with guaranteed data"""
    print("🚀 ProTech Guaranteed Launcher")
    print("=" * 50)
    
    # Change to the correct directory
    desktop_path = r"c:\Users\<USER>\OneDrive\Desktop\accounting program"
    if os.path.exists(desktop_path):
        os.chdir(desktop_path)
        print(f"📁 Changed to: {desktop_path}")
    
    # Ensure data file exists
    if not ensure_data_file():
        print("❌ Failed to ensure data file")
        return False
    
    # Check if ProTech file exists
    protech_file = "protech_simple_working.py"
    if not os.path.exists(protech_file):
        print(f"❌ ProTech file not found: {protech_file}")
        return False
    
    print(f"✅ All files ready, launching ProTech...")
    
    try:
        # Run ProTech
        subprocess.run([sys.executable, protech_file])
        return True
    except Exception as e:
        print(f"❌ Error running ProTech: {e}")
        return False

if __name__ == "__main__":
    run_protech()
    input("\nPress Enter to exit...")
