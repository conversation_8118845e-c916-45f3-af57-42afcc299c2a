#!/usr/bin/env python3
"""
ProTech Accounting System - Final Working Version
نظام ProTech للمحاسبة - النسخة النهائية العاملة

Simple, reliable desktop accounting application
تطبيق محاسبة سطح مكتب بسيط وموثوق
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime

class ProTechFinal:
    def __init__(self):
        print("🚀 بدء تشغيل نظام ProTech...")
        print("🚀 Starting ProTech system...")
        
        # Create main window
        self.root = tk.Tk()
        self.root.title("نظام ProTech للمحاسبة - ProTech Accounting System")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f8ff')
        
        # Initialize data
        self.data_file = "protech_data.json"
        self.init_data()
        
        # Create interface
        self.create_interface()
        
        print("✅ تم تحميل النظام بنجاح!")
        print("✅ System loaded successfully!")
    
    def init_data(self):
        """Initialize data storage"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.products = data.get('products', [])
                    self.customers = data.get('customers', [])
                    self.sales = data.get('sales', [])
            else:
                self.create_sample_data()
        except Exception as e:
            print(f"خطأ في تحميل البيانات: {e}")
            self.create_sample_data()
    
    def create_sample_data(self):
        """Create sample data"""
        self.products = [
            {
                'id': 1,
                'code': 'LAPTOP001',
                'name': 'Business Laptop',
                'name_ar': 'لابتوب الأعمال',
                'category': 'Electronics',
                'price': 1000.0,
                'cost': 800.0,
                'stock': 50,
                'min_stock': 10
            },
            {
                'id': 2,
                'code': 'MOUSE001',
                'name': 'Wireless Mouse',
                'name_ar': 'فأرة لاسلكية',
                'category': 'Electronics',
                'price': 25.0,
                'cost': 15.0,
                'stock': 200,
                'min_stock': 50
            },
            {
                'id': 3,
                'code': 'PHONE001',
                'name': 'Smartphone',
                'name_ar': 'هاتف ذكي',
                'category': 'Electronics',
                'price': 550.0,
                'cost': 400.0,
                'stock': 5,
                'min_stock': 10
            },
            {
                'id': 4,
                'code': 'DESK001',
                'name': 'Office Desk',
                'name_ar': 'مكتب مكتبي',
                'category': 'Furniture',
                'price': 300.0,
                'cost': 200.0,
                'stock': 0,
                'min_stock': 5
            }
        ]
        
        self.customers = [
            {
                'id': 1,
                'name': 'John Smith',
                'name_ar': 'جون سميث',
                'email': '<EMAIL>',
                'phone': '******-1234',
                'balance': 1250.0,
                'type': 'RETAIL'
            },
            {
                'id': 2,
                'name': 'ABC Corporation',
                'name_ar': 'شركة ABC',
                'email': '<EMAIL>',
                'phone': '******-5678',
                'balance': 8750.0,
                'type': 'WHOLESALE'
            },
            {
                'id': 3,
                'name': 'Ahmed Al-Rashid',
                'name_ar': 'أحمد الراشد',
                'email': '<EMAIL>',
                'phone': '+966-50-123-4567',
                'balance': 2500.0,
                'type': 'RETAIL'
            }
        ]
        
        self.sales = []
        self.save_data()
    
    def save_data(self):
        """Save data to file"""
        try:
            data = {
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"فشل في حفظ البيانات\nFailed to save data:\n{str(e)}")
    
    def create_interface(self):
        """Create main interface"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#2563eb', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Title
        title_label = tk.Label(
            header_frame,
            text="🎉 نظام ProTech للمحاسبة",
            font=('Arial', 22, 'bold'),
            fg='white',
            bg='#2563eb'
        )
        title_label.pack(side='left', padx=20, pady=20)
        
        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="ProTech Accounting System - Desktop Application",
            font=('Arial', 12),
            fg='#bfdbfe',
            bg='#2563eb'
        )
        subtitle_label.pack(side='left', padx=(0, 20), pady=(35, 20))
        
        # Time
        time_label = tk.Label(
            header_frame,
            text=f"🕒 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            font=('Arial', 11),
            fg='white',
            bg='#2563eb'
        )
        time_label.pack(side='right', padx=20, pady=20)
        
        # Main container
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Navigation panel
        nav_frame = tk.Frame(main_frame, bg='#3b82f6', width=200)
        nav_frame.pack(side='left', fill='y', padx=(0, 10))
        nav_frame.pack_propagate(False)
        
        # Navigation title
        nav_title = tk.Label(
            nav_frame,
            text="📋 القوائم الرئيسية\nMain Menus",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='#3b82f6'
        )
        nav_title.pack(pady=20)
        
        # Navigation buttons
        nav_buttons = [
            ("🏠 لوحة التحكم\nDashboard", self.show_dashboard),
            ("📦 المخزون\nInventory", self.show_inventory),
            ("👥 العملاء\nCustomers", self.show_customers),
            ("💰 المبيعات\nSales", self.show_sales),
            ("📊 التقارير\nReports", self.show_reports),
            ("⚙️ الإعدادات\nSettings", self.show_settings),
            ("❓ المساعدة\nHelp", self.show_help)
        ]
        
        for text, command in nav_buttons:
            btn = tk.Button(
                nav_frame,
                text=text,
                font=('Arial', 11, 'bold'),
                fg='white',
                bg='#3b82f6',
                activebackground='#1d4ed8',
                relief='flat',
                width=18,
                height=3,
                command=command,
                cursor='hand2'
            )
            btn.pack(pady=5, padx=10, fill='x')
        
        # Content area
        self.content_frame = tk.Frame(main_frame, bg='white', relief='ridge', bd=2)
        self.content_frame.pack(side='right', fill='both', expand=True)
        
        # Status bar
        status_frame = tk.Frame(self.root, bg='#374151', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز / Ready",
            font=('Arial', 10),
            fg='white',
            bg='#374151'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        version_label = tk.Label(
            status_frame,
            text="ProTech Final v1.0 | © 2024",
            font=('Arial', 9),
            fg='#9ca3af',
            bg='#374151'
        )
        version_label.pack(side='right', padx=10, pady=5)
        
        # Show dashboard by default
        self.show_dashboard()
        
        # Show welcome message
        self.root.after(1000, self.show_welcome)
    
    def clear_content(self):
        """Clear content area"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def update_status(self, message):
        """Update status bar"""
        self.status_label.config(text=message)
        self.root.update()
    
    def show_welcome(self):
        """Show welcome message"""
        messagebox.showinfo(
            "مرحباً / Welcome",
            "🎉 مرحباً بك في نظام ProTech للمحاسبة!\n\n" +
            "Welcome to ProTech Accounting System!\n\n" +
            "✅ تطبيق سطح مكتب مستقل\n" +
            "✅ Standalone desktop application\n\n" +
            "🚀 جاهز للاستخدام!"
        )
    
    def show_dashboard(self):
        """Show dashboard"""
        self.clear_content()
        self.update_status("عرض لوحة التحكم / Showing dashboard")
        
        # Title
        title_label = tk.Label(
            self.content_frame,
            text="📊 لوحة التحكم / Dashboard",
            font=('Arial', 20, 'bold'),
            fg='#1f2937',
            bg='white'
        )
        title_label.pack(pady=20)
        
        # Statistics
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # Calculate stats
        total_products = len(self.products)
        low_stock = len([p for p in self.products if p['stock'] <= p['min_stock']])
        out_of_stock = len([p for p in self.products if p['stock'] == 0])
        total_customers = len(self.customers)
        total_value = sum(p['price'] * p['stock'] for p in self.products)
        
        # Create stat cards
        stats = [
            ("📦", "المنتجات\nProducts", total_products, "#3b82f6"),
            ("⚠️", "مخزون منخفض\nLow Stock", low_stock, "#ef4444"),
            ("👥", "العملاء\nCustomers", total_customers, "#10b981"),
            ("💰", "قيمة المخزون\nInventory Value", f"${total_value:,.0f}", "#8b5cf6")
        ]
        
        for icon, title, value, color in stats:
            card = tk.Frame(stats_frame, bg=color, relief='raised', bd=2)
            card.pack(side='left', fill='both', expand=True, padx=5)
            
            tk.Label(card, text=icon, font=('Arial', 24), fg='white', bg=color).pack(pady=5)
            tk.Label(card, text=title, font=('Arial', 10, 'bold'), fg='white', bg=color).pack()
            tk.Label(card, text=str(value), font=('Arial', 16, 'bold'), fg='white', bg=color).pack(pady=5)
        
        # Welcome section
        welcome_frame = tk.Frame(self.content_frame, bg='#dcfce7', relief='ridge', bd=2)
        welcome_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(
            welcome_frame,
            text="🎉 مرحباً بك في نظام ProTech للمحاسبة",
            font=('Arial', 18, 'bold'),
            fg='#16a34a',
            bg='#dcfce7'
        ).pack(pady=15)
        
        tk.Label(
            welcome_frame,
            text="Welcome to ProTech Accounting System",
            font=('Arial', 14),
            fg='#15803d',
            bg='#dcfce7'
        ).pack(pady=(0, 15))
        
        # Quick actions
        actions_frame = tk.LabelFrame(
            self.content_frame,
            text="إجراءات سريعة / Quick Actions",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        actions_frame.pack(fill='x', padx=20, pady=10)
        
        btn_frame = tk.Frame(actions_frame, bg='white')
        btn_frame.pack(pady=15)
        
        actions = [
            ("📦 عرض المخزون", self.show_inventory, "#3b82f6"),
            ("👥 عرض العملاء", self.show_customers, "#10b981"),
            ("➕ إضافة منتج", self.add_product, "#8b5cf6"),
            ("📊 التقارير", self.show_reports, "#f59e0b")
        ]
        
        for text, command, color in actions:
            tk.Button(
                btn_frame,
                text=text,
                font=('Arial', 11, 'bold'),
                bg=color,
                fg='white',
                width=15,
                height=2,
                command=command,
                cursor='hand2'
            ).pack(side='left', padx=8)
        
        # System info
        info_frame = tk.LabelFrame(
            self.content_frame,
            text="معلومات النظام / System Information",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        info_frame.pack(fill='x', padx=20, pady=10)
        
        info_text = tk.Text(info_frame, height=6, font=('Arial', 10), bg='#f9fafb', wrap='word')
        info_text.pack(fill='x', padx=10, pady=10)
        
        info_content = f"""
📊 إحصائيات سريعة / Quick Statistics:
• إجمالي المنتجات / Total Products: {total_products}
• منتجات منخفضة المخزون / Low Stock Products: {low_stock}
• منتجات نفد مخزونها / Out of Stock Products: {out_of_stock}
• إجمالي العملاء / Total Customers: {total_customers}
• قيمة المخزون الإجمالية / Total Inventory Value: ${total_value:,.2f}

🕒 آخر تحديث / Last Update: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
💾 حالة البيانات / Data Status: محفوظة / Saved
        """
        
        info_text.insert('1.0', info_content)
        info_text.config(state='disabled')

    def show_inventory(self):
        """Show inventory management"""
        self.clear_content()
        self.update_status("عرض المخزون / Showing inventory")

        # Header
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            header_frame,
            text="📦 إدارة المخزون / Inventory Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Control buttons
        btn_frame = tk.Frame(header_frame, bg='white')
        btn_frame.pack(side='right')

        tk.Button(
            btn_frame,
            text="➕ إضافة منتج",
            font=('Arial', 10, 'bold'),
            bg='#10b981',
            fg='white',
            command=self.add_product,
            cursor='hand2'
        ).pack(side='left', padx=5)

        tk.Button(
            btn_frame,
            text="🔄 تحديث",
            font=('Arial', 10, 'bold'),
            bg='#3b82f6',
            fg='white',
            command=self.show_inventory,
            cursor='hand2'
        ).pack(side='left', padx=5)

        # Products table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create treeview
        columns = ('Code', 'Name', 'Name_AR', 'Category', 'Price', 'Stock', 'Status')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Configure columns
        headings = ['الكود', 'الاسم', 'الاسم بالعربية', 'الفئة', 'السعر', 'المخزون', 'الحالة']
        for col, heading in zip(columns, headings):
            tree.heading(col, text=heading)
            tree.column(col, width=120, anchor='center')

        # Add data
        for product in self.products:
            if product['stock'] == 0:
                status = "نفد / Out"
                tags = ('out_of_stock',)
            elif product['stock'] <= product['min_stock']:
                status = "منخفض / Low"
                tags = ('low_stock',)
            else:
                status = "جيد / Good"
                tags = ('good_stock',)

            tree.insert('', 'end', values=(
                product['code'],
                product['name'],
                product['name_ar'],
                product['category'],
                f"${product['price']:.2f}",
                product['stock'],
                status
            ), tags=tags)

        # Configure tags
        tree.tag_configure('out_of_stock', background='#fee2e2', foreground='#dc2626')
        tree.tag_configure('low_stock', background='#fef3c7', foreground='#d97706')
        tree.tag_configure('good_stock', background='#dcfce7', foreground='#16a34a')

        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)

        tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

    def add_product(self):
        """Add new product"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد / Add New Product")
        dialog.geometry("450x600")
        dialog.configure(bg='#f0f8ff')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (dialog.winfo_screenheight() // 2) - (600 // 2)
        dialog.geometry(f"450x600+{x}+{y}")

        # Header
        header_frame = tk.Frame(dialog, bg='#10b981', height=60)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        tk.Label(
            header_frame,
            text="➕ إضافة منتج جديد / Add New Product",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#10b981'
        ).pack(pady=15)

        # Form
        form_frame = tk.Frame(dialog, bg='#f0f8ff')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)

        entries = {}
        fields = [
            ('code', 'كود المنتج / Product Code *'),
            ('name', 'اسم المنتج (إنجليزي) / Name (English) *'),
            ('name_ar', 'اسم المنتج (عربي) / Name (Arabic) *'),
            ('category', 'الفئة / Category *'),
            ('price', 'سعر البيع / Selling Price *'),
            ('cost', 'سعر التكلفة / Cost Price *'),
            ('stock', 'الكمية الحالية / Current Stock *'),
            ('min_stock', 'الحد الأدنى / Minimum Stock *')
        ]

        for field, label in fields:
            # Label
            tk.Label(
                form_frame,
                text=label,
                font=('Arial', 11, 'bold'),
                fg='#374151',
                bg='#f0f8ff'
            ).pack(anchor='w', pady=(10, 5))

            # Entry
            entry = tk.Entry(
                form_frame,
                font=('Arial', 11),
                width=40,
                relief='solid',
                bd=1
            )
            entry.pack(fill='x', pady=(0, 5))
            entries[field] = entry

        # Buttons
        btn_frame = tk.Frame(dialog, bg='#f0f8ff')
        btn_frame.pack(fill='x', padx=20, pady=20)

        def save_product():
            try:
                # Get new ID
                new_id = max([p['id'] for p in self.products], default=0) + 1

                # Collect data
                new_product = {
                    'id': new_id,
                    'code': entries['code'].get().strip(),
                    'name': entries['name'].get().strip(),
                    'name_ar': entries['name_ar'].get().strip(),
                    'category': entries['category'].get().strip(),
                    'price': float(entries['price'].get().strip()),
                    'cost': float(entries['cost'].get().strip()),
                    'stock': int(entries['stock'].get().strip()),
                    'min_stock': int(entries['min_stock'].get().strip())
                }

                # Validate
                if not all([new_product['code'], new_product['name'], new_product['name_ar'], new_product['category']]):
                    messagebox.showerror("خطأ / Error", "يرجى ملء جميع الحقول المطلوبة\nPlease fill all required fields")
                    return

                # Check if code exists
                if any(p['code'] == new_product['code'] for p in self.products):
                    messagebox.showerror("خطأ / Error", "كود المنتج موجود مسبقاً\nProduct code already exists")
                    return

                # Add product
                self.products.append(new_product)
                self.save_data()

                messagebox.showinfo("نجح / Success", "تم إضافة المنتج بنجاح\nProduct added successfully")
                dialog.destroy()
                self.show_inventory()  # Refresh inventory view

            except ValueError:
                messagebox.showerror("خطأ / Error", "قيم غير صحيحة في الحقول الرقمية\nInvalid values in numeric fields")
            except Exception as e:
                messagebox.showerror("خطأ / Error", f"خطأ في حفظ المنتج\nError saving product:\n{str(e)}")

        tk.Button(
            btn_frame,
            text="💾 حفظ / Save",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            width=15,
            command=save_product,
            cursor='hand2'
        ).pack(side='left', padx=5)

        tk.Button(
            btn_frame,
            text="❌ إلغاء / Cancel",
            font=('Arial', 12, 'bold'),
            bg='#6b7280',
            fg='white',
            width=15,
            command=dialog.destroy,
            cursor='hand2'
        ).pack(side='right', padx=5)

    def show_customers(self):
        """Show customers management"""
        self.clear_content()
        self.update_status("عرض العملاء / Showing customers")

        # Title
        tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء / Customer Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Customers list
        customers_frame = tk.Frame(self.content_frame, bg='white')
        customers_frame.pack(fill='both', expand=True, padx=20, pady=10)

        for customer in self.customers:
            customer_card = tk.Frame(customers_frame, bg='#f9fafb', relief='ridge', bd=1)
            customer_card.pack(fill='x', pady=5)

            # Customer info
            info_frame = tk.Frame(customer_card, bg='#f9fafb')
            info_frame.pack(fill='x', padx=15, pady=10)

            # Name
            tk.Label(
                info_frame,
                text=f"👤 {customer['name']} | {customer['name_ar']}",
                font=('Arial', 14, 'bold'),
                fg='#1f2937',
                bg='#f9fafb'
            ).pack(side='left')

            # Balance
            balance_color = '#10b981' if customer['balance'] >= 0 else '#ef4444'
            tk.Label(
                info_frame,
                text=f"💰 ${customer['balance']:,.2f}",
                font=('Arial', 12, 'bold'),
                fg=balance_color,
                bg='#f9fafb'
            ).pack(side='right')

            # Details
            details_frame = tk.Frame(customer_card, bg='#f9fafb')
            details_frame.pack(fill='x', padx=15, pady=(0, 10))

            tk.Label(
                details_frame,
                text=f"📧 {customer['email']} | 📱 {customer['phone']} | 🏷️ {customer['type']}",
                font=('Arial', 10),
                fg='#6b7280',
                bg='#f9fafb'
            ).pack(side='left')

    def show_sales(self):
        """Show sales management"""
        self.clear_content()
        self.update_status("عرض المبيعات / Showing sales")

        tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات / Sales Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=50)

        tk.Label(
            self.content_frame,
            text="🚧 هذه الميزة قيد التطوير\nThis feature is under development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=20)

    def show_reports(self):
        """Show reports"""
        self.clear_content()
        self.update_status("عرض التقارير / Showing reports")

        # Title
        tk.Label(
            self.content_frame,
            text="📊 التقارير / Reports",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Report content
        report_frame = tk.Frame(self.content_frame, bg='white')
        report_frame.pack(fill='both', expand=True, padx=20, pady=10)

        report_text = tk.Text(report_frame, font=('Arial', 11), bg='#f9fafb', wrap='word')
        report_text.pack(fill='both', expand=True)

        # Generate report
        total_products = len(self.products)
        total_value = sum(p['price'] * p['stock'] for p in self.products)
        low_stock_items = [p for p in self.products if p['stock'] <= p['min_stock']]
        out_of_stock_items = [p for p in self.products if p['stock'] == 0]

        report_content = f"""
📊 تقرير نظام ProTech للمحاسبة
ProTech Accounting System Report
{'='*60}

📅 تاريخ التقرير / Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📦 تقرير المخزون / Inventory Report:
{'─'*40}
• إجمالي المنتجات / Total Products: {total_products}
• قيمة المخزون الإجمالية / Total Inventory Value: ${total_value:,.2f}
• منتجات منخفضة المخزون / Low Stock Products: {len(low_stock_items)}
• منتجات نفد مخزونها / Out of Stock Products: {len(out_of_stock_items)}

👥 تقرير العملاء / Customer Report:
{'─'*40}
• إجمالي العملاء / Total Customers: {len(self.customers)}
• عملاء التجزئة / Retail Customers: {len([c for c in self.customers if c['type'] == 'RETAIL'])}
• عملاء الجملة / Wholesale Customers: {len([c for c in self.customers if c['type'] == 'WHOLESALE'])}
• إجمالي الأرصدة / Total Balances: ${sum(c['balance'] for c in self.customers):,.2f}

⚠️ تنبيهات / Alerts:
{'─'*40}
"""

        if out_of_stock_items:
            report_content += "\n🔴 منتجات نفد مخزونها / Out of Stock Products:\n"
            for item in out_of_stock_items:
                report_content += f"   • {item['name']} ({item['name_ar']}) - الكود: {item['code']}\n"

        if low_stock_items:
            report_content += "\n🟡 منتجات منخفضة المخزون / Low Stock Products:\n"
            for item in low_stock_items:
                if item['stock'] > 0:  # Exclude out of stock items
                    report_content += f"   • {item['name']} ({item['name_ar']}) - المخزون: {item['stock']}\n"

        if not low_stock_items and not out_of_stock_items:
            report_content += "\n✅ جميع المنتجات لديها مخزون كافي / All products have sufficient stock\n"

        report_content += f"\n\n📈 إحصائيات إضافية / Additional Statistics:\n"
        report_content += f"{'─'*40}\n"
        report_content += f"• متوسط سعر المنتجات / Average Product Price: ${sum(p['price'] for p in self.products) / len(self.products):,.2f}\n"
        report_content += f"• أعلى سعر / Highest Price: ${max(p['price'] for p in self.products):,.2f}\n"
        report_content += f"• أقل سعر / Lowest Price: ${min(p['price'] for p in self.products):,.2f}\n"
        report_content += f"• إجمالي الكميات / Total Quantities: {sum(p['stock'] for p in self.products):,}\n"

        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

    def show_settings(self):
        """Show settings"""
        self.clear_content()
        self.update_status("عرض الإعدادات / Showing settings")

        tk.Label(
            self.content_frame,
            text="⚙️ الإعدادات / Settings",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=50)

        tk.Label(
            self.content_frame,
            text="🚧 هذه الميزة قيد التطوير\nThis feature is under development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=20)

    def show_help(self):
        """Show help"""
        self.clear_content()
        self.update_status("عرض المساعدة / Showing help")

        # Title
        tk.Label(
            self.content_frame,
            text="❓ المساعدة والدعم / Help & Support",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Help content
        help_frame = tk.Frame(self.content_frame, bg='white')
        help_frame.pack(fill='both', expand=True, padx=20, pady=10)

        help_text = tk.Text(help_frame, font=('Arial', 11), bg='#f9fafb', wrap='word')
        help_text.pack(fill='both', expand=True)

        help_content = """
🎉 مرحباً بك في نظام ProTech للمحاسبة
Welcome to ProTech Accounting System

📋 كيفية الاستخدام / How to Use:

1. 🏠 لوحة التحكم / Dashboard
   - عرض الإحصائيات العامة للنظام
   - مراقبة حالة المخزون والعملاء
   - الوصول السريع للوظائف الرئيسية

2. 📦 إدارة المخزون / Inventory Management
   - عرض جميع المنتجات في جدول منظم
   - إضافة منتجات جديدة بسهولة
   - مراقبة المخزون المنخفض والنافد
   - تصنيف المنتجات حسب الحالة (جيد/منخفض/نافد)

3. 👥 إدارة العملاء / Customer Management
   - عرض قائمة العملاء مع تفاصيلهم
   - متابعة أرصدة العملاء
   - تصنيف العملاء (تجزئة/جملة)

4. 💰 المبيعات / Sales (قيد التطوير)
   - تسجيل عمليات البيع
   - إدارة الفواتير
   - متابعة المدفوعات

5. 📊 التقارير / Reports
   - تقارير شاملة عن المخزون
   - إحصائيات العملاء
   - تنبيهات المخزون المنخفض
   - إحصائيات مالية

6. ⚙️ الإعدادات / Settings (قيد التطوير)
   - إعدادات النظام
   - تخصيص الواجهة
   - إعدادات النسخ الاحتياطي

🔧 نصائح مهمة / Important Tips:

• البيانات تُحفظ تلقائياً في ملف JSON
• استخدم الأزرار في القائمة الجانبية للتنقل
• راجع التقارير بانتظام لمتابعة الأداء
• تأكد من تحديث بيانات المخزون بانتظام

📞 الدعم الفني / Technical Support:

📧 البريد الإلكتروني / Email: <EMAIL>
🌐 الموقع الإلكتروني / Website: www.protech.com
📱 الهاتف / Phone: +966-11-123-4567
💬 الدردشة المباشرة / Live Chat: متاح على الموقع

🆔 معلومات النسخة / Version Information:

• الإصدار / Version: ProTech Final v1.0
• تاريخ الإصدار / Release Date: 2024
• نوع الترخيص / License: تجاري / Commercial
• المطور / Developer: ProTech Solutions

🎯 شكراً لاستخدام نظام ProTech!
Thank you for using ProTech System!
        """

        help_text.insert('1.0', help_content)
        help_text.config(state='disabled')

    def run(self):
        """Run the application"""
        try:
            print("✅ تم تشغيل النظام بنجاح!")
            print("✅ System started successfully!")
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل النظام: {e}")
            messagebox.showerror("خطأ / Error", f"خطأ في التطبيق\nApplication error:\n{str(e)}")
        finally:
            self.save_data()
            print("💾 تم حفظ البيانات")
            print("💾 Data saved")

def main():
    """Main function"""
    try:
        print("="*60)
        print("🚀 بدء تشغيل نظام ProTech للمحاسبة")
        print("🚀 Starting ProTech Accounting System")
        print("="*60)

        app = ProTechFinal()
        app.run()

        print("="*60)
        print("🛑 تم إغلاق النظام")
        print("🛑 System closed")
        print("="*60)

    except Exception as e:
        print(f"❌ خطأ في بدء التشغيل: {e}")
        print(f"❌ Startup error: {e}")
        try:
            messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق\nFailed to start application:\n{str(e)}")
        except:
            print("فشل في عرض رسالة الخطأ")

if __name__ == '__main__':
    main()
