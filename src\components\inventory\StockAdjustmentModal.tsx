'use client';

import React, { useState } from 'react';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import { Product } from '@/types';

interface StockAdjustmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: Product | null;
  onAdjust: (data: {
    productId: string;
    newQuantity: number;
    reason: string;
    notes?: string;
  }) => Promise<void>;
}

export default function StockAdjustmentModal({
  isOpen,
  onClose,
  product,
  onAdjust,
}: StockAdjustmentModalProps) {
  const [newQuantity, setNewQuantity] = useState<number>(0);
  const [reason, setReason] = useState('');
  const [notes, setNotes] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  React.useEffect(() => {
    if (product && isOpen) {
      setNewQuantity(product.currentStock);
      setReason('');
      setNotes('');
      setErrors({});
    }
  }, [product, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!product) return;

    // Validate form
    const newErrors: Record<string, string> = {};
    
    if (newQuantity < 0) {
      newErrors.newQuantity = 'Quantity cannot be negative';
    }
    
    if (!reason.trim()) {
      newErrors.reason = 'Reason is required';
    }

    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }

    setLoading(true);
    try {
      await onAdjust({
        productId: product.id,
        newQuantity,
        reason: reason.trim(),
        notes: notes.trim() || undefined,
      });
      onClose();
    } catch (error) {
      console.error('Error adjusting stock:', error);
      setErrors({ submit: 'Failed to adjust stock. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  if (!isOpen || !product) return null;

  const difference = newQuantity - product.currentStock;
  const adjustmentType = difference > 0 ? 'increase' : difference < 0 ? 'decrease' : 'no change';

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        />

        {/* Modal */}
        <div className="relative w-full max-w-md transform overflow-hidden rounded-lg bg-white shadow-xl transition-all">
          {/* Header */}
          <div className="flex items-center justify-between border-b border-gray-200 px-6 py-4">
            <h3 className="text-lg font-medium text-gray-900">
              Adjust Stock
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <form onSubmit={handleSubmit} className="px-6 py-4">
            {/* Product Info */}
            <div className="mb-4 rounded-lg bg-gray-50 p-4">
              <h4 className="font-medium text-gray-900">{product.name}</h4>
              <p className="text-sm text-gray-600">Code: {product.code}</p>
              <p className="text-sm text-gray-600">
                Current Stock: {product.currentStock} {product.unit}
              </p>
            </div>

            {/* New Quantity */}
            <div className="mb-4">
              <Input
                label="New Quantity"
                type="number"
                value={newQuantity}
                onChange={(e) => setNewQuantity(parseInt(e.target.value) || 0)}
                error={errors.newQuantity}
                required
                min="0"
                step="1"
              />
            </div>

            {/* Adjustment Summary */}
            {difference !== 0 && (
              <div className="mb-4 rounded-lg bg-blue-50 p-3">
                <p className="text-sm font-medium text-blue-900">
                  Adjustment Summary:
                </p>
                <p className="text-sm text-blue-700">
                  {adjustmentType === 'increase' ? '+' : ''}{difference} {product.unit}
                  {adjustmentType === 'increase' && (
                    <span className="text-green-600"> (Stock Increase)</span>
                  )}
                  {adjustmentType === 'decrease' && (
                    <span className="text-red-600"> (Stock Decrease)</span>
                  )}
                </p>
              </div>
            )}

            {/* Reason */}
            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Reason for Adjustment *
              </label>
              <select
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                required
              >
                <option value="">Select a reason</option>
                <option value="Physical Count">Physical Count</option>
                <option value="Damaged Goods">Damaged Goods</option>
                <option value="Expired Items">Expired Items</option>
                <option value="Theft/Loss">Theft/Loss</option>
                <option value="Found Items">Found Items</option>
                <option value="System Correction">System Correction</option>
                <option value="Other">Other</option>
              </select>
              {errors.reason && (
                <p className="mt-1 text-sm text-red-600">{errors.reason}</p>
              )}
            </div>

            {/* Notes */}
            <div className="mb-6">
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Notes (Optional)
              </label>
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={3}
                className="block w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm"
                placeholder="Additional notes about this adjustment..."
              />
            </div>

            {/* Error Message */}
            {errors.submit && (
              <div className="mb-4 rounded-md bg-red-50 p-3">
                <p className="text-sm text-red-700">{errors.submit}</p>
              </div>
            )}

            {/* Actions */}
            <div className="flex justify-end space-x-3">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={loading}
                disabled={difference === 0}
              >
                Adjust Stock
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
