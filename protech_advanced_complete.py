#!/usr/bin/env python3
"""
ProTech Accounting System - Advanced Complete Version
نظام ProTech للمحاسبة - النسخة المتقدمة الشاملة

Complete advanced desktop accounting application with all features
تطبيق محاسبة سطح مكتب متقدم وشامل مع جميع المميزات
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
import sqlite3
from datetime import datetime, date
import threading
import time
from functools import lru_cache
import uuid
import csv
from contextlib import contextmanager

class DatabaseManager:
    """Advanced database manager with full functionality"""
    
    def __init__(self, db_path="protech_advanced.db"):
        self.db_path = db_path
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """Initialize complete database schema"""
        with self.get_connection() as conn:
            # Products table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_ar TEXT NOT NULL,
                    category TEXT NOT NULL,
                    brand TEXT,
                    description TEXT,
                    price REAL NOT NULL DEFAULT 0,
                    cost_price REAL NOT NULL DEFAULT 0,
                    stock INTEGER NOT NULL DEFAULT 0,
                    min_stock INTEGER NOT NULL DEFAULT 0,
                    max_stock INTEGER DEFAULT 1000,
                    unit TEXT DEFAULT 'piece',
                    barcode TEXT,
                    location TEXT,
                    supplier_id INTEGER,
                    tax_rate REAL DEFAULT 0.15,
                    discount_rate REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            ''')
            
            # Customers table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_ar TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    mobile TEXT,
                    address TEXT,
                    city TEXT,
                    country TEXT DEFAULT 'Saudi Arabia',
                    postal_code TEXT,
                    balance REAL NOT NULL DEFAULT 0,
                    credit_limit REAL DEFAULT 0,
                    category TEXT NOT NULL DEFAULT 'RETAIL',
                    payment_terms INTEGER DEFAULT 30,
                    tax_number TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Suppliers table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_ar TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    mobile TEXT,
                    address TEXT,
                    city TEXT,
                    country TEXT DEFAULT 'Saudi Arabia',
                    contact_person TEXT,
                    contact_title TEXT,
                    payment_terms INTEGER DEFAULT 30,
                    tax_number TEXT,
                    bank_account TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Sales table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    sale_date TEXT NOT NULL,
                    due_date TEXT,
                    subtotal REAL NOT NULL DEFAULT 0,
                    discount_amount REAL NOT NULL DEFAULT 0,
                    tax_amount REAL NOT NULL DEFAULT 0,
                    total_amount REAL NOT NULL DEFAULT 0,
                    paid_amount REAL NOT NULL DEFAULT 0,
                    balance_due REAL NOT NULL DEFAULT 0,
                    status TEXT DEFAULT 'PENDING',
                    payment_method TEXT,
                    notes TEXT,
                    created_by TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')
            
            # Sale items table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sale_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sale_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_price REAL NOT NULL,
                    discount_rate REAL DEFAULT 0,
                    tax_rate REAL DEFAULT 0.15,
                    line_total REAL NOT NULL,
                    FOREIGN KEY (sale_id) REFERENCES sales (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            # Purchases table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS purchases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    supplier_id INTEGER,
                    purchase_date TEXT NOT NULL,
                    due_date TEXT,
                    subtotal REAL NOT NULL DEFAULT 0,
                    discount_amount REAL NOT NULL DEFAULT 0,
                    tax_amount REAL NOT NULL DEFAULT 0,
                    total_amount REAL NOT NULL DEFAULT 0,
                    paid_amount REAL NOT NULL DEFAULT 0,
                    balance_due REAL NOT NULL DEFAULT 0,
                    status TEXT DEFAULT 'PENDING',
                    notes TEXT,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            ''')
            
            # Purchase items table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS purchase_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    purchase_id INTEGER NOT NULL,
                    product_id INTEGER NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit_cost REAL NOT NULL,
                    line_total REAL NOT NULL,
                    FOREIGN KEY (purchase_id) REFERENCES purchases (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')
            
            # Payments table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    payment_number TEXT UNIQUE NOT NULL,
                    type TEXT NOT NULL, -- 'SALE' or 'PURCHASE'
                    reference_id INTEGER NOT NULL,
                    amount REAL NOT NULL,
                    payment_method TEXT NOT NULL,
                    payment_date TEXT NOT NULL,
                    reference_number TEXT,
                    notes TEXT,
                    created_at TEXT NOT NULL
                )
            ''')
            
            # Categories table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    name_ar TEXT NOT NULL,
                    description TEXT,
                    parent_id INTEGER,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TEXT NOT NULL,
                    FOREIGN KEY (parent_id) REFERENCES categories (id)
                )
            ''')
            
            # System settings table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    key TEXT UNIQUE NOT NULL,
                    value TEXT,
                    description TEXT,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Create indexes for better performance
            indexes = [
                'CREATE INDEX IF NOT EXISTS idx_products_code ON products(code)',
                'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)',
                'CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock)',
                'CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(code)',
                'CREATE INDEX IF NOT EXISTS idx_suppliers_code ON suppliers(code)',
                'CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)',
                'CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)',
                'CREATE INDEX IF NOT EXISTS idx_purchases_date ON purchases(purchase_date)',
                'CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)'
            ]
            
            for index in indexes:
                conn.execute(index)
            
            conn.commit()
    
    def load_sample_data(self):
        """Load comprehensive sample data"""
        with self.get_connection() as conn:
            # Check if data exists
            if conn.execute("SELECT COUNT(*) FROM products").fetchone()[0] > 0:
                return
            
            now = datetime.now().isoformat()
            
            # Sample categories
            categories = [
                ('Electronics', 'إلكترونيات', 'Electronic devices and accessories'),
                ('Office Supplies', 'مستلزمات مكتبية', 'Office and stationery items'),
                ('Furniture', 'أثاث', 'Office and home furniture'),
                ('Software', 'برمجيات', 'Software and licenses'),
                ('Hardware', 'أجهزة', 'Computer hardware and components')
            ]
            
            for name, name_ar, desc in categories:
                conn.execute('''
                    INSERT OR IGNORE INTO categories (name, name_ar, description, created_at)
                    VALUES (?, ?, ?, ?)
                ''', (name, name_ar, desc, now))
            
            # Sample suppliers
            suppliers = [
                ('SUP001', 'Tech Solutions Inc.', 'شركة الحلول التقنية', '<EMAIL>', '******-0123', 'Mike Johnson'),
                ('SUP002', 'Office World Ltd.', 'شركة عالم المكاتب', '<EMAIL>', '******-0456', 'Sarah Wilson'),
                ('SUP003', 'Furniture Plus', 'أثاث بلس', '<EMAIL>', '******-0789', 'David Brown'),
                ('SUP004', 'Software Solutions', 'حلول البرمجيات', '<EMAIL>', '******-0321', 'Lisa Chen'),
                ('SUP005', 'Hardware Direct', 'الأجهزة المباشرة', '<EMAIL>', '******-0654', 'Ahmed Ali')
            ]
            
            for code, name, name_ar, email, phone, contact in suppliers:
                conn.execute('''
                    INSERT OR IGNORE INTO suppliers 
                    (code, name, name_ar, email, phone, contact_person, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (code, name, name_ar, email, phone, contact, now, now))
            
            # Sample customers
            customers = [
                ('CUST001', 'John Smith', 'جون سميث', '<EMAIL>', '******-1234', 1250.0, 'RETAIL'),
                ('CUST002', 'ABC Corporation', 'شركة ABC', '<EMAIL>', '******-5678', 8750.0, 'WHOLESALE'),
                ('CUST003', 'Ahmed Al-Rashid', 'أحمد الراشد', '<EMAIL>', '+966-50-123-4567', 2500.0, 'RETAIL'),
                ('CUST004', 'XYZ Trading', 'شركة XYZ للتجارة', '<EMAIL>', '+966-11-987-6543', 15000.0, 'WHOLESALE'),
                ('CUST005', 'Sarah Johnson', 'سارة جونسون', '<EMAIL>', '******-9876', 750.0, 'RETAIL')
            ]
            
            for code, name, name_ar, email, phone, balance, category in customers:
                conn.execute('''
                    INSERT OR IGNORE INTO customers 
                    (code, name, name_ar, email, phone, balance, category, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (code, name, name_ar, email, phone, balance, category, now, now))
            
            # Sample products
            products = [
                ('LAPTOP001', 'Business Laptop', 'لابتوب الأعمال', 'Electronics', 'Dell', 1000.0, 800.0, 50, 10, 100),
                ('MOUSE001', 'Wireless Mouse', 'فأرة لاسلكية', 'Electronics', 'Logitech', 25.0, 15.0, 200, 50, 500),
                ('NOTE001', 'Professional Notebook', 'دفتر مهني', 'Office Supplies', 'Moleskine', 5.0, 3.0, 500, 200, 1000),
                ('PHONE001', 'Business Smartphone', 'هاتف ذكي للأعمال', 'Electronics', 'Samsung', 550.0, 400.0, 25, 5, 50),
                ('DESK001', 'Office Desk', 'مكتب مكتبي', 'Furniture', 'IKEA', 300.0, 200.0, 15, 3, 30),
                ('CHAIR001', 'Ergonomic Chair', 'كرسي مريح', 'Furniture', 'Herman Miller', 450.0, 300.0, 20, 5, 40),
                ('PRINTER001', 'Laser Printer', 'طابعة ليزر', 'Electronics', 'HP', 200.0, 150.0, 30, 8, 60),
                ('SOFTWARE001', 'Office Suite', 'حزمة المكتب', 'Software', 'Microsoft', 150.0, 100.0, 100, 20, 200),
                ('MONITOR001', '24" Monitor', 'شاشة 24 بوصة', 'Electronics', 'LG', 180.0, 130.0, 40, 10, 80),
                ('KEYBOARD001', 'Mechanical Keyboard', 'لوحة مفاتيح ميكانيكية', 'Electronics', 'Corsair', 80.0, 50.0, 60, 15, 120)
            ]
            
            for code, name, name_ar, category, brand, price, cost, stock, min_stock, max_stock in products:
                conn.execute('''
                    INSERT OR IGNORE INTO products 
                    (code, name, name_ar, category, brand, price, cost_price, stock, min_stock, max_stock, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (code, name, name_ar, category, brand, price, cost, stock, min_stock, max_stock, now, now))
            
            # Sample settings
            settings = [
                ('company_name', 'ProTech Solutions', 'Company name'),
                ('company_name_ar', 'شركة ProTech للحلول', 'Company name in Arabic'),
                ('company_address', '123 Business Street, Riyadh, Saudi Arabia', 'Company address'),
                ('company_phone', '+966-11-123-4567', 'Company phone'),
                ('company_email', '<EMAIL>', 'Company email'),
                ('tax_rate', '0.15', 'Default tax rate'),
                ('currency', 'SAR', 'Default currency'),
                ('language', 'ar', 'Default language'),
                ('invoice_prefix', 'INV', 'Invoice number prefix'),
                ('backup_interval', '24', 'Backup interval in hours')
            ]
            
            for key, value, desc in settings:
                conn.execute('''
                    INSERT OR IGNORE INTO settings (key, value, description, updated_at)
                    VALUES (?, ?, ?, ?)
                ''', (key, value, desc, now))
            
            conn.commit()

class ProTechAdvanced:
    """Advanced ProTech Accounting System with complete functionality"""
    
    def __init__(self):
        print("🚀 تشغيل نظام ProTech المتقدم...")
        print("🚀 Starting ProTech Advanced System...")
        
        # Initialize database
        self.db = DatabaseManager()
        self.db.load_sample_data()
        
        # Initialize main window
        self.root = tk.Tk()
        self.root.title("نظام ProTech المتقدم للمحاسبة - ProTech Advanced Accounting System")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8fafc')
        self.root.state('zoomed')  # Start maximized
        
        # Initialize variables
        self.current_user = "Admin"
        self.current_theme = "modern"
        
        # Setup styles
        self.setup_styles()
        
        # Create interface
        self.create_advanced_interface()
        
        # Start background tasks
        self.start_background_tasks()
        
        print("✅ تم تحميل النظام المتقدم بنجاح!")
        print("✅ Advanced system loaded successfully!")
    
    def setup_styles(self):
        """Setup advanced styles and themes"""
        self.style = ttk.Style()
        self.style.theme_use('clam')
        
        # Configure advanced styles
        self.style.configure("Title.TLabel", font=('Arial', 16, 'bold'), foreground='#1f2937')
        self.style.configure("Subtitle.TLabel", font=('Arial', 12), foreground='#6b7280')
        self.style.configure("Card.TFrame", relief='solid', borderwidth=1, background='white')
        
        # Treeview styles
        self.style.configure("Advanced.Treeview", 
                           background="#ffffff",
                           foreground="#1f2937",
                           rowheight=30,
                           fieldbackground="#ffffff")
        self.style.configure("Advanced.Treeview.Heading",
                           background="#f3f4f6",
                           foreground="#374151",
                           font=('Arial', 10, 'bold'))
        
        # Button styles
        self.style.configure("Primary.TButton", font=('Arial', 10, 'bold'))
        self.style.configure("Success.TButton", font=('Arial', 10, 'bold'))
        self.style.configure("Warning.TButton", font=('Arial', 10, 'bold'))
        self.style.configure("Danger.TButton", font=('Arial', 10, 'bold'))

    def create_advanced_interface(self):
        """Create advanced interface with modern design"""

        # Create main header
        self.create_header()

        # Create main container
        main_container = tk.Frame(self.root, bg='#f8fafc')
        main_container.pack(fill='both', expand=True, padx=5, pady=5)

        # Create sidebar
        self.create_sidebar(main_container)

        # Create content area
        self.content_frame = tk.Frame(main_container, bg='white', relief='flat', bd=1)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))

        # Create status bar
        self.create_status_bar()

        # Show dashboard by default
        self.show_dashboard()

        # Show welcome message
        self.root.after(2000, self.show_welcome)

    def create_header(self):
        """Create advanced header with company info"""
        header_frame = tk.Frame(self.root, bg='#1e40af', height=90)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Left side - Company info
        left_frame = tk.Frame(header_frame, bg='#1e40af')
        left_frame.pack(side='left', fill='y', padx=20, pady=10)

        # Company logo/title
        title_label = tk.Label(
            left_frame,
            text="🏢 نظام ProTech المتقدم للمحاسبة",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#1e40af'
        )
        title_label.pack(anchor='w')

        subtitle_label = tk.Label(
            left_frame,
            text="ProTech Advanced Accounting System | نظام محاسبة شامل ومتقدم",
            font=('Arial', 11),
            fg='#bfdbfe',
            bg='#1e40af'
        )
        subtitle_label.pack(anchor='w')

        # Right side - User info and controls
        right_frame = tk.Frame(header_frame, bg='#1e40af')
        right_frame.pack(side='right', fill='y', padx=20, pady=10)

        # User info
        user_frame = tk.Frame(right_frame, bg='#1e40af')
        user_frame.pack(side='top', anchor='e')

        tk.Label(
            user_frame,
            text=f"👤 المستخدم: {self.current_user} | User: {self.current_user}",
            font=('Arial', 10, 'bold'),
            fg='white',
            bg='#1e40af'
        ).pack(side='left', padx=5)

        # Time and date
        self.time_label = tk.Label(
            user_frame,
            text="",
            font=('Arial', 10),
            fg='#86efac',
            bg='#1e40af'
        )
        self.time_label.pack(side='left', padx=10)

        # Quick actions
        actions_frame = tk.Frame(right_frame, bg='#1e40af')
        actions_frame.pack(side='bottom', anchor='e')

        quick_actions = [
            ("💾 نسخ احتياطي", self.backup_data),
            ("📊 تقرير سريع", self.quick_report),
            ("⚙️ إعدادات", self.show_settings)
        ]

        for text, command in quick_actions:
            btn = tk.Button(
                actions_frame,
                text=text,
                font=('Arial', 8),
                bg='#3b82f6',
                fg='white',
                relief='flat',
                command=command,
                cursor='hand2'
            )
            btn.pack(side='left', padx=2)

    def create_sidebar(self, parent):
        """Create advanced sidebar with categorized menus"""
        sidebar = tk.Frame(parent, bg='#2563eb', width=250)
        sidebar.pack(side='left', fill='y')
        sidebar.pack_propagate(False)

        # Sidebar header
        sidebar_header = tk.Frame(sidebar, bg='#1d4ed8', height=60)
        sidebar_header.pack(fill='x')
        sidebar_header.pack_propagate(False)

        tk.Label(
            sidebar_header,
            text="📋 القوائم الرئيسية\nMain Navigation",
            font=('Arial', 12, 'bold'),
            fg='white',
            bg='#1d4ed8'
        ).pack(expand=True)

        # Create scrollable frame for menu items
        canvas = tk.Canvas(sidebar, bg='#2563eb', highlightthickness=0)
        scrollbar = ttk.Scrollbar(sidebar, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg='#2563eb')

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Menu categories
        menu_categories = [
            ("📊 التحليلات والتقارير", "Analytics & Reports", [
                ("🏠", "لوحة التحكم", "Dashboard", self.show_dashboard, "#3b82f6"),
                ("📈", "التقارير المالية", "Financial Reports", self.show_financial_reports, "#8b5cf6"),
                ("📊", "تحليل المبيعات", "Sales Analytics", self.show_sales_analytics, "#10b981"),
                ("📉", "تحليل المخزون", "Inventory Analytics", self.show_inventory_analytics, "#f59e0b")
            ]),
            ("💼 إدارة المبيعات", "Sales Management", [
                ("💰", "المبيعات", "Sales", self.show_sales, "#10b981"),
                ("🧾", "الفواتير", "Invoices", self.show_invoices, "#3b82f6"),
                ("💳", "المدفوعات", "Payments", self.show_payments, "#8b5cf6"),
                ("🎯", "عروض الأسعار", "Quotations", self.show_quotations, "#f59e0b")
            ]),
            ("📦 إدارة المخزون", "Inventory Management", [
                ("📦", "المخزون", "Inventory", self.show_inventory, "#3b82f6"),
                ("📥", "المشتريات", "Purchases", self.show_purchases, "#10b981"),
                ("🏷️", "الفئات", "Categories", self.show_categories, "#8b5cf6"),
                ("📊", "حركة المخزون", "Stock Movement", self.show_stock_movement, "#f59e0b")
            ]),
            ("👥 إدارة العلاقات", "Relationship Management", [
                ("👥", "العملاء", "Customers", self.show_customers, "#10b981"),
                ("🏢", "الموردين", "Suppliers", self.show_suppliers, "#3b82f6"),
                ("📞", "جهات الاتصال", "Contacts", self.show_contacts, "#8b5cf6")
            ]),
            ("⚙️ النظام والإعدادات", "System & Settings", [
                ("⚙️", "الإعدادات", "Settings", self.show_settings, "#6b7280"),
                ("👤", "المستخدمين", "Users", self.show_users, "#8b5cf6"),
                ("🔒", "الصلاحيات", "Permissions", self.show_permissions, "#ef4444"),
                ("💾", "النسخ الاحتياطي", "Backup", self.show_backup, "#10b981"),
                ("❓", "المساعدة", "Help", self.show_help, "#14b8a6")
            ])
        ]

        for category_ar, category_en, items in menu_categories:
            # Category header
            category_frame = tk.Frame(scrollable_frame, bg='#1d4ed8')
            category_frame.pack(fill='x', pady=(10, 5), padx=5)

            tk.Label(
                category_frame,
                text=f"{category_ar}\n{category_en}",
                font=('Arial', 9, 'bold'),
                fg='white',
                bg='#1d4ed8',
                justify='center'
            ).pack(pady=5)

            # Category items
            for icon, ar_text, en_text, command, color in items:
                self.create_menu_button(scrollable_frame, icon, ar_text, en_text, command, color)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

    def create_menu_button(self, parent, icon, ar_text, en_text, command, color):
        """Create styled menu button"""
        btn_frame = tk.Frame(parent, bg='#2563eb')
        btn_frame.pack(fill='x', padx=8, pady=1)

        btn = tk.Button(
            btn_frame,
            text=f"{icon} {ar_text}\n{en_text}",
            font=('Arial', 9, 'bold'),
            fg='white',
            bg=color,
            activebackground='#1d4ed8',
            activeforeground='white',
            relief='flat',
            height=3,
            command=command,
            cursor='hand2'
        )
        btn.pack(fill='x')

        # Add hover effects
        def on_enter(e):
            btn.config(bg='#1d4ed8')

        def on_leave(e):
            btn.config(bg=color)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

    def create_status_bar(self):
        """Create advanced status bar"""
        status_frame = tk.Frame(self.root, bg='#374151', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        # Left side - Status message
        self.status_label = tk.Label(
            status_frame,
            text="جاهز / Ready",
            font=('Arial', 9),
            fg='white',
            bg='#374151'
        )
        self.status_label.pack(side='left', padx=10, pady=5)

        # Center - Connection status
        self.connection_label = tk.Label(
            status_frame,
            text="🟢 متصل / Connected",
            font=('Arial', 9),
            fg='#86efac',
            bg='#374151'
        )
        self.connection_label.pack(side='left', padx=20)

        # Right side - System info
        system_info = tk.Label(
            status_frame,
            text="ProTech Advanced v2.0 | © 2024 | قاعدة البيانات: SQLite",
            font=('Arial', 8),
            fg='#9ca3af',
            bg='#374151'
        )
        system_info.pack(side='right', padx=10, pady=5)

    def start_background_tasks(self):
        """Start background tasks for real-time updates"""
        def update_time():
            while True:
                try:
                    current_time = datetime.now().strftime('%🕒 %H:%M:%S | %Y-%m-%d')
                    if hasattr(self, 'time_label'):
                        self.root.after(0, lambda: self.time_label.config(text=current_time))
                    time.sleep(1)
                except:
                    break

        # Start time update thread
        time_thread = threading.Thread(target=update_time, daemon=True)
        time_thread.start()

    def clear_content(self):
        """Clear content area"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def update_status(self, message, color='white'):
        """Update status bar message"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        self.status_label.config(text=f"[{timestamp}] {message}", fg=color)
        self.root.update_idletasks()

    def show_welcome(self):
        """Show advanced welcome message"""
        welcome_msg = """
🎉 مرحباً بك في نظام ProTech المتقدم للمحاسبة!
Welcome to ProTech Advanced Accounting System!

🚀 النسخة المتقدمة تتضمن:
Advanced version includes:

✅ إدارة شاملة للمبيعات والمشتريات
✅ نظام فوترة متقدم
✅ تقارير مالية مفصلة
✅ إدارة متقدمة للمخزون
✅ نظام إدارة العملاء والموردين
✅ تحليلات ذكية للأعمال
✅ نظام صلاحيات متقدم
✅ نسخ احتياطي تلقائي

🎯 جاهز للاستخدام المتقدم!
Ready for advanced usage!
        """

        messagebox.showinfo("مرحباً / Welcome", welcome_msg)

    def show_dashboard(self):
        """Show advanced dashboard with comprehensive analytics"""
        self.clear_content()
        self.update_status("تحميل لوحة التحكم المتقدمة... / Loading advanced dashboard...")

        # Create dashboard header
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=15)

        tk.Label(
            header_frame,
            text="📊 لوحة التحكم المتقدمة / Advanced Dashboard",
            font=('Arial', 22, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Refresh button
        tk.Button(
            header_frame,
            text="🔄 تحديث البيانات / Refresh Data",
            font=('Arial', 10, 'bold'),
            bg='#3b82f6',
            fg='white',
            command=self.refresh_dashboard,
            cursor='hand2'
        ).pack(side='right')

        # Create main dashboard container
        dashboard_container = tk.Frame(self.content_frame, bg='white')
        dashboard_container.pack(fill='both', expand=True, padx=20, pady=10)

        # Load dashboard data in background
        threading.Thread(target=self.load_dashboard_data, args=(dashboard_container,), daemon=True).start()

        # Show loading indicator
        loading_frame = tk.Frame(dashboard_container, bg='white')
        loading_frame.pack(fill='both', expand=True)

        tk.Label(
            loading_frame,
            text="⏳ جاري تحميل البيانات... / Loading data...",
            font=('Arial', 16),
            fg='#6b7280',
            bg='white'
        ).pack(expand=True)

    def load_dashboard_data(self, container):
        """Load dashboard data in background"""
        try:
            # Get comprehensive statistics
            with self.db.get_connection() as conn:
                # Products statistics
                product_stats = conn.execute('''
                    SELECT
                        COUNT(*) as total_products,
                        SUM(CASE WHEN stock <= min_stock THEN 1 ELSE 0 END) as low_stock,
                        SUM(CASE WHEN stock = 0 THEN 1 ELSE 0 END) as out_of_stock,
                        SUM(stock * price) as inventory_value,
                        AVG(price) as avg_price
                    FROM products WHERE is_active = 1
                ''').fetchone()

                # Sales statistics
                sales_stats = conn.execute('''
                    SELECT
                        COUNT(*) as total_sales,
                        SUM(total_amount) as total_revenue,
                        SUM(paid_amount) as total_paid,
                        SUM(balance_due) as total_due,
                        AVG(total_amount) as avg_sale
                    FROM sales
                    WHERE date(sale_date) >= date('now', '-30 days')
                ''').fetchone()

                # Customer statistics
                customer_stats = conn.execute('''
                    SELECT
                        COUNT(*) as total_customers,
                        SUM(balance) as total_balance,
                        COUNT(CASE WHEN category = 'RETAIL' THEN 1 END) as retail_customers,
                        COUNT(CASE WHEN category = 'WHOLESALE' THEN 1 END) as wholesale_customers
                    FROM customers WHERE is_active = 1
                ''').fetchone()

                # Recent activities
                recent_sales = conn.execute('''
                    SELECT s.invoice_number, c.name, s.total_amount, s.sale_date
                    FROM sales s
                    LEFT JOIN customers c ON s.customer_id = c.id
                    ORDER BY s.created_at DESC
                    LIMIT 5
                ''').fetchall()

                # Low stock products
                low_stock_products = conn.execute('''
                    SELECT name, name_ar, stock, min_stock
                    FROM products
                    WHERE stock <= min_stock AND is_active = 1
                    ORDER BY stock ASC
                    LIMIT 10
                ''').fetchall()

            # Update UI in main thread
            self.root.after(0, lambda: self.display_dashboard(
                container, product_stats, sales_stats, customer_stats, recent_sales, low_stock_products
            ))

        except Exception as e:
            self.root.after(0, lambda: self.show_error(f"خطأ في تحميل البيانات: {str(e)}"))

    def display_dashboard(self, container, product_stats, sales_stats, customer_stats, recent_sales, low_stock_products):
        """Display dashboard with loaded data"""
        # Clear loading indicator
        for widget in container.winfo_children():
            widget.destroy()

        # Create statistics cards
        stats_frame = tk.Frame(container, bg='white')
        stats_frame.pack(fill='x', pady=10)

        # Statistics cards data
        stats_cards = [
            ("📦", "إجمالي المنتجات", "Total Products", product_stats[0], "#3b82f6"),
            ("⚠️", "مخزون منخفض", "Low Stock", product_stats[1], "#ef4444"),
            ("💰", "قيمة المخزون", "Inventory Value", f"${product_stats[3]:,.0f}" if product_stats[3] else "$0", "#8b5cf6"),
            ("👥", "العملاء", "Customers", customer_stats[0], "#10b981"),
            ("🧾", "مبيعات الشهر", "Monthly Sales", sales_stats[0] or 0, "#f59e0b"),
            ("💵", "إيرادات الشهر", "Monthly Revenue", f"${sales_stats[1]:,.0f}" if sales_stats[1] else "$0", "#06b6d4")
        ]

        # Create cards in grid layout
        for i, (icon, ar_title, en_title, value, color) in enumerate(stats_cards):
            row = i // 3
            col = i % 3

            card_frame = tk.Frame(stats_frame, bg='white')
            card_frame.grid(row=row, column=col, padx=10, pady=10, sticky='ew')

            card = tk.Frame(card_frame, bg=color, relief='raised', bd=2)
            card.pack(fill='both', expand=True, ipadx=20, ipady=15)

            # Icon
            tk.Label(
                card,
                text=icon,
                font=('Arial', 28),
                fg='white',
                bg=color
            ).pack(pady=(10, 5))

            # Title
            tk.Label(
                card,
                text=f"{ar_title}\n{en_title}",
                font=('Arial', 11, 'bold'),
                fg='white',
                bg=color,
                justify='center'
            ).pack()

            # Value
            tk.Label(
                card,
                text=str(value),
                font=('Arial', 18, 'bold'),
                fg='white',
                bg=color
            ).pack(pady=(5, 10))

        # Configure grid weights
        for i in range(3):
            stats_frame.columnconfigure(i, weight=1)

        # Create bottom section with recent activities and alerts
        bottom_frame = tk.Frame(container, bg='white')
        bottom_frame.pack(fill='both', expand=True, pady=10)

        # Recent sales section
        recent_frame = tk.LabelFrame(
            bottom_frame,
            text="🧾 المبيعات الحديثة / Recent Sales",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        recent_frame.pack(side='left', fill='both', expand=True, padx=(0, 10))

        if recent_sales:
            for sale in recent_sales:
                sale_item = tk.Frame(recent_frame, bg='#f9fafb', relief='ridge', bd=1)
                sale_item.pack(fill='x', padx=10, pady=5)

                tk.Label(
                    sale_item,
                    text=f"🧾 {sale[0]} | {sale[1] or 'عميل مباشر'} | ${sale[2]:,.2f}",
                    font=('Arial', 10),
                    fg='#1f2937',
                    bg='#f9fafb'
                ).pack(side='left', padx=10, pady=5)

                tk.Label(
                    sale_item,
                    text=sale[3][:10],
                    font=('Arial', 9),
                    fg='#6b7280',
                    bg='#f9fafb'
                ).pack(side='right', padx=10, pady=5)
        else:
            tk.Label(
                recent_frame,
                text="لا توجد مبيعات حديثة\nNo recent sales",
                font=('Arial', 12),
                fg='#6b7280',
                bg='white'
            ).pack(expand=True, pady=20)

        # Low stock alerts section
        alerts_frame = tk.LabelFrame(
            bottom_frame,
            text="⚠️ تنبيهات المخزون / Stock Alerts",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        alerts_frame.pack(side='right', fill='both', expand=True, padx=(10, 0))

        if low_stock_products:
            for product in low_stock_products:
                alert_item = tk.Frame(alerts_frame, bg='#fef3c7', relief='ridge', bd=1)
                alert_item.pack(fill='x', padx=10, pady=5)

                tk.Label(
                    alert_item,
                    text=f"⚠️ {product[0]} ({product[1]})",
                    font=('Arial', 10, 'bold'),
                    fg='#d97706',
                    bg='#fef3c7'
                ).pack(side='left', padx=10, pady=5)

                tk.Label(
                    alert_item,
                    text=f"المخزون: {product[2]} | الحد الأدنى: {product[3]}",
                    font=('Arial', 9),
                    fg='#92400e',
                    bg='#fef3c7'
                ).pack(side='right', padx=10, pady=5)
        else:
            tk.Label(
                alerts_frame,
                text="✅ جميع المنتجات لديها مخزون كافي\nAll products have sufficient stock",
                font=('Arial', 12),
                fg='#10b981',
                bg='white'
            ).pack(expand=True, pady=20)

        self.update_status("تم تحميل لوحة التحكم / Dashboard loaded", '#86efac')

    def refresh_dashboard(self):
        """Refresh dashboard data"""
        self.show_dashboard()

    def show_sales(self):
        """Show advanced sales management"""
        self.clear_content()
        self.update_status("عرض إدارة المبيعات / Showing sales management")

        # Header
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=15)

        tk.Label(
            header_frame,
            text="💰 إدارة المبيعات المتقدمة / Advanced Sales Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Control buttons
        controls_frame = tk.Frame(header_frame, bg='white')
        controls_frame.pack(side='right')

        control_buttons = [
            ("➕ فاتورة جديدة", self.new_sale, "#10b981"),
            ("📋 عرض الفواتير", self.show_invoices, "#3b82f6"),
            ("💳 المدفوعات", self.show_payments, "#8b5cf6"),
            ("📊 تقرير المبيعات", self.sales_report, "#f59e0b")
        ]

        for text, command, color in control_buttons:
            tk.Button(
                controls_frame,
                text=text,
                font=('Arial', 10, 'bold'),
                bg=color,
                fg='white',
                command=command,
                cursor='hand2'
            ).pack(side='left', padx=5)

        # Sales summary cards
        summary_frame = tk.Frame(self.content_frame, bg='white')
        summary_frame.pack(fill='x', padx=20, pady=10)

        # Load sales summary
        self.load_sales_summary(summary_frame)

        # Sales table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create sales table
        self.create_sales_table(table_frame)

    def load_sales_summary(self, parent):
        """Load sales summary cards"""
        try:
            with self.db.get_connection() as conn:
                # Today's sales
                today_sales = conn.execute('''
                    SELECT COUNT(*), COALESCE(SUM(total_amount), 0)
                    FROM sales WHERE date(sale_date) = date('now')
                ''').fetchone()

                # This month's sales
                month_sales = conn.execute('''
                    SELECT COUNT(*), COALESCE(SUM(total_amount), 0)
                    FROM sales WHERE strftime('%Y-%m', sale_date) = strftime('%Y-%m', 'now')
                ''').fetchone()

                # Pending payments
                pending_payments = conn.execute('''
                    SELECT COUNT(*), COALESCE(SUM(balance_due), 0)
                    FROM sales WHERE balance_due > 0
                ''').fetchone()

            # Create summary cards
            summary_cards = [
                ("📅", "مبيعات اليوم", "Today's Sales", f"{today_sales[0]} | ${today_sales[1]:,.0f}", "#10b981"),
                ("📊", "مبيعات الشهر", "Monthly Sales", f"{month_sales[0]} | ${month_sales[1]:,.0f}", "#3b82f6"),
                ("⏳", "مدفوعات معلقة", "Pending Payments", f"{pending_payments[0]} | ${pending_payments[1]:,.0f}", "#ef4444")
            ]

            for icon, ar_title, en_title, value, color in summary_cards:
                card = tk.Frame(parent, bg=color, relief='raised', bd=2)
                card.pack(side='left', fill='both', expand=True, padx=5, pady=5)

                tk.Label(card, text=icon, font=('Arial', 20), fg='white', bg=color).pack(pady=5)
                tk.Label(card, text=f"{ar_title}\n{en_title}", font=('Arial', 10, 'bold'), fg='white', bg=color).pack()
                tk.Label(card, text=value, font=('Arial', 12, 'bold'), fg='white', bg=color).pack(pady=5)

        except Exception as e:
            self.show_error(f"خطأ في تحميل ملخص المبيعات: {str(e)}")

    def create_sales_table(self, parent):
        """Create advanced sales table"""
        # Table header
        tk.Label(
            parent,
            text="📋 قائمة المبيعات / Sales List",
            font=('Arial', 14, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=10)

        # Create treeview
        columns = ('Invoice', 'Customer', 'Date', 'Amount', 'Paid', 'Balance', 'Status')
        self.sales_tree = ttk.Treeview(parent, columns=columns, show='headings', height=15, style="Advanced.Treeview")

        # Configure columns
        column_configs = [
            ('Invoice', 'رقم الفاتورة', 120),
            ('Customer', 'العميل', 150),
            ('Date', 'التاريخ', 100),
            ('Amount', 'المبلغ', 100),
            ('Paid', 'المدفوع', 100),
            ('Balance', 'الرصيد', 100),
            ('Status', 'الحالة', 100)
        ]

        for col, ar_header, width in column_configs:
            self.sales_tree.heading(col, text=ar_header)
            self.sales_tree.column(col, width=width, anchor='center')

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(parent, orient='vertical', command=self.sales_tree.yview)
        h_scrollbar = ttk.Scrollbar(parent, orient='horizontal', command=self.sales_tree.xview)

        self.sales_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack widgets
        self.sales_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')

        # Load sales data
        self.load_sales_data()

    def load_sales_data(self):
        """Load sales data into table"""
        try:
            # Clear existing data
            for item in self.sales_tree.get_children():
                self.sales_tree.delete(item)

            with self.db.get_connection() as conn:
                sales = conn.execute('''
                    SELECT s.invoice_number, c.name, s.sale_date, s.total_amount,
                           s.paid_amount, s.balance_due, s.status
                    FROM sales s
                    LEFT JOIN customers c ON s.customer_id = c.id
                    ORDER BY s.created_at DESC
                    LIMIT 100
                ''').fetchall()

                for sale in sales:
                    # Determine status color
                    status = sale[6] or 'PENDING'
                    if status == 'PAID':
                        tags = ('paid',)
                    elif status == 'PENDING':
                        tags = ('pending',)
                    else:
                        tags = ('overdue',)

                    self.sales_tree.insert('', 'end', values=(
                        sale[0],  # Invoice number
                        sale[1] or 'عميل مباشر',  # Customer name
                        sale[2][:10],  # Date
                        f"${sale[3]:,.2f}",  # Total amount
                        f"${sale[4]:,.2f}",  # Paid amount
                        f"${sale[5]:,.2f}",  # Balance due
                        status
                    ), tags=tags)

            # Configure tags
            self.sales_tree.tag_configure('paid', background='#dcfce7', foreground='#16a34a')
            self.sales_tree.tag_configure('pending', background='#fef3c7', foreground='#d97706')
            self.sales_tree.tag_configure('overdue', background='#fee2e2', foreground='#dc2626')

        except Exception as e:
            self.show_error(f"خطأ في تحميل بيانات المبيعات: {str(e)}")

    # Placeholder methods for other functions
    def new_sale(self):
        """Create new sale"""
        messagebox.showinfo("قيد التطوير", "نافذة إنشاء فاتورة جديدة قيد التطوير\nNew invoice window under development")

    def show_invoices(self):
        """Show invoices"""
        messagebox.showinfo("قيد التطوير", "عرض الفواتير قيد التطوير\nInvoices view under development")

    def show_payments(self):
        """Show payments"""
        messagebox.showinfo("قيد التطوير", "عرض المدفوعات قيد التطوير\nPayments view under development")

    def sales_report(self):
        """Generate sales report"""
        messagebox.showinfo("قيد التطوير", "تقرير المبيعات قيد التطوير\nSales report under development")

    def show_error(self, message):
        """Show error message"""
        messagebox.showerror("خطأ / Error", message)

    # Advanced inventory management
    def show_inventory(self):
        """Show advanced inventory management"""
        self.clear_content()
        self.update_status("عرض إدارة المخزون المتقدمة / Showing advanced inventory")

        # Header with search and filters
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=15)

        tk.Label(
            header_frame,
            text="📦 إدارة المخزون المتقدمة / Advanced Inventory Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Search and filter frame
        search_frame = tk.Frame(header_frame, bg='white')
        search_frame.pack(side='right')

        # Search box
        tk.Label(search_frame, text="🔍 بحث:", font=('Arial', 10), bg='white').pack(side='left')
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, font=('Arial', 10), width=20)
        search_entry.pack(side='left', padx=5)
        search_entry.bind('<KeyRelease>', self.on_inventory_search)

        # Filter by category
        tk.Label(search_frame, text="فئة:", font=('Arial', 10), bg='white').pack(side='left', padx=(10, 0))
        self.category_var = tk.StringVar()
        category_combo = ttk.Combobox(search_frame, textvariable=self.category_var, width=15, state='readonly')
        category_combo['values'] = ['الكل', 'Electronics', 'Office Supplies', 'Furniture', 'Software', 'Hardware']
        category_combo.set('الكل')
        category_combo.pack(side='left', padx=5)
        category_combo.bind('<<ComboboxSelected>>', self.on_category_filter)

        # Control buttons
        controls_frame = tk.Frame(self.content_frame, bg='white')
        controls_frame.pack(fill='x', padx=20, pady=5)

        control_buttons = [
            ("➕ إضافة منتج", self.add_product, "#10b981"),
            ("✏️ تعديل", self.edit_product, "#f59e0b"),
            ("🗑️ حذف", self.delete_product, "#ef4444"),
            ("📊 تقرير المخزون", self.inventory_report, "#8b5cf6"),
            ("📥 استيراد", self.import_products, "#3b82f6"),
            ("📤 تصدير", self.export_products, "#6b7280")
        ]

        for text, command, color in control_buttons:
            tk.Button(
                controls_frame,
                text=text,
                font=('Arial', 10, 'bold'),
                bg=color,
                fg='white',
                command=command,
                cursor='hand2'
            ).pack(side='left', padx=5)

        # Inventory summary
        self.create_inventory_summary()

        # Products table
        self.create_products_table()

        # Load initial data
        self.load_products_data()

    def create_inventory_summary(self):
        """Create inventory summary cards"""
        summary_frame = tk.Frame(self.content_frame, bg='white')
        summary_frame.pack(fill='x', padx=20, pady=10)

        try:
            with self.db.get_connection() as conn:
                stats = conn.execute('''
                    SELECT
                        COUNT(*) as total,
                        SUM(CASE WHEN stock <= min_stock THEN 1 ELSE 0 END) as low_stock,
                        SUM(CASE WHEN stock = 0 THEN 1 ELSE 0 END) as out_of_stock,
                        SUM(stock * price) as total_value,
                        SUM(stock) as total_quantity
                    FROM products WHERE is_active = 1
                ''').fetchone()

                summary_cards = [
                    ("📦", "إجمالي المنتجات", f"{stats[0]}", "#3b82f6"),
                    ("⚠️", "مخزون منخفض", f"{stats[1]}", "#ef4444"),
                    ("🚫", "نفد المخزون", f"{stats[2]}", "#dc2626"),
                    ("💰", "قيمة المخزون", f"${stats[3]:,.0f}" if stats[3] else "$0", "#8b5cf6"),
                    ("📊", "إجمالي الكمية", f"{stats[4]:,}" if stats[4] else "0", "#10b981")
                ]

                for icon, title, value, color in summary_cards:
                    card = tk.Frame(summary_frame, bg=color, relief='raised', bd=2)
                    card.pack(side='left', fill='both', expand=True, padx=5)

                    tk.Label(card, text=icon, font=('Arial', 16), fg='white', bg=color).pack(pady=2)
                    tk.Label(card, text=title, font=('Arial', 9, 'bold'), fg='white', bg=color).pack()
                    tk.Label(card, text=value, font=('Arial', 12, 'bold'), fg='white', bg=color).pack(pady=2)

        except Exception as e:
            self.show_error(f"خطأ في تحميل ملخص المخزون: {str(e)}")

    def create_products_table(self):
        """Create advanced products table"""
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Table header
        tk.Label(
            table_frame,
            text="📋 قائمة المنتجات / Products List",
            font=('Arial', 14, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=5)

        # Create treeview
        columns = ('Code', 'Name', 'Name_AR', 'Category', 'Brand', 'Price', 'Cost', 'Stock', 'Min_Stock', 'Status')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15, style="Advanced.Treeview")

        # Configure columns
        column_configs = [
            ('Code', 'الكود', 80),
            ('Name', 'الاسم', 120),
            ('Name_AR', 'الاسم بالعربية', 120),
            ('Category', 'الفئة', 100),
            ('Brand', 'الماركة', 80),
            ('Price', 'السعر', 80),
            ('Cost', 'التكلفة', 80),
            ('Stock', 'المخزون', 70),
            ('Min_Stock', 'الحد الأدنى', 80),
            ('Status', 'الحالة', 80)
        ]

        for col, ar_header, width in column_configs:
            self.products_tree.heading(col, text=ar_header)
            self.products_tree.column(col, width=width, anchor='center')

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.products_tree.xview)

        self.products_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack widgets
        self.products_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')

    def load_products_data(self, search_term=None, category_filter=None):
        """Load products data with filtering"""
        try:
            # Clear existing data
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            with self.db.get_connection() as conn:
                query = '''
                    SELECT code, name, name_ar, category, brand, price, cost_price,
                           stock, min_stock, is_active
                    FROM products
                    WHERE is_active = 1
                '''
                params = []

                if search_term:
                    query += ' AND (name LIKE ? OR name_ar LIKE ? OR code LIKE ? OR category LIKE ?)'
                    search_pattern = f'%{search_term}%'
                    params.extend([search_pattern] * 4)

                if category_filter and category_filter != 'الكل':
                    query += ' AND category = ?'
                    params.append(category_filter)

                query += ' ORDER BY name'

                products = conn.execute(query, params).fetchall()

                for product in products:
                    # Determine status and color
                    stock = product[7]
                    min_stock = product[8]

                    if stock == 0:
                        status = "نفد"
                        tags = ('out_of_stock',)
                    elif stock <= min_stock:
                        status = "منخفض"
                        tags = ('low_stock',)
                    else:
                        status = "جيد"
                        tags = ('good_stock',)

                    self.products_tree.insert('', 'end', values=(
                        product[0],  # Code
                        product[1],  # Name
                        product[2],  # Name AR
                        product[3],  # Category
                        product[4] or '',  # Brand
                        f"${product[5]:,.2f}",  # Price
                        f"${product[6]:,.2f}",  # Cost
                        product[7],  # Stock
                        product[8],  # Min stock
                        status
                    ), tags=tags)

            # Configure tags
            self.products_tree.tag_configure('out_of_stock', background='#fee2e2', foreground='#dc2626')
            self.products_tree.tag_configure('low_stock', background='#fef3c7', foreground='#d97706')
            self.products_tree.tag_configure('good_stock', background='#dcfce7', foreground='#16a34a')

        except Exception as e:
            self.show_error(f"خطأ في تحميل بيانات المنتجات: {str(e)}")

    def on_inventory_search(self, event):
        """Handle inventory search"""
        search_term = self.search_var.get().strip()
        category_filter = self.category_var.get()
        self.load_products_data(search_term if search_term else None, category_filter)

    def on_category_filter(self, event):
        """Handle category filter"""
        search_term = self.search_var.get().strip()
        category_filter = self.category_var.get()
        self.load_products_data(search_term if search_term else None, category_filter)

    # Placeholder methods for advanced features
    def add_product(self):
        """Add new product"""
        messagebox.showinfo("قيد التطوير", "نافذة إضافة منتج متقدمة قيد التطوير\nAdvanced add product window under development")

    def edit_product(self):
        """Edit selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للتعديل\nPlease select a product to edit")
            return
        messagebox.showinfo("قيد التطوير", "نافذة تعديل المنتج قيد التطوير\nEdit product window under development")

    def delete_product(self):
        """Delete selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج للحذف\nPlease select a product to delete")
            return
        messagebox.showinfo("قيد التطوير", "حذف المنتج قيد التطوير\nDelete product under development")

    def inventory_report(self):
        """Generate inventory report"""
        messagebox.showinfo("قيد التطوير", "تقرير المخزون قيد التطوير\nInventory report under development")

    def import_products(self):
        """Import products from file"""
        messagebox.showinfo("قيد التطوير", "استيراد المنتجات قيد التطوير\nImport products under development")

    def export_products(self):
        """Export products to file"""
        messagebox.showinfo("قيد التطوير", "تصدير المنتجات قيد التطوير\nExport products under development")

    # Customer management
    def show_customers(self):
        """Show advanced customer management"""
        self.clear_content()
        self.update_status("عرض إدارة العملاء / Showing customer management")

        tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء المتقدمة / Advanced Customer Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=50)

        tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير / Under Development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack()

    # Placeholder methods for other advanced features
    def show_suppliers(self):
        """Show suppliers management"""
        self.clear_content()
        self.update_status("عرض إدارة الموردين / Showing suppliers")
        tk.Label(self.content_frame, text="🏢 إدارة الموردين قيد التطوير\nSuppliers management under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_purchases(self):
        """Show purchases management"""
        self.clear_content()
        self.update_status("عرض إدارة المشتريات / Showing purchases")
        tk.Label(self.content_frame, text="📥 إدارة المشتريات قيد التطوير\nPurchases management under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_categories(self):
        """Show categories management"""
        self.clear_content()
        self.update_status("عرض إدارة الفئات / Showing categories")
        tk.Label(self.content_frame, text="🏷️ إدارة الفئات قيد التطوير\nCategories management under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_financial_reports(self):
        """Show financial reports"""
        self.clear_content()
        self.update_status("عرض التقارير المالية / Showing financial reports")
        tk.Label(self.content_frame, text="📈 التقارير المالية قيد التطوير\nFinancial reports under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_sales_analytics(self):
        """Show sales analytics"""
        self.clear_content()
        self.update_status("عرض تحليل المبيعات / Showing sales analytics")
        tk.Label(self.content_frame, text="📊 تحليل المبيعات قيد التطوير\nSales analytics under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_inventory_analytics(self):
        """Show inventory analytics"""
        self.clear_content()
        self.update_status("عرض تحليل المخزون / Showing inventory analytics")
        tk.Label(self.content_frame, text="📉 تحليل المخزون قيد التطوير\nInventory analytics under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_quotations(self):
        """Show quotations"""
        self.clear_content()
        self.update_status("عرض عروض الأسعار / Showing quotations")
        tk.Label(self.content_frame, text="🎯 عروض الأسعار قيد التطوير\nQuotations under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_stock_movement(self):
        """Show stock movement"""
        self.clear_content()
        self.update_status("عرض حركة المخزون / Showing stock movement")
        tk.Label(self.content_frame, text="📊 حركة المخزون قيد التطوير\nStock movement under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_contacts(self):
        """Show contacts"""
        self.clear_content()
        self.update_status("عرض جهات الاتصال / Showing contacts")
        tk.Label(self.content_frame, text="📞 جهات الاتصال قيد التطوير\nContacts under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_settings(self):
        """Show system settings"""
        self.clear_content()
        self.update_status("عرض الإعدادات / Showing settings")
        tk.Label(self.content_frame, text="⚙️ الإعدادات قيد التطوير\nSettings under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_users(self):
        """Show users management"""
        self.clear_content()
        self.update_status("عرض إدارة المستخدمين / Showing users")
        tk.Label(self.content_frame, text="👤 إدارة المستخدمين قيد التطوير\nUsers management under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_permissions(self):
        """Show permissions management"""
        self.clear_content()
        self.update_status("عرض إدارة الصلاحيات / Showing permissions")
        tk.Label(self.content_frame, text="🔒 إدارة الصلاحيات قيد التطوير\nPermissions management under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_backup(self):
        """Show backup management"""
        self.clear_content()
        self.update_status("عرض إدارة النسخ الاحتياطي / Showing backup")
        tk.Label(self.content_frame, text="💾 إدارة النسخ الاحتياطي قيد التطوير\nBackup management under development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_help(self):
        """Show help and documentation"""
        self.clear_content()
        self.update_status("عرض المساعدة / Showing help")

        # Help content
        help_frame = tk.Frame(self.content_frame, bg='white')
        help_frame.pack(fill='both', expand=True, padx=20, pady=20)

        tk.Label(
            help_frame,
            text="❓ المساعدة والدعم / Help & Support",
            font=('Arial', 20, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=15)

        help_text = tk.Text(help_frame, font=('Arial', 11), bg='#f9fafb', wrap='word')
        help_text.pack(fill='both', expand=True)

        help_content = """
🚀 نظام ProTech المتقدم للمحاسبة
ProTech Advanced Accounting System

📋 المميزات المتقدمة / Advanced Features:

✅ لوحة تحكم شاملة مع تحليلات متقدمة
✅ إدارة مبيعات متكاملة مع نظام فوترة
✅ إدارة مخزون متقدمة مع تتبع الحركة
✅ إدارة العملاء والموردين
✅ تقارير مالية مفصلة وتحليلات ذكية
✅ نظام مدفوعات متقدم
✅ إدارة المشتريات والفواتير
✅ نظام صلاحيات متعدد المستويات
✅ نسخ احتياطي تلقائي
✅ واجهة متجاوبة وحديثة

🎯 كيفية الاستخدام / How to Use:

1. 📊 لوحة التحكم: نظرة شاملة على الأعمال
2. 💰 المبيعات: إدارة الفواتير والمدفوعات
3. 📦 المخزون: تتبع المنتجات والكميات
4. 👥 العملاء: إدارة بيانات العملاء
5. 📈 التقارير: تحليلات مالية مفصلة
6. ⚙️ الإعدادات: تخصيص النظام

🔧 المميزات التقنية / Technical Features:

• قاعدة بيانات SQLite محسنة
• واجهة مستخدم متقدمة
• بحث وفلترة متقدمة
• تصدير واستيراد البيانات
• نظام تنبيهات ذكي
• دعم متعدد اللغات

📞 الدعم الفني / Technical Support:
📧 <EMAIL>
🌐 www.protech.com
📱 +966-11-123-4567

🆔 معلومات النسخة / Version Info:
النسخة: ProTech Advanced v2.0
تاريخ الإصدار: 2024
المطور: ProTech Solutions
        """

        help_text.insert('1.0', help_content)
        help_text.config(state='disabled')

    # Utility methods
    def backup_data(self):
        """Backup database"""
        messagebox.showinfo("قيد التطوير", "النسخ الاحتياطي قيد التطوير\nBackup under development")

    def quick_report(self):
        """Generate quick report"""
        messagebox.showinfo("قيد التطوير", "التقرير السريع قيد التطوير\nQuick report under development")

    def run(self):
        """Run the advanced application"""
        try:
            print("✅ تم تشغيل النظام المتقدم بنجاح!")
            print("✅ Advanced system started successfully!")
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل النظام: {e}")
            messagebox.showerror("خطأ / Error", f"خطأ في التطبيق\nApplication error:\n{str(e)}")

def main():
    """Main function for advanced system"""
    try:
        print("="*70)
        print("🚀 بدء تشغيل نظام ProTech المتقدم للمحاسبة")
        print("🚀 Starting ProTech Advanced Accounting System")
        print("="*70)

        app = ProTechAdvanced()
        app.run()

        print("="*70)
        print("🛑 تم إغلاق النظام المتقدم")
        print("🛑 Advanced system closed")
        print("="*70)

    except Exception as e:
        print(f"❌ خطأ في بدء التشغيل: {e}")
        print(f"❌ Startup error: {e}")
        try:
            messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق المتقدم\nFailed to start advanced application:\n{str(e)}")
        except:
            print("فشل في عرض رسالة الخطأ")

if __name__ == '__main__':
    main()
