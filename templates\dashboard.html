{% extends "base.html" %}

{% block title %}Dashboard - ProTech Accounting{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Enhanced Welcome Section -->
    <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg shadow-lg p-8 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-3xl font-bold mb-2">مرحباً بك في نظام ProTech للمحاسبة</h2>
                <h3 class="text-xl font-semibold mb-2 text-blue-100">Welcome to ProTech Accounting</h3>
                <p class="text-blue-100 mb-2">نظام محاسبة شامل مع إدارة المخزون ومسح الباركود</p>
                <p class="text-blue-100 text-sm">Comprehensive accounting software with inventory management and barcode scanning</p>
                <div class="mt-4 flex items-center space-x-4 space-x-reverse text-sm">
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">🐍 Python Flask محسن</span>
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">📊 بيانات فورية</span>
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">🌍 دعم ثنائي اللغة</span>
                    <span class="bg-white bg-opacity-20 px-3 py-1 rounded-full">⚡ أداء محسن</span>
                </div>
            </div>
            <div class="hidden md:block">
                <div class="relative">
                    <svg class="h-24 w-24 text-white opacity-50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                    </svg>
                    <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full flex items-center justify-center">
                        <span class="text-xs font-bold text-white">✓</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Sales -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Sales</p>
                    <p class="text-2xl font-semibold text-gray-900">${{ "%.2f"|format(stats.total_sales) }}</p>
                    <p class="text-xs text-green-600">+12% from last month</p>
                </div>
            </div>
        </div>

        <!-- Total Products -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Products</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.total_products }}</p>
                    <p class="text-xs text-gray-500">Active inventory items</p>
                </div>
            </div>
        </div>

        <!-- Total Customers -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">Total Customers</p>
                    <p class="text-2xl font-semibold text-gray-900">{{ stats.total_customers }}</p>
                    <p class="text-xs text-gray-500">Active customer accounts</p>
                </div>
            </div>
        </div>

        <!-- Low Stock Alert -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">مخزون منخفض / Low Stock</p>
                    <p class="text-2xl font-semibold text-orange-600">{{ stats.low_stock_items }}</p>
                    <p class="text-xs text-orange-600">يحتاج انتباه / Require attention</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Enhanced Metrics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Pending Amount -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">مبالغ معلقة / Pending Amount</p>
                    <p class="text-2xl font-semibold text-yellow-600">${{ "%.2f"|format(stats.pending_amount or 0) }}</p>
                    <p class="text-xs text-yellow-600">في انتظار الدفع / Awaiting payment</p>
                </div>
            </div>
        </div>

        <!-- Profit Margin -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-indigo-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">هامش الربح / Profit Margin</p>
                    <p class="text-2xl font-semibold text-indigo-600">{{ "%.1f"|format(stats.profit_margin or 0) }}%</p>
                    <p class="text-xs text-indigo-600">من إجمالي المبيعات / Of total sales</p>
                </div>
            </div>
        </div>

        <!-- Recent Movements -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-teal-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">حركات حديثة / Recent Movements</p>
                    <p class="text-2xl font-semibold text-teal-600">{{ stats.recent_movements or 0 }}</p>
                    <p class="text-xs text-teal-600">آخر 5 أيام / Last 5 days</p>
                </div>
            </div>
        </div>

        <!-- Total Receivables -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-pink-100 rounded-lg flex items-center justify-center">
                        <svg class="h-6 w-6 text-pink-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-500">إجمالي المستحقات / Total Receivables</p>
                    <p class="text-2xl font-semibold text-pink-600">${{ "%.2f"|format(stats.total_receivables or 0) }}</p>
                    <p class="text-xs text-pink-600">أرصدة العملاء / Customer balances</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Enhanced Inventory Value -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">قيمة المخزون / Inventory Value</h3>
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                </svg>
            </div>
            <div class="space-y-3">
                <div>
                    <div class="text-2xl font-bold text-gray-900">
                        ${{ "%.2f"|format(stats.total_inventory_value) }}
                    </div>
                    <p class="text-sm text-gray-500">قيمة التكلفة / Cost Value</p>
                </div>
                <div>
                    <div class="text-xl font-semibold text-green-600">
                        ${{ "%.2f"|format(stats.total_retail_value or 0) }}
                    </div>
                    <p class="text-sm text-gray-500">قيمة البيع / Retail Value</p>
                </div>
                <div class="pt-2 border-t border-gray-200">
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-600">الربح المحتمل / Potential Profit:</span>
                        <span class="text-sm font-medium text-green-600">
                            ${{ "%.2f"|format((stats.total_retail_value or 0) - stats.total_inventory_value) }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="mt-4">
                <a href="{{ url_for('inventory') }}" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    عرض المخزون / View Inventory →
                </a>
            </div>
        </div>

        <!-- Enhanced Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">النشاط الحديث / Recent Activity</h3>
                <div class="flex items-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span class="text-xs text-gray-500">مباشر / Live</span>
                </div>
            </div>
            <div class="space-y-4">
                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">بيع جديد / New Sale</p>
                        <p class="text-sm text-gray-500">فاتورة INV-202412-0001 - $1,150.00</p>
                        <p class="text-xs text-gray-400">منذ ساعة / 1 hour ago</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">تحديث المخزون / Stock Updated</p>
                        <p class="text-sm text-gray-500">فأرة لاسلكية / Wireless Mouse - تعديل الكمية</p>
                        <p class="text-xs text-gray-400">منذ ساعتين / 2 hours ago</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-orange-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">تنبيه مخزون منخفض / Low Stock Alert</p>
                        <p class="text-sm text-gray-500">دفتر مهني / Professional Notebook - 5 قطع متبقية</p>
                        <p class="text-xs text-gray-400">منذ 3 ساعات / 3 hours ago</p>
                    </div>
                </div>

                <div class="flex items-start space-x-3">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                            <svg class="w-4 h-4 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </div>
                    </div>
                    <div class="flex-1 min-w-0">
                        <p class="text-sm font-medium text-gray-900">عميل جديد / New Customer</p>
                        <p class="text-sm text-gray-500">أحمد الراشد / Ahmed Al-Rashid - تم التسجيل</p>
                        <p class="text-xs text-gray-400">أمس / Yesterday</p>
                    </div>
                </div>
            </div>
            <div class="mt-4 pt-4 border-t border-gray-200">
                <button onclick="refreshActivity()" class="text-blue-600 hover:text-blue-800 text-sm font-medium">
                    تحديث النشاط / Refresh Activity →
                </button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
                <svg class="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
            </div>
            <div class="space-y-3">
                <a href="{{ url_for('sales') }}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                    📄 Create New Invoice
                </a>
                <a href="{{ url_for('inventory') }}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                    📦 Add New Product
                </a>
                <a href="{{ url_for('customers') }}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                    👤 Add New Customer
                </a>
                <a href="{{ url_for('reports') }}" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md border border-gray-200">
                    📊 Generate Report
                </a>
            </div>
        </div>
    </div>

    <!-- System Status -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">🐍 Python Flask Implementation Status</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-green-700 mb-3">✅ Implemented Features</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Dashboard with real-time statistics
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Inventory management system
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Customer relationship management
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Sales and invoice tracking
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        RESTful API endpoints
                    </li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-blue-700 mb-3">🚀 Technical Stack</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li>• Python Flask web framework</li>
                    <li>• Jinja2 templating engine</li>
                    <li>• Tailwind CSS for styling</li>
                    <li>• Responsive design system</li>
                    <li>• RESTful API architecture</li>
                    <li>• JSON data handling</li>
                    <li>• Interactive JavaScript components</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Performance Monitoring -->
<div id="performance-monitor" class="fixed bottom-4 right-4 bg-white rounded-lg shadow-lg p-3 text-xs text-gray-600 border border-gray-200 hidden">
    <div class="flex items-center space-x-2">
        <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
        <span>أداء الصفحة / Page Performance: <span id="load-time">--</span>ms</span>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Enhanced dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Performance monitoring
    const loadTime = performance.now();
    document.getElementById('load-time').textContent = Math.round(loadTime);

    // Show performance monitor for 3 seconds
    const monitor = document.getElementById('performance-monitor');
    monitor.classList.remove('hidden');
    setTimeout(() => {
        monitor.classList.add('hidden');
    }, 3000);

    // Auto-refresh dashboard stats every 30 seconds
    setInterval(refreshDashboardStats, 30000);

    // Initialize real-time features
    initializeRealTimeFeatures();
});

// Refresh dashboard statistics
async function refreshDashboardStats() {
    try {
        const response = await fetch('/api/dashboard-stats');
        const data = await response.json();

        if (data.success) {
            updateDashboardUI(data.data);
            console.log('Dashboard stats refreshed successfully');
        }
    } catch (error) {
        console.error('Error refreshing dashboard stats:', error);
    }
}

// Update dashboard UI with new data
function updateDashboardUI(stats) {
    // Update key metrics
    const elements = {
        'total-sales': stats.total_sales,
        'total-products': stats.total_products,
        'total-customers': stats.total_customers,
        'low-stock-items': stats.low_stock_items,
        'pending-amount': stats.pending_amount,
        'profit-margin': stats.profit_margin,
        'recent-movements': stats.recent_movements,
        'total-receivables': stats.total_receivables
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            if (typeof value === 'number' && id.includes('amount') || id.includes('sales') || id.includes('receivables')) {
                element.textContent = formatCurrency(value);
            } else if (typeof value === 'number' && id.includes('margin')) {
                element.textContent = value.toFixed(1) + '%';
            } else {
                element.textContent = value;
            }
        }
    });
}

// Initialize real-time features
function initializeRealTimeFeatures() {
    // Add hover effects to cards
    const cards = document.querySelectorAll('.bg-white.rounded-lg');
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.transition = 'transform 0.2s ease-in-out';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });

    // Add click tracking
    document.addEventListener('click', function(e) {
        if (e.target.tagName === 'A' || e.target.closest('a')) {
            const link = e.target.closest('a');
            console.log('Navigation:', link.href);
        }
    });
}

// Refresh activity feed
async function refreshActivity() {
    try {
        showSuccess('تم تحديث النشاط / Activity refreshed');
        // In a real app, this would fetch new activity data
        console.log('Activity refreshed');
    } catch (error) {
        showError('فشل في تحديث النشاط / Failed to refresh activity');
    }
}

// Enhanced search functionality
function initializeGlobalSearch() {
    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'بحث عام / Global Search...';
    searchInput.className = 'fixed top-4 left-4 w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:border-blue-500 focus:ring-blue-500 text-sm z-50';

    searchInput.addEventListener('input', debounce(async function(e) {
        const query = e.target.value;
        if (query.length >= 2) {
            await performGlobalSearch(query);
        }
    }, 300));

    document.body.appendChild(searchInput);
}

// Global search function
async function performGlobalSearch(query) {
    try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
        const data = await response.json();

        if (data.success) {
            displaySearchResults(data.results);
        }
    } catch (error) {
        console.error('Search error:', error);
    }
}

// Display search results
function displaySearchResults(results) {
    // Create or update search results dropdown
    let dropdown = document.getElementById('search-results');
    if (!dropdown) {
        dropdown = document.createElement('div');
        dropdown.id = 'search-results';
        dropdown.className = 'fixed top-16 left-4 w-64 bg-white border border-gray-300 rounded-md shadow-lg z-50 max-h-64 overflow-y-auto';
        document.body.appendChild(dropdown);
    }

    dropdown.innerHTML = '';

    Object.entries(results).forEach(([category, items]) => {
        if (items.length > 0) {
            const categoryDiv = document.createElement('div');
            categoryDiv.className = 'p-2 border-b border-gray-200';
            categoryDiv.innerHTML = `<h4 class="font-medium text-gray-900 text-sm">${category}</h4>`;

            items.slice(0, 3).forEach(item => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'p-2 hover:bg-gray-50 cursor-pointer text-sm';
                itemDiv.textContent = item.name || item.number || item.code;
                itemDiv.addEventListener('click', () => {
                    // Navigate to item details
                    console.log('Navigate to:', item);
                });
                categoryDiv.appendChild(itemDiv);
            });

            dropdown.appendChild(categoryDiv);
        }
    });
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Initialize global search on load
setTimeout(initializeGlobalSearch, 1000);

// Add keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl+K for global search
    if (e.ctrlKey && e.key === 'k') {
        e.preventDefault();
        const searchInput = document.querySelector('input[placeholder*="Global Search"]');
        if (searchInput) {
            searchInput.focus();
        }
    }

    // Escape to close search results
    if (e.key === 'Escape') {
        const dropdown = document.getElementById('search-results');
        if (dropdown) {
            dropdown.remove();
        }
    }
});

// Log performance metrics
console.log('ProTech Dashboard Enhanced - Performance Metrics:', {
    loadTime: performance.now(),
    userAgent: navigator.userAgent,
    language: navigator.language,
    timestamp: new Date().toISOString()
});
</script>
{% endblock %}
