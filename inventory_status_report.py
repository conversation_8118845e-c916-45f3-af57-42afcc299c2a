#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Inventory Status Report for ProTech
تقرير حالة المخزون لـ ProTech

Generate comprehensive report of current inventory status
إنشاء تقرير شامل لحالة المخزون الحالية
"""

import os
import json
from datetime import datetime

def generate_inventory_status_report():
    """Generate comprehensive inventory status report"""
    try:
        print("📊 إنشاء تقرير حالة المخزون الشامل...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        if not os.path.exists(data_path):
            print("❌ ملف البيانات غير موجود!")
            return False
        
        # Read data
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Generate report
        report = []
        report.append("="*80)
        report.append("📊 تقرير حالة المخزون الشامل - ProTech")
        report.append("📊 Comprehensive Inventory Status Report - ProTech")
        report.append("="*80)
        report.append(f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # Summary statistics
        suppliers_count = len(data.get('suppliers', []))
        products_count = len(data.get('products', []))
        customers_count = len(data.get('customers', []))
        sales_count = len(data.get('sales', []))
        
        report.append("📈 إحصائيات عامة / General Statistics:")
        report.append("-" * 50)
        report.append(f"🏢 إجمالي الموردين / Total Suppliers: {suppliers_count}")
        report.append(f"📦 إجمالي المنتجات / Total Products: {products_count}")
        report.append(f"👥 إجمالي العملاء / Total Customers: {customers_count}")
        report.append(f"💰 إجمالي المبيعات / Total Sales: {sales_count}")
        report.append("")
        
        # Suppliers details
        if suppliers_count > 0:
            report.append("🏢 تفاصيل الموردين / Suppliers Details:")
            report.append("-" * 50)
            for i, supplier in enumerate(data.get('suppliers', []), 1):
                name_ar = supplier.get('name_ar', supplier.get('name', 'غير محدد'))
                phone = supplier.get('phone', 'غير محدد')
                email = supplier.get('email', 'غير محدد')
                report.append(f"{i:2d}. {name_ar}")
                report.append(f"    📞 الهاتف: {phone}")
                report.append(f"    📧 البريد: {email}")
                report.append("")
        
        # Products details
        if products_count > 0:
            report.append("📦 تفاصيل المنتجات / Products Details:")
            report.append("-" * 50)
            
            total_value = 0
            low_stock_count = 0
            out_of_stock_count = 0
            
            for i, product in enumerate(data.get('products', []), 1):
                name_ar = product.get('name_ar', product.get('name', 'غير محدد'))
                stock = product.get('stock', 0)
                min_stock = product.get('min_stock', 0)
                price = product.get('price', 0)
                category = product.get('category', 'غير محدد')
                barcode = product.get('barcode', 'غير محدد')
                
                # Calculate status
                if stock == 0:
                    status = "نافد"
                    out_of_stock_count += 1
                elif stock <= min_stock:
                    status = "منخفض"
                    low_stock_count += 1
                else:
                    status = "جيد"
                
                # Calculate value
                product_value = stock * price
                total_value += product_value
                
                report.append(f"{i:2d}. {name_ar}")
                report.append(f"    🏷️ الباركود: {barcode}")
                report.append(f"    📂 الفئة: {category}")
                report.append(f"    📦 المخزون: {stock} (الحد الأدنى: {min_stock}) - الحالة: {status}")
                report.append(f"    💰 السعر: ${price:.2f}")
                report.append(f"    💵 قيمة المخزون: ${product_value:.2f}")
                
                # Show customer prices if available
                if 'shop_owner_price' in product:
                    report.append(f"    💼 أسعار العملاء:")
                    report.append(f"       • صاحب محل: ${product.get('shop_owner_price', 0):.2f}")
                    report.append(f"       • موزع معتمد: ${product.get('authorized_distributor_price', 0):.2f}")
                    report.append(f"       • جملة: ${product.get('wholesale_price', 0):.2f}")
                    report.append(f"       • تجزئة: ${product.get('retail_price', 0):.2f}")
                
                report.append("")
            
            # Inventory summary
            report.append("📊 ملخص المخزون / Inventory Summary:")
            report.append("-" * 50)
            report.append(f"💰 إجمالي قيمة المخزون: ${total_value:,.2f}")
            report.append(f"✅ منتجات بمخزون جيد: {products_count - low_stock_count - out_of_stock_count}")
            report.append(f"⚠️ منتجات بمخزون منخفض: {low_stock_count}")
            report.append(f"❌ منتجات نافدة: {out_of_stock_count}")
            report.append("")
        
        # Customers details
        if customers_count > 0:
            report.append("👥 تفاصيل العملاء / Customers Details:")
            report.append("-" * 50)
            total_balance = 0
            for i, customer in enumerate(data.get('customers', []), 1):
                name = customer.get('name', 'غير محدد')
                phone = customer.get('phone', 'غير محدد')
                customer_type = customer.get('type', 'غير محدد')
                balance = customer.get('balance', 0)
                total_balance += balance
                
                report.append(f"{i:2d}. {name}")
                report.append(f"    📞 الهاتف: {phone}")
                report.append(f"    🏷️ النوع: {customer_type}")
                report.append(f"    💰 الرصيد: ${balance:.2f}")
                report.append("")
            
            report.append(f"💰 إجمالي أرصدة العملاء: ${total_balance:.2f}")
            report.append("")
        
        # Sales summary
        if sales_count > 0:
            report.append("💰 ملخص المبيعات / Sales Summary:")
            report.append("-" * 50)
            total_sales_value = 0
            for i, sale in enumerate(data.get('sales', []), 1):
                date = sale.get('date', 'غير محدد')
                customer = sale.get('customer', 'غير محدد')
                total = sale.get('total', 0)
                payment = sale.get('payment', 0)
                balance = sale.get('balance', 0)
                total_sales_value += total
                
                report.append(f"{i:2d}. فاتورة {date}")
                report.append(f"    👤 العميل: {customer}")
                report.append(f"    💰 الإجمالي: ${total:.2f}")
                report.append(f"    💵 المدفوع: ${payment:.2f}")
                report.append(f"    📊 الرصيد: ${balance:.2f}")
                report.append("")
            
            report.append(f"💰 إجمالي قيمة المبيعات: ${total_sales_value:.2f}")
            report.append("")
        
        # System status
        report.append("🔧 حالة النظام / System Status:")
        report.append("-" * 50)
        last_updated = data.get('last_updated', 'غير محدد')
        report.append(f"📅 آخر تحديث: {last_updated}")
        
        # Check for data integrity
        integrity_issues = []
        
        # Check products
        for product in data.get('products', []):
            if 'min_stock' not in product:
                integrity_issues.append(f"منتج بدون حد أدنى: {product.get('name', 'غير محدد')}")
            if 'price' not in product or product.get('price', 0) <= 0:
                integrity_issues.append(f"منتج بسعر غير صحيح: {product.get('name', 'غير محدد')}")
        
        # Check customers
        for customer in data.get('customers', []):
            if 'id' not in customer:
                integrity_issues.append(f"عميل بدون معرف: {customer.get('name', 'غير محدد')}")
        
        if integrity_issues:
            report.append("⚠️ مشاكل في سلامة البيانات:")
            for issue in integrity_issues[:5]:  # Show first 5 issues
                report.append(f"  • {issue}")
        else:
            report.append("✅ سلامة البيانات: ممتازة")
        
        report.append("")
        report.append("="*80)
        report.append("📊 انتهى التقرير / End of Report")
        report.append("="*80)
        
        # Print report
        for line in report:
            print(line)
        
        # Save report to file
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"inventory_status_report_{timestamp}.txt"
        report_path = os.path.join(data_dir, report_file)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))
        
        print(f"\n📄 تم حفظ التقرير في: {report_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير المخزون: {e}")
        return False

def main():
    """Main function"""
    print("📊 مولد تقرير حالة المخزون لـ ProTech")
    print("📊 ProTech Inventory Status Report Generator")
    print("="*60)
    
    if generate_inventory_status_report():
        print("\n✅ تم إنشاء تقرير حالة المخزون بنجاح!")
        print("✅ Inventory status report generated successfully!")
    else:
        print("\n❌ فشل في إنشاء تقرير حالة المخزون")
        print("❌ Failed to generate inventory status report")

if __name__ == "__main__":
    main()
