#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Connect Reports to Real Data
ربط التقارير بالبيانات الحقيقية

Connect reports system to actual program state for accurate reporting
ربط نظام التقارير بحالة البرنامج الفعلية للحصول على تقارير دقيقة
"""

import os
import shutil
from datetime import datetime

def connect_reports_to_real_data():
    """ربط التقارير بالبيانات الحقيقية"""
    try:
        print("🔗 ربط التقارير بحالة البرنامج الفعلية")
        print("🔗 Connecting Reports to Real Program State")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.real_data_connection_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Enhanced data access methods
        data_access_methods = '''
    def get_real_products_data(self):
        """الحصول على بيانات المنتجات الحقيقية"""
        try:
            # محاولة الوصول للبيانات من مصادر مختلفة
            products_data = []
            
            # 1. محاولة الوصول من قاعدة البيانات إذا كانت موجودة
            if hasattr(self, 'db') and self.db:
                try:
                    with self.db.get_connection() as conn:
                        cursor = conn.execute('''
                            SELECT name, category, stock, price, cost_price, min_stock, 
                                   barcode, base_price, shop_owner_price, wholesale_price,
                                   retail_price, distributor_price, unit, is_active
                            FROM products 
                            WHERE is_active = 1 OR is_active IS NULL
                        ''')
                        for row in cursor.fetchall():
                            product = {
                                'name': row[0] or 'غير محدد',
                                'category': row[1] or 'غير مصنف',
                                'quantity': row[2] or 0,
                                'price': row[3] or 0,
                                'base_price': row[4] or row[3] or 0,
                                'min_stock': row[5] or 10,
                                'barcode': row[6] or '',
                                'shop_owner_price': row[7] or (row[3] * 1.05 if row[3] else 0),
                                'wholesale_price': row[8] or (row[3] * 1.20 if row[3] else 0),
                                'retail_price': row[9] or (row[3] * 1.30 if row[3] else 0),
                                'distributor_price': row[10] or (row[3] * 1.15 if row[3] else 0),
                                'unit': row[11] or 'قطعة',
                                'is_active': row[12] if row[12] is not None else True
                            }
                            products_data.append(product)
                    print(f"✅ تم تحميل {len(products_data)} منتج من قاعدة البيانات")
                    return products_data
                except Exception as db_error:
                    print(f"⚠️ خطأ في قاعدة البيانات: {db_error}")
            
            # 2. محاولة الوصول من متغير البرنامج
            if hasattr(self, 'products') and self.products:
                products_data = []
                for product in self.products:
                    if isinstance(product, dict):
                        # تنظيف وتوحيد البيانات
                        clean_product = {
                            'name': product.get('name', 'غير محدد'),
                            'category': product.get('category', 'غير مصنف'),
                            'quantity': int(product.get('quantity', 0)),
                            'price': float(product.get('price', 0)),
                            'base_price': float(product.get('base_price', product.get('price', 0))),
                            'min_stock': int(product.get('min_stock', 10)),
                            'barcode': product.get('barcode', ''),
                            'shop_owner_price': float(product.get('shop_owner_price', product.get('price', 0) * 1.05)),
                            'wholesale_price': float(product.get('wholesale_price', product.get('price', 0) * 1.20)),
                            'retail_price': float(product.get('retail_price', product.get('price', 0) * 1.30)),
                            'distributor_price': float(product.get('distributor_price', product.get('price', 0) * 1.15)),
                            'unit': product.get('unit', 'قطعة'),
                            'is_active': product.get('is_active', True)
                        }
                        products_data.append(clean_product)
                print(f"✅ تم تحميل {len(products_data)} منتج من متغير البرنامج")
                return products_data
            
            # 3. محاولة تحميل من ملف JSON
            try:
                data_file = "protech_simple_data.json"
                if os.path.exists(data_file):
                    import json
                    with open(data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    products_from_file = data.get('products', [])
                    for product in products_from_file:
                        clean_product = {
                            'name': product.get('name', 'غير محدد'),
                            'category': product.get('category', 'غير مصنف'),
                            'quantity': int(product.get('quantity', 0)),
                            'price': float(product.get('price', 0)),
                            'base_price': float(product.get('base_price', product.get('price', 0))),
                            'min_stock': int(product.get('min_stock', 10)),
                            'barcode': product.get('barcode', ''),
                            'shop_owner_price': float(product.get('shop_owner_price', product.get('price', 0) * 1.05)),
                            'wholesale_price': float(product.get('wholesale_price', product.get('price', 0) * 1.20)),
                            'retail_price': float(product.get('retail_price', product.get('price', 0) * 1.30)),
                            'distributor_price': float(product.get('distributor_price', product.get('price', 0) * 1.15)),
                            'unit': product.get('unit', 'قطعة'),
                            'is_active': product.get('is_active', True)
                        }
                        products_data.append(clean_product)
                    print(f"✅ تم تحميل {len(products_data)} منتج من ملف JSON")
                    return products_data
            except Exception as file_error:
                print(f"⚠️ خطأ في تحميل ملف JSON: {file_error}")
            
            # 4. إنشاء بيانات تجريبية إذا لم توجد بيانات
            print("⚠️ لم توجد بيانات حقيقية، سيتم إنشاء بيانات تجريبية")
            return self.create_sample_products_data()
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على بيانات المنتجات: {e}")
            return self.create_sample_products_data()
    
    def get_real_sales_data(self):
        """الحصول على بيانات المبيعات الحقيقية"""
        try:
            sales_data = []
            
            # 1. محاولة الوصول من قاعدة البيانات
            if hasattr(self, 'db') and self.db:
                try:
                    with self.db.get_connection() as conn:
                        cursor = conn.execute('''
                            SELECT invoice_number, customer_name, customer_type, sale_date,
                                   total_amount, paid_amount, balance_due, status, subtotal,
                                   discount_amount, tax_amount
                            FROM sales 
                            ORDER BY sale_date DESC
                        ''')
                        for row in cursor.fetchall():
                            sale = {
                                'invoice_number': row[0] or '',
                                'customer_name': row[1] or 'غير محدد',
                                'customer_type': row[2] or 'تجزئة',
                                'date': row[3] or datetime.now().strftime('%Y-%m-%d'),
                                'total': float(row[4] or 0),
                                'paid_amount': float(row[5] or 0),
                                'balance_due': float(row[6] or 0),
                                'status': row[7] or 'مكتمل',
                                'subtotal': float(row[8] or 0),
                                'discount': float(row[9] or 0),
                                'tax': float(row[10] or 0),
                                'items': []  # سيتم تحميلها من جدول منفصل إذا وجد
                            }
                            sales_data.append(sale)
                    print(f"✅ تم تحميل {len(sales_data)} مبيعة من قاعدة البيانات")
                    return sales_data
                except Exception as db_error:
                    print(f"⚠️ خطأ في قاعدة بيانات المبيعات: {db_error}")
            
            # 2. محاولة الوصول من متغير البرنامج
            if hasattr(self, 'sales') and self.sales:
                sales_data = []
                for sale in self.sales:
                    if isinstance(sale, dict):
                        clean_sale = {
                            'invoice_number': sale.get('invoice_number', ''),
                            'customer_name': sale.get('customer_name', 'غير محدد'),
                            'customer_type': sale.get('customer_type', 'تجزئة'),
                            'date': sale.get('date', datetime.now().strftime('%Y-%m-%d')),
                            'total': float(sale.get('total', 0)),
                            'paid_amount': float(sale.get('paid_amount', sale.get('total', 0))),
                            'balance_due': float(sale.get('balance_due', 0)),
                            'status': sale.get('status', 'مكتمل'),
                            'subtotal': float(sale.get('subtotal', sale.get('total', 0))),
                            'discount': float(sale.get('discount', 0)),
                            'tax': float(sale.get('tax', 0)),
                            'items': sale.get('items', [])
                        }
                        sales_data.append(clean_sale)
                print(f"✅ تم تحميل {len(sales_data)} مبيعة من متغير البرنامج")
                return sales_data
            
            # 3. محاولة تحميل من ملف JSON
            try:
                data_file = "protech_simple_data.json"
                if os.path.exists(data_file):
                    import json
                    with open(data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    sales_from_file = data.get('sales', [])
                    for sale in sales_from_file:
                        clean_sale = {
                            'invoice_number': sale.get('invoice_number', ''),
                            'customer_name': sale.get('customer_name', 'غير محدد'),
                            'customer_type': sale.get('customer_type', 'تجزئة'),
                            'date': sale.get('date', datetime.now().strftime('%Y-%m-%d')),
                            'total': float(sale.get('total', 0)),
                            'paid_amount': float(sale.get('paid_amount', sale.get('total', 0))),
                            'balance_due': float(sale.get('balance_due', 0)),
                            'status': sale.get('status', 'مكتمل'),
                            'subtotal': float(sale.get('subtotal', sale.get('total', 0))),
                            'discount': float(sale.get('discount', 0)),
                            'tax': float(sale.get('tax', 0)),
                            'items': sale.get('items', [])
                        }
                        sales_data.append(clean_sale)
                    print(f"✅ تم تحميل {len(sales_data)} مبيعة من ملف JSON")
                    return sales_data
            except Exception as file_error:
                print(f"⚠️ خطأ في تحميل مبيعات من JSON: {file_error}")
            
            # 4. إنشاء بيانات تجريبية
            print("⚠️ لم توجد مبيعات حقيقية، سيتم إنشاء بيانات تجريبية")
            return self.create_sample_sales_data()
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على بيانات المبيعات: {e}")
            return self.create_sample_sales_data()
    
    def get_real_customers_data(self):
        """الحصول على بيانات العملاء الحقيقية"""
        try:
            customers_data = []
            
            # 1. محاولة الوصول من قاعدة البيانات
            if hasattr(self, 'db') and self.db:
                try:
                    with self.db.get_connection() as conn:
                        cursor = conn.execute('''
                            SELECT name, name_ar, email, phone, balance, category,
                                   customer_type, credit_limit, current_balance, is_active
                            FROM customers 
                            WHERE is_active = 1 OR is_active IS NULL
                        ''')
                        for row in cursor.fetchall():
                            customer = {
                                'name': row[0] or 'غير محدد',
                                'name_ar': row[1] or row[0] or 'غير محدد',
                                'email': row[2] or '',
                                'phone': row[3] or '',
                                'balance': float(row[4] or 0),
                                'category': row[5] or 'تجزئة',
                                'type': row[6] or row[5] or 'تجزئة',
                                'credit_limit': float(row[7] or 0),
                                'current_balance': float(row[8] or row[4] or 0),
                                'is_active': row[9] if row[9] is not None else True
                            }
                            customers_data.append(customer)
                    print(f"✅ تم تحميل {len(customers_data)} عميل من قاعدة البيانات")
                    return customers_data
                except Exception as db_error:
                    print(f"⚠️ خطأ في قاعدة بيانات العملاء: {db_error}")
            
            # 2. محاولة الوصول من متغير البرنامج
            if hasattr(self, 'customers') and self.customers:
                customers_data = []
                for customer in self.customers:
                    if isinstance(customer, dict):
                        clean_customer = {
                            'name': customer.get('name', 'غير محدد'),
                            'name_ar': customer.get('name_ar', customer.get('name', 'غير محدد')),
                            'email': customer.get('email', ''),
                            'phone': customer.get('phone', ''),
                            'balance': float(customer.get('balance', 0)),
                            'category': customer.get('category', 'تجزئة'),
                            'type': customer.get('type', customer.get('category', 'تجزئة')),
                            'credit_limit': float(customer.get('credit_limit', 0)),
                            'current_balance': float(customer.get('current_balance', customer.get('balance', 0))),
                            'is_active': customer.get('is_active', True)
                        }
                        customers_data.append(clean_customer)
                print(f"✅ تم تحميل {len(customers_data)} عميل من متغير البرنامج")
                return customers_data
            
            # 3. محاولة تحميل من ملف JSON
            try:
                data_file = "protech_simple_data.json"
                if os.path.exists(data_file):
                    import json
                    with open(data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    customers_from_file = data.get('customers', [])
                    for customer in customers_from_file:
                        clean_customer = {
                            'name': customer.get('name', 'غير محدد'),
                            'name_ar': customer.get('name_ar', customer.get('name', 'غير محدد')),
                            'email': customer.get('email', ''),
                            'phone': customer.get('phone', ''),
                            'balance': float(customer.get('balance', 0)),
                            'category': customer.get('category', 'تجزئة'),
                            'type': customer.get('type', customer.get('category', 'تجزئة')),
                            'credit_limit': float(customer.get('credit_limit', 0)),
                            'current_balance': float(customer.get('current_balance', customer.get('balance', 0))),
                            'is_active': customer.get('is_active', True)
                        }
                        customers_data.append(clean_customer)
                    print(f"✅ تم تحميل {len(customers_data)} عميل من ملف JSON")
                    return customers_data
            except Exception as file_error:
                print(f"⚠️ خطأ في تحميل عملاء من JSON: {file_error}")
            
            # 4. إنشاء بيانات تجريبية
            print("⚠️ لم توجد عملاء حقيقيين، سيتم إنشاء بيانات تجريبية")
            return self.create_sample_customers_data()
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على بيانات العملاء: {e}")
            return self.create_sample_customers_data()
    
    def create_sample_products_data(self):
        """إنشاء بيانات منتجات تجريبية"""
        return [
            {
                'name': 'منتج تجريبي 1',
                'category': 'فئة أ',
                'quantity': 100,
                'price': 50.0,
                'base_price': 45.0,
                'min_stock': 10,
                'barcode': '*********',
                'shop_owner_price': 47.25,
                'wholesale_price': 54.0,
                'retail_price': 58.5,
                'distributor_price': 51.75,
                'unit': 'قطعة',
                'is_active': True
            },
            {
                'name': 'منتج تجريبي 2',
                'category': 'فئة ب',
                'quantity': 75,
                'price': 30.0,
                'base_price': 25.0,
                'min_stock': 5,
                'barcode': '987654321',
                'shop_owner_price': 26.25,
                'wholesale_price': 30.0,
                'retail_price': 32.5,
                'distributor_price': 28.75,
                'unit': 'قطعة',
                'is_active': True
            }
        ]
    
    def create_sample_sales_data(self):
        """إنشاء بيانات مبيعات تجريبية"""
        return [
            {
                'invoice_number': 'INV-001',
                'customer_name': 'عميل تجريبي 1',
                'customer_type': 'تجزئة',
                'date': '2024-01-15',
                'total': 1500.0,
                'paid_amount': 1500.0,
                'balance_due': 0.0,
                'status': 'مكتمل',
                'subtotal': 1300.0,
                'discount': 0.0,
                'tax': 200.0,
                'items': [
                    {'name': 'منتج تجريبي 1', 'quantity': 10, 'unit_price': 50.0}
                ]
            }
        ]
    
    def create_sample_customers_data(self):
        """إنشاء بيانات عملاء تجريبية"""
        return [
            {
                'name': 'عميل تجريبي 1',
                'name_ar': 'عميل تجريبي 1',
                'email': '<EMAIL>',
                'phone': '*********',
                'balance': 500.0,
                'category': 'تجزئة',
                'type': 'تجزئة',
                'credit_limit': 1000.0,
                'current_balance': 500.0,
                'is_active': True
            }
        ]
'''
        
        # Add the data access methods before the last method
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + data_access_methods + content[last_method:]
        else:
            # If fallback method not found, add before any method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + data_access_methods + content[last_method:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة طرق الوصول للبيانات الحقيقية")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في ربط التقارير بالبيانات الحقيقية: {e}")
        return False

def update_reports_to_use_real_data():
    """تحديث التقارير لاستخدام البيانات الحقيقية"""
    try:
        print("\n🔄 تحديث التقارير لاستخدام البيانات الحقيقية")

        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")

        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Update existing report methods to use real data

        # 1. Update show_general_stats method
        if "def show_general_stats(self):" in content:
            old_stats = '''def show_general_stats(self):
        """عرض الإحصائيات العامة"""
        try:
            self.report_title.config(text="الإحصائيات العامة")

            products_count = len(getattr(self, 'products', []))
            customers_count = len(getattr(self, 'customers', []))
            sales_count = len(getattr(self, 'sales', []))
            suppliers_count = len(getattr(self, 'suppliers', []))'''

            new_stats = '''def show_general_stats(self):
        """عرض الإحصائيات العامة"""
        try:
            self.report_title.config(text="الإحصائيات العامة")

            # الحصول على البيانات الحقيقية
            real_products = self.get_real_products_data()
            real_customers = self.get_real_customers_data()
            real_sales = self.get_real_sales_data()
            suppliers_count = len(getattr(self, 'suppliers', []))

            products_count = len(real_products)
            customers_count = len(real_customers)
            sales_count = len(real_sales)'''

            content = content.replace(old_stats, new_stats)

        # 2. Update show_sales_data method
        if "def show_sales_data(self):" in content:
            old_sales = '''def show_sales_data(self):
        """عرض بيانات المبيعات"""
        try:
            self.report_title.config(text="تقرير المبيعات")

            sales = getattr(self, 'sales', [])'''

            new_sales = '''def show_sales_data(self):
        """عرض بيانات المبيعات"""
        try:
            self.report_title.config(text="تقرير المبيعات")

            # الحصول على بيانات المبيعات الحقيقية
            sales = self.get_real_sales_data()'''

            content = content.replace(old_sales, new_sales)

        # 3. Update show_inventory_data method
        if "def show_inventory_data(self):" in content:
            old_inventory = '''def show_inventory_data(self):
        """عرض بيانات المخزون"""
        try:
            self.report_title.config(text="تقرير المخزون")

            products = getattr(self, 'products', [])'''

            new_inventory = '''def show_inventory_data(self):
        """عرض بيانات المخزون"""
        try:
            self.report_title.config(text="تقرير المخزون")

            # الحصول على بيانات المخزون الحقيقية
            products = self.get_real_products_data()'''

            content = content.replace(old_inventory, new_inventory)

        # 4. Update show_customers_data method
        if "def show_customers_data(self):" in content:
            old_customers = '''def show_customers_data(self):
        """عرض بيانات العملاء"""
        try:
            self.report_title.config(text="تقرير العملاء")

            customers = getattr(self, 'customers', [])'''

            new_customers = '''def show_customers_data(self):
        """عرض بيانات العملاء"""
        try:
            self.report_title.config(text="تقرير العملاء")

            # الحصول على بيانات العملاء الحقيقية
            customers = self.get_real_customers_data()'''

            content = content.replace(old_customers, new_customers)

        # 5. Update show_comprehensive_data method
        if "def show_comprehensive_data(self):" in content:
            old_comprehensive = '''def show_comprehensive_data(self):
        """عرض التقرير الشامل"""
        try:
            self.report_title.config(text="التقرير الشامل")

            products_count = len(getattr(self, 'products', []))
            customers_count = len(getattr(self, 'customers', []))
            sales_count = len(getattr(self, 'sales', []))
            suppliers_count = len(getattr(self, 'suppliers', []))'''

            new_comprehensive = '''def show_comprehensive_data(self):
        """عرض التقرير الشامل"""
        try:
            self.report_title.config(text="التقرير الشامل")

            # الحصول على البيانات الحقيقية
            real_products = self.get_real_products_data()
            real_customers = self.get_real_customers_data()
            real_sales = self.get_real_sales_data()
            suppliers_count = len(getattr(self, 'suppliers', []))

            products_count = len(real_products)
            customers_count = len(real_customers)
            sales_count = len(real_sales)'''

            content = content.replace(old_comprehensive, new_comprehensive)

        # Write the updated content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم تحديث جميع التقارير لاستخدام البيانات الحقيقية")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث التقارير: {e}")
        return False

def main():
    """Main function"""
    print("🔗 ربط التقارير بحالة البرنامج الفعلية")
    print("🔗 Connecting Reports to Real Program State")
    print("="*60)

    success_count = 0
    total_steps = 2

    # Step 1: Connect reports to real data
    if connect_reports_to_real_data():
        success_count += 1
        print("✅ الخطوة 1: تم ربط التقارير بالبيانات الحقيقية")
    else:
        print("❌ الخطوة 1: فشل في ربط التقارير بالبيانات")

    # Step 2: Update existing reports
    if update_reports_to_use_real_data():
        success_count += 1
        print("✅ الخطوة 2: تم تحديث التقارير الموجودة")
    else:
        print("❌ الخطوة 2: فشل في تحديث التقارير")

    # Summary
    print("\n" + "="*60)
    print("📊 ملخص العملية:")
    print(f"✅ نجح: {success_count}/{total_steps} خطوات")

    if success_count == total_steps:
        print("\n🎉 تم ربط التقارير بحالة البرنامج الفعلية بنجاح!")

        print("\n🔗 الميزات الجديدة:")
        print("• 📊 التقارير تستخدم البيانات الحقيقية من:")
        print("  - قاعدة البيانات (إذا كانت موجودة)")
        print("  - متغيرات البرنامج")
        print("  - ملفات JSON")
        print("  - بيانات تجريبية (كاحتياطي)")

        print("\n• 📈 دقة التقارير:")
        print("  - بيانات حقيقية ومحدثة")
        print("  - حسابات دقيقة")
        print("  - معلومات موثوقة")
        print("  - تتبع مصدر البيانات")

        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. اختر أي تقرير")
        print("4. ستحصل على بيانات حقيقية ودقيقة!")

        print("\n💡 نصائح:")
        print("• تأكد من حفظ البيانات في البرنامج")
        print("• راجع التقارير بانتظام")
        print("• استخدم التقارير لاتخاذ القرارات")

    else:
        print(f"\n⚠️ تم إنجاز {success_count} من {total_steps} خطوات فقط")
        print("قد تحتاج مراجعة إضافية")

    print("\n🔧 تم الانتهاء من ربط التقارير بالبيانات الحقيقية")

if __name__ == "__main__":
    main()
