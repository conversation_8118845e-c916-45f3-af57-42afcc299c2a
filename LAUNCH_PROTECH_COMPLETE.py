#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 مشغل ProTech Complete Standalone
LAUNCH PROTECH COMPLETE STANDALONE

مشغل بسيط لنظام ProTech الشامل المستقل
Simple launcher for ProTech Complete Standalone System
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def show_welcome():
    """Show welcome message"""
    root = tk.Tk()
    root.withdraw()
    
    welcome_msg = """
🏢 ProTech Complete Standalone - نظام ProTech الشامل المستقل
All-in-One Accounting System in Single File

📋 الميزات الشاملة / Complete Features:
• إدارة المخزون مع تفاصيل كاملة
• إدارة العملاء والموردين
• نظام المبيعات والفواتير
• التقارير والإحصائيات المفصلة
• أدوات النسخ الاحتياطي والاستيراد/التصدير
• فحص وتحليل البيانات
• واجهة ثنائية اللغة (عربي/إنجليزي)
• جميع الأدوات مدمجة في ملف واحد

🔒 مضمون عدم فقدان البيانات
🔒 Guaranteed No Data Loss

🚀 هل تريد تشغيل النظام الشامل الآن؟
Do you want to start the complete system now?
    """
    
    result = messagebox.askyesno(
        "ProTech Complete Standalone",
        welcome_msg
    )
    
    root.destroy()
    return result

def launch_protech_complete():
    """Launch ProTech Complete Standalone"""
    try:
        print("🚀 تشغيل ProTech Complete Standalone...")
        print("🚀 Starting ProTech Complete Standalone...")
        
        # Find the program file
        program_files = [
            "PROTECH_COMPLETE_STANDALONE.py",
            r"C:\Users\<USER>\OneDrive\Desktop\PROTECH_COMPLETE_STANDALONE.py",
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "PROTECH_COMPLETE_STANDALONE.py")
        ]
        
        program_file = None
        for file_path in program_files:
            if os.path.exists(file_path):
                program_file = file_path
                print(f"✅ تم العثور على البرنامج: {file_path}")
                break
        
        if not program_file:
            print("❌ لم يتم العثور على ملف البرنامج")
            messagebox.showerror(
                "خطأ - Error",
                "لم يتم العثور على ملف البرنامج\nProgram file not found:\nPROTECH_COMPLETE_STANDALONE.py"
            )
            return False
        
        # Launch the program
        subprocess.run([sys.executable, program_file])
        
        print("✅ تم إغلاق البرنامج بنجاح")
        print("✅ Program closed successfully")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        messagebox.showerror(
            "خطأ - Error",
            f"فشل في تشغيل البرنامج\nFailed to launch program:\n{str(e)}"
        )
        return False

def main():
    """Main function"""
    print("=" * 70)
    print("🏢 مشغل ProTech Complete Standalone")
    print("🏢 ProTech Complete Standalone Launcher")
    print("=" * 70)
    print("📋 نظام محاسبة شامل في ملف واحد")
    print("📋 Complete accounting system in single file")
    print("=" * 70)
    
    # Show welcome message
    if show_welcome():
        # Launch the program
        success = launch_protech_complete()
        
        if success:
            print("🎉 تم تشغيل البرنامج بنجاح!")
            print("🎉 Program launched successfully!")
        else:
            print("❌ فشل في تشغيل البرنامج")
            print("❌ Failed to launch program")
    else:
        print("❌ تم إلغاء تشغيل البرنامج")
        print("❌ Program launch cancelled")
    
    input("\nاضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
