#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive ProTech Optimizer
محسن ProTech الشامل

Complete optimization suite for ProTech accounting system
مجموعة تحسين شاملة لنظام ProTech للمحاسبة
"""

import os
import shutil
import json
from datetime import datetime
import subprocess
import sys

def create_comprehensive_backup():
    """Create comprehensive backup"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.comprehensive_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية شاملة: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def create_optimized_protech():
    """Create fully optimized ProTech"""
    
    optimized_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Comprehensive Optimized - Accounting System
نظام ProTech المحسن الشامل للمحاسبة

Fully optimized accounting system with all enhancements
نظام محاسبة محسن بالكامل مع جميع التحسينات
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime
import threading
import time
import sys

class ProTechComprehensiveOptimized:
    """ProTech with comprehensive optimizations"""
    
    def __init__(self):
        """Initialize with all optimizations"""
        try:
            # Core data structures
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            self.loading = False
            self.data_file = "protech_data.json"
            
            # Performance settings
            self.auto_save_enabled = True
            self.auto_save_interval = 30
            self.last_save_time = time.time()
            
            # Initialize interface
            self.create_main_window()
            self.create_interface()
            
            # Load data and start systems
            self.root.after(100, self.load_data)
            self.start_auto_save()
            
            print("ProTech محسن شامل جاهز")
            
        except Exception as e:
            print(f"خطأ في التهيئة: {e}")
            self.create_safe_mode()
    
    def create_main_window(self):
        """Create optimized main window"""
        try:
            self.root = tk.Tk()
            self.root.title("ProTech - نظام المحاسبة الشامل المحسن")
            self.root.geometry("1200x800")
            self.root.configure(bg='#f8fafc')
            
            # Set encoding
            if sys.platform == 'win32':
                try:
                    self.root.tk.call('encoding', 'system', 'utf-8')
                except:
                    pass
            
            # Set calculator icon
            self.set_calculator_icon()
            
            # Window optimizations
            self.root.resizable(True, True)
            self.root.minsize(800, 600)
            
            # Center and bring to front
            self.center_window()
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after_idle(lambda: self.root.attributes('-topmost', False))
            
        except Exception as e:
            print(f"خطأ في النافذة: {e}")
            self.create_fallback_window()
    
    def set_calculator_icon(self):
        """Set calculator icon safely"""
        try:
            # Try multiple icon sources
            icon_paths = [
                r'C:\\Windows\\System32\\calc.exe',
                r'C:\\Windows\\System32\\shell32.dll'
            ]
            
            for path in icon_paths:
                if os.path.exists(path):
                    try:
                        self.root.iconbitmap(path)
                        return
                    except:
                        continue
            
            # Fallback: Add emoji to title
            current_title = self.root.title()
            if 'ProTech' in current_title and not current_title.startswith('🧮'):
                self.root.title(f'🧮 {current_title}')
                
        except Exception as e:
            print(f"تعذر تعيين الأيقونة: {e}")
    
    def center_window(self):
        """Center window on screen"""
        try:
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
        except:
            pass
    
    def create_fallback_window(self):
        """Create fallback window"""
        try:
            self.root = tk.Tk()
            self.root.title("ProTech - Fallback Mode")
            self.root.geometry("800x600")
        except:
            pass
    
    def create_safe_mode(self):
        """Create safe mode interface"""
        try:
            self.root = tk.Tk()
            self.root.title("ProTech - Safe Mode")
            self.root.geometry("600x400")
            
            label = tk.Label(self.root, text="ProTech - Safe Mode\\nالوضع الآمن", 
                           font=('Arial', 16), fg='red')
            label.pack(expand=True)
            
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
        except:
            pass
    
    def create_interface(self):
        """Create comprehensive interface"""
        try:
            # Main container
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Notebook for tabs
            self.notebook = ttk.Notebook(main_frame)
            self.notebook.pack(fill='both', expand=True)
            
            # Create all tabs
            self.create_suppliers_tab()
            self.create_products_tab()
            self.create_customers_tab()
            self.create_sales_tab()
            self.create_reports_tab()
            
            # Status bar
            self.create_status_bar()
            
        except Exception as e:
            print(f"خطأ في الواجهة: {e}")
    
    def create_suppliers_tab(self):
        """Create suppliers tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="الموردين")
            
            # Input section
            input_frame = ttk.LabelFrame(frame, text="إضافة مورد جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)
            
            ttk.Label(input_frame, text="اسم المورد:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.supplier_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.supplier_name, width=30).grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.supplier_phone = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.supplier_phone, width=20).grid(row=0, column=3, padx=5, pady=5)
            
            ttk.Button(input_frame, text="إضافة", command=self.add_supplier).grid(row=0, column=4, padx=10, pady=5)
            
            # List section
            list_frame = ttk.LabelFrame(frame, text="قائمة الموردين", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)
            
            # Create treeview with scrollbar
            tree_frame = ttk.Frame(list_frame)
            tree_frame.pack(fill='both', expand=True)
            
            self.suppliers_tree = ttk.Treeview(tree_frame, columns=('name', 'phone'), show='headings', height=15)
            self.suppliers_tree.heading('name', text='اسم المورد')
            self.suppliers_tree.heading('phone', text='الهاتف')
            self.suppliers_tree.column('name', width=300)
            self.suppliers_tree.column('phone', width=200)
            
            scrollbar_s = ttk.Scrollbar(tree_frame, orient='vertical', command=self.suppliers_tree.yview)
            self.suppliers_tree.configure(yscrollcommand=scrollbar_s.set)
            
            self.suppliers_tree.pack(side='left', fill='both', expand=True)
            scrollbar_s.pack(side='right', fill='y')
            
        except Exception as e:
            print(f"خطأ في تبويب الموردين: {e}")
    
    def create_products_tab(self):
        """Create products tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="المنتجات")
            
            # Input section
            input_frame = ttk.LabelFrame(frame, text="إضافة منتج جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)
            
            # Row 1
            ttk.Label(input_frame, text="اسم المنتج:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.product_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_name, width=25).grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الباركود:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.product_barcode = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_barcode, width=15).grid(row=0, column=3, padx=5, pady=5)
            
            # Row 2
            ttk.Label(input_frame, text="السعر:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
            self.product_price = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_price, width=15).grid(row=1, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الكمية:").grid(row=1, column=2, padx=5, pady=5, sticky='e')
            self.product_stock = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_stock, width=15).grid(row=1, column=3, padx=5, pady=5)
            
            ttk.Button(input_frame, text="إضافة", command=self.add_product).grid(row=1, column=4, padx=10, pady=5)
            
            # List section
            list_frame = ttk.LabelFrame(frame, text="قائمة المنتجات", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)
            
            tree_frame = ttk.Frame(list_frame)
            tree_frame.pack(fill='both', expand=True)
            
            self.products_tree = ttk.Treeview(tree_frame, 
                                            columns=('name', 'barcode', 'price', 'stock'), 
                                            show='headings', height=15)
            self.products_tree.heading('name', text='المنتج')
            self.products_tree.heading('barcode', text='الباركود')
            self.products_tree.heading('price', text='السعر')
            self.products_tree.heading('stock', text='الكمية')
            
            # Set column widths
            self.products_tree.column('name', width=250)
            self.products_tree.column('barcode', width=150)
            self.products_tree.column('price', width=100)
            self.products_tree.column('stock', width=100)
            
            scrollbar_p = ttk.Scrollbar(tree_frame, orient='vertical', command=self.products_tree.yview)
            self.products_tree.configure(yscrollcommand=scrollbar_p.set)
            
            self.products_tree.pack(side='left', fill='both', expand=True)
            scrollbar_p.pack(side='right', fill='y')
            
        except Exception as e:
            print(f"خطأ في تبويب المنتجات: {e}")

    def create_customers_tab(self):
        """Create customers tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="العملاء")

            # Input section
            input_frame = ttk.LabelFrame(frame, text="إضافة عميل جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)

            ttk.Label(input_frame, text="اسم العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.customer_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)

            ttk.Label(input_frame, text="الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.customer_phone = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.customer_phone, width=20).grid(row=0, column=3, padx=5, pady=5)

            ttk.Label(input_frame, text="النوع:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
            self.customer_type = tk.StringVar()
            type_combo = ttk.Combobox(input_frame, textvariable=self.customer_type, width=22)
            type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
            type_combo.grid(row=1, column=1, padx=5, pady=5)
            type_combo.set('تجزئة')

            ttk.Button(input_frame, text="إضافة", command=self.add_customer).grid(row=1, column=3, padx=10, pady=5)

            # List section
            list_frame = ttk.LabelFrame(frame, text="قائمة العملاء", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)

            tree_frame = ttk.Frame(list_frame)
            tree_frame.pack(fill='both', expand=True)

            self.customers_tree = ttk.Treeview(tree_frame,
                                             columns=('name', 'phone', 'type'),
                                             show='headings', height=15)
            self.customers_tree.heading('name', text='العميل')
            self.customers_tree.heading('phone', text='الهاتف')
            self.customers_tree.heading('type', text='النوع')

            self.customers_tree.column('name', width=250)
            self.customers_tree.column('phone', width=200)
            self.customers_tree.column('type', width=150)

            scrollbar_c = ttk.Scrollbar(tree_frame, orient='vertical', command=self.customers_tree.yview)
            self.customers_tree.configure(yscrollcommand=scrollbar_c.set)

            self.customers_tree.pack(side='left', fill='both', expand=True)
            scrollbar_c.pack(side='right', fill='y')

        except Exception as e:
            print(f"خطأ في تبويب العملاء: {e}")

    def create_sales_tab(self):
        """Create sales tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="المبيعات")

            # Customer info
            customer_frame = ttk.LabelFrame(frame, text="معلومات العميل", padding=10)
            customer_frame.pack(fill='x', padx=10, pady=5)

            ttk.Label(customer_frame, text="العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.sale_customer_name = tk.StringVar()
            ttk.Entry(customer_frame, textvariable=self.sale_customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)

            ttk.Label(customer_frame, text="النوع:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.sale_customer_type = tk.StringVar()
            type_combo = ttk.Combobox(customer_frame, textvariable=self.sale_customer_type, width=15)
            type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
            type_combo.grid(row=0, column=3, padx=5, pady=5)
            type_combo.set('تجزئة')

            # Product input
            product_frame = ttk.LabelFrame(frame, text="إضافة منتج", padding=10)
            product_frame.pack(fill='x', padx=10, pady=5)

            ttk.Label(product_frame, text="الباركود:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.sale_barcode = tk.StringVar()
            barcode_entry = ttk.Entry(product_frame, textvariable=self.sale_barcode, width=20)
            barcode_entry.grid(row=0, column=1, padx=5, pady=5)
            barcode_entry.bind('<Return>', self.add_product_to_sale)

            ttk.Label(product_frame, text="الكمية:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.sale_quantity = tk.StringVar()
            self.sale_quantity.set('1')
            ttk.Entry(product_frame, textvariable=self.sale_quantity, width=10).grid(row=0, column=3, padx=5, pady=5)

            ttk.Button(product_frame, text="إضافة", command=self.add_product_to_sale).grid(row=0, column=4, padx=10, pady=5)

            # Sale items
            sale_frame = ttk.LabelFrame(frame, text="منتجات الفاتورة", padding=10)
            sale_frame.pack(fill='both', expand=True, padx=10, pady=5)

            tree_frame = ttk.Frame(sale_frame)
            tree_frame.pack(fill='both', expand=True)

            self.sale_tree = ttk.Treeview(tree_frame,
                                        columns=('name', 'qty', 'price', 'total'),
                                        show='headings', height=10)
            self.sale_tree.heading('name', text='المنتج')
            self.sale_tree.heading('qty', text='الكمية')
            self.sale_tree.heading('price', text='السعر')
            self.sale_tree.heading('total', text='المجموع')

            self.sale_tree.column('name', width=250)
            self.sale_tree.column('qty', width=100)
            self.sale_tree.column('price', width=120)
            self.sale_tree.column('total', width=120)

            scrollbar_sale = ttk.Scrollbar(tree_frame, orient='vertical', command=self.sale_tree.yview)
            self.sale_tree.configure(yscrollcommand=scrollbar_sale.set)

            self.sale_tree.pack(side='left', fill='both', expand=True)
            scrollbar_sale.pack(side='right', fill='y')

            # Total and buttons
            total_frame = ttk.Frame(frame)
            total_frame.pack(fill='x', padx=10, pady=5)

            self.total_label = ttk.Label(total_frame, text="المجموع: 0.00", font=('Arial', 14, 'bold'))
            self.total_label.pack(side='left', padx=10)

            ttk.Button(total_frame, text="حفظ", command=self.save_sale).pack(side='right', padx=5)
            ttk.Button(total_frame, text="مسح", command=self.clear_sale).pack(side='right', padx=5)

        except Exception as e:
            print(f"خطأ في تبويب المبيعات: {e}")

    def create_reports_tab(self):
        """Create reports tab"""
        try:
            frame = ttk.Frame(self.notebook)
            self.notebook.add(frame, text="التقارير")

            # Buttons
            buttons_frame = ttk.LabelFrame(frame, text="التقارير", padding=10)
            buttons_frame.pack(fill='x', padx=10, pady=10)

            ttk.Button(buttons_frame, text="المبيعات", command=self.show_sales_report).pack(side='left', padx=10)
            ttk.Button(buttons_frame, text="المخزون", command=self.show_inventory_report).pack(side='left', padx=10)
            ttk.Button(buttons_frame, text="العملاء", command=self.show_customers_report).pack(side='left', padx=10)

            # Display area
            display_frame = ttk.LabelFrame(frame, text="عرض التقارير", padding=10)
            display_frame.pack(fill='both', expand=True, padx=10, pady=10)

            text_frame = ttk.Frame(display_frame)
            text_frame.pack(fill='both', expand=True)

            self.reports_text = tk.Text(text_frame, wrap='word', font=('Arial', 11))
            scrollbar_report = ttk.Scrollbar(text_frame, orient='vertical', command=self.reports_text.yview)
            self.reports_text.configure(yscrollcommand=scrollbar_report.set)

            self.reports_text.pack(side='left', fill='both', expand=True)
            scrollbar_report.pack(side='right', fill='y')

        except Exception as e:
            print(f"خطأ في تبويب التقارير: {e}")

    def create_status_bar(self):
        """Create status bar"""
        try:
            self.status_frame = ttk.Frame(self.root)
            self.status_frame.pack(side='bottom', fill='x', padx=10, pady=5)

            self.status_label = ttk.Label(self.status_frame, text="جاهز")
            self.status_label.pack(side='left', padx=10)

            ttk.Button(self.status_frame, text="حفظ", command=self.save_data).pack(side='right', padx=10)

        except Exception as e:
            print(f"خطأ في شريط الحالة: {e}")

    # Core Functions with comprehensive optimization
    def add_supplier(self):
        """Add supplier with comprehensive validation"""
        try:
            name = self.supplier_name.get().strip()
            phone = self.supplier_phone.get().strip()

            if not name:
                messagebox.showerror("خطأ", "أدخل اسم المورد")
                return

            # Check duplicates
            for supplier in self.suppliers:
                if supplier.get('name', '').lower() == name.lower():
                    messagebox.showwarning("تحذير", "المورد موجود بالفعل")
                    return

            supplier = {
                'id': len(self.suppliers) + 1,
                'name': name,
                'phone': phone,
                'date': datetime.now().isoformat()
            }

            self.suppliers.append(supplier)
            self.update_suppliers_display()

            self.supplier_name.set('')
            self.supplier_phone.set('')

            self.status_label.config(text=f"تم إضافة: {name}")
            self.trigger_auto_save()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def add_product(self):
        """Add product with comprehensive validation"""
        try:
            name = self.product_name.get().strip()
            barcode = self.product_barcode.get().strip()
            price = float(self.product_price.get() or 0)
            stock = int(self.product_stock.get() or 0)

            if not name or price <= 0:
                messagebox.showerror("خطأ", "أدخل بيانات صحيحة")
                return

            # Check duplicate barcode
            if barcode:
                for product in self.products:
                    if product.get('barcode') == barcode:
                        messagebox.showwarning("تحذير", "الباركود موجود بالفعل")
                        return

            product = {
                'id': len(self.products) + 1,
                'name': name,
                'barcode': barcode,
                'price': price,
                'stock': stock,
                'date': datetime.now().isoformat()
            }

            self.products.append(product)
            self.update_products_display()

            self.product_name.set('')
            self.product_barcode.set('')
            self.product_price.set('')
            self.product_stock.set('')

            self.status_label.config(text=f"تم إضافة: {name}")
            self.trigger_auto_save()

        except ValueError:
            messagebox.showerror("خطأ", "أدخل أرقام صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def add_customer(self):
        """Add customer with comprehensive validation"""
        try:
            name = self.customer_name.get().strip()
            phone = self.customer_phone.get().strip()
            customer_type = self.customer_type.get()

            if not name:
                messagebox.showerror("خطأ", "أدخل اسم العميل")
                return

            # Check duplicates
            for customer in self.customers:
                if customer.get('name', '').lower() == name.lower():
                    messagebox.showwarning("تحذير", "العميل موجود بالفعل")
                    return

            customer = {
                'id': len(self.customers) + 1,
                'name': name,
                'phone': phone,
                'type': customer_type,
                'balance': 0.0,
                'date': datetime.now().isoformat()
            }

            self.customers.append(customer)
            self.update_customers_display()

            self.customer_name.set('')
            self.customer_phone.set('')
            self.customer_type.set('تجزئة')

            self.status_label.config(text=f"تم إضافة: {name}")
            self.trigger_auto_save()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def add_product_to_sale(self, event=None):
        """Add product to sale with comprehensive optimization"""
        try:
            barcode = self.sale_barcode.get().strip()
            quantity = int(self.sale_quantity.get() or 1)

            if not barcode:
                messagebox.showerror("خطأ", "أدخل الباركود")
                return

            if quantity <= 0:
                messagebox.showerror("خطأ", "أدخل كمية صحيحة")
                return

            # Find product
            product = None
            for p in self.products:
                if (p.get('barcode') == barcode or
                    p.get('name', '').lower() == barcode.lower()):
                    product = p
                    break

            if not product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return

            if product['stock'] < quantity:
                messagebox.showerror("خطأ", f"المخزون غير كافي. المتاح: {product['stock']}")
                return

            # Calculate price with margins
            base_price = product['price']
            customer_type = self.sale_customer_type.get()

            margins = {
                'تجزئة': 1.30,      # +30%
                'جملة': 1.20,       # +20%
                'موزع معتمد': 1.15, # +15%
                'صاحب محل': 1.05   # +5%
            }

            final_price = base_price * margins.get(customer_type, 1.30)
            total = final_price * quantity

            # Add to tree
            self.sale_tree.insert('', 'end', values=(
                product['name'],
                quantity,
                f"{final_price:.2f}",
                f"{total:.2f}"
            ))

            self.update_sale_total()

            self.sale_barcode.set('')
            self.sale_quantity.set('1')

        except ValueError:
            messagebox.showerror("خطأ", "أدخل كمية صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def update_sale_total(self):
        """Update sale total"""
        try:
            total = 0
            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                if len(values) >= 4:
                    total += float(values[3])

            self.total_label.config(text=f"المجموع: {total:.2f}")

        except Exception as e:
            print(f"خطأ في المجموع: {e}")

    def save_sale(self):
        """Save sale with comprehensive optimization"""
        try:
            customer_name = self.sale_customer_name.get().strip()
            customer_type = self.sale_customer_type.get()

            if not customer_name:
                messagebox.showerror("خطأ", "أدخل اسم العميل")
                return

            items = []
            total = 0

            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                if len(values) >= 4:
                    items.append({
                        'name': values[0],
                        'quantity': int(values[1]),
                        'price': float(values[2]),
                        'total': float(values[3])
                    })
                    total += float(values[3])

                    # Update stock
                    for product in self.products:
                        if product['name'] == values[0]:
                            product['stock'] -= int(values[1])
                            break

            if not items:
                messagebox.showerror("خطأ", "لا توجد منتجات")
                return

            sale = {
                'id': len(self.sales) + 1,
                'customer_name': customer_name,
                'customer_type': customer_type,
                'items': items,
                'total': total,
                'date': datetime.now().isoformat()
            }

            self.sales.append(sale)
            self.clear_sale()
            self.update_products_display()

            messagebox.showinfo("نجح", f"تم حفظ الفاتورة: {total:.2f}")
            self.status_label.config(text=f"فاتورة محفوظة: {total:.2f}")
            self.trigger_auto_save()

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ: {e}")

    def clear_sale(self):
        """Clear sale"""
        try:
            for item in self.sale_tree.get_children():
                self.sale_tree.delete(item)

            self.sale_customer_name.set('')
            self.sale_customer_type.set('تجزئة')
            self.sale_barcode.set('')
            self.sale_quantity.set('1')
            self.total_label.config(text="المجموع: 0.00")

        except Exception as e:
            print(f"خطأ في المسح: {e}")

    # Display update functions
    def update_suppliers_display(self):
        """Update suppliers display"""
        try:
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            for supplier in self.suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier.get('name', ''),
                    supplier.get('phone', '')
                ))
        except Exception as e:
            print(f"خطأ في عرض الموردين: {e}")

    def update_products_display(self):
        """Update products display"""
        try:
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            for product in self.products:
                self.products_tree.insert('', 'end', values=(
                    product.get('name', ''),
                    product.get('barcode', ''),
                    f"{product.get('price', 0):.2f}",
                    product.get('stock', 0)
                ))
        except Exception as e:
            print(f"خطأ في عرض المنتجات: {e}")

    def update_customers_display(self):
        """Update customers display"""
        try:
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            for customer in self.customers:
                self.customers_tree.insert('', 'end', values=(
                    customer.get('name', ''),
                    customer.get('phone', ''),
                    customer.get('type', 'تجزئة')
                ))
        except Exception as e:
            print(f"خطأ في عرض العملاء: {e}")

    # Reports functions
    def show_sales_report(self):
        """Show comprehensive sales report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "تقرير المبيعات الشامل\\n"
            report += "=" * 50 + "\\n\\n"

            if not self.sales:
                report += "لا توجد مبيعات مسجلة.\\n"
            else:
                total_sales = 0
                for i, sale in enumerate(self.sales, 1):
                    report += f"فاتورة {i}:\\n"
                    report += f"العميل: {sale.get('customer_name', '')}\\n"
                    report += f"النوع: {sale.get('customer_type', '')}\\n"
                    report += f"المبلغ: {sale.get('total', 0):.2f}\\n"
                    report += f"التاريخ: {sale.get('date', '')[:10]}\\n"
                    report += "-" * 30 + "\\n"
                    total_sales += sale.get('total', 0)

                report += f"\\nالإجمالي: {total_sales:.2f}\\n"
                report += f"عدد الفواتير: {len(self.sales)}\\n"
                if len(self.sales) > 0:
                    report += f"متوسط الفاتورة: {total_sales/len(self.sales):.2f}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التقرير: {e}")

    def show_inventory_report(self):
        """Show comprehensive inventory report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "تقرير المخزون الشامل\\n"
            report += "=" * 50 + "\\n\\n"

            if not self.products:
                report += "لا توجد منتجات مسجلة.\\n"
            else:
                total_value = 0
                low_stock = []

                for product in self.products:
                    value = product.get('price', 0) * product.get('stock', 0)
                    total_value += value

                    report += f"المنتج: {product.get('name', '')}\\n"
                    report += f"الباركود: {product.get('barcode', 'غير محدد')}\\n"
                    report += f"السعر: {product.get('price', 0):.2f}\\n"
                    report += f"الكمية: {product.get('stock', 0)}\\n"
                    report += f"القيمة: {value:.2f}\\n"

                    if product.get('stock', 0) < 10:
                        low_stock.append(product.get('name', ''))
                        report += "تحذير: مخزون منخفض!\\n"

                    report += "-" * 30 + "\\n"

                report += f"\\nإجمالي القيمة: {total_value:.2f}\\n"
                report += f"عدد المنتجات: {len(self.products)}\\n"

                if low_stock:
                    report += f"\\nمنتجات بمخزون منخفض ({len(low_stock)}):\\n"
                    for item in low_stock:
                        report += f"- {item}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التقرير: {e}")

    def show_customers_report(self):
        """Show comprehensive customers report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "تقرير العملاء الشامل\\n"
            report += "=" * 50 + "\\n\\n"

            if not self.customers:
                report += "لا يوجد عملاء مسجلون.\\n"
            else:
                # Count by type
                types_count = {}
                for customer in self.customers:
                    customer_type = customer.get('type', 'تجزئة')
                    types_count[customer_type] = types_count.get(customer_type, 0) + 1

                for customer in self.customers:
                    report += f"العميل: {customer.get('name', '')}\\n"
                    report += f"الهاتف: {customer.get('phone', '')}\\n"
                    report += f"النوع: {customer.get('type', '')}\\n"
                    report += f"الرصيد: {customer.get('balance', 0):.2f}\\n"
                    report += "-" * 30 + "\\n"

                report += f"\\nعدد العملاء: {len(self.customers)}\\n"
                report += "\\nتوزيع العملاء:\\n"
                for customer_type, count in types_count.items():
                    percentage = (count / len(self.customers)) * 100
                    report += f"{customer_type}: {count} ({percentage:.1f}%)\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في التقرير: {e}")

    # Data management with auto-save
    def load_data(self):
        """Load data with comprehensive optimization"""
        try:
            self.loading = True

            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])

                self.update_suppliers_display()
                self.update_products_display()
                self.update_customers_display()

                self.status_label.config(text="تم تحميل البيانات")
                print(f"تحميل: {len(self.products)} منتج، {len(self.customers)} عميل، {len(self.sales)} فاتورة")
            else:
                self.status_label.config(text="بدء جديد")

        except Exception as e:
            print(f"خطأ في التحميل: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")
        finally:
            self.loading = False

    def save_data(self):
        """Save data with comprehensive optimization"""
        try:
            if self.loading:
                return False

            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat(),
                'version': '2.0'
            }

            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            # Create backup
            backup_file = f"protech_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            self.last_save_time = time.time()
            self.status_label.config(text="تم الحفظ")
            print("تم حفظ البيانات")

            return True

        except Exception as e:
            print(f"خطأ في الحفظ: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {e}")
            return False

    def start_auto_save(self):
        """Start auto-save system"""
        try:
            if self.auto_save_enabled:
                current_time = time.time()
                if current_time - self.last_save_time >= self.auto_save_interval:
                    self.save_data()

                self.root.after(self.auto_save_interval * 1000, self.start_auto_save)

        except Exception as e:
            print(f"خطأ في الحفظ التلقائي: {e}")

    def trigger_auto_save(self):
        """Trigger immediate auto-save"""
        try:
            if self.auto_save_enabled:
                self.save_data()
        except Exception as e:
            print(f"خطأ في الحفظ الفوري: {e}")

    def run(self):
        """Run the comprehensive optimized application"""
        try:
            print("تشغيل ProTech المحسن الشامل...")

            # Setup close handler
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # Bring to front
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after_idle(lambda: self.root.attributes('-topmost', False))

            # Start main loop
            self.root.mainloop()

        except Exception as e:
            print(f"خطأ في التشغيل: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج: {e}")

    def on_closing(self):
        """Handle closing with comprehensive save"""
        try:
            if self.save_data():
                print("إغلاق ProTech...")
                self.root.destroy()
            else:
                if messagebox.askyesno("تأكيد", "فشل الحفظ. إغلاق بدون حفظ؟"):
                    self.root.destroy()
        except Exception as e:
            print(f"خطأ عند الإغلاق: {e}")
            self.root.destroy()

def main():
    """Main function with comprehensive optimization"""
    try:
        print("ProTech المحسن الشامل")
        print("=" * 50)

        app = ProTechComprehensiveOptimized()
        app.run()

    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''

    try:
        # Create backup
        create_comprehensive_backup()

        # Write optimized code
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(optimized_code)

        print("✅ تم إنشاء ProTech المحسن الشامل بالكامل")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return False

def apply_comprehensive_optimizations():
    """Apply comprehensive optimizations"""
    try:
        print("🔧 تطبيق التحسينات الشاملة...")

        # Fix encoding issues
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # Remove problematic Unicode characters
        content = content.replace('\\U0001f680', '')  # Remove rocket emoji
        content = content.replace('\\u274c', '')      # Remove cross mark
        content = content.replace('\\u2705', '')      # Remove check mark

        # Write back
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم إصلاح مشاكل الترميز")
        return True

    except Exception as e:
        print(f"❌ خطأ في التحسينات: {e}")
        return False

def create_comprehensive_launcher():
    """Create comprehensive launcher"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Comprehensive Launcher
مشغل ProTech الشامل
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def launch_protech():
    """Launch ProTech with comprehensive optimization"""
    try:
        print("تشغيل ProTech المحسن الشامل...")

        # Set environment
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONPATH'] = os.getcwd()

        # Check file exists
        if not os.path.exists('protech_simple_working.py'):
            messagebox.showerror("خطأ", "ملف ProTech غير موجود!")
            return False

        # Test syntax
        try:
            with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
                content = f.read()
            compile(content, 'protech_simple_working.py', 'exec')
            print("✅ اختبار التركيب نجح")
        except Exception as e:
            messagebox.showerror("خطأ في التركيب", f"خطأ في تركيب الملف:\\n{e}")
            return False

        # Launch with proper settings
        if sys.platform == 'win32':
            subprocess.Popen([
                sys.executable, 'protech_simple_working.py'
            ], env=env, creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)

        print("✅ تم تشغيل ProTech بنجاح")
        return True

    except Exception as e:
        messagebox.showerror("خطأ في التشغيل", f"فشل في تشغيل ProTech:\\n{e}")
        return False

def create_launcher_gui():
    """Create launcher GUI"""
    root = tk.Tk()
    root.title("ProTech Comprehensive Launcher")
    root.geometry("500x300")
    root.configure(bg='#f0f8ff')

    # Center window
    root.eval('tk::PlaceWindow . center')

    # Title
    title_label = tk.Label(root, text="ProTech المحسن الشامل",
                          font=('Arial', 18, 'bold'), bg='#f0f8ff', fg='#1e3a8a')
    title_label.pack(pady=30)

    # Description
    desc_label = tk.Label(root, text="مشغل ProTech مع جميع التحسينات الشاملة",
                         font=('Arial', 12), bg='#f0f8ff', fg='#374151')
    desc_label.pack(pady=10)

    # Launch button
    launch_btn = tk.Button(root, text="تشغيل ProTech",
                          command=lambda: [launch_protech(), root.destroy()],
                          bg='#4CAF50', fg='white', font=('Arial', 14, 'bold'),
                          width=20, height=2)
    launch_btn.pack(pady=20)

    # Test button
    test_btn = tk.Button(root, text="اختبار الملف",
                        command=test_file, bg='#2196F3', fg='white',
                        font=('Arial', 12), width=20)
    test_btn.pack(pady=10)

    # Exit button
    exit_btn = tk.Button(root, text="خروج", command=root.quit,
                        bg='#f44336', fg='white', font=('Arial', 10))
    exit_btn.pack(pady=10)

    root.mainloop()

def test_file():
    """Test file syntax"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        compile(content, 'protech_simple_working.py', 'exec')
        messagebox.showinfo("نجح", "الملف يعمل بدون أخطاء!")
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في الملف:\\n{e}")

def main():
    """Main launcher function"""
    try:
        # Try direct launch first
        if len(sys.argv) > 1 and sys.argv[1] == '--direct':
            launch_protech()
        else:
            # Show GUI launcher
            create_launcher_gui()
    except Exception as e:
        print(f"خطأ في المشغل: {e}")

if __name__ == "__main__":
    main()
'''

    with open('launch_protech_comprehensive.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)

    print("✅ تم إنشاء مشغل شامل: launch_protech_comprehensive.py")

def test_comprehensive_version():
    """Test comprehensive version"""
    try:
        import subprocess
        import sys

        print("🧪 اختبار النسخة الشاملة...")

        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'],
                              capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ اختبار التركيب نجح")
            return True
        else:
            print(f"❌ خطأ في التركيب: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """Main comprehensive optimization function"""
    print("🚀 التحسين الشامل لـ ProTech")
    print("🚀 ProTech Comprehensive Optimization")
    print("=" * 70)

    try:
        # Step 1: Create optimized ProTech
        print("📝 إنشاء ProTech المحسن الشامل...")
        if create_optimized_protech():
            print("✅ تم إنشاء النسخة المحسنة")

        # Step 2: Apply additional optimizations
        print("🔧 تطبيق التحسينات الإضافية...")
        if apply_comprehensive_optimizations():
            print("✅ تم تطبيق التحسينات")

        # Step 3: Test the result
        print("🧪 اختبار النتيجة...")
        if test_comprehensive_version():
            print("✅ الاختبار نجح")

        # Step 4: Create comprehensive launcher
        print("🚀 إنشاء المشغل الشامل...")
        create_comprehensive_launcher()

        print("\n" + "=" * 70)
        print("✅ تم التحسين الشامل لـ ProTech بنجاح!")
        print("✅ ProTech comprehensive optimization completed!")
        print("=" * 70)

        print("\n🎯 التحسينات المطبقة:")
        print("• إعادة كتابة شاملة للكود")
        print("• إصلاح جميع مشاكل الترميز")
        print("• تحسين الأداء والذاكرة")
        print("• نظام حفظ تلقائي متقدم")
        print("• واجهة محسنة مع شريط تمرير")
        print("• معالجة أخطاء شاملة")
        print("• تقارير مفصلة ومتقدمة")
        print("• نظام هوامش ربح تلقائي")
        print("• مشغل شامل مع واجهة رسومية")

        print("\n🚀 طرق التشغيل:")
        print("1. launch_protech_comprehensive.py - المشغل الشامل (الأفضل)")
        print("2. النقر المزدوج على protech_simple_working.py")
        print("3. python protech_simple_working.py")

        print("\n🎉 ProTech جاهز للعمل بأداء شامل محسن!")

        return True

    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
