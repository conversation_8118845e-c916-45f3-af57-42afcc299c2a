import AppLayout from '@/components/layout/AppLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import LowStockAlerts from '@/components/inventory/LowStockAlerts';
import InventoryMovementsTable from '@/components/inventory/InventoryMovementsTable';
import ProductsTable from '@/components/inventory/ProductsTable';
import InventoryDashboard from '@/components/inventory/InventoryDashboard';
import { PlusIcon, QrCodeIcon } from '@heroicons/react/24/outline';

export default function InventoryPage() {
  return (
    <AppLayout title="Inventory Management">
      <div className="space-y-6">
        {/* Header Actions */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Inventory</h1>
            <p className="text-gray-600">Manage your products and stock levels</p>
          </div>
          <div className="flex space-x-3">
            <Button variant="outline">
              <QrCodeIcon className="h-4 w-4 mr-2" />
              Scan Barcode
            </Button>
            <Button>
              <PlusIcon className="h-4 w-4 mr-2" />
              Add Product
            </Button>
          </div>
        </div>

        {/* Inventory Dashboard */}
        <InventoryDashboard />

        {/* Low Stock Alerts */}
        <LowStockAlerts
          threshold={10}
          onProductClick={(product) => {
            console.log('Product clicked:', product);
            // Navigate to product details or open modal
          }}
          onCreatePurchaseOrder={(product) => {
            console.log('Create PO for:', product);
            // Navigate to create purchase order
          }}
        />

        {/* Recent Inventory Movements */}
        <InventoryMovementsTable
          limit={15}
          showFilters={true}
        />

        {/* Products Table */}
        <ProductsTable
          onProductView={(product) => {
            console.log('View product:', product);
            // Navigate to product details page
          }}
          onProductEdit={(product) => {
            console.log('Edit product:', product);
            // Open product edit modal or navigate to edit page
          }}
          onProductDelete={(product) => {
            console.log('Delete product:', product);
            // Show confirmation dialog and delete
          }}
        />
      </div>
    </AppLayout>
  );
}
