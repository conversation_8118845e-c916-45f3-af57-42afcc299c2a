import { NextRequest, NextResponse } from 'next/server';
import { productOperations } from '@/lib/database';
import { productSchema, paginationSchema } from '@/lib/validations';
import { generateCode, calculatePrice } from '@/lib/utils';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || undefined;

    const result = await productOperations.getAll(page, limit, search);

    return NextResponse.json({
      success: true,
      data: result,
    });
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch products',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = productSchema.parse(body);

    // Generate code if not provided
    if (!validatedData.code) {
      validatedData.code = generateCode('PRD', 6);
    }

    // Calculate price levels based on base price
    const priceLevel1 = calculatePrice(validatedData.basePrice, 1);
    const priceLevel2 = calculatePrice(validatedData.basePrice, 2);
    const priceLevel3 = calculatePrice(validatedData.basePrice, 3);
    const priceLevel4 = calculatePrice(validatedData.basePrice, 4);

    const productData = {
      ...validatedData,
      priceLevel1,
      priceLevel2,
      priceLevel3,
      priceLevel4,
    };

    const product = await productOperations.create(productData);

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product created successfully',
    });
  } catch (error) {
    console.error('Error creating product:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to create product',
      },
      { status: 500 }
    );
  }
}
