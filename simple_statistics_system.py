#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Statistics System
نظام الإحصائيات البسيط

Create a simple but effective statistics system
إنشاء نظام إحصائيات بسيط وفعال
"""

import os
import shutil
from datetime import datetime

def create_simple_statistics():
    """إنشاء نظام الإحصائيات البسيط"""
    try:
        print("📊 إنشاء نظام الإحصائيات البسيط")
        print("📊 Creating Simple Statistics System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.simple_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple statistics method
        statistics_method = '''
    def create_advanced_statistics_page(self):
        """إنشاء صفحة الإحصائيات المتقدمة"""
        try:
            # Clear current page
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            
            self.current_page = 'advanced_statistics'
            
            # Main container
            main_container = tk.Frame(self.main_frame, bg='#f0f0f0')
            main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Title
            title_label = tk.Label(main_container, text="📊 Advanced Business Statistics", 
                                 font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#2c3e50')
            title_label.pack(pady=(0, 20))
            
            # Get data for calculations
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Calculate key metrics
            total_products = len(products)
            total_customers = len(customers)
            total_sales = len(sales)
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            avg_sale = total_revenue / total_sales if total_sales > 0 else 0
            
            # Stats cards frame
            cards_frame = tk.Frame(main_container, bg='#f0f0f0')
            cards_frame.pack(fill='x', pady=(0, 20))
            
            # Create stats cards
            cards_data = [
                ("Total Products", f"{total_products:,}", "#3498db"),
                ("Total Customers", f"{total_customers:,}", "#e74c3c"),
                ("Total Sales", f"{total_sales:,}", "#2ecc71"),
                ("Revenue (LBP)", f"{total_revenue:,.0f}", "#f39c12"),
                ("Revenue (USD)", f"${total_revenue/exchange_rate:,.2f}", "#9b59b6"),
                ("Inventory Value", f"{inventory_value:,.0f} LBP", "#1abc9c"),
                ("Customer Debt", f"{customer_debt:,.0f} LBP", "#e67e22"),
                ("Avg Sale", f"{avg_sale:,.0f} LBP", "#34495e")
            ]
            
            # Create cards in grid
            for i, (title, value, color) in enumerate(cards_data):
                row = i // 4
                col = i % 4
                
                card_frame = tk.Frame(cards_frame, bg=color, relief='raised', bd=3)
                card_frame.grid(row=row, col=col, padx=5, pady=5, sticky='ew')
                
                title_label = tk.Label(card_frame, text=title, font=("Arial", 10, "bold"), 
                                     bg=color, fg='white')
                title_label.pack(pady=(10, 5))
                
                value_label = tk.Label(card_frame, text=value, font=("Arial", 12, "bold"), 
                                     bg=color, fg='white')
                value_label.pack(pady=(0, 10))
                
                cards_frame.grid_columnconfigure(col, weight=1)
            
            # Table section
            table_section = tk.Frame(main_container, bg='#ffffff', relief='ridge', bd=2)
            table_section.pack(fill='both', expand=True)
            
            # Table header
            header_frame = tk.Frame(table_section, bg='#34495e')
            header_frame.pack(fill='x')
            
            header_label = tk.Label(header_frame, text="📊 Business Overview Table", 
                                  font=("Arial", 14, "bold"), bg='#34495e', fg='white')
            header_label.pack(side='left', padx=15, pady=10)
            
            # Export button
            export_btn = tk.Button(header_frame, text="Export CSV", 
                                 command=self.export_statistics_table,
                                 bg='#e74c3c', fg='white', font=("Arial", 9, "bold"))
            export_btn.pack(side='right', padx=15, pady=10)
            
            # Table content
            table_frame = tk.Frame(table_section, bg='#ffffff')
            table_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Create scrollable text widget for table
            text_widget = tk.Text(table_frame, font=("Courier", 10), wrap='none', height=15)
            scrollbar_v = tk.Scrollbar(table_frame, orient="vertical", command=text_widget.yview)
            scrollbar_h = tk.Scrollbar(table_frame, orient="horizontal", command=text_widget.xview)
            
            text_widget.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            text_widget.pack(side="left", fill="both", expand=True)
            
            # Create table content
            table_content = "BUSINESS STATISTICS OVERVIEW\\n"
            table_content += "="*80 + "\\n\\n"
            
            # Basic metrics table
            table_content += f"{'Metric':<25} {'Value (LBP)':<20} {'Value (USD)':<15} {'Status':<15}\\n"
            table_content += "-"*80 + "\\n"
            
            metrics = [
                ("Total Revenue", total_revenue, f"${total_revenue/exchange_rate:,.2f}", "Active"),
                ("Inventory Value", inventory_value, f"${inventory_value/exchange_rate:,.2f}", "Stocked"),
                ("Customer Debts", customer_debt, f"${customer_debt/exchange_rate:,.2f}", "Receivable"),
                ("Average Sale", avg_sale, f"${avg_sale/exchange_rate:,.2f}", "Per Order")
            ]
            
            for metric, value_lbp, value_usd, status in metrics:
                table_content += f"{metric:<25} {value_lbp:>19,.0f} {value_usd:<15} {status:<15}\\n"
            
            table_content += "\\n" + "="*80 + "\\n\\n"
            
            # Product analysis
            table_content += "PRODUCT ANALYSIS:\\n"
            table_content += "-"*40 + "\\n"
            
            low_stock = [p for p in products if float(p.get('quantity', 0)) <= 10]
            high_value = [p for p in products if float(p.get('quantity', 0)) * float(p.get('base_price', 0)) > 1000000]
            
            table_content += f"Total Products: {total_products}\\n"
            table_content += f"Low Stock Items: {len(low_stock)}\\n"
            table_content += f"High Value Items: {len(high_value)}\\n\\n"
            
            # Customer analysis
            table_content += "CUSTOMER ANALYSIS:\\n"
            table_content += "-"*40 + "\\n"
            
            active_customers = [c for c in customers if float(c.get('balance', 0)) != 0]
            debt_customers = [c for c in customers if float(c.get('balance', 0)) > 0]
            
            table_content += f"Total Customers: {total_customers}\\n"
            table_content += f"Active Customers: {len(active_customers)}\\n"
            table_content += f"Customers with Debt: {len(debt_customers)}\\n\\n"
            
            # Sales analysis
            table_content += "SALES ANALYSIS:\\n"
            table_content += "-"*40 + "\\n"
            
            table_content += f"Total Sales: {total_sales}\\n"
            table_content += f"Total Revenue: {total_revenue:,.0f} LBP\\n"
            table_content += f"Average Sale: {avg_sale:,.0f} LBP\\n"
            table_content += f"Revenue (USD): ${total_revenue/exchange_rate:,.2f}\\n\\n"
            
            # Business health assessment
            table_content += "BUSINESS HEALTH ASSESSMENT:\\n"
            table_content += "-"*40 + "\\n"
            
            net_worth = inventory_value + customer_debt + total_revenue
            net_worth_usd = net_worth / exchange_rate
            
            if net_worth_usd >= 50000:
                health_status = "EXCELLENT"
            elif net_worth_usd >= 25000:
                health_status = "VERY GOOD"
            elif net_worth_usd >= 10000:
                health_status = "GOOD"
            else:
                health_status = "NEEDS IMPROVEMENT"
            
            table_content += f"Net Business Worth: {net_worth:,.0f} LBP\\n"
            table_content += f"Net Business Worth: ${net_worth_usd:,.2f} USD\\n"
            table_content += f"Health Status: {health_status}\\n\\n"
            
            # Recommendations
            table_content += "RECOMMENDATIONS:\\n"
            table_content += "-"*40 + "\\n"
            
            if len(low_stock) > 0:
                table_content += f"- Restock {len(low_stock)} low inventory items\\n"
            if avg_sale < 100000:
                table_content += "- Focus on increasing average sale value\\n"
            if len(debt_customers) > total_customers * 0.3:
                table_content += "- Follow up on customer payments\\n"
            
            table_content += "- Monitor inventory turnover\\n"
            table_content += "- Track customer satisfaction\\n"
            table_content += "- Update pricing regularly\\n"
            
            text_widget.insert(1.0, table_content)
            text_widget.config(state='disabled')
            
            # Store for export
            self.current_statistics_data = table_content
            
            print("✅ تم إنشاء صفحة الإحصائيات المتقدمة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء صفحة الإحصائيات: {e}")
    
    def export_statistics_table(self):
        """تصدير جدول الإحصائيات"""
        try:
            if hasattr(self, 'current_statistics_data'):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"protech_statistics_{timestamp}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.current_statistics_data)
                
                print(f"✅ تم تصدير الإحصائيات إلى {filename}")
                
                # Show success message
                import tkinter.messagebox as msgbox
                msgbox.showinfo("Export Success", f"Statistics exported to {filename}")
            else:
                print("❌ لا توجد إحصائيات لتصديرها")
                
        except Exception as e:
            print(f"❌ خطأ في تصدير الإحصائيات: {e}")
'''
        
        # Add the statistics method
        last_method = content.rfind("\n    def export_current_table(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + statistics_method + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + statistics_method + content[last_method:]
        
        # Update the statistics button
        if 'btn6 = tk.Button(sidebar, text="رصيد المحل"' in content:
            # Find and replace the button
            start_pos = content.find('btn6 = tk.Button(sidebar, text="رصيد المحل"')
            if start_pos != -1:
                # Find the end of this button definition
                end_pos = start_pos
                paren_count = 0
                in_button = False
                
                for i, char in enumerate(content[start_pos:]):
                    if char == '(':
                        paren_count += 1
                        in_button = True
                    elif char == ')':
                        paren_count -= 1
                        if in_button and paren_count == 0:
                            end_pos = start_pos + i + 1
                            break
                
                old_button = content[start_pos:end_pos]
                new_button = '''btn6 = tk.Button(sidebar, text="Advanced Stats", 
                           font=("Arial", 10), bg='#16a085', fg='white', 
                           width=18, height=2, command=self.create_advanced_statistics_page)'''
                
                content = content.replace(old_button, new_button)
                print("✅ تم تحديث زر الإحصائيات")
            else:
                print("❌ لم يتم العثور على بداية تعريف الزر")
                return False
        else:
            print("❌ لم يتم العثور على زر رصيد المحل")
            return False
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إنشاء نظام الإحصائيات البسيط")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نظام الإحصائيات البسيط: {e}")
        return False

def main():
    """Main function"""
    print("📊 إنشاء نظام الإحصائيات البسيط لـ ProTech")
    print("📊 Creating Simple Statistics System for ProTech")
    print("="*70)
    
    if create_simple_statistics():
        print("\n🎉 تم إنشاء نظام الإحصائيات البسيط بنجاح!")
        
        print("\n📊 الميزات الجديدة:")
        print("• 📈 صفحة إحصائيات متقدمة")
        print("• 🎯 8 بطاقات إحصائيات ملونة")
        print("• 📋 جدول شامل للبيانات")
        print("• 📊 تحليل الأعمال")
        print("• 📤 تصدير البيانات")
        print("• 🔄 حسابات فورية")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Advanced Stats' في الشريط الجانبي")
        print("3. استعرض بطاقات الإحصائيات السريعة")
        print("4. اقرأ جدول نظرة عامة الأعمال")
        print("5. انقر على 'Export CSV' لتصدير البيانات")
        print("6. راجع التوصيات والتحليل")
        
        print("\n💡 المعلومات المعروضة:")
        print("• إجمالي المنتجات والعملاء والمبيعات")
        print("• الإيرادات بالليرة والدولار")
        print("• قيمة المخزون وديون العملاء")
        print("• متوسط قيمة البيع")
        print("• تحليل المنتجات والعملاء")
        print("• تقييم صحة الأعمال")
        print("• توصيات لتحسين الأداء")
        
    else:
        print("\n❌ فشل في إنشاء نظام الإحصائيات البسيط")
    
    print("\n🔧 تم الانتهاء من إنشاء نظام الإحصائيات")

if __name__ == "__main__":
    main()
