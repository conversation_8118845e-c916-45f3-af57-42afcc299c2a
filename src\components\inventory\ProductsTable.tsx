'use client';

import React, { useState, useEffect } from 'react';
import { 
  PencilIcon, 
  TrashIcon, 
  AdjustmentsHorizontalIcon,
  EyeIcon,
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import Input from '@/components/ui/Input';
import StockAdjustmentModal from './StockAdjustmentModal';
import { formatCurrency } from '@/lib/utils';
import { Product } from '@/types';

interface ProductsTableProps {
  onProductEdit?: (product: Product) => void;
  onProductView?: (product: Product) => void;
  onProductDelete?: (product: Product) => void;
}

export default function ProductsTable({ 
  onProductEdit, 
  onProductView, 
  onProductDelete 
}: ProductsTableProps) {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [search, setSearch] = useState('');
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [showAdjustmentModal, setShowAdjustmentModal] = useState(false);

  useEffect(() => {
    fetchProducts();
  }, [page, search]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '20',
      });

      if (search) params.append('search', search);

      const response = await fetch(`/api/products?${params}`);
      const data = await response.json();

      if (data.success) {
        setProducts(data.data.data);
        setTotalPages(data.data.totalPages);
      } else {
        setError(data.error || 'Failed to fetch products');
      }
    } catch (err) {
      setError('Failed to fetch products');
      console.error('Error fetching products:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleStockAdjustment = async (adjustmentData: {
    productId: string;
    newQuantity: number;
    reason: string;
    notes?: string;
  }) => {
    try {
      const response = await fetch('/api/inventory/adjust', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(adjustmentData),
      });

      const data = await response.json();

      if (data.success) {
        // Update the product in the local state
        setProducts(prev => prev.map(product => 
          product.id === adjustmentData.productId 
            ? { ...product, currentStock: adjustmentData.newQuantity }
            : product
        ));
        
        // Show success message (you might want to use a toast notification)
        console.log('Stock adjusted successfully');
      } else {
        throw new Error(data.error || 'Failed to adjust stock');
      }
    } catch (error) {
      console.error('Error adjusting stock:', error);
      throw error;
    }
  };

  const getStockStatus = (product: Product) => {
    if (product.currentStock <= 0) {
      return { status: 'out-of-stock', color: 'text-red-600 bg-red-50', text: 'Out of Stock' };
    } else if (product.currentStock <= product.minStock) {
      return { status: 'low-stock', color: 'text-orange-600 bg-orange-50', text: 'Low Stock' };
    } else {
      return { status: 'in-stock', color: 'text-green-600 bg-green-50', text: 'In Stock' };
    }
  };

  const handleSearchChange = (value: string) => {
    setSearch(value);
    setPage(1); // Reset to first page when searching
  };

  if (loading && products.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Products</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Products</CardTitle>
            <div className="flex items-center space-x-4">
              <Input
                placeholder="Search products..."
                value={search}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="w-64"
              />
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {error ? (
            <div className="text-center py-8">
              <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
              <p className="mt-1 text-sm text-gray-500">{error}</p>
              <Button onClick={fetchProducts} className="mt-4">
                Try Again
              </Button>
            </div>
          ) : products.length === 0 ? (
            <div className="text-center py-8">
              <div className="mx-auto h-12 w-12 text-gray-400">
                <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                </svg>
              </div>
              <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {search ? 'Try adjusting your search terms.' : 'Get started by adding your first product.'}
              </p>
            </div>
          ) : (
            <>
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Product
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Stock
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Price
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {products.map((product) => {
                      const stockStatus = getStockStatus(product);
                      return (
                        <tr key={product.id} className="hover:bg-gray-50">
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {product.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                {product.code}
                              </div>
                              {product.barcode && (
                                <div className="text-xs text-gray-400">
                                  Barcode: {product.barcode}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                            {product.category?.name || 'Uncategorized'}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {product.currentStock} {product.unit}
                            </div>
                            <div className="text-xs text-gray-500">
                              Min: {product.minStock} {product.unit}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <div className="text-sm text-gray-900">
                              {formatCurrency(product.basePrice)}
                            </div>
                            <div className="text-xs text-gray-500">
                              Cost: {formatCurrency(product.costPrice)}
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap">
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${stockStatus.color}`}>
                              {stockStatus.text}
                            </span>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                            <div className="flex items-center space-x-2">
                              {onProductView && (
                                <button
                                  onClick={() => onProductView(product)}
                                  className="text-blue-600 hover:text-blue-900"
                                  title="View Details"
                                >
                                  <EyeIcon className="h-4 w-4" />
                                </button>
                              )}
                              
                              <button
                                onClick={() => {
                                  setSelectedProduct(product);
                                  setShowAdjustmentModal(true);
                                }}
                                className="text-green-600 hover:text-green-900"
                                title="Adjust Stock"
                              >
                                <AdjustmentsHorizontalIcon className="h-4 w-4" />
                              </button>
                              
                              {onProductEdit && (
                                <button
                                  onClick={() => onProductEdit(product)}
                                  className="text-indigo-600 hover:text-indigo-900"
                                  title="Edit Product"
                                >
                                  <PencilIcon className="h-4 w-4" />
                                </button>
                              )}
                              
                              {onProductDelete && (
                                <button
                                  onClick={() => onProductDelete(product)}
                                  className="text-red-600 hover:text-red-900"
                                  title="Delete Product"
                                >
                                  <TrashIcon className="h-4 w-4" />
                                </button>
                              )}
                            </div>
                          </td>
                        </tr>
                      );
                    })}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-gray-700">
                    Page {page} of {totalPages}
                  </div>
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(page - 1)}
                      disabled={page === 1}
                    >
                      Previous
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setPage(page + 1)}
                      disabled={page === totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>

      {/* Stock Adjustment Modal */}
      <StockAdjustmentModal
        isOpen={showAdjustmentModal}
        onClose={() => {
          setShowAdjustmentModal(false);
          setSelectedProduct(null);
        }}
        product={selectedProduct}
        onAdjust={handleStockAdjustment}
      />
    </>
  );
}
