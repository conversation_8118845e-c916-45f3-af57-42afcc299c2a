#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test Performance Improvements for ProTech
اختبار تحسينات الأداء لـ ProTech

Test the performance improvements and data persistence
اختبار تحسينات الأداء والحفاظ على البيانات
"""

import os
import json
import time
import subprocess
import sys
from datetime import datetime

def test_data_persistence():
    """Test data persistence and auto-save"""
    try:
        print("💾 اختبار نظام حفظ البيانات المحسن...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        # Check if data file exists
        if not os.path.exists(data_path):
            print("❌ ملف البيانات غير موجود!")
            return False
        
        # Read data
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check data structure
        required_sections = ['suppliers', 'products', 'customers', 'sales']
        for section in required_sections:
            if section not in data:
                print(f"❌ قسم مفقود: {section}")
                return False
        
        # Check for version and checksum (new features)
        has_version = 'version' in data
        has_checksum = 'checksum' in data
        has_last_updated = 'last_updated' in data
        
        print(f"✅ هيكل البيانات سليم")
        print(f"📊 الموردين: {len(data['suppliers'])}")
        print(f"📦 المنتجات: {len(data['products'])}")
        print(f"👥 العملاء: {len(data['customers'])}")
        print(f"💰 المبيعات: {len(data['sales'])}")
        
        if has_version:
            print(f"🔢 إصدار البيانات: {data['version']}")
        if has_checksum:
            print(f"🔐 المجموع الاختباري: {data['checksum']}")
        if has_last_updated:
            print(f"🕐 آخر تحديث: {data['last_updated']}")
        
        # Check for backup files
        import glob
        backup_pattern = f"{data_path}.backup_*"
        backup_files = glob.glob(backup_pattern)
        print(f"💾 النسخ الاحتياطية: {len(backup_files)} ملف")
        
        # Check for emergency saves
        emergency_pattern = "protech_emergency_save_*.json"
        emergency_files = glob.glob(os.path.join(data_dir, emergency_pattern))
        print(f"🚨 حفظ الطوارئ: {len(emergency_files)} ملف")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حفظ البيانات: {e}")
        return False

def test_performance_monitoring():
    """Test performance monitoring"""
    try:
        print("📊 اختبار مراقبة الأداء...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        
        # Check if performance monitor exists
        monitor_file = os.path.join(data_dir, "performance_monitor.py")
        if not os.path.exists(monitor_file):
            print("❌ مراقب الأداء غير موجود!")
            return False
        
        print("✅ مراقب الأداء موجود")
        
        # Check if performance log exists
        log_file = os.path.join(data_dir, "performance_log.json")
        if os.path.exists(log_file):
            with open(log_file, 'r', encoding='utf-8') as f:
                logs = json.load(f)
            print(f"📈 سجلات الأداء: {len(logs)} إدخال")
            
            if logs:
                latest_log = logs[-1]
                print(f"🖥️ آخر قراءة CPU: {latest_log.get('cpu_percent', 0):.1f}%")
                print(f"💾 آخر قراءة RAM: {latest_log.get('memory_percent', 0):.1f}%")
        else:
            print("⚠️ لا توجد سجلات أداء بعد")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مراقبة الأداء: {e}")
        return False

def test_code_compilation():
    """Test code compilation after optimizations"""
    try:
        print("🔧 اختبار تجميع الكود المحسن...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        code_file = "protech_simple_working.py"
        code_path = os.path.join(data_dir, code_file)
        
        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', code_path], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ تجميع الكود: نجح")
            
            # Check file size
            file_size = os.path.getsize(code_path) / 1024  # KB
            print(f"📄 حجم الملف: {file_size:.1f} KB")
            
            return True
        else:
            print(f"❌ تجميع الكود: فشل")
            print(f"الخطأ: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التجميع: {e}")
        return False

def test_optimization_features():
    """Test specific optimization features"""
    try:
        print("🚀 اختبار ميزات التحسين...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        code_file = "protech_simple_working.py"
        code_path = os.path.join(data_dir, code_file)
        
        # Read code and check for optimization features
        with open(code_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        features = {
            'enhanced_data_persistence': 'setup_enhanced_data_persistence' in content,
            'inventory_optimization': 'setup_inventory_optimization' in content,
            'memory_optimization': 'setup_memory_optimization' in content,
            'auto_save_worker': 'auto_save_worker' in content,
            'emergency_save': 'emergency_save_on_exit' in content,
            'data_caching': 'get_products_cached' in content,
            'low_stock_monitoring': 'check_low_stock_alert' in content,
            'memory_cleanup': 'memory_cleanup_worker' in content
        }
        
        print("🔍 ميزات التحسين المكتشفة:")
        for feature, present in features.items():
            status = "✅" if present else "❌"
            print(f"  {status} {feature}")
        
        # Count successful features
        successful_features = sum(features.values())
        total_features = len(features)
        
        print(f"\n📊 ميزات مفعلة: {successful_features}/{total_features}")
        
        return successful_features >= total_features * 0.8  # 80% success rate
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ميزات التحسين: {e}")
        return False

def test_program_startup():
    """Test program startup performance"""
    try:
        print("⏱️ اختبار أداء بدء التشغيل...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        code_file = "protech_simple_working.py"
        code_path = os.path.join(data_dir, code_file)
        
        # Measure startup time
        start_time = time.time()
        
        # Start program in background
        process = subprocess.Popen([sys.executable, code_path], 
                                 cwd=data_dir,
                                 stdout=subprocess.PIPE, 
                                 stderr=subprocess.PIPE)
        
        # Wait a bit for startup
        time.sleep(3)
        
        startup_time = time.time() - start_time
        
        # Check if process is running
        if process.poll() is None:
            print(f"✅ البرنامج بدأ بنجاح في {startup_time:.2f} ثانية")
            
            # Terminate test process
            process.terminate()
            try:
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"❌ البرنامج فشل في البدء")
            if stderr:
                print(f"الخطأ: {stderr.decode('utf-8', errors='ignore')[:200]}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بدء التشغيل: {e}")
        return False

def generate_test_report(results):
    """Generate comprehensive test report"""
    try:
        print("\n📄 إنشاء تقرير الاختبار...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        report_file = f"performance_test_report_{timestamp}.json"
        
        report = {
            'timestamp': datetime.now().isoformat(),
            'test_results': results,
            'summary': {
                'total_tests': len(results),
                'passed_tests': sum(results.values()),
                'success_rate': (sum(results.values()) / len(results)) * 100
            },
            'recommendations': []
        }
        
        # Add recommendations based on results
        if not results.get('data_persistence', False):
            report['recommendations'].append("فحص نظام حفظ البيانات")
        
        if not results.get('performance_monitoring', False):
            report['recommendations'].append("تفعيل مراقبة الأداء")
        
        if not results.get('optimization_features', False):
            report['recommendations'].append("إعادة تطبيق ميزات التحسين")
        
        if not results.get('program_startup', False):
            report['recommendations'].append("فحص مشاكل بدء التشغيل")
        
        if not report['recommendations']:
            report['recommendations'].append("جميع الاختبارات نجحت - النظام محسن بشكل مثالي")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        report_path = os.path.join(data_dir, report_file)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم حفظ تقرير الاختبار: {report_file}")
        
        return report
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير الاختبار: {e}")
        return None

def main():
    """Main testing function"""
    print("🧪 اختبار تحسينات الأداء لـ ProTech")
    print("🧪 ProTech Performance Improvements Test")
    print("="*60)
    
    # Run all tests
    tests = [
        ("اختبار حفظ البيانات", test_data_persistence),
        ("اختبار مراقبة الأداء", test_performance_monitoring),
        ("اختبار تجميع الكود", test_code_compilation),
        ("اختبار ميزات التحسين", test_optimization_features),
        ("اختبار بدء التشغيل", test_program_startup),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}...")
        try:
            result = test_func()
            results[test_name.replace("اختبار ", "").replace(" ", "_")] = result
            status = "✅ نجح" if result else "❌ فشل"
            print(f"{status}")
        except Exception as e:
            print(f"❌ خطأ في {test_name}: {e}")
            results[test_name.replace("اختبار ", "").replace(" ", "_")] = False
    
    # Generate report
    report = generate_test_report(results)
    
    # Summary
    passed_tests = sum(results.values())
    total_tests = len(results)
    success_rate = (passed_tests / total_tests) * 100
    
    print("\n" + "="*60)
    print(f"📊 ملخص الاختبار:")
    print(f"✅ اختبارات ناجحة: {passed_tests}/{total_tests}")
    print(f"📈 معدل النجاح: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("\n🎉 ممتاز! جميع التحسينات تعمل بشكل مثالي")
        print("🎉 Excellent! All optimizations working perfectly")
    elif success_rate >= 60:
        print("\n✅ جيد! معظم التحسينات تعمل")
        print("✅ Good! Most optimizations working")
    else:
        print("\n⚠️ يحتاج مراجعة! بعض التحسينات لا تعمل")
        print("⚠️ Needs review! Some optimizations not working")
    
    print("="*60)
    
    return success_rate >= 60

if __name__ == "__main__":
    main()
