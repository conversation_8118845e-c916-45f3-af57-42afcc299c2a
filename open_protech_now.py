#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Open ProTech Now - Immediate Launch
فتح ProTech الآن - تشغيل فوري

Launch ProTech immediately with status display
تشغيل ProTech فوراً مع عرض الحالة
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def open_protech_now():
    """Open ProTech immediately"""
    try:
        print("🚀 فتح ProTech الآن...")
        print("🚀 Opening ProTech Now...")
        print("="*50)
        
        # Check current directory
        current_dir = os.getcwd()
        print(f"📁 المجلد الحالي: {current_dir}")
        
        # Check if we're in the right directory
        target_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        if current_dir != target_dir:
            os.chdir(target_dir)
            print(f"📁 تم التغيير إلى: {target_dir}")
        
        # Check available files
        print("\n📋 الملفات المتاحة:")
        files_to_check = [
            'protech_minimal_working.py',
            'protech_simple_working.py',
            'protech_simple_data.json'
        ]
        
        available_files = []
        for file in files_to_check:
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024
                print(f"✅ {file} ({size:.1f} KB)")
                available_files.append(file)
            else:
                print(f"❌ {file} (غير موجود)")
        
        # Try to launch the best available version
        if 'protech_minimal_working.py' in available_files:
            print("\n🚀 تشغيل النسخة الأساسية العاملة...")
            
            # Launch minimal working version
            process = subprocess.Popen([sys.executable, 'protech_minimal_working.py'])
            
            # Wait a moment to check if it started
            time.sleep(2)
            
            if process.poll() is None:
                print("✅ تم فتح ProTech بنجاح!")
                print("✅ ProTech opened successfully!")
                print("\n🖥️ تحقق من النافذة المفتوحة على الشاشة")
                print("🖥️ Check the opened window on your screen")
                
                # Show data status
                if 'protech_simple_data.json' in available_files:
                    try:
                        import json
                        with open('protech_simple_data.json', 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        print("\n📊 حالة البيانات:")
                        print(f"📊 الموردين: {len(data.get('suppliers', []))}")
                        print(f"📦 المنتجات: {len(data.get('products', []))}")
                        print(f"👥 العملاء: {len(data.get('customers', []))}")
                        print(f"💰 المبيعات: {len(data.get('sales', []))}")
                    except:
                        print("📊 البيانات متاحة")
                
                return True
            else:
                print("❌ فشل في تشغيل النسخة الأساسية")
                return False
        
        elif 'protech_simple_working.py' in available_files:
            print("\n🚀 محاولة تشغيل الملف الرئيسي...")
            
            # Try main file with safe launcher
            if os.path.exists('safe_launch_protech.py'):
                process = subprocess.Popen([sys.executable, 'safe_launch_protech.py'])
                time.sleep(3)
                
                if process.poll() is None:
                    print("✅ تم فتح ProTech عبر المشغل الآمن!")
                    return True
                else:
                    print("❌ فشل المشغل الآمن")
            
            # Direct launch attempt
            process = subprocess.Popen([sys.executable, 'protech_simple_working.py'])
            time.sleep(2)
            
            if process.poll() is None:
                print("✅ تم فتح ProTech مباشرة!")
                return True
            else:
                print("❌ فشل في التشغيل المباشر")
                return False
        
        else:
            print("❌ لا توجد ملفات ProTech متاحة!")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في فتح ProTech: {e}")
        return False

def show_launch_status():
    """Show current launch status"""
    try:
        print("\n📊 حالة التشغيل الحالية:")
        
        # Check running processes
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            
            if 'python.exe' in result.stdout:
                python_processes = result.stdout.count('python.exe')
                print(f"🐍 عمليات Python نشطة: {python_processes}")
            else:
                print("🐍 لا توجد عمليات Python نشطة")
        except:
            print("🐍 لا يمكن فحص العمليات")
        
        # Check data file status
        data_file = "protech_simple_data.json"
        if os.path.exists(data_file):
            mod_time = os.path.getmtime(data_file)
            mod_datetime = datetime.fromtimestamp(mod_time)
            print(f"💾 آخر تحديث للبيانات: {mod_datetime.strftime('%Y-%m-%d %H:%M:%S')}")
        
        print("\n💡 نصائح:")
        print("• إذا لم تظهر النافذة، تحقق من شريط المهام")
        print("• قد تكون النافذة مخفية خلف نوافذ أخرى")
        print("• استخدم Alt+Tab للتنقل بين النوافذ")
        
    except Exception as e:
        print(f"❌ خطأ في عرض الحالة: {e}")

def main():
    """Main function"""
    print("🎯 فتح ProTech فوراً")
    print("🎯 Opening ProTech Immediately")
    print("="*50)
    
    success = open_protech_now()
    
    if success:
        print("\n🎉 تم فتح ProTech بنجاح!")
        print("🎉 ProTech opened successfully!")
        
        show_launch_status()
        
        print("\n🖥️ البرنامج مفتوح الآن على الشاشة")
        print("🖥️ The program is now open on your screen")
        
    else:
        print("\n❌ فشل في فتح ProTech")
        print("❌ Failed to open ProTech")
        
        print("\n🔧 محاولة إصلاح...")
        # Try to create minimal version if it doesn't exist
        if not os.path.exists('protech_minimal_working.py'):
            print("🔧 إنشاء نسخة أساسية...")
            try:
                # Run emergency fixer to create minimal version
                if os.path.exists('emergency_syntax_fixer.py'):
                    subprocess.run([sys.executable, 'emergency_syntax_fixer.py'])
                    print("✅ تم إنشاء نسخة أساسية")
                    
                    # Try again
                    if open_protech_now():
                        print("🎉 تم فتح ProTech بعد الإصلاح!")
                    else:
                        print("❌ لا يزال هناك مشكلة")
            except Exception as e:
                print(f"❌ فشل الإصلاح: {e}")
    
    print("\n" + "="*50)

if __name__ == "__main__":
    main()
