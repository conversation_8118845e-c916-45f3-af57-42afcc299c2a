# ProTech Accounting System - Python Dependencies
# Core web framework
Flask==3.1.1

# Template engine (included with Flask)
Jinja2==3.1.6

# WSGI server
Werkzeug==3.1.3

# Security utilities
itsdangerous==2.2.0

# CLI utilities
click==8.2.1

# Markup safety
MarkupSafe==3.0.2

# Signal support
blinker==1.9.0

# Color support for Windows
colorama==0.4.6

# Optional: Database support (for future PostgreSQL integration)
# psycopg2-binary==2.9.9
# SQLAlchemy==2.0.23

# Optional: API documentation (for future Swagger integration)
# flask-restx==1.3.0

# Optional: CORS support (for future API access)
# flask-cors==4.0.0

# Optional: Environment variable management
# python-dotenv==1.0.0

# Optional: Input validation
# marshmallow==3.20.2

# Optional: JWT authentication (for future auth)
# PyJWT==2.8.0

# Optional: Rate limiting (for future API protection)
# flask-limiter==3.5.0

# Optional: Caching (for performance)
# flask-caching==2.1.0

# Development dependencies (uncomment for development)
# pytest==7.4.3
# pytest-flask==1.3.0
# black==23.11.0
# flake8==6.1.0
