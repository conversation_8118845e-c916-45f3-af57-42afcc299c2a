#!/usr/bin/env python3
"""
ProTech Accounting System - Optimized Performance Version
نظام ProTech للمحاسبة - نسخة محسنة الأداء

High-performance desktop accounting application with optimizations
تطبيق محاسبة سطح مكتب عالي الأداء مع تحسينات
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
import threading
import time
from datetime import datetime
from functools import lru_cache
import sqlite3
from contextlib import contextmanager

class PerformanceOptimizer:
    """Performance optimization utilities"""
    
    @staticmethod
    @lru_cache(maxsize=128)
    def format_currency(amount):
        """Cached currency formatting"""
        return f"${amount:,.2f}"
    
    @staticmethod
    @lru_cache(maxsize=64)
    def get_status_color(stock, min_stock):
        """Cached status color calculation"""
        if stock == 0:
            return "#fee2e2", "#dc2626", "نفد / Out"
        elif stock <= min_stock:
            return "#fef3c7", "#d97706", "منخفض / Low"
        else:
            return "#dcfce7", "#16a34a", "جيد / Good"

class DatabaseManager:
    """Optimized database manager with connection pooling"""
    
    def __init__(self, db_path="protech_optimized.db"):
        self.db_path = db_path
        self.init_database()
    
    @contextmanager
    def get_connection(self):
        """Context manager for database connections"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Enable column access by name
        try:
            yield conn
        finally:
            conn.close()
    
    def init_database(self):
        """Initialize database with optimized schema"""
        with self.get_connection() as conn:
            # Products table with indexes
            conn.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_ar TEXT NOT NULL,
                    category TEXT NOT NULL,
                    price REAL NOT NULL DEFAULT 0,
                    cost_price REAL NOT NULL DEFAULT 0,
                    stock INTEGER NOT NULL DEFAULT 0,
                    min_stock INTEGER NOT NULL DEFAULT 0,
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            # Create indexes for better performance
            conn.execute('CREATE INDEX IF NOT EXISTS idx_products_code ON products(code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_products_stock ON products(stock)')
            
            # Customers table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    name_ar TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    balance REAL NOT NULL DEFAULT 0,
                    category TEXT NOT NULL DEFAULT 'RETAIL',
                    created_at TEXT NOT NULL,
                    updated_at TEXT NOT NULL
                )
            ''')
            
            conn.execute('CREATE INDEX IF NOT EXISTS idx_customers_code ON customers(code)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_customers_category ON customers(category)')
            
            # Sales table
            conn.execute('''
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    total_amount REAL NOT NULL DEFAULT 0,
                    discount REAL NOT NULL DEFAULT 0,
                    tax REAL NOT NULL DEFAULT 0,
                    final_amount REAL NOT NULL DEFAULT 0,
                    sale_date TEXT NOT NULL,
                    notes TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')
            
            conn.execute('CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)')
            
            conn.commit()
    
    def get_products(self, limit=None, offset=0):
        """Get products with pagination"""
        with self.get_connection() as conn:
            query = "SELECT * FROM products ORDER BY name"
            if limit:
                query += f" LIMIT {limit} OFFSET {offset}"
            return conn.execute(query).fetchall()
    
    def get_product_count(self):
        """Get total product count"""
        with self.get_connection() as conn:
            return conn.execute("SELECT COUNT(*) FROM products").fetchone()[0]
    
    def search_products(self, search_term):
        """Search products efficiently"""
        with self.get_connection() as conn:
            query = '''
                SELECT * FROM products 
                WHERE name LIKE ? OR name_ar LIKE ? OR code LIKE ? OR category LIKE ?
                ORDER BY name
            '''
            term = f"%{search_term}%"
            return conn.execute(query, (term, term, term, term)).fetchall()
    
    def get_low_stock_products(self):
        """Get low stock products efficiently"""
        with self.get_connection() as conn:
            return conn.execute(
                "SELECT * FROM products WHERE stock <= min_stock ORDER BY stock ASC"
            ).fetchall()
    
    def get_dashboard_stats(self):
        """Get dashboard statistics in one query"""
        with self.get_connection() as conn:
            stats = conn.execute('''
                SELECT 
                    COUNT(*) as total_products,
                    SUM(CASE WHEN stock <= min_stock THEN 1 ELSE 0 END) as low_stock_count,
                    SUM(CASE WHEN stock = 0 THEN 1 ELSE 0 END) as out_of_stock_count,
                    SUM(stock * price) as inventory_value,
                    AVG(price) as avg_price
                FROM products
            ''').fetchone()
            
            customer_count = conn.execute("SELECT COUNT(*) FROM customers").fetchone()[0]
            sales_count = conn.execute("SELECT COUNT(*) FROM sales").fetchone()[0]
            
            return {
                'total_products': stats[0],
                'low_stock_count': stats[1],
                'out_of_stock_count': stats[2],
                'inventory_value': stats[3] or 0,
                'avg_price': stats[4] or 0,
                'customer_count': customer_count,
                'sales_count': sales_count
            }
    
    def add_product(self, product_data):
        """Add product with validation"""
        with self.get_connection() as conn:
            now = datetime.now().isoformat()
            conn.execute('''
                INSERT INTO products 
                (code, name, name_ar, category, price, cost_price, stock, min_stock, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (*product_data, now, now))
            conn.commit()
            return conn.lastrowid
    
    def update_product(self, product_id, product_data):
        """Update product efficiently"""
        with self.get_connection() as conn:
            now = datetime.now().isoformat()
            conn.execute('''
                UPDATE products SET 
                code=?, name=?, name_ar=?, category=?, price=?, cost_price=?, stock=?, min_stock=?, updated_at=?
                WHERE id=?
            ''', (*product_data, now, product_id))
            conn.commit()
    
    def delete_product(self, product_id):
        """Delete product safely"""
        with self.get_connection() as conn:
            conn.execute("DELETE FROM products WHERE id=?", (product_id,))
            conn.commit()
    
    def load_sample_data(self):
        """Load sample data if database is empty"""
        if self.get_product_count() == 0:
            sample_products = [
                ('LAPTOP001', 'Business Laptop', 'لابتوب الأعمال', 'Electronics', 1000.0, 800.0, 50, 10),
                ('MOUSE001', 'Wireless Mouse', 'فأرة لاسلكية', 'Electronics', 25.0, 15.0, 200, 50),
                ('NOTE001', 'Professional Notebook', 'دفتر مهني', 'Office Supplies', 5.0, 3.0, 5, 200),
                ('PHONE001', 'Business Smartphone', 'هاتف ذكي للأعمال', 'Electronics', 550.0, 400.0, 25, 5),
                ('DESK001', 'Office Desk', 'مكتب مكتبي', 'Furniture', 300.0, 200.0, 0, 3)
            ]
            
            for product in sample_products:
                try:
                    self.add_product(product)
                except sqlite3.IntegrityError:
                    pass  # Skip if already exists

class ProTechOptimized:
    """Optimized ProTech Accounting System"""
    
    def __init__(self):
        # Initialize database
        self.db = DatabaseManager()
        self.db.load_sample_data()
        
        # Performance settings
        self.page_size = 50  # Pagination for large datasets
        self.current_page = 0
        self.search_delay = 500  # ms delay for search
        self.last_search_time = 0
        
        # Create main window with optimizations
        self.root = tk.Tk()
        self.root.title("نظام ProTech للمحاسبة المحسن - ProTech Optimized")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f8fafc')
        self.root.state('zoomed')  # Start maximized
        
        # Configure styles for better performance
        self.setup_styles()
        
        # Create interface
        self.create_optimized_interface()
        
        # Cache frequently used data
        self.cached_stats = None
        self.stats_cache_time = 0
        self.cache_duration = 30  # seconds
        
        # Start background tasks
        self.start_background_tasks()
        
        # Show welcome
        self.show_welcome()
    
    def setup_styles(self):
        """Setup optimized styles"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # Configure treeview for better performance
        style.configure("Treeview", 
                       background="#ffffff",
                       foreground="#1f2937",
                       rowheight=25,
                       fieldbackground="#ffffff")
        style.configure("Treeview.Heading",
                       background="#f3f4f6",
                       foreground="#374151",
                       font=('Arial', 10, 'bold'))
    
    def create_optimized_interface(self):
        """Create optimized interface with lazy loading"""
        
        # Header with gradient effect
        header_frame = tk.Frame(self.root, bg='#1e40af', height=70)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        # Title with better typography
        title_label = tk.Label(
            header_frame,
            text="🚀 نظام ProTech المحسن للمحاسبة",
            font=('Arial', 22, 'bold'),
            fg='white',
            bg='#1e40af'
        )
        title_label.pack(side='left', padx=20, pady=15)
        
        # Performance indicator
        self.perf_label = tk.Label(
            header_frame,
            text="⚡ محسن الأداء / Performance Optimized",
            font=('Arial', 11),
            fg='#86efac',
            bg='#1e40af'
        )
        self.perf_label.pack(side='right', padx=20, pady=15)
        
        # Main container with better layout
        main_container = tk.Frame(self.root, bg='#f8fafc')
        main_container.pack(fill='both', expand=True, padx=5, pady=5)
        
        # Sidebar with improved navigation
        self.create_sidebar(main_container)
        
        # Content area with lazy loading
        self.content_frame = tk.Frame(main_container, bg='white', relief='flat', bd=1)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=(5, 0))
        
        # Status bar with performance metrics
        self.create_status_bar()
        
        # Load dashboard by default
        self.show_dashboard()
    
    def create_sidebar(self, parent):
        """Create optimized sidebar"""
        sidebar = tk.Frame(parent, bg='#2563eb', width=220)
        sidebar.pack(side='left', fill='y')
        sidebar.pack_propagate(False)
        
        # Sidebar title
        tk.Label(
            sidebar,
            text="📋 القوائم الرئيسية\nMain Menus",
            font=('Arial', 13, 'bold'),
            fg='white',
            bg='#2563eb'
        ).pack(pady=15)
        
        # Navigation buttons with hover effects
        nav_items = [
            ("🏠", "لوحة التحكم", "Dashboard", self.show_dashboard, "#3b82f6"),
            ("📦", "المخزون", "Inventory", self.show_inventory, "#10b981"),
            ("👥", "العملاء", "Customers", self.show_customers, "#8b5cf6"),
            ("💰", "المبيعات", "Sales", self.show_sales, "#f59e0b"),
            ("📊", "التقارير", "Reports", self.show_reports, "#ef4444"),
            ("⚙️", "الإعدادات", "Settings", self.show_settings, "#6b7280"),
            ("❓", "المساعدة", "Help", self.show_help, "#14b8a6")
        ]
        
        for icon, ar_text, en_text, command, color in nav_items:
            btn_frame = tk.Frame(sidebar, bg='#2563eb')
            btn_frame.pack(fill='x', padx=8, pady=2)
            
            btn = tk.Button(
                btn_frame,
                text=f"{icon} {ar_text}\n{en_text}",
                font=('Arial', 10, 'bold'),
                fg='white',
                bg=color,
                activebackground='#1d4ed8',
                activeforeground='white',
                relief='flat',
                height=3,
                command=command,
                cursor='hand2'
            )
            btn.pack(fill='x')
            
            # Add hover effect
            def on_enter(e, button=btn, hover_color='#1d4ed8'):
                button.config(bg=hover_color)
            
            def on_leave(e, button=btn, normal_color=color):
                button.config(bg=normal_color)
            
            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)
    
    def create_status_bar(self):
        """Create status bar with performance metrics"""
        status_frame = tk.Frame(self.root, bg='#374151', height=25)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز / Ready",
            font=('Arial', 9),
            fg='white',
            bg='#374151'
        )
        self.status_label.pack(side='left', padx=10, pady=3)
        
        # Performance metrics
        self.perf_metrics = tk.Label(
            status_frame,
            text="⚡ محسن / Optimized",
            font=('Arial', 9),
            fg='#86efac',
            bg='#374151'
        )
        self.perf_metrics.pack(side='right', padx=10, pady=3)
    
    def start_background_tasks(self):
        """Start background optimization tasks"""
        def update_time():
            while True:
                try:
                    current_time = datetime.now().strftime('%H:%M:%S')
                    if hasattr(self, 'perf_label'):
                        self.root.after(0, lambda: self.perf_label.config(
                            text=f"⚡ {current_time} | محسن الأداء"
                        ))
                    time.sleep(1)
                except:
                    break
        
        # Start time update in background
        time_thread = threading.Thread(target=update_time, daemon=True)
        time_thread.start()
    
    def get_cached_stats(self):
        """Get cached dashboard statistics"""
        current_time = time.time()
        if (self.cached_stats is None or 
            current_time - self.stats_cache_time > self.cache_duration):
            self.cached_stats = self.db.get_dashboard_stats()
            self.stats_cache_time = current_time
        return self.cached_stats
    
    def clear_content(self):
        """Optimized content clearing"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def update_status(self, message, show_time=True):
        """Update status with optional timestamp"""
        if show_time:
            timestamp = datetime.now().strftime('%H:%M:%S')
            message = f"[{timestamp}] {message}"
        self.status_label.config(text=message)
        self.root.update_idletasks()
    
    def show_welcome(self):
        """Show optimized welcome message"""
        self.root.after(1000, lambda: messagebox.showinfo(
            "مرحباً / Welcome",
            "🚀 مرحباً بك في نظام ProTech المحسن!\n\n" +
            "Welcome to ProTech Optimized!\n\n" +
            "✅ أداء محسن / Optimized Performance\n" +
            "✅ واجهة سريعة / Fast Interface\n" +
            "✅ قاعدة بيانات محسنة / Optimized Database\n\n" +
            "🎯 جاهز للاستخدام!"
        ))

    def show_dashboard(self):
        """Show optimized dashboard with cached data"""
        self.clear_content()
        self.update_status("تحميل لوحة التحكم... / Loading dashboard...")

        # Title with better styling
        title_frame = tk.Frame(self.content_frame, bg='white')
        title_frame.pack(fill='x', padx=20, pady=15)

        tk.Label(
            title_frame,
            text="📊 لوحة التحكم المحسنة / Optimized Dashboard",
            font=('Arial', 20, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Refresh button
        tk.Button(
            title_frame,
            text="🔄 تحديث / Refresh",
            font=('Arial', 10, 'bold'),
            bg='#3b82f6',
            fg='white',
            command=self.refresh_dashboard,
            cursor='hand2'
        ).pack(side='right')

        # Load statistics asynchronously
        def load_stats():
            stats = self.get_cached_stats()
            self.root.after(0, lambda: self.display_dashboard_stats(stats))

        # Start loading in background
        threading.Thread(target=load_stats, daemon=True).start()

        # Show loading indicator
        loading_frame = tk.Frame(self.content_frame, bg='white')
        loading_frame.pack(fill='both', expand=True)

        tk.Label(
            loading_frame,
            text="⏳ جاري التحميل... / Loading...",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(expand=True)

    def display_dashboard_stats(self, stats):
        """Display dashboard statistics efficiently"""
        # Clear loading indicator
        for widget in self.content_frame.winfo_children()[1:]:  # Keep title
            widget.destroy()

        # Statistics cards with improved layout
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)

        # Create optimized stat cards
        stat_cards = [
            ("📦", "إجمالي المنتجات", "Total Products", stats['total_products'], "#3b82f6"),
            ("⚠️", "مخزون منخفض", "Low Stock", stats['low_stock_count'], "#ef4444"),
            ("👥", "العملاء", "Customers", stats['customer_count'], "#10b981"),
            ("💰", "قيمة المخزون", "Inventory Value",
             PerformanceOptimizer.format_currency(stats['inventory_value']), "#8b5cf6")
        ]

        for icon, ar_title, en_title, value, color in stat_cards:
            self.create_stat_card(stats_frame, icon, ar_title, en_title, value, color)

        # Performance metrics
        perf_frame = tk.LabelFrame(
            self.content_frame,
            text="📈 مقاييس الأداء / Performance Metrics",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        perf_frame.pack(fill='x', padx=20, pady=10)

        perf_info = tk.Frame(perf_frame, bg='white')
        perf_info.pack(fill='x', padx=10, pady=10)

        metrics = [
            f"⚡ متوسط السعر / Avg Price: {PerformanceOptimizer.format_currency(stats['avg_price'])}",
            f"📊 نفد المخزون / Out of Stock: {stats['out_of_stock_count']} منتج",
            f"🔄 آخر تحديث / Last Update: {datetime.now().strftime('%H:%M:%S')}",
            f"💾 قاعدة البيانات / Database: SQLite محسنة"
        ]

        for i, metric in enumerate(metrics):
            tk.Label(
                perf_info,
                text=metric,
                font=('Arial', 10),
                fg='#374151',
                bg='white'
            ).grid(row=i//2, column=i%2, sticky='w', padx=10, pady=2)

        # Quick actions with better performance
        self.create_quick_actions()

        self.update_status("تم تحميل لوحة التحكم / Dashboard loaded")

    def create_stat_card(self, parent, icon, ar_title, en_title, value, color):
        """Create optimized statistics card"""
        card = tk.Frame(parent, bg=color, relief='raised', bd=2)
        card.pack(side='left', fill='both', expand=True, padx=5)

        # Icon
        tk.Label(
            card,
            text=icon,
            font=('Arial', 24),
            fg='white',
            bg=color
        ).pack(pady=(10, 5))

        # Title
        tk.Label(
            card,
            text=f"{ar_title}\n{en_title}",
            font=('Arial', 10, 'bold'),
            fg='white',
            bg=color,
            justify='center'
        ).pack()

        # Value
        tk.Label(
            card,
            text=str(value),
            font=('Arial', 16, 'bold'),
            fg='white',
            bg=color
        ).pack(pady=(5, 10))

    def create_quick_actions(self):
        """Create quick action buttons"""
        actions_frame = tk.LabelFrame(
            self.content_frame,
            text="⚡ إجراءات سريعة / Quick Actions",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        actions_frame.pack(fill='x', padx=20, pady=10)

        btn_frame = tk.Frame(actions_frame, bg='white')
        btn_frame.pack(pady=15)

        actions = [
            ("📦 عرض المخزون", self.show_inventory, "#3b82f6"),
            ("➕ إضافة منتج", self.quick_add_product, "#10b981"),
            ("📊 تقرير سريع", self.quick_report, "#8b5cf6"),
            ("🔍 بحث متقدم", self.advanced_search, "#f59e0b")
        ]

        for text, command, color in actions:
            btn = tk.Button(
                btn_frame,
                text=text,
                font=('Arial', 11, 'bold'),
                bg=color,
                fg='white',
                width=15,
                height=2,
                command=command,
                cursor='hand2',
                relief='flat'
            )
            btn.pack(side='left', padx=8)

            # Add hover effect
            def on_enter(e, button=btn):
                button.config(relief='raised')

            def on_leave(e, button=btn):
                button.config(relief='flat')

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

    def refresh_dashboard(self):
        """Refresh dashboard data"""
        self.cached_stats = None  # Clear cache
        self.show_dashboard()

    def show_inventory(self):
        """Show optimized inventory with pagination"""
        self.clear_content()
        self.update_status("تحميل المخزون... / Loading inventory...")

        # Header with search and controls
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            header_frame,
            text="📦 إدارة المخزون المحسنة / Optimized Inventory",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Search box with real-time search
        search_frame = tk.Frame(header_frame, bg='white')
        search_frame.pack(side='right')

        tk.Label(search_frame, text="🔍", font=('Arial', 12), bg='white').pack(side='left')

        self.search_var = tk.StringVar()
        search_entry = tk.Entry(
            search_frame,
            textvariable=self.search_var,
            font=('Arial', 11),
            width=20
        )
        search_entry.pack(side='left', padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_change)

        # Control buttons
        controls_frame = tk.Frame(self.content_frame, bg='white')
        controls_frame.pack(fill='x', padx=20, pady=5)

        control_buttons = [
            ("➕ إضافة", self.quick_add_product, "#10b981"),
            ("✏️ تعديل", self.edit_selected_product, "#f59e0b"),
            ("🗑️ حذف", self.delete_selected_product, "#ef4444"),
            ("📊 تقرير", self.inventory_report, "#8b5cf6")
        ]

        for text, command, color in control_buttons:
            tk.Button(
                controls_frame,
                text=text,
                font=('Arial', 10, 'bold'),
                bg=color,
                fg='white',
                command=command,
                cursor='hand2'
            ).pack(side='left', padx=5)

        # Products table with pagination
        self.create_products_table()

        # Pagination controls
        self.create_pagination_controls()

        # Load initial data
        self.load_products_page()

    def create_products_table(self):
        """Create optimized products table"""
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create treeview with optimized columns
        columns = ('ID', 'Code', 'Name', 'Name_AR', 'Category', 'Price', 'Stock', 'Status')
        self.products_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show='headings',
            height=15
        )

        # Configure columns
        column_configs = [
            ('ID', 50, 'center'),
            ('Code', 100, 'center'),
            ('Name', 150, 'w'),
            ('Name_AR', 150, 'w'),
            ('Category', 120, 'center'),
            ('Price', 100, 'e'),
            ('Stock', 80, 'center'),
            ('Status', 100, 'center')
        ]

        for col, width, anchor in zip(columns, [c[1] for c in column_configs], [c[2] for c in column_configs]):
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=width, anchor=anchor)

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.products_tree.xview)

        self.products_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack widgets
        self.products_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

    def create_pagination_controls(self):
        """Create pagination controls"""
        pagination_frame = tk.Frame(self.content_frame, bg='white')
        pagination_frame.pack(fill='x', padx=20, pady=5)

        self.page_info_label = tk.Label(
            pagination_frame,
            text="صفحة 1 / Page 1",
            font=('Arial', 10),
            bg='white'
        )
        self.page_info_label.pack(side='left')

        # Navigation buttons
        nav_frame = tk.Frame(pagination_frame, bg='white')
        nav_frame.pack(side='right')

        tk.Button(
            nav_frame,
            text="⏮️ الأولى",
            command=self.first_page,
            font=('Arial', 9)
        ).pack(side='left', padx=2)

        tk.Button(
            nav_frame,
            text="◀️ السابقة",
            command=self.prev_page,
            font=('Arial', 9)
        ).pack(side='left', padx=2)

        tk.Button(
            nav_frame,
            text="▶️ التالية",
            command=self.next_page,
            font=('Arial', 9)
        ).pack(side='left', padx=2)

        tk.Button(
            nav_frame,
            text="⏭️ الأخيرة",
            command=self.last_page,
            font=('Arial', 9)
        ).pack(side='left', padx=2)

    def load_products_page(self, search_term=None):
        """Load products page with optimization"""
        # Clear existing items
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        # Load data based on search or pagination
        if search_term:
            products = self.db.search_products(search_term)
            total_products = len(products)
        else:
            products = self.db.get_products(self.page_size, self.current_page * self.page_size)
            total_products = self.db.get_product_count()

        # Populate tree efficiently
        for product in products:
            bg_color, fg_color, status = PerformanceOptimizer.get_status_color(
                product['stock'], product['min_stock']
            )

            item_id = self.products_tree.insert('', 'end', values=(
                product['id'],
                product['code'],
                product['name'],
                product['name_ar'],
                product['category'],
                PerformanceOptimizer.format_currency(product['price']),
                product['stock'],
                status
            ))

            # Apply color coding
            self.products_tree.set(item_id, 'Status', status)

        # Update pagination info
        total_pages = (total_products + self.page_size - 1) // self.page_size
        self.page_info_label.config(
            text=f"صفحة {self.current_page + 1} من {total_pages} | {total_products} منتج"
        )

        self.update_status(f"تم تحميل {len(products)} منتج / Loaded {len(products)} products")

    def on_search_change(self, event):
        """Handle search with debouncing"""
        current_time = time.time() * 1000  # Convert to milliseconds
        self.last_search_time = current_time

        # Debounce search
        def delayed_search():
            time.sleep(0.5)  # 500ms delay
            if self.last_search_time == current_time:  # Only search if this is the latest
                search_term = self.search_var.get().strip()
                self.root.after(0, lambda: self.load_products_page(search_term if search_term else None))

        threading.Thread(target=delayed_search, daemon=True).start()

    def first_page(self):
        """Go to first page"""
        self.current_page = 0
        self.load_products_page()

    def prev_page(self):
        """Go to previous page"""
        if self.current_page > 0:
            self.current_page -= 1
            self.load_products_page()

    def next_page(self):
        """Go to next page"""
        total_products = self.db.get_product_count()
        total_pages = (total_products + self.page_size - 1) // self.page_size
        if self.current_page < total_pages - 1:
            self.current_page += 1
            self.load_products_page()

    def last_page(self):
        """Go to last page"""
        total_products = self.db.get_product_count()
        total_pages = (total_products + self.page_size - 1) // self.page_size
        self.current_page = max(0, total_pages - 1)
        self.load_products_page()

    def quick_add_product(self):
        """Quick add product with optimized form"""
        self.update_status("فتح نموذج إضافة منتج... / Opening add product form...")

        dialog = tk.Toplevel(self.root)
        dialog.title("➕ إضافة منتج سريع / Quick Add Product")
        dialog.geometry("450x550")
        dialog.configure(bg='#f8fafc')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (450 // 2)
        y = (dialog.winfo_screenheight() // 2) - (550 // 2)
        dialog.geometry(f"450x550+{x}+{y}")

        # Header
        header = tk.Frame(dialog, bg='#10b981', height=60)
        header.pack(fill='x')
        header.pack_propagate(False)

        tk.Label(
            header,
            text="➕ إضافة منتج جديد / Add New Product",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#10b981'
        ).pack(pady=15)

        # Form with validation
        form_frame = tk.Frame(dialog, bg='#f8fafc')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)

        entries = {}
        fields = [
            ('code', 'كود المنتج / Product Code *', 'PROD001'),
            ('name', 'اسم المنتج (إنجليزي) / Name (English) *', 'Product Name'),
            ('name_ar', 'اسم المنتج (عربي) / Name (Arabic) *', 'اسم المنتج'),
            ('category', 'الفئة / Category *', 'Electronics'),
            ('price', 'سعر البيع / Selling Price *', '100.00'),
            ('cost_price', 'سعر التكلفة / Cost Price *', '80.00'),
            ('stock', 'الكمية الحالية / Current Stock *', '50'),
            ('min_stock', 'الحد الأدنى / Minimum Stock *', '10')
        ]

        for i, (field, label, placeholder) in enumerate(fields):
            # Label with better styling
            label_frame = tk.Frame(form_frame, bg='#f8fafc')
            label_frame.pack(fill='x', pady=(10, 5))

            tk.Label(
                label_frame,
                text=label,
                font=('Arial', 11, 'bold'),
                fg='#374151',
                bg='#f8fafc'
            ).pack(anchor='w')

            # Entry with validation
            entry = tk.Entry(
                form_frame,
                font=('Arial', 11),
                width=40,
                relief='solid',
                bd=1,
                bg='white'
            )
            entry.pack(fill='x', pady=(0, 5))
            entry.insert(0, placeholder)

            # Add validation for numeric fields
            if field in ['price', 'cost_price', 'stock', 'min_stock']:
                def validate_numeric(event, field_name=field):
                    try:
                        value = event.widget.get()
                        if value and field_name in ['price', 'cost_price']:
                            float(value)
                        elif value and field_name in ['stock', 'min_stock']:
                            int(value)
                        event.widget.config(bg='white')
                    except ValueError:
                        event.widget.config(bg='#fee2e2')

                entry.bind('<KeyRelease>', validate_numeric)

            entries[field] = entry

        # Buttons with better styling
        btn_frame = tk.Frame(dialog, bg='#f8fafc')
        btn_frame.pack(fill='x', padx=20, pady=20)

        def save_product():
            try:
                # Validate and collect data
                product_data = []
                for field in ['code', 'name', 'name_ar', 'category', 'price', 'cost_price', 'stock', 'min_stock']:
                    value = entries[field].get().strip()

                    if not value:
                        messagebox.showerror("خطأ / Error", f"يرجى ملء حقل {field}\nPlease fill {field} field")
                        entries[field].focus()
                        return

                    # Convert numeric fields
                    if field in ['price', 'cost_price']:
                        try:
                            value = float(value)
                            if value < 0:
                                raise ValueError("Negative value")
                        except ValueError:
                            messagebox.showerror("خطأ / Error", f"قيمة غير صحيحة في {field}\nInvalid value in {field}")
                            entries[field].focus()
                            return
                    elif field in ['stock', 'min_stock']:
                        try:
                            value = int(value)
                            if value < 0:
                                raise ValueError("Negative value")
                        except ValueError:
                            messagebox.showerror("خطأ / Error", f"قيمة غير صحيحة في {field}\nInvalid value in {field}")
                            entries[field].focus()
                            return

                    product_data.append(value)

                # Save to database
                self.db.add_product(tuple(product_data))

                # Clear cache and refresh
                self.cached_stats = None

                messagebox.showinfo("نجح / Success", "تم إضافة المنتج بنجاح\nProduct added successfully")
                dialog.destroy()

                # Refresh current view
                if hasattr(self, 'products_tree'):
                    self.load_products_page()

                self.update_status("تم إضافة منتج جديد / New product added")

            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ / Error", "كود المنتج موجود مسبقاً\nProduct code already exists")
            except Exception as e:
                messagebox.showerror("خطأ / Error", f"خطأ في حفظ المنتج\nError saving product:\n{str(e)}")

        # Save button
        save_btn = tk.Button(
            btn_frame,
            text="💾 حفظ / Save",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            width=15,
            command=save_product,
            cursor='hand2'
        )
        save_btn.pack(side='left', padx=5)

        # Cancel button
        tk.Button(
            btn_frame,
            text="❌ إلغاء / Cancel",
            font=('Arial', 12, 'bold'),
            bg='#6b7280',
            fg='white',
            width=15,
            command=dialog.destroy,
            cursor='hand2'
        ).pack(side='right', padx=5)

        # Focus on first field
        entries['code'].focus()
        entries['code'].select_range(0, 'end')

    # Placeholder methods for other functions
    def edit_selected_product(self):
        """Edit selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير / Warning", "يرجى اختيار منتج للتعديل\nPlease select a product to edit")
            return
        messagebox.showinfo("قيد التطوير / Under Development", "هذه الميزة قيد التطوير\nThis feature is under development")

    def delete_selected_product(self):
        """Delete selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير / Warning", "يرجى اختيار منتج للحذف\nPlease select a product to delete")
            return
        messagebox.showinfo("قيد التطوير / Under Development", "هذه الميزة قيد التطوير\nThis feature is under development")

    def inventory_report(self):
        """Generate inventory report"""
        messagebox.showinfo("قيد التطوير / Under Development", "تقرير المخزون قيد التطوير\nInventory report under development")

    def quick_report(self):
        """Generate quick report"""
        messagebox.showinfo("قيد التطوير / Under Development", "التقرير السريع قيد التطوير\nQuick report under development")

    def advanced_search(self):
        """Advanced search functionality"""
        messagebox.showinfo("قيد التطوير / Under Development", "البحث المتقدم قيد التطوير\nAdvanced search under development")

    def show_customers(self):
        """Show customers (placeholder)"""
        self.clear_content()
        self.update_status("عرض العملاء / Showing customers")
        tk.Label(self.content_frame, text="👥 إدارة العملاء قيد التطوير\nCustomer Management Under Development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_sales(self):
        """Show sales (placeholder)"""
        self.clear_content()
        self.update_status("عرض المبيعات / Showing sales")
        tk.Label(self.content_frame, text="💰 إدارة المبيعات قيد التطوير\nSales Management Under Development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_reports(self):
        """Show reports (placeholder)"""
        self.clear_content()
        self.update_status("عرض التقارير / Showing reports")
        tk.Label(self.content_frame, text="📊 التقارير قيد التطوير\nReports Under Development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_settings(self):
        """Show settings (placeholder)"""
        self.clear_content()
        self.update_status("عرض الإعدادات / Showing settings")
        tk.Label(self.content_frame, text="⚙️ الإعدادات قيد التطوير\nSettings Under Development",
                font=('Arial', 16), bg='white').pack(expand=True)

    def show_help(self):
        """Show help"""
        self.clear_content()
        self.update_status("عرض المساعدة / Showing help")

        help_frame = tk.Frame(self.content_frame, bg='white')
        help_frame.pack(fill='both', expand=True, padx=20, pady=20)

        tk.Label(
            help_frame,
            text="❓ المساعدة والدعم / Help & Support",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=10)

        help_text = tk.Text(help_frame, font=('Arial', 11), bg='#f9fafb', wrap='word')
        help_text.pack(fill='both', expand=True)

        help_content = """
🚀 نظام ProTech المحسن للمحاسبة
ProTech Optimized Accounting System

📈 التحسينات المطبقة / Applied Optimizations:

✅ قاعدة بيانات محسنة / Optimized Database:
   • فهارس محسنة للبحث السريع
   • استعلامات محسنة للأداء
   • تجميع البيانات في استعلام واحد
   • إدارة الاتصالات بكفاءة

✅ واجهة المستخدم / User Interface:
   • تحميل البيانات في الخلفية
   • تقسيم الصفحات للبيانات الكبيرة
   • بحث فوري مع تأخير ذكي
   • تخزين مؤقت للإحصائيات

✅ الأداء / Performance:
   • تحميل البيانات بشكل تدريجي
   • تحديث الواجهة بدون تجميد
   • استخدام الذاكرة بكفاءة
   • معالجة متوازية للمهام

🎯 كيفية الاستخدام / How to Use:

1. 📊 لوحة التحكم: عرض الإحصائيات المحدثة
2. 📦 المخزون: إدارة المنتجات مع البحث والتقسيم
3. ➕ إضافة سريعة: نموذج محسن لإضافة المنتجات
4. 🔍 البحث: بحث فوري في جميع البيانات

💡 نصائح للأداء الأمثل / Performance Tips:

• استخدم البحث للعثور على المنتجات بسرعة
• راجع لوحة التحكم للحصول على نظرة عامة
• استخدم التقسيم للتنقل في البيانات الكبيرة
• احفظ البيانات بانتظام

📞 الدعم الفني / Technical Support:
📧 <EMAIL>
🌐 www.protech.com
📱 +966-11-123-4567
        """

        help_text.insert('1.0', help_content)
        help_text.config(state='disabled')

    def run(self):
        """Run the optimized application"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"خطأ في التطبيق\nApplication error:\n{str(e)}")

def main():
    """Main function with error handling"""
    try:
        print("🚀 Starting ProTech Optimized Application...")
        print("🚀 تشغيل تطبيق ProTech المحسن...")

        app = ProTechOptimized()
        app.run()

    except Exception as e:
        print(f"❌ Error starting application: {e}")
        try:
            messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق\nFailed to start application:\n{str(e)}")
        except:
            print("Failed to show error dialog")

if __name__ == '__main__':
    main()
