#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Fixed & Optimized - Accounting System
نظام ProTech المصلح والمحسن للمحاسبة

A complete, optimized accounting system with enhanced performance
نظام محاسبة شامل ومحسن مع أداء متطور
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
import threading
import time

class ProTechFixedOptimized:
    """ProTech Fixed & Optimized Accounting System"""
    
    def __init__(self):
        """Initialize the application with optimizations"""
        try:
            # Initialize data structures
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            self.loading = False
            self.data_file = "protech_data.json"
            
            # Performance tracking
            self.performance_stats = {
                'startup_time': time.time(),
                'button_clicks': {},
                'data_operations': 0,
                'ui_updates': 0
            }
            
            # Create main window with optimizations
            self.create_main_window()
            
            # Setup interface
            self.create_interface()
            
            # Load data after interface is ready
            self.root.after(100, self.load_data)
            
            print("✅ ProTech تم تهيئته بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في التهيئة: {e}")
            self.create_minimal_interface()
    
    def create_main_window(self):
        """Create and configure main window"""
        try:
            self.root = tk.Tk()
            self.root.title("🧮 ProTech - نظام المحاسبة المحسن")
            self.root.geometry("1200x800")
            self.root.configure(bg='#f8fafc')
            
            # Set calculator icon
            self.set_calculator_icon()
            
            # Optimize window performance
            self.root.resizable(True, True)
            self.root.minsize(800, 600)
            
            # Center window
            self.center_window()
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            self.root = tk.Tk()
            self.root.title("ProTech - Safe Mode")
    
    def set_calculator_icon(self):
        """Set calculator icon for the application"""
        try:
            # Try to use Windows calculator icon
            import os
            calc_paths = [
                r'C:\Windows\System32\calc.exe',
                r'C:\Windows\System32\shell32.dll'
            ]
            
            for path in calc_paths:
                if os.path.exists(path):
                    try:
                        self.root.iconbitmap(path)
                        print('✅ تم تعيين أيقونة الآلة الحاسبة')
                        return
                    except:
                        continue
            
            # Fallback: Add calculator emoji to title
            current_title = self.root.title()
            if not current_title.startswith('🧮'):
                self.root.title(f'🧮 {current_title}')
                print('✅ تم إضافة رمز الآلة الحاسبة للعنوان')
                
        except Exception as e:
            print(f'⚠️ تعذر تعيين أيقونة الآلة الحاسبة: {e}')
    
    def center_window(self):
        """Center window on screen"""
        try:
            self.root.update_idletasks()
            width = self.root.winfo_width()
            height = self.root.winfo_height()
            x = (self.root.winfo_screenwidth() // 2) - (width // 2)
            y = (self.root.winfo_screenheight() // 2) - (height // 2)
            self.root.geometry(f'{width}x{height}+{x}+{y}')
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")
    
    def create_minimal_interface(self):
        """Create minimal interface in case of errors"""
        try:
            self.root = tk.Tk()
            self.root.title("ProTech - Safe Mode")
            self.root.geometry("800x600")
            
            label = tk.Label(self.root, text="ProTech - Safe Mode\nيعمل في الوضع الآمن", 
                           font=('Arial', 16), fg='red')
            label.pack(expand=True)
            
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            
        except Exception as e:
            print(f"❌ خطأ في الوضع الآمن: {e}")
    
    def create_interface(self):
        """Create the main interface with optimizations"""
        try:
            # Create main container
            main_container = ttk.Frame(self.root)
            main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Create notebook for tabs
            self.notebook = ttk.Notebook(main_container)
            self.notebook.pack(fill='both', expand=True)
            
            # Create tabs with optimized loading
            self.create_suppliers_tab()
            self.create_products_tab()
            self.create_customers_tab()
            self.create_sales_tab()
            self.create_reports_tab()
            
            # Create status bar
            self.create_status_bar()
            
            print("✅ تم إنشاء الواجهة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الواجهة: {e}")
    
    def create_suppliers_tab(self):
        """Create optimized suppliers management tab"""
        try:
            # Suppliers tab
            suppliers_frame = ttk.Frame(self.notebook)
            self.notebook.add(suppliers_frame, text="🏪 الموردين")
            
            # Input frame
            input_frame = ttk.LabelFrame(suppliers_frame, text="إضافة مورد جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)
            
            # Create input grid
            ttk.Label(input_frame, text="اسم المورد:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.supplier_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.supplier_name, width=30).grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="رقم الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.supplier_phone = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.supplier_phone, width=20).grid(row=0, column=3, padx=5, pady=5)
            
            # Add button with optimization
            add_btn = ttk.Button(input_frame, text="إضافة مورد", command=self.add_supplier)
            add_btn.grid(row=0, column=4, padx=10, pady=5)
            
            # Suppliers list
            list_frame = ttk.LabelFrame(suppliers_frame, text="قائمة الموردين", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)
            
            # Optimized Treeview
            self.suppliers_tree = ttk.Treeview(list_frame, columns=('name', 'phone'), show='headings', height=15)
            self.suppliers_tree.heading('name', text='اسم المورد')
            self.suppliers_tree.heading('phone', text='رقم الهاتف')
            self.suppliers_tree.column('name', width=300)
            self.suppliers_tree.column('phone', width=200)
            
            # Add scrollbar
            scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.suppliers_tree.yview)
            self.suppliers_tree.configure(yscrollcommand=scrollbar.set)
            
            self.suppliers_tree.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')
            
        except Exception as e:
            print(f"❌ خطأ في تبويب الموردين: {e}")
    
    def create_products_tab(self):
        """Create optimized products management tab"""
        try:
            # Products tab
            products_frame = ttk.Frame(self.notebook)
            self.notebook.add(products_frame, text="📦 المنتجات")
            
            # Input frame
            input_frame = ttk.LabelFrame(products_frame, text="إضافة منتج جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)
            
            # Row 1
            ttk.Label(input_frame, text="اسم المنتج:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.product_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_name, width=25).grid(row=0, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الباركود:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.product_barcode = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_barcode, width=15).grid(row=0, column=3, padx=5, pady=5)
            
            # Row 2
            ttk.Label(input_frame, text="السعر الأساسي:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
            self.product_price = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_price, width=15).grid(row=1, column=1, padx=5, pady=5)
            
            ttk.Label(input_frame, text="الكمية:").grid(row=1, column=2, padx=5, pady=5, sticky='e')
            self.product_stock = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.product_stock, width=15).grid(row=1, column=3, padx=5, pady=5)
            
            # Add button
            add_btn = ttk.Button(input_frame, text="إضافة منتج", command=self.add_product)
            add_btn.grid(row=1, column=4, padx=10, pady=5)
            
            # Products list
            list_frame = ttk.LabelFrame(products_frame, text="قائمة المنتجات", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)
            
            # Optimized Treeview
            self.products_tree = ttk.Treeview(list_frame, 
                                            columns=('name', 'barcode', 'price', 'stock'), 
                                            show='headings', height=15)
            self.products_tree.heading('name', text='اسم المنتج')
            self.products_tree.heading('barcode', text='الباركود')
            self.products_tree.heading('price', text='السعر')
            self.products_tree.heading('stock', text='الكمية')
            
            # Column widths
            self.products_tree.column('name', width=250)
            self.products_tree.column('barcode', width=150)
            self.products_tree.column('price', width=100)
            self.products_tree.column('stock', width=100)
            
            # Add scrollbar
            scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.products_tree.yview)
            self.products_tree.configure(yscrollcommand=scrollbar.set)
            
            self.products_tree.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')
            
        except Exception as e:
            print(f"❌ خطأ في تبويب المنتجات: {e}")

    def create_customers_tab(self):
        """Create optimized customers management tab"""
        try:
            # Customers tab
            customers_frame = ttk.Frame(self.notebook)
            self.notebook.add(customers_frame, text="👥 العملاء")

            # Input frame
            input_frame = ttk.LabelFrame(customers_frame, text="إضافة عميل جديد", padding=10)
            input_frame.pack(fill='x', padx=10, pady=5)

            # Customer name
            ttk.Label(input_frame, text="اسم العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.customer_name = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)

            # Customer phone
            ttk.Label(input_frame, text="رقم الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.customer_phone = tk.StringVar()
            ttk.Entry(input_frame, textvariable=self.customer_phone, width=20).grid(row=0, column=3, padx=5, pady=5)

            # Customer type
            ttk.Label(input_frame, text="نوع العميل:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
            self.customer_type = tk.StringVar()
            customer_type_combo = ttk.Combobox(input_frame, textvariable=self.customer_type, width=22)
            customer_type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
            customer_type_combo.grid(row=1, column=1, padx=5, pady=5)
            customer_type_combo.set('تجزئة')

            # Add button
            add_btn = ttk.Button(input_frame, text="إضافة عميل", command=self.add_customer)
            add_btn.grid(row=1, column=3, padx=10, pady=5)

            # Customers list
            list_frame = ttk.LabelFrame(customers_frame, text="قائمة العملاء", padding=10)
            list_frame.pack(fill='both', expand=True, padx=10, pady=5)

            # Optimized Treeview
            self.customers_tree = ttk.Treeview(list_frame,
                                             columns=('name', 'phone', 'type'),
                                             show='headings', height=15)
            self.customers_tree.heading('name', text='اسم العميل')
            self.customers_tree.heading('phone', text='رقم الهاتف')
            self.customers_tree.heading('type', text='نوع العميل')

            # Column widths
            self.customers_tree.column('name', width=250)
            self.customers_tree.column('phone', width=200)
            self.customers_tree.column('type', width=150)

            # Add scrollbar
            scrollbar = ttk.Scrollbar(list_frame, orient='vertical', command=self.customers_tree.yview)
            self.customers_tree.configure(yscrollcommand=scrollbar.set)

            self.customers_tree.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')

        except Exception as e:
            print(f"❌ خطأ في تبويب العملاء: {e}")

    def create_sales_tab(self):
        """Create optimized sales management tab"""
        try:
            # Sales tab
            sales_frame = ttk.Frame(self.notebook)
            self.notebook.add(sales_frame, text="💰 المبيعات")

            # Customer info frame
            customer_frame = ttk.LabelFrame(sales_frame, text="معلومات العميل", padding=10)
            customer_frame.pack(fill='x', padx=10, pady=5)

            # Customer name
            ttk.Label(customer_frame, text="اسم العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.sale_customer_name = tk.StringVar()
            ttk.Entry(customer_frame, textvariable=self.sale_customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)

            # Customer type
            ttk.Label(customer_frame, text="نوع العميل:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.sale_customer_type = tk.StringVar()
            sale_customer_type_combo = ttk.Combobox(customer_frame, textvariable=self.sale_customer_type, width=15)
            sale_customer_type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
            sale_customer_type_combo.grid(row=0, column=3, padx=5, pady=5)
            sale_customer_type_combo.set('تجزئة')

            # Product input frame
            product_frame = ttk.LabelFrame(sales_frame, text="إضافة منتج للفاتورة", padding=10)
            product_frame.pack(fill='x', padx=10, pady=5)

            # Barcode input
            ttk.Label(product_frame, text="الباركود:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
            self.sale_barcode = tk.StringVar()
            barcode_entry = ttk.Entry(product_frame, textvariable=self.sale_barcode, width=20)
            barcode_entry.grid(row=0, column=1, padx=5, pady=5)
            barcode_entry.bind('<Return>', self.add_product_to_sale)

            # Quantity
            ttk.Label(product_frame, text="الكمية:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
            self.sale_quantity = tk.StringVar()
            self.sale_quantity.set('1')
            ttk.Entry(product_frame, textvariable=self.sale_quantity, width=10).grid(row=0, column=3, padx=5, pady=5)

            # Add button
            add_btn = ttk.Button(product_frame, text="إضافة", command=self.add_product_to_sale)
            add_btn.grid(row=0, column=4, padx=10, pady=5)

            # Sale products list
            sale_list_frame = ttk.LabelFrame(sales_frame, text="منتجات الفاتورة", padding=10)
            sale_list_frame.pack(fill='both', expand=True, padx=10, pady=5)

            # Optimized Treeview for sale products
            self.sale_tree = ttk.Treeview(sale_list_frame,
                                        columns=('name', 'quantity', 'unit_price', 'total'),
                                        show='headings', height=10)
            self.sale_tree.heading('name', text='اسم المنتج')
            self.sale_tree.heading('quantity', text='الكمية')
            self.sale_tree.heading('unit_price', text='سعر الوحدة')
            self.sale_tree.heading('total', text='المجموع')

            # Column widths
            self.sale_tree.column('name', width=250)
            self.sale_tree.column('quantity', width=100)
            self.sale_tree.column('unit_price', width=120)
            self.sale_tree.column('total', width=120)

            # Add scrollbar
            scrollbar = ttk.Scrollbar(sale_list_frame, orient='vertical', command=self.sale_tree.yview)
            self.sale_tree.configure(yscrollcommand=scrollbar.set)

            self.sale_tree.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')

            # Total and buttons frame
            total_frame = ttk.Frame(sales_frame)
            total_frame.pack(fill='x', padx=10, pady=5)

            # Total label
            self.total_label = ttk.Label(total_frame, text="المجموع الكلي: 0.00",
                                       font=('Arial', 14, 'bold'))
            self.total_label.pack(side='left', padx=10)

            # Buttons
            ttk.Button(total_frame, text="حفظ الفاتورة", command=self.save_sale).pack(side='right', padx=5)
            ttk.Button(total_frame, text="مسح الكل", command=self.clear_sale).pack(side='right', padx=5)

        except Exception as e:
            print(f"❌ خطأ في تبويب المبيعات: {e}")

    def create_reports_tab(self):
        """Create optimized reports tab"""
        try:
            # Reports tab
            reports_frame = ttk.Frame(self.notebook)
            self.notebook.add(reports_frame, text="📊 التقارير")

            # Reports buttons
            buttons_frame = ttk.LabelFrame(reports_frame, text="التقارير المتاحة", padding=10)
            buttons_frame.pack(fill='x', padx=10, pady=10)

            ttk.Button(buttons_frame, text="تقرير المبيعات",
                      command=self.show_sales_report).pack(side='left', padx=10, pady=10)
            ttk.Button(buttons_frame, text="تقرير المخزون",
                      command=self.show_inventory_report).pack(side='left', padx=10, pady=10)
            ttk.Button(buttons_frame, text="تقرير العملاء",
                      command=self.show_customers_report).pack(side='left', padx=10, pady=10)

            # Reports display area
            display_frame = ttk.LabelFrame(reports_frame, text="عرض التقارير", padding=10)
            display_frame.pack(fill='both', expand=True, padx=10, pady=10)

            # Text widget with scrollbar
            text_frame = ttk.Frame(display_frame)
            text_frame.pack(fill='both', expand=True)

            self.reports_text = tk.Text(text_frame, wrap='word', font=('Arial', 11),
                                      bg='white', fg='black')
            scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.reports_text.yview)
            self.reports_text.configure(yscrollcommand=scrollbar.set)

            self.reports_text.pack(side='left', fill='both', expand=True)
            scrollbar.pack(side='right', fill='y')

        except Exception as e:
            print(f"❌ خطأ في تبويب التقارير: {e}")

    def create_status_bar(self):
        """Create optimized status bar"""
        try:
            self.status_frame = ttk.Frame(self.root)
            self.status_frame.pack(side='bottom', fill='x', padx=10, pady=5)

            # Status label
            self.status_label = ttk.Label(self.status_frame, text="جاهز | Ready",
                                        font=('Arial', 10))
            self.status_label.pack(side='left', padx=10)

            # Performance info
            self.performance_label = ttk.Label(self.status_frame, text="",
                                             font=('Arial', 9), foreground='gray')
            self.performance_label.pack(side='left', padx=20)

            # Save button
            save_btn = ttk.Button(self.status_frame, text="💾 حفظ البيانات",
                                command=self.save_data)
            save_btn.pack(side='right', padx=10)

            # Update performance info periodically
            self.update_performance_info()

        except Exception as e:
            print(f"❌ خطأ في شريط الحالة: {e}")

    def update_performance_info(self):
        """Update performance information"""
        try:
            uptime = time.time() - self.performance_stats['startup_time']
            total_records = len(self.products) + len(self.customers) + len(self.suppliers)

            info_text = f"وقت التشغيل: {uptime:.0f}s | السجلات: {total_records} | العمليات: {self.performance_stats['data_operations']}"
            self.performance_label.config(text=info_text)

            # Schedule next update
            self.root.after(5000, self.update_performance_info)

        except Exception as e:
            print(f"خطأ في تحديث معلومات الأداء: {e}")

    # Data Management Functions
    def add_supplier(self):
        """Add new supplier with optimization"""
        try:
            name = self.supplier_name.get().strip()
            phone = self.supplier_phone.get().strip()

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المورد")
                return

            # Check for duplicates
            for supplier in self.suppliers:
                if supplier.get('name', '').lower() == name.lower():
                    messagebox.showwarning("تحذير", "المورد موجود بالفعل")
                    return

            supplier = {
                'id': len(self.suppliers) + 1,
                'name': name,
                'phone': phone,
                'date_added': datetime.now().isoformat()
            }

            self.suppliers.append(supplier)
            self.update_suppliers_display()

            # Clear inputs
            self.supplier_name.set('')
            self.supplier_phone.set('')

            self.status_label.config(text=f"تم إضافة المورد: {name}")
            self.performance_stats['data_operations'] += 1

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المورد: {e}")

    def add_product(self):
        """Add new product with optimization"""
        try:
            name = self.product_name.get().strip()
            barcode = self.product_barcode.get().strip()
            price = float(self.product_price.get() or 0)
            stock = int(self.product_stock.get() or 0)

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                return

            if price <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال سعر صحيح")
                return

            # Check for duplicate barcode
            if barcode:
                for product in self.products:
                    if product.get('barcode') == barcode:
                        messagebox.showwarning("تحذير", "الباركود موجود بالفعل")
                        return

            product = {
                'id': len(self.products) + 1,
                'name': name,
                'barcode': barcode,
                'price': price,
                'stock': stock,
                'date_added': datetime.now().isoformat()
            }

            self.products.append(product)
            self.update_products_display()

            # Clear inputs
            self.product_name.set('')
            self.product_barcode.set('')
            self.product_price.set('')
            self.product_stock.set('')

            self.status_label.config(text=f"تم إضافة المنتج: {name}")
            self.performance_stats['data_operations'] += 1

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للسعر والكمية")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المنتج: {e}")

    def add_customer(self):
        """Add new customer with optimization"""
        try:
            name = self.customer_name.get().strip()
            phone = self.customer_phone.get().strip()
            customer_type = self.customer_type.get()

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            # Check for duplicates
            for customer in self.customers:
                if customer.get('name', '').lower() == name.lower():
                    messagebox.showwarning("تحذير", "العميل موجود بالفعل")
                    return

            customer = {
                'id': len(self.customers) + 1,
                'name': name,
                'phone': phone,
                'type': customer_type,
                'balance': 0.0,
                'date_added': datetime.now().isoformat()
            }

            self.customers.append(customer)
            self.update_customers_display()

            # Clear inputs
            self.customer_name.set('')
            self.customer_phone.set('')
            self.customer_type.set('تجزئة')

            self.status_label.config(text=f"تم إضافة العميل: {name}")
            self.performance_stats['data_operations'] += 1

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة العميل: {e}")

    def add_product_to_sale(self, event=None):
        """Add product to current sale with optimization"""
        try:
            barcode = self.sale_barcode.get().strip()
            quantity = int(self.sale_quantity.get() or 1)

            if not barcode:
                messagebox.showerror("خطأ", "يرجى إدخال الباركود")
                return

            if quantity <= 0:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
                return

            # Find product by barcode
            product = None
            for p in self.products:
                if p.get('barcode') == barcode or p.get('name', '').lower() == barcode.lower():
                    product = p
                    break

            if not product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return

            if product['stock'] < quantity:
                messagebox.showerror("خطأ", f"المخزون غير كافي. المتاح: {product['stock']}")
                return

            # Calculate price based on customer type
            base_price = product['price']
            customer_type = self.sale_customer_type.get()

            # Price margins
            margins = {
                'تجزئة': 1.30,      # +30%
                'جملة': 1.20,       # +20%
                'موزع معتمد': 1.15, # +15%
                'صاحب محل': 1.05   # +5%
            }

            final_price = base_price * margins.get(customer_type, 1.30)
            total = final_price * quantity

            # Add to sale tree
            self.sale_tree.insert('', 'end', values=(
                product['name'],
                quantity,
                f"{final_price:.2f}",
                f"{total:.2f}"
            ))

            # Update total
            self.update_sale_total()

            # Clear barcode input
            self.sale_barcode.set('')
            self.sale_quantity.set('1')

            self.status_label.config(text=f"تم إضافة {product['name']} للفاتورة")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المنتج: {e}")

    def update_sale_total(self):
        """Update sale total with optimization"""
        try:
            total = 0
            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                if len(values) >= 4:
                    total += float(values[3])  # Total column

            self.total_label.config(text=f"المجموع الكلي: {total:.2f}")

        except Exception as e:
            print(f"خطأ في حساب المجموع: {e}")

    def save_sale(self):
        """Save current sale with optimization"""
        try:
            customer_name = self.sale_customer_name.get().strip()
            customer_type = self.sale_customer_type.get()

            if not customer_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            # Get sale items
            items = []
            total = 0

            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                if len(values) >= 4:
                    item_data = {
                        'name': values[0],
                        'quantity': int(values[1]),
                        'unit_price': float(values[2]),
                        'total': float(values[3])
                    }
                    items.append(item_data)
                    total += float(values[3])

                    # Update product stock
                    for product in self.products:
                        if product['name'] == values[0]:
                            product['stock'] -= int(values[1])
                            break

            if not items:
                messagebox.showerror("خطأ", "لا توجد منتجات في الفاتورة")
                return

            # Create sale record
            sale = {
                'id': len(self.sales) + 1,
                'customer_name': customer_name,
                'customer_type': customer_type,
                'items': items,
                'total': total,
                'date': datetime.now().isoformat()
            }

            self.sales.append(sale)

            # Clear sale
            self.clear_sale()

            # Update displays
            self.update_products_display()

            messagebox.showinfo("نجح", f"تم حفظ الفاتورة بمبلغ {total:.2f}")
            self.status_label.config(text=f"تم حفظ فاتورة بمبلغ {total:.2f}")
            self.performance_stats['data_operations'] += 1

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الفاتورة: {e}")

    def clear_sale(self):
        """Clear current sale"""
        try:
            # Clear tree
            for item in self.sale_tree.get_children():
                self.sale_tree.delete(item)

            # Clear inputs
            self.sale_customer_name.set('')
            self.sale_customer_type.set('تجزئة')
            self.sale_barcode.set('')
            self.sale_quantity.set('1')

            # Reset total
            self.total_label.config(text="المجموع الكلي: 0.00")

            self.status_label.config(text="تم مسح الفاتورة")

        except Exception as e:
            print(f"خطأ في مسح الفاتورة: {e}")

    # Display Update Functions
    def update_suppliers_display(self):
        """Update suppliers display with optimization"""
        try:
            # Clear tree
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # Add suppliers
            for supplier in self.suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier.get('name', ''),
                    supplier.get('phone', '')
                ))

        except Exception as e:
            print(f"خطأ في تحديث عرض الموردين: {e}")

    def update_products_display(self):
        """Update products display with optimization"""
        try:
            # Clear tree
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # Add products
            for product in self.products:
                self.products_tree.insert('', 'end', values=(
                    product.get('name', ''),
                    product.get('barcode', ''),
                    f"{product.get('price', 0):.2f}",
                    product.get('stock', 0)
                ))

        except Exception as e:
            print(f"خطأ في تحديث عرض المنتجات: {e}")

    def update_customers_display(self):
        """Update customers display with optimization"""
        try:
            # Clear tree
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # Add customers
            for customer in self.customers:
                self.customers_tree.insert('', 'end', values=(
                    customer.get('name', ''),
                    customer.get('phone', ''),
                    customer.get('type', 'تجزئة')
                ))

        except Exception as e:
            print(f"خطأ في تحديث عرض العملاء: {e}")

    # Reports Functions
    def show_sales_report(self):
        """Show optimized sales report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "📊 تقرير المبيعات الشامل\n"
            report += "=" * 60 + "\n\n"

            if not self.sales:
                report += "لا توجد مبيعات مسجلة.\n"
            else:
                total_sales = 0
                total_profit = 0

                for i, sale in enumerate(self.sales, 1):
                    report += f"🧾 فاتورة رقم {i}:\n"
                    report += f"   العميل: {sale.get('customer_name', 'غير محدد')}\n"
                    report += f"   نوع العميل: {sale.get('customer_type', 'غير محدد')}\n"
                    report += f"   التاريخ: {sale.get('date', '')[:10]}\n"
                    report += f"   المبلغ: {sale.get('total', 0):.2f}\n"

                    # Calculate profit
                    sale_profit = 0
                    for item in sale.get('items', []):
                        # Find original product price
                        for product in self.products:
                            if product['name'] == item['name']:
                                item_profit = (item['unit_price'] - product['price']) * item['quantity']
                                sale_profit += item_profit
                                break

                    report += f"   الربح المقدر: {sale_profit:.2f}\n"
                    report += "-" * 40 + "\n"

                    total_sales += sale.get('total', 0)
                    total_profit += sale_profit

                report += f"\n📈 الإحصائيات الإجمالية:\n"
                report += f"   إجمالي المبيعات: {total_sales:.2f}\n"
                report += f"   إجمالي الربح المقدر: {total_profit:.2f}\n"
                report += f"   عدد الفواتير: {len(self.sales)}\n"
                report += f"   متوسط قيمة الفاتورة: {total_sales/len(self.sales):.2f}\n"

            self.reports_text.insert(1.0, report)
            self.status_label.config(text="تم عرض تقرير المبيعات")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض تقرير المبيعات: {e}")

    def show_inventory_report(self):
        """Show optimized inventory report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "📦 تقرير المخزون الشامل\n"
            report += "=" * 60 + "\n\n"

            if not self.products:
                report += "لا توجد منتجات مسجلة.\n"
            else:
                total_value = 0
                low_stock_items = []

                for product in self.products:
                    value = product.get('price', 0) * product.get('stock', 0)
                    total_value += value

                    report += f"📦 المنتج: {product.get('name', 'غير محدد')}\n"
                    report += f"   الباركود: {product.get('barcode', 'غير محدد')}\n"
                    report += f"   السعر الأساسي: {product.get('price', 0):.2f}\n"
                    report += f"   الكمية المتاحة: {product.get('stock', 0)}\n"
                    report += f"   القيمة الإجمالية: {value:.2f}\n"

                    # Check for low stock
                    if product.get('stock', 0) < 10:
                        low_stock_items.append(product['name'])
                        report += "   ⚠️ مخزون منخفض!\n"

                    report += "-" * 40 + "\n"

                report += f"\n📊 إحصائيات المخزون:\n"
                report += f"   إجمالي قيمة المخزون: {total_value:.2f}\n"
                report += f"   عدد المنتجات: {len(self.products)}\n"
                report += f"   متوسط قيمة المنتج: {total_value/len(self.products):.2f}\n"

                if low_stock_items:
                    report += f"\n⚠️ منتجات بمخزون منخفض ({len(low_stock_items)}):\n"
                    for item in low_stock_items:
                        report += f"   • {item}\n"

            self.reports_text.insert(1.0, report)
            self.status_label.config(text="تم عرض تقرير المخزون")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض تقرير المخزون: {e}")

    def show_customers_report(self):
        """Show optimized customers report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "👥 تقرير العملاء الشامل\n"
            report += "=" * 60 + "\n\n"

            if not self.customers:
                report += "لا يوجد عملاء مسجلون.\n"
            else:
                # Count customers by type
                customer_types = {}
                for customer in self.customers:
                    customer_type = customer.get('type', 'تجزئة')
                    customer_types[customer_type] = customer_types.get(customer_type, 0) + 1

                for customer in self.customers:
                    report += f"👤 العميل: {customer.get('name', 'غير محدد')}\n"
                    report += f"   الهاتف: {customer.get('phone', 'غير محدد')}\n"
                    report += f"   النوع: {customer.get('type', 'تجزئة')}\n"
                    report += f"   الرصيد: {customer.get('balance', 0):.2f}\n"
                    report += f"   تاريخ الإضافة: {customer.get('date_added', '')[:10]}\n"
                    report += "-" * 40 + "\n"

                report += f"\n📊 إحصائيات العملاء:\n"
                report += f"   إجمالي العملاء: {len(self.customers)}\n"

                report += "\n📈 توزيع العملاء حسب النوع:\n"
                for customer_type, count in customer_types.items():
                    percentage = (count / len(self.customers)) * 100
                    report += f"   {customer_type}: {count} ({percentage:.1f}%)\n"

            self.reports_text.insert(1.0, report)
            self.status_label.config(text="تم عرض تقرير العملاء")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض تقرير العملاء: {e}")

    # Data Management Functions
    def load_data(self):
        """Load data from file with optimization"""
        try:
            self.loading = True

            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])

                print(f"📥 تم تحميل البيانات: {len(self.suppliers)} موردين، {len(self.products)} منتجات، {len(self.customers)} عملاء، {len(self.sales)} مبيعات")

                # Update displays
                self.update_suppliers_display()
                self.update_products_display()
                self.update_customers_display()

                self.status_label.config(text="تم تحميل البيانات بنجاح")
                self.performance_stats['data_operations'] += 1
            else:
                print("📝 لا يوجد ملف بيانات، سيتم البدء بملف فارغ")
                self.status_label.config(text="بدء جديد - لا توجد بيانات سابقة")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")
        finally:
            self.loading = False

    def save_data(self):
        """Save data to file with optimization"""
        try:
            if self.loading:
                return

            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0'
            }

            # Save to main file
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            # Create backup
            backup_file = f"protech_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            print("💾 تم حفظ البيانات بنجاح")
            self.status_label.config(text="تم حفظ البيانات بنجاح")
            self.performance_stats['data_operations'] += 1

            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {e}")
            return False

    def run(self):
        """Run the application with optimization"""
        try:
            print("🚀 تشغيل ProTech المحسن...")
            print("🧮 ProTech Fixed & Optimized Accounting System")
            print("=" * 60)

            # Setup window close handler
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # Bring window to front
            self.root.lift()
            self.root.attributes('-topmost', True)
            self.root.after_idle(lambda: self.root.attributes('-topmost', False))

            # Start the main loop
            self.root.mainloop()

        except Exception as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج: {e}")

    def on_closing(self):
        """Handle window closing with optimization"""
        try:
            # Save data before closing
            if self.save_data():
                print("👋 إغلاق ProTech...")
                self.root.destroy()
            else:
                if messagebox.askyesno("تأكيد", "فشل في حفظ البيانات. هل تريد الإغلاق بدون حفظ؟"):
                    self.root.destroy()
        except Exception as e:
            print(f"خطأ عند الإغلاق: {e}")
            self.root.destroy()

def main():
    """Main function with optimization"""
    try:
        print("🧮 ProTech Fixed & Optimized - Accounting System")
        print("نظام ProTech المصلح والمحسن للمحاسبة")
        print("=" * 70)

        # Create and run the application
        app = ProTechFixedOptimized()
        app.run()

    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
