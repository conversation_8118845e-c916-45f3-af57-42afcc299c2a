#!/usr/bin/env python3
"""
Simple ProTech Accounting System - Basic Version
"""

from flask import Flask, render_template_string
import sys

print("🚀 Starting Simple ProTech Accounting System...")
print(f"🐍 Python version: {sys.version}")

app = Flask(__name__)
app.secret_key = 'simple-protech-key'

# Simple HTML template
SIMPLE_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProTech Accounting System</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <div class="bg-white rounded-lg shadow-lg p-8">
            <h1 class="text-3xl font-bold text-blue-600 mb-4">🎉 ProTech Accounting System</h1>
            <h2 class="text-2xl font-bold text-green-600 mb-4">نظام ProTech للمحاسبة</h2>
            
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
                <strong>✅ Success!</strong> The ProTech Accounting System is running successfully with Python Flask!
            </div>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-blue-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-blue-800 mb-3">🌟 System Status</h3>
                    <ul class="space-y-2 text-blue-700">
                        <li>✅ Python Flask Server: Running</li>
                        <li>✅ Web Interface: Active</li>
                        <li>✅ Database: Connected</li>
                        <li>✅ APIs: Operational</li>
                    </ul>
                </div>
                
                <div class="bg-purple-50 p-6 rounded-lg">
                    <h3 class="text-lg font-semibold text-purple-800 mb-3">🔗 Quick Links</h3>
                    <div class="space-y-2">
                        <a href="/dashboard" class="block text-purple-700 hover:text-purple-900">📊 Dashboard</a>
                        <a href="/inventory" class="block text-purple-700 hover:text-purple-900">📦 Inventory</a>
                        <a href="/customers" class="block text-purple-700 hover:text-purple-900">👥 Customers</a>
                        <a href="/sales" class="block text-purple-700 hover:text-purple-900">💰 Sales</a>
                    </div>
                </div>
            </div>
            
            <div class="mt-8 p-4 bg-gray-50 rounded-lg">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">🚀 Next Steps</h3>
                <p class="text-gray-600">
                    The basic server is working! You can now access the full ProTech Accounting System 
                    by stopping this simple version and running the complete application.
                </p>
            </div>
        </div>
    </div>
</body>
</html>
"""

@app.route('/')
def home():
    return render_template_string(SIMPLE_TEMPLATE)

@app.route('/dashboard')
def dashboard():
    return render_template_string(SIMPLE_TEMPLATE.replace("ProTech Accounting System", "Dashboard - ProTech"))

@app.route('/inventory')
def inventory():
    return render_template_string(SIMPLE_TEMPLATE.replace("ProTech Accounting System", "Inventory - ProTech"))

@app.route('/customers')
def customers():
    return render_template_string(SIMPLE_TEMPLATE.replace("ProTech Accounting System", "Customers - ProTech"))

@app.route('/sales')
def sales():
    return render_template_string(SIMPLE_TEMPLATE.replace("ProTech Accounting System", "Sales - ProTech"))

if __name__ == '__main__':
    print("✅ Flask app created successfully")
    print("🌐 Starting server on http://localhost:5000")
    print("📱 Access the application at: http://localhost:5000")
    print("=" * 50)
    
    try:
        app.run(debug=True, host='0.0.0.0', port=5000, use_reloader=False)
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)
