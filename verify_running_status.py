#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Verify ProTech Running Status
التحقق من حالة تشغيل ProTech

Verify that ProTech is running correctly from both locations
التحقق من أن ProTech يعمل بشكل صحيح من كلا الموقعين
"""

import os
import subprocess
import time
from datetime import datetime

def check_running_processes():
    """Check for running ProTech processes"""
    try:
        print("🔍 فحص العمليات النشطة...")
        print("🔍 Checking Running Processes...")
        print("="*50)
        
        # Check for Python processes
        try:
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                python_processes = result.stdout.count('python.exe')
                print(f"🐍 عمليات Python نشطة: {python_processes}")
                
                if python_processes > 0:
                    print("✅ توجد عمليات Python نشطة")
                    
                    # Try to get more details
                    lines = result.stdout.split('\n')
                    for line in lines:
                        if 'python.exe' in line:
                            parts = line.split()
                            if len(parts) >= 2:
                                pid = parts[1]
                                print(f"  🔹 PID: {pid}")
                else:
                    print("⚠️ لا توجد عمليات Python نشطة")
            else:
                print("❌ فشل في فحص العمليات")
                
        except Exception as e:
            print(f"❌ خطأ في فحص العمليات: {e}")
        
        return python_processes > 0 if 'python_processes' in locals() else False
        
    except Exception as e:
        print(f"❌ خطأ عام في فحص العمليات: {e}")
        return False

def check_file_locations():
    """Check ProTech files in both locations"""
    try:
        print("\n📁 فحص مواقع الملفات...")
        print("📁 Checking File Locations...")
        print("="*50)
        
        locations = [
            ("المجلد الأصلي", "C:\\Users\\<USER>\\Documents\\augment-projects\\protech"),
            ("سطح المكتب", "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program")
        ]
        
        for location_name, path in locations:
            print(f"\n📍 {location_name}: {path}")
            
            if os.path.exists(path):
                print(f"✅ المجلد موجود")
                
                # Check for main file
                main_file = os.path.join(path, "protech_simple_working.py")
                if os.path.exists(main_file):
                    size = os.path.getsize(main_file) / 1024
                    mod_time = datetime.fromtimestamp(os.path.getmtime(main_file))
                    print(f"✅ protech_simple_working.py: {size:.1f} KB - {mod_time.strftime('%H:%M:%S')}")
                else:
                    print("❌ protech_simple_working.py: غير موجود")
                
                # Check for data file
                data_file = os.path.join(path, "protech_simple_data.json")
                if os.path.exists(data_file):
                    size = os.path.getsize(data_file)
                    print(f"✅ protech_simple_data.json: {size} bytes")
                else:
                    print("❌ protech_simple_data.json: غير موجود")
                
                # Check for smart tools (only in original location)
                if "augment-projects" in path:
                    smart_files = [
                        "smart_protech_wrapper.py",
                        "start_smart_protech.bat"
                    ]
                    
                    for smart_file in smart_files:
                        smart_path = os.path.join(path, smart_file)
                        if os.path.exists(smart_path):
                            size = os.path.getsize(smart_path) / 1024
                            print(f"✅ {smart_file}: {size:.1f} KB")
                        else:
                            print(f"❌ {smart_file}: غير موجود")
            else:
                print(f"❌ المجلد غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في فحص المواقع: {e}")
        return False

def test_program_startup():
    """Test program startup from both locations"""
    try:
        print("\n🚀 اختبار بدء التشغيل...")
        print("🚀 Testing Program Startup...")
        print("="*50)
        
        # Test 1: Original location with smart wrapper
        print("\n🧠 اختبار الغلاف الذكي...")
        original_path = "C:\\Users\\<USER>\\Documents\\augment-projects\\protech"
        
        if os.path.exists(os.path.join(original_path, "smart_protech_wrapper.py")):
            try:
                # Quick test of smart wrapper
                result = subprocess.run([
                    'python', '-c', 
                    'import sys; sys.path.insert(0, r"' + original_path + '"); '
                    'from smart_protech_wrapper import SmartWrapper; '
                    'print("✅ الغلاف الذكي قابل للتحميل")'
                ], capture_output=True, text=True, timeout=5)
                
                if result.returncode == 0:
                    print("✅ الغلاف الذكي: يعمل")
                else:
                    print(f"❌ الغلاف الذكي: خطأ - {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print("⏰ الغلاف الذكي: انتهت المهلة الزمنية")
            except Exception as e:
                print(f"❌ الغلاف الذكي: خطأ - {e}")
        else:
            print("❌ الغلاف الذكي غير موجود")
        
        # Test 2: Desktop location
        print("\n🖥️ اختبار النسخة على سطح المكتب...")
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        
        if os.path.exists(os.path.join(desktop_path, "protech_simple_working.py")):
            try:
                # Quick syntax check
                result = subprocess.run([
                    'python', '-m', 'py_compile', 
                    os.path.join(desktop_path, "protech_simple_working.py")
                ], capture_output=True, text=True, timeout=10)
                
                if result.returncode == 0:
                    print("✅ النسخة على سطح المكتب: تُجمع بنجاح")
                else:
                    print(f"❌ النسخة على سطح المكتب: خطأ تجميع")
                    
            except subprocess.TimeoutExpired:
                print("⏰ النسخة على سطح المكتب: انتهت المهلة الزمنية")
            except Exception as e:
                print(f"❌ النسخة على سطح المكتب: خطأ - {e}")
        else:
            print("❌ النسخة على سطح المكتب غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار بدء التشغيل: {e}")
        return False

def check_window_visibility():
    """Check if ProTech windows are visible"""
    try:
        print("\n🖥️ فحص النوافذ المرئية...")
        print("🖥️ Checking Visible Windows...")
        print("="*50)
        
        # Try to detect ProTech windows
        try:
            result = subprocess.run(['tasklist', '/V'], capture_output=True, text=True)
            
            if result.returncode == 0:
                lines = result.stdout.split('\n')
                protech_windows = []
                
                for line in lines:
                    if 'python.exe' in line and ('ProTech' in line or 'protech' in line):
                        protech_windows.append(line.strip())
                
                if protech_windows:
                    print(f"✅ تم العثور على {len(protech_windows)} نافذة ProTech")
                    for window in protech_windows:
                        print(f"  🖼️ {window}")
                else:
                    print("⚠️ لم يتم العثور على نوافذ ProTech مرئية")
                    print("💡 قد تكون النوافذ مخفية أو تعمل في الخلفية")
            else:
                print("❌ فشل في فحص النوافذ")
                
        except Exception as e:
            print(f"❌ خطأ في فحص النوافذ: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في فحص النوافذ: {e}")
        return False

def generate_running_status_report():
    """Generate comprehensive running status report"""
    try:
        print("📊 تقرير حالة التشغيل الشامل")
        print("📊 Comprehensive Running Status Report")
        print("="*60)
        
        # Run all checks
        processes_running = check_running_processes()
        files_ok = check_file_locations()
        startup_ok = test_program_startup()
        windows_ok = check_window_visibility()
        
        # Overall assessment
        print("\n📊 التقييم الشامل:")
        
        checks = [
            ("العمليات النشطة", processes_running),
            ("مواقع الملفات", files_ok),
            ("اختبار بدء التشغيل", startup_ok),
            ("فحص النوافذ", windows_ok)
        ]
        
        passed_checks = sum(1 for _, passed in checks if passed)
        total_checks = len(checks)
        success_rate = (passed_checks / total_checks) * 100
        
        if success_rate >= 75:
            status = "يعمل بنجاح"
            status_color = "🟢"
        elif success_rate >= 50:
            status = "يعمل جزئياً"
            status_color = "🟡"
        else:
            status = "مشكلة في التشغيل"
            status_color = "🔴"
        
        print(f"{status_color} حالة ProTech: {status} ({success_rate:.0f}%)")
        
        print(f"\n📋 تفاصيل الفحص:")
        for check_name, passed in checks:
            status = "✅" if passed else "❌"
            print(f"  {status} {check_name}")
        
        # Recommendations
        print("\n💡 التوصيات:")
        
        if success_rate >= 75:
            print("🎉 ProTech يعمل بنجاح!")
            print("• تحقق من النوافذ على الشاشة")
            print("• استخدم Alt+Tab للتنقل بين النوافذ")
            print("• ابحث عن أيقونة Python في شريط المهام")
        elif success_rate >= 50:
            print("⚠️ ProTech يعمل جزئياً")
            print("• تحقق من الأخطاء المذكورة أعلاه")
            print("• جرب إعادة التشغيل")
        else:
            print("❌ مشكلة في تشغيل ProTech")
            print("• استخدم الغلاف الذكي: start_smart_protech.bat")
            print("• أو شغل من سطح المكتب")
        
        # Current running status
        print("\n🎯 الحالة الحالية:")
        
        if processes_running:
            print("✅ ProTech يعمل حالياً")
            print("🖥️ تحقق من النوافذ على الشاشة")
            print("⌨️ استخدم Alt+Tab للعثور على النافذة")
        else:
            print("⚠️ ProTech غير نشط حالياً")
            print("🚀 شغل start_smart_protech.bat للتشغيل الآمن")
        
        print("\n" + "="*60)
        
        return success_rate >= 50
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير حالة التشغيل: {e}")
        return False

def main():
    """Main function"""
    success = generate_running_status_report()
    
    if success:
        print("\n🎉 تم التحقق من حالة التشغيل!")
        print("🎉 Running status verified!")
    else:
        print("\n❌ فشل في التحقق من حالة التشغيل")
        print("❌ Failed to verify running status")

if __name__ == "__main__":
    main()
