#!/usr/bin/env python3
"""
Fix Manual Startup Issue for ProTech
إصلاح مشكلة التشغيل اليدوي لـ ProTech

Fix the issue where manual startup doesn't load data properly
إصلاح مشكلة عدم تحميل البيانات عند التشغيل اليدوي
"""

import os
import re
import shutil
from datetime import datetime

def backup_file():
    """Create backup of current file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.startup_fix_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def fix_data_loading():
    """Fix data loading on startup"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find the __init__ method and ensure data loading is called
        init_pattern = r'(def __init__\(self.*?\):.*?)(def\s+\w+|class\s+\w+|\Z)'
        
        def fix_init_method(match):
            init_content = match.group(1)
            next_part = match.group(2) if match.group(2) else ''
            
            # Check if load_data is already called
            if 'self.load_data()' not in init_content:
                # Add load_data call at the end of __init__
                lines = init_content.split('\n')
                
                # Find the last meaningful line in __init__
                insert_index = len(lines) - 1
                for i in range(len(lines) - 1, -1, -1):
                    if lines[i].strip() and not lines[i].strip().startswith('#'):
                        insert_index = i + 1
                        break
                
                # Insert load_data call
                lines.insert(insert_index, '        ')
                lines.insert(insert_index + 1, '        # Load existing data on startup')
                lines.insert(insert_index + 2, '        self.load_data()')
                lines.insert(insert_index + 3, '        print("✅ تم تحميل البيانات الموجودة")')
                
                init_content = '\n'.join(lines)
                print("✅ تم إضافة استدعاء تحميل البيانات في __init__")
            
            return init_content + next_part
        
        content = re.sub(init_pattern, fix_init_method, content, flags=re.DOTALL)
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح تحميل البيانات: {e}")
        return False

def fix_load_data_method():
    """Fix the load_data method to ensure it works properly"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and improve the load_data method
        load_data_pattern = r'def load_data\(self\):(.*?)(?=def|\Z)'
        
        new_load_data = '''def load_data(self):
        """Load data from file with improved error handling"""
        try:
            # Set loading flag to prevent save during load
            self.loading = True
            
            # Define data file path
            data_file = getattr(self, 'data_file', 'protech_simple_data.json')
            
            # Check if data file exists
            if os.path.exists(data_file):
                print(f"📥 تحميل البيانات من: {data_file}")
                
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Load each data type with fallback
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                # Print loading summary
                print(f"📊 تم تحميل:")
                print(f"  • الموردين: {len(self.suppliers)}")
                print(f"  • المنتجات: {len(self.products)}")
                print(f"  • العملاء: {len(self.customers)}")
                print(f"  • المبيعات: {len(self.sales)}")
                
                # Update UI if methods exist
                if hasattr(self, 'update_suppliers_display'):
                    self.update_suppliers_display()
                if hasattr(self, 'update_products_display'):
                    self.update_products_display()
                if hasattr(self, 'update_customers_display'):
                    self.update_customers_display()
                
                print("✅ تم تحميل البيانات بنجاح")
                
            else:
                print(f"📝 ملف البيانات غير موجود، سيتم إنشاؤه: {data_file}")
                
                # Initialize empty data
                self.suppliers = []
                self.products = []
                self.customers = []
                self.sales = []
                
                # Create initial data file
                self.save_data()
                
            return True
            
        except json.JSONDecodeError as e:
            print(f"❌ خطأ في تنسيق ملف البيانات: {e}")
            # Try to load backup or create new
            return self.load_backup_data()
            
        except FileNotFoundError:
            print("📝 ملف البيانات غير موجود، سيتم إنشاء ملف جديد")
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            self.save_data()
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            return self.load_backup_data()
            
        finally:
            # Reset loading flag
            self.loading = False

    def load_backup_data(self):
        """Load data from backup files"""
        try:
            backup_files = [
                'protech_data_backup.json',
                'protech_simple_data.json.bak',
                'protech_emergency_save.json'
            ]
            
            for backup_file in backup_files:
                if os.path.exists(backup_file):
                    print(f"🔄 محاولة تحميل من النسخة الاحتياطية: {backup_file}")
                    
                    with open(backup_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    self.suppliers = data.get('suppliers', [])
                    self.products = data.get('products', [])
                    self.customers = data.get('customers', [])
                    self.sales = data.get('sales', [])
                    
                    print("✅ تم تحميل البيانات من النسخة الاحتياطية")
                    return True
            
            # If no backup found, initialize empty
            print("📝 لم يتم العثور على نسخ احتياطية، سيتم البدء بملف فارغ")
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تحميل النسخة الاحتياطية: {e}")
            # Initialize empty as last resort
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            return False

    '''
        
        # Replace the load_data method
        if 'def load_data(self):' in content:
            content = re.sub(load_data_pattern, new_load_data, content, flags=re.DOTALL)
            print("✅ تم تحسين دالة تحميل البيانات")
        else:
            # Add the method if it doesn't exist
            # Find a good place to add it (before save_data or at end of class)
            if 'def save_data(self):' in content:
                content = content.replace('def save_data(self):', new_load_data + '\n    def save_data(self):')
            else:
                # Add before the last method or end of class
                content = content.replace('if __name__ == "__main__":', new_load_data + '\nif __name__ == "__main__":')
            print("✅ تم إضافة دالة تحميل البيانات")
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح دالة التحميل: {e}")
        return False

def ensure_data_file_path():
    """Ensure data file path is properly set"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if data_file is defined in __init__
        if 'self.data_file' not in content:
            # Add data_file definition in __init__
            init_pattern = r'(def __init__\(self.*?\):.*?)(self\.root = tk\.Tk\(\))'
            replacement = r'\1self.data_file = "protech_simple_data.json"\n        \2'
            content = re.sub(init_pattern, replacement, content, flags=re.DOTALL)
            
            with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إضافة مسار ملف البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعيين مسار الملف: {e}")
        return False

def add_startup_debug():
    """Add debug information for startup"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add debug info at the start of __init__
        init_pattern = r'(def __init__\(self.*?\):)(.*?)(self\.root = tk\.Tk\(\))'
        
        def add_debug(match):
            method_def = match.group(1)
            existing_content = match.group(2)
            root_creation = match.group(3)
            
            debug_code = '''
        # Debug: Print startup information
        print("🚀 بدء تشغيل ProTech...")
        print(f"📁 مجلد العمل الحالي: {os.getcwd()}")
        print(f"📄 ملف البيانات: protech_simple_data.json")
        print(f"📊 فحص وجود ملف البيانات: {os.path.exists('protech_simple_data.json')}")
        
        '''
            
            return method_def + debug_code + existing_content + root_creation
        
        content = re.sub(init_pattern, add_debug, content, flags=re.DOTALL)
        
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة معلومات التشخيص")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة التشخيص: {e}")
        return False

def test_file_compilation():
    """Test if the file compiles correctly"""
    try:
        import py_compile
        py_compile.compile('protech_simple_working.py', doraise=True)
        print("✅ الملف يعمل بدون أخطاء تركيبية")
        return True
    except Exception as e:
        print(f"❌ خطأ في تركيب الملف: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح مشكلة التشغيل اليدوي لـ ProTech")
    print("🔧 Fixing Manual Startup Issue for ProTech")
    print()
    
    try:
        # Step 1: Create backup
        backup_file()
        
        # Step 2: Ensure data file path is set
        print("📁 تعيين مسار ملف البيانات...")
        ensure_data_file_path()
        
        # Step 3: Add startup debug info
        print("🔍 إضافة معلومات التشخيص...")
        add_startup_debug()
        
        # Step 4: Fix data loading
        print("📥 إصلاح تحميل البيانات...")
        if fix_data_loading():
            print("✅ تم إصلاح استدعاء تحميل البيانات")
        
        # Step 5: Improve load_data method
        print("🔧 تحسين دالة تحميل البيانات...")
        if fix_load_data_method():
            print("✅ تم تحسين دالة التحميل")
        
        # Step 6: Test compilation
        print("🧪 اختبار الملف...")
        if test_file_compilation():
            print("✅ الملف جاهز للتشغيل")
        
        print("\n" + "="*60)
        print("✅ تم إصلاح مشكلة التشغيل اليدوي بنجاح!")
        print("✅ Manual startup issue fixed successfully!")
        print("="*60)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إضافة استدعاء تحميل البيانات في __init__")
        print("• تحسين دالة load_data مع معالجة الأخطاء")
        print("• إضافة تحميل من النسخ الاحتياطية")
        print("• تعيين مسار ملف البيانات بوضوح")
        print("• إضافة معلومات تشخيص للتشغيل")
        print("• معالجة شاملة لحالات الخطأ")
        
        print("\n🚀 الآن عند الضغط يدوياً على الملف:")
        print("• سيتم تحميل البيانات تلقائياً")
        print("• ستظهر معلومات التشخيص")
        print("• سيعمل البرنامج مع جميع البيانات")
        print("• لن تحتاج لفتحه من خلالي")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح: {e}")
        return False

if __name__ == "__main__":
    main()
