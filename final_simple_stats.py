#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Final Simple Statistics
الإحصائيات البسيطة النهائية

Create a very simple statistics system without syntax issues
إنشاء نظام إحصائيات بسيط جداً بدون مشاكل تركيبية
"""

import os
import shutil
from datetime import datetime

def create_final_simple_stats():
    """إنشاء نظام الإحصائيات البسيط النهائي"""
    try:
        print("📊 إنشاء نظام الإحصائيات البسيط النهائي")
        print("📊 Creating Final Simple Statistics System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.final_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Very simple statistics method
        simple_stats_method = '''
    def create_advanced_statistics_page(self):
        """إنشاء صفحة الإحصائيات المتقدمة"""
        try:
            # Clear current page
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            
            self.current_page = 'advanced_statistics'
            
            # Main container
            main_container = tk.Frame(self.main_frame, bg='#f0f0f0')
            main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Title
            title_label = tk.Label(main_container, text="📊 Business Statistics Dashboard", 
                                 font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#2c3e50')
            title_label.pack(pady=(0, 20))
            
            # Get data
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            
            # Calculate basic metrics
            total_products = len(products)
            total_customers = len(customers)
            total_sales = len(sales)
            total_revenue = 0
            inventory_value = 0
            customer_debt = 0
            
            for sale in sales:
                total_revenue += float(sale.get('total', 0))
            
            for product in products:
                qty = float(product.get('quantity', 0))
                price = float(product.get('base_price', 0))
                inventory_value += qty * price
            
            for customer in customers:
                balance = float(customer.get('balance', 0))
                if balance > 0:
                    customer_debt += balance
            
            exchange_rate = 89500
            avg_sale = total_revenue / total_sales if total_sales > 0 else 0
            
            # Stats cards frame
            cards_frame = tk.Frame(main_container, bg='#f0f0f0')
            cards_frame.pack(fill='x', pady=(0, 20))
            
            # Create 8 stat cards
            card1 = tk.Frame(cards_frame, bg='#3498db', relief='raised', bd=3)
            card1.grid(row=0, col=0, padx=5, pady=5, sticky='ew')
            tk.Label(card1, text="🛍️ Products", font=("Arial", 10, "bold"), bg='#3498db', fg='white').pack(pady=(10, 5))
            tk.Label(card1, text=str(total_products), font=("Arial", 12, "bold"), bg='#3498db', fg='white').pack(pady=(0, 10))
            
            card2 = tk.Frame(cards_frame, bg='#e74c3c', relief='raised', bd=3)
            card2.grid(row=0, col=1, padx=5, pady=5, sticky='ew')
            tk.Label(card2, text="👥 Customers", font=("Arial", 10, "bold"), bg='#e74c3c', fg='white').pack(pady=(10, 5))
            tk.Label(card2, text=str(total_customers), font=("Arial", 12, "bold"), bg='#e74c3c', fg='white').pack(pady=(0, 10))
            
            card3 = tk.Frame(cards_frame, bg='#2ecc71', relief='raised', bd=3)
            card3.grid(row=0, col=2, padx=5, pady=5, sticky='ew')
            tk.Label(card3, text="💰 Sales", font=("Arial", 10, "bold"), bg='#2ecc71', fg='white').pack(pady=(10, 5))
            tk.Label(card3, text=str(total_sales), font=("Arial", 12, "bold"), bg='#2ecc71', fg='white').pack(pady=(0, 10))
            
            card4 = tk.Frame(cards_frame, bg='#f39c12', relief='raised', bd=3)
            card4.grid(row=0, col=3, padx=5, pady=5, sticky='ew')
            tk.Label(card4, text="💵 Revenue LBP", font=("Arial", 10, "bold"), bg='#f39c12', fg='white').pack(pady=(10, 5))
            tk.Label(card4, text=f"{total_revenue:,.0f}", font=("Arial", 12, "bold"), bg='#f39c12', fg='white').pack(pady=(0, 10))
            
            card5 = tk.Frame(cards_frame, bg='#9b59b6', relief='raised', bd=3)
            card5.grid(row=1, col=0, padx=5, pady=5, sticky='ew')
            tk.Label(card5, text="💲 Revenue USD", font=("Arial", 10, "bold"), bg='#9b59b6', fg='white').pack(pady=(10, 5))
            revenue_usd = total_revenue / exchange_rate
            tk.Label(card5, text=f"${revenue_usd:,.2f}", font=("Arial", 12, "bold"), bg='#9b59b6', fg='white').pack(pady=(0, 10))
            
            card6 = tk.Frame(cards_frame, bg='#1abc9c', relief='raised', bd=3)
            card6.grid(row=1, col=1, padx=5, pady=5, sticky='ew')
            tk.Label(card6, text="📦 Inventory", font=("Arial", 10, "bold"), bg='#1abc9c', fg='white').pack(pady=(10, 5))
            tk.Label(card6, text=f"{inventory_value:,.0f}", font=("Arial", 12, "bold"), bg='#1abc9c', fg='white').pack(pady=(0, 10))
            
            card7 = tk.Frame(cards_frame, bg='#e67e22', relief='raised', bd=3)
            card7.grid(row=1, col=2, padx=5, pady=5, sticky='ew')
            tk.Label(card7, text="📋 Debts", font=("Arial", 10, "bold"), bg='#e67e22', fg='white').pack(pady=(10, 5))
            tk.Label(card7, text=f"{customer_debt:,.0f}", font=("Arial", 12, "bold"), bg='#e67e22', fg='white').pack(pady=(0, 10))
            
            card8 = tk.Frame(cards_frame, bg='#34495e', relief='raised', bd=3)
            card8.grid(row=1, col=3, padx=5, pady=5, sticky='ew')
            tk.Label(card8, text="📈 Avg Sale", font=("Arial", 10, "bold"), bg='#34495e', fg='white').pack(pady=(10, 5))
            tk.Label(card8, text=f"{avg_sale:,.0f}", font=("Arial", 12, "bold"), bg='#34495e', fg='white').pack(pady=(0, 10))
            
            # Configure grid weights
            for i in range(4):
                cards_frame.grid_columnconfigure(i, weight=1)
            
            # Analysis section
            analysis_section = tk.Frame(main_container, bg='#ffffff', relief='ridge', bd=2)
            analysis_section.pack(fill='both', expand=True)
            
            # Analysis header
            header_frame = tk.Frame(analysis_section, bg='#34495e')
            header_frame.pack(fill='x')
            
            header_label = tk.Label(header_frame, text="📊 Business Analysis Report", 
                                  font=("Arial", 14, "bold"), bg='#34495e', fg='white')
            header_label.pack(side='left', padx=15, pady=10)
            
            # Export button
            export_btn = tk.Button(header_frame, text="Export Report", 
                                 command=self.export_business_report,
                                 bg='#e74c3c', fg='white', font=("Arial", 9, "bold"))
            export_btn.pack(side='right', padx=15, pady=10)
            
            # Analysis content
            content_frame = tk.Frame(analysis_section, bg='#ffffff')
            content_frame.pack(fill='both', expand=True, padx=15, pady=15)
            
            # Create analysis text
            analysis_text = self.create_analysis_text(total_products, total_customers, total_sales, 
                                                    total_revenue, inventory_value, customer_debt, exchange_rate)
            
            # Display analysis
            text_widget = tk.Text(content_frame, font=("Courier", 10), wrap='word', height=15)
            scrollbar = tk.Scrollbar(content_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            scrollbar.pack(side="right", fill="y")
            text_widget.pack(side="left", fill="both", expand=True)
            
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state='disabled')
            
            # Store for export
            self.current_report_data = analysis_text
            
            print("✅ تم إنشاء صفحة الإحصائيات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء صفحة الإحصائيات: {e}")
    
    def create_analysis_text(self, products_count, customers_count, sales_count, revenue, inventory, debt, rate):
        """إنشاء نص التحليل"""
        try:
            text = "BUSINESS ANALYSIS REPORT\\n"
            text += "=" * 50 + "\\n\\n"
            
            text += "OVERVIEW:\\n"
            text += "-" * 20 + "\\n"
            text += f"Total Products:    {products_count:>10}\\n"
            text += f"Total Customers:   {customers_count:>10}\\n"
            text += f"Total Sales:       {sales_count:>10}\\n\\n"
            
            text += "FINANCIAL SUMMARY:\\n"
            text += "-" * 20 + "\\n"
            text += f"Revenue (LBP):     {revenue:>15,.0f}\\n"
            text += f"Revenue (USD):     ${revenue/rate:>14,.2f}\\n"
            text += f"Inventory Value:   {inventory:>15,.0f} LBP\\n"
            text += f"Customer Debts:    {debt:>15,.0f} LBP\\n\\n"
            
            # Business health
            net_worth = revenue + inventory
            net_worth_usd = net_worth / rate
            
            text += "BUSINESS HEALTH:\\n"
            text += "-" * 20 + "\\n"
            text += f"Net Worth (LBP):   {net_worth:>15,.0f}\\n"
            text += f"Net Worth (USD):   ${net_worth_usd:>14,.2f}\\n"
            
            if net_worth_usd >= 50000:
                status = "EXCELLENT"
            elif net_worth_usd >= 25000:
                status = "VERY GOOD"
            elif net_worth_usd >= 10000:
                status = "GOOD"
            else:
                status = "NEEDS IMPROVEMENT"
            
            text += f"Status:            {status:>15}\\n\\n"
            
            text += "RECOMMENDATIONS:\\n"
            text += "-" * 20 + "\\n"
            text += "• Monitor inventory levels regularly\\n"
            text += "• Follow up on customer payments\\n"
            text += "• Track sales performance trends\\n"
            text += "• Update pricing strategies\\n"
            text += "• Maintain customer relationships\\n"
            
            return text
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء نص التحليل: {e}")
            return "Error creating analysis"
    
    def export_business_report(self):
        """تصدير تقرير الأعمال"""
        try:
            if hasattr(self, 'current_report_data'):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"business_report_{timestamp}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.current_report_data)
                
                print(f"✅ تم تصدير التقرير إلى {filename}")
                
                try:
                    import tkinter.messagebox as msgbox
                    msgbox.showinfo("Export Success", f"Report exported to {filename}")
                except:
                    pass
            else:
                print("❌ لا يوجد تقرير لتصديره")
                
        except Exception as e:
            print(f"❌ خطأ في تصدير التقرير: {e}")
'''
        
        # Add the statistics method
        last_method = content.rfind("\n    def export_current_table(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + simple_stats_method + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + simple_stats_method + content[last_method:]
        
        # Update the statistics button
        if 'btn6 = tk.Button(sidebar, text="رصيد المحل"' in content:
            # Find and replace the button
            start_pos = content.find('btn6 = tk.Button(sidebar, text="رصيد المحل"')
            if start_pos != -1:
                # Find the end of this button definition
                end_pos = start_pos
                paren_count = 0
                in_button = False
                
                for i, char in enumerate(content[start_pos:]):
                    if char == '(':
                        paren_count += 1
                        in_button = True
                    elif char == ')':
                        paren_count -= 1
                        if in_button and paren_count == 0:
                            end_pos = start_pos + i + 1
                            break
                
                old_button = content[start_pos:end_pos]
                new_button = '''btn6 = tk.Button(sidebar, text="Business Stats", 
                           font=("Arial", 10), bg='#16a085', fg='white', 
                           width=18, height=2, command=self.create_advanced_statistics_page)'''
                
                content = content.replace(old_button, new_button)
                print("✅ تم تحديث زر الإحصائيات")
            else:
                print("❌ لم يتم العثور على بداية تعريف الزر")
                return False
        else:
            print("❌ لم يتم العثور على زر رصيد المحل")
            return False
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إنشاء نظام الإحصائيات البسيط النهائي")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نظام الإحصائيات البسيط النهائي: {e}")
        return False

def main():
    """Main function"""
    print("📊 إنشاء نظام الإحصائيات البسيط النهائي لـ ProTech")
    print("📊 Creating Final Simple Statistics System for ProTech")
    print("="*70)
    
    if create_final_simple_stats():
        print("\n🎉 تم إنشاء نظام الإحصائيات البسيط النهائي بنجاح!")
        
        print("\n📊 الميزات الجديدة:")
        print("• 📈 لوحة معلومات إحصائيات الأعمال")
        print("• 🎯 8 بطاقات إحصائيات ملونة")
        print("• 📋 تقرير تحليل الأعمال")
        print("• 📊 تقييم صحة الأعمال")
        print("• 📤 تصدير التقرير")
        print("• 🔄 حسابات فورية")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Business Stats' في الشريط الجانبي")
        print("3. استعرض بطاقات الإحصائيات الملونة")
        print("4. اقرأ تقرير تحليل الأعمال")
        print("5. انقر على 'Export Report' لتصدير التقرير")
        print("6. راجع التوصيات والحالة المالية")
        
        print("\n💡 البطاقات المعروضة:")
        print("• 🛍️ إجمالي المنتجات")
        print("• 👥 إجمالي العملاء")
        print("• 💰 إجمالي المبيعات")
        print("• 💵 الإيرادات بالليرة")
        print("• 💲 الإيرادات بالدولار")
        print("• 📦 قيمة المخزون")
        print("• 📋 ديون العملاء")
        print("• 📈 متوسط البيع")
        
        print("\n📊 التقرير يشمل:")
        print("• نظرة عامة على الأرقام")
        print("• الملخص المالي")
        print("• تقييم صحة الأعمال")
        print("• توصيات للتحسين")
        
    else:
        print("\n❌ فشل في إنشاء نظام الإحصائيات البسيط النهائي")
    
    print("\n🔧 تم الانتهاء من العملية")

if __name__ == "__main__":
    main()
