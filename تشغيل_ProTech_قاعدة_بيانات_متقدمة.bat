@echo off
title ProTech Advanced Database System - نظام ProTech مع قاعدة البيانات المتقدمة
color 0A

echo.
echo ================================================================
echo    🗄️ نظام ProTech مع قاعدة البيانات المتقدمة
echo    🗄️ ProTech Advanced Database System
echo ================================================================
echo.

echo [1/6] فحص متطلبات النظام / Checking system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت / Python not installed
    echo يرجى تثبيت Python 3.8+ من https://python.org
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر / Python available
)

echo.
echo [2/6] فحص المكتبات المطلوبة / Checking required libraries...
python -c "import tkinter, json, os, datetime, threading, time, gc, logging, sqlite3, hashlib, gzip, pickle" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبات مطلوبة مفقودة / Required libraries missing
    echo تثبيت المكتبات المطلوبة / Installing required libraries...
    pip install --upgrade pip
    echo ✅ تم تحديث pip / pip updated
) else (
    echo ✅ جميع المكتبات متوفرة / All libraries available
)

echo.
echo [3/6] إنشاء مجلدات النظام / Creating system directories...
if not exist "database_backups" mkdir database_backups
if not exist "database_archives" mkdir database_archives
if not exist "logs" mkdir logs
if not exist "temp" mkdir temp
if not exist "exports" mkdir exports
echo ✅ تم إنشاء مجلدات النظام / System directories created

echo.
echo [4/6] فحص ملفات قاعدة البيانات / Checking database files...
if exist "database_manager.py" (
    echo ✅ مدير قاعدة البيانات موجود / Database manager found
) else (
    echo ❌ مدير قاعدة البيانات مفقود / Database manager missing
    echo يرجى التأكد من وجود ملف database_manager.py
    pause
    exit /b 1
)

if exist "protech_with_database.py" (
    echo ✅ التطبيق الرئيسي موجود / Main application found
) else (
    echo ❌ التطبيق الرئيسي مفقود / Main application missing
    echo يرجى التأكد من وجود ملف protech_with_database.py
    pause
    exit /b 1
)

echo.
echo [5/6] تهيئة قاعدة البيانات / Initializing database...
python -c "from database_manager import ProTechDatabaseManager; db = ProTechDatabaseManager(); print('Database initialized successfully'); db.close()" >nul 2>&1
if errorlevel 1 (
    echo ⚠️ تحذير: مشكلة في تهيئة قاعدة البيانات / Warning: Database initialization issue
    echo سيتم المحاولة مرة أخرى عند التشغيل / Will retry during application startup
) else (
    echo ✅ تم تهيئة قاعدة البيانات / Database initialized
)

echo.
echo [6/6] تشغيل نظام ProTech مع قاعدة البيانات المتقدمة / Starting ProTech with Advanced Database...
echo.
echo 🎯 المميزات المتقدمة / Advanced Features:
echo    • قاعدة بيانات SQLite محسنة / Optimized SQLite database
echo    • نسخ احتياطية تلقائية مضغوطة / Automatic compressed backups
echo    • فهرسة متقدمة للبحث السريع / Advanced indexing for fast search
echo    • ذاكرة مؤقتة ذكية / Smart caching system
echo    • مراقبة الأداء في الوقت الفعلي / Real-time performance monitoring
echo    • تصدير واستيراد JSON / JSON export/import
echo    • تحسين تلقائي لقاعدة البيانات / Automatic database optimization
echo    • سجلات مفصلة للعمليات / Detailed operation logging
echo    • حماية البيانات المتقدمة / Advanced data protection
echo    • واجهة إدارة قاعدة البيانات / Database management interface
echo.

echo 🚀 بدء التشغيل / Starting application...
echo.

python protech_with_database.py

echo.
if errorlevel 1 (
    echo ❌ حدث خطأ في تشغيل البرنامج / Error occurred while running the application
    echo.
    echo 🔧 خطوات استكشاف الأخطاء / Troubleshooting steps:
    echo    1. تأكد من وجود جميع الملفات المطلوبة / Ensure all required files exist
    echo    2. تحقق من سجل الأخطاء في مجلد logs / Check error logs in logs folder
    echo    3. تأكد من صحة قاعدة البيانات / Verify database integrity
    echo    4. جرب إعادة تهيئة قاعدة البيانات / Try reinitializing database
    echo.
    echo 📁 الملفات المطلوبة / Required files:
    echo    - database_manager.py
    echo    - protech_with_database.py
    echo    - protech_simple_data.json (اختياري / optional)
    echo.
) else (
    echo ✅ تم إغلاق البرنامج بنجاح / Application closed successfully
    echo.
    echo 📊 معلومات النظام / System Information:
    echo    • تم حفظ النسخ الاحتياطية في مجلد database_backups
    echo    • تم حفظ السجلات في مجلد logs
    echo    • تم حفظ الصادرات في مجلد exports
    echo    • Backups saved in database_backups folder
    echo    • Logs saved in logs folder
    echo    • Exports saved in exports folder
)

echo.
echo ================================================================
echo    شكراً لاستخدام نظام ProTech مع قاعدة البيانات المتقدمة
echo    Thank you for using ProTech Advanced Database System
echo ================================================================
echo.

pause
