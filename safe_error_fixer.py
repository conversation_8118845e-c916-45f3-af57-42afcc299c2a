#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Safe Error Fixer for ProTech
مصلح الأخطاء الآمن لـ ProTech

Safe and precise error fixing for ProTech without breaking syntax
إصلاح آمن ودقيق للأخطاء في ProTech دون كسر التركيب
"""

import os
import re
import shutil
from datetime import datetime

def create_backup():
    """Create backup before fixing"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.safe_fix_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def fix_specific_syntax_errors():
    """Fix specific syntax errors found"""
    try:
        print("🔧 إصلاح أخطاء التركيب المحددة...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_count = 0
        
        # Fix specific syntax error patterns
        fixes = [
            # Fix malformed if statements in expressions
            (r'= time\.time\(\) - if hasattr\(', '= time.time() - (self.performance_stats.get("start_time", time.time()) if hasattr('),
            
            # Fix incomplete if statements
            (r'(\w+) = ([^=]+) - if hasattr\(([^)]+)\):', r'\1 = \2 - (\3.get("start_time", 0) if hasattr(\3) else 0)'),
            
            # Fix broken attribute access
            (r'if hasattr\(self, "([^"]+)"\) and \1:', r'if hasattr(self, "\1") and getattr(self, "\1", None):'),
        ]
        
        for pattern, replacement in fixes:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                fixes_count += 1
        
        # Write fixed content
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم إصلاح {fixes_count} خطأ تركيب محدد")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح التركيب: {e}")
        return 0

def add_safe_error_handling():
    """Add safe error handling without breaking syntax"""
    try:
        print("🔧 إضافة معالجة أخطاء آمنة...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add safe error handling functions at the end of the class
        safe_functions = '''
    # Safe error handling functions - Added by error fixer
    def safe_get_attribute(self, obj, attr, default=None):
        """Safely get attribute with default value"""
        try:
            return getattr(obj, attr, default) if hasattr(obj, attr) else default
        except:
            return default
    
    def safe_dict_get(self, dictionary, key, default=None):
        """Safely get dictionary value"""
        try:
            return dictionary.get(key, default) if isinstance(dictionary, dict) else default
        except:
            return default
    
    def safe_list_access(self, lst, index, default=None):
        """Safely access list element"""
        try:
            return lst[index] if isinstance(lst, list) and 0 <= index < len(lst) else default
        except:
            return default
    
    def safe_operation(self, operation, *args, **kwargs):
        """Safely execute operation with error handling"""
        try:
            return operation(*args, **kwargs)
        except Exception as e:
            if hasattr(self, 'log_error'):
                self.log_error(e, f"safe_operation: {operation.__name__ if hasattr(operation, '__name__') else 'unknown'}")
            return None
'''
        
        # Insert before the last method (run method)
        run_pattern = r'(\s+def run\(self\):)'
        content = re.sub(run_pattern, safe_functions + r'\1', content)
        
        # Write enhanced content
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة دوال معالجة الأخطاء الآمنة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة معالجة الأخطاء: {e}")
        return False

def fix_empty_except_blocks():
    """Fix empty except blocks"""
    try:
        print("🔧 إصلاح كتل except الفارغة...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        fixed_lines = []
        fixes_count = 0
        
        i = 0
        while i < len(lines):
            line = lines[i]
            
            # Check for except blocks
            if line.strip().startswith('except') and ':' in line:
                fixed_lines.append(line)
                
                # Check if the next non-empty line is just 'pass'
                j = i + 1
                while j < len(lines) and not lines[j].strip():
                    fixed_lines.append(lines[j])
                    j += 1
                
                if j < len(lines) and lines[j].strip() == 'pass':
                    # Replace pass with proper error handling
                    indent = len(lines[j]) - len(lines[j].lstrip())
                    error_line = ' ' * indent + 'if hasattr(self, "log_error"): self.log_error(e, "error_handler")'
                    fixed_lines.append(error_line)
                    fixes_count += 1
                    i = j + 1
                    continue
            
            fixed_lines.append(line)
            i += 1
        
        # Write fixed content
        fixed_content = '\n'.join(fixed_lines)
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ تم إصلاح {fixes_count} كتلة except فارغة")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح except: {e}")
        return 0

def add_missing_imports():
    """Add missing imports safely"""
    try:
        print("🔧 إضافة الاستيرادات المفقودة...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for missing imports
        missing_imports = []
        
        # Common imports that might be missing
        import_checks = [
            ('time.time()', 'import time'),
            ('datetime.', 'from datetime import datetime'),
            ('json.', 'import json'),
            ('os.path', 'import os'),
            ('threading.', 'import threading'),
        ]
        
        for usage, import_stmt in import_checks:
            if usage in content and import_stmt not in content:
                missing_imports.append(import_stmt)
        
        if missing_imports:
            lines = content.split('\n')
            
            # Find the best place to insert imports
            insert_index = 0
            for i, line in enumerate(lines):
                if line.startswith(('import ', 'from ')) and 'tkinter' not in line:
                    insert_index = i + 1
                elif line.strip() == '' and i > 0:
                    break
            
            # Insert missing imports
            for imp in missing_imports:
                lines.insert(insert_index, imp)
                insert_index += 1
            
            # Add empty line after imports
            if missing_imports:
                lines.insert(insert_index, '')
            
            content = '\n'.join(lines)
            
            with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إضافة {len(missing_imports)} استيراد مفقود")
            return len(missing_imports)
        
        print("✅ لا توجد استيرادات مفقودة")
        return 0
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الاستيرادات: {e}")
        return 0

def test_syntax():
    """Test syntax after fixes"""
    try:
        print("🧪 اختبار التركيب...")
        
        import subprocess
        import sys
        
        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التركيب: نجح")
            return True
        else:
            print(f"❌ اختبار التركيب: فشل")
            print(f"الخطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التركيب: {e}")
        return False

def create_error_free_launcher():
    """Create launcher for error-free version"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Error-Free Launcher
مشغل ProTech الخالي من الأخطاء
"""

import os
import sys
import subprocess

def main():
    """Launch ProTech error-free version"""
    try:
        print("🚀 تشغيل ProTech الخالي من الأخطاء...")
        
        # Set environment
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PROTECH_ERROR_FREE'] = '1'
        
        # Check file exists
        if not os.path.exists('protech_simple_working.py'):
            print("❌ ملف ProTech غير موجود!")
            input("اضغط Enter للخروج...")
            return
        
        # Test syntax first
        print("🧪 اختبار التركيب...")
        test_result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                                   capture_output=True, text=True)
        
        if test_result.returncode != 0:
            print(f"❌ خطأ في التركيب: {test_result.stderr}")
            input("اضغط Enter للخروج...")
            return
        
        print("✅ التركيب سليم")
        
        # Launch
        if sys.platform == 'win32':
            subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)
        else:
            subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)
        
        print("✅ تم تشغيل ProTech بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
    
    with open('launch_protech_error_free.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل خالي من الأخطاء: launch_protech_error_free.py")

def main():
    """Main safe error fixing function"""
    print("🛡️ مصلح الأخطاء الآمن لـ ProTech")
    print("🛡️ ProTech Safe Error Fixer")
    print("="*60)
    
    try:
        # Create backup
        create_backup()
        
        # Apply safe fixes
        total_fixes = 0
        
        print("\n🔧 تطبيق الإصلاحات الآمنة...")
        
        # Fix 1: Specific syntax errors
        total_fixes += fix_specific_syntax_errors()
        
        # Fix 2: Add safe error handling
        if add_safe_error_handling():
            total_fixes += 1
        
        # Fix 3: Fix empty except blocks
        total_fixes += fix_empty_except_blocks()
        
        # Fix 4: Add missing imports
        total_fixes += add_missing_imports()
        
        # Test syntax
        syntax_ok = test_syntax()
        
        # Create launcher
        create_error_free_launcher()
        
        print("\n" + "="*60)
        print(f"📊 ملخص الإصلاحات الآمنة:")
        print(f"• إجمالي الإصلاحات: {total_fixes}")
        print(f"• اختبار التركيب: {'نجح' if syntax_ok else 'فشل'}")
        
        if syntax_ok:
            print("\n🎉 تم إصلاح الأخطاء بأمان!")
            print("🎉 Errors fixed safely!")
            print("\n🚀 يمكنك الآن:")
            print("1. تشغيل launch_protech_error_free.py")
            print("2. النقر المزدوج على protech_simple_working.py")
        else:
            print("\n⚠️ يحتاج مراجعة إضافية")
            print("⚠️ Needs additional review")
        
        print("="*60)
        
        return syntax_ok
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح الآمن: {e}")
        return False

if __name__ == "__main__":
    main()
