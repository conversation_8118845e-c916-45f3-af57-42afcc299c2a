#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
💰 إضافة نسبة الأرباح مع التواريخ
Add Profit Margins with Dates

إضافة تقرير الأرباح المفصل مع التواريخ لنظام ProTech
Add detailed profit report with dates to ProTech system
"""

import os
import re
import shutil
from datetime import datetime

def add_profits_with_dates():
    """إضافة تقرير الأرباح مع التواريخ"""
    try:
        print("💰 إضافة نسبة الأرباح مع التواريخ...")
        print("💰 Adding Profit Margins with Dates...")
        print("=" * 50)
        
        # مسار ملف ProTech
        protech_file = r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_working.py"
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # إنشاء نسخة احتياطية
        backup_name = f"{protech_file}.profits_dates_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # قراءة الملف الحالي
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # إضافة دالة تقرير الأرباح مع التواريخ
        profits_method = '''
    def show_profits_with_dates(self):
        """عرض تقرير الأرباح مع التواريخ"""
        try:
            self.report_title.config(text="💰 تقرير الأرباح والتواريخ")
            
            products = getattr(self, 'products', [])
            sales = getattr(self, 'sales', [])
            
            # حساب الأرباح
            total_cost = 0
            total_revenue = 0
            total_profit = 0
            profit_details = []
            
            # حساب تكلفة المخزون
            inventory_cost = 0
            inventory_value = 0
            for product in products:
                cost_price = product.get('cost_price', 0)
                retail_price = product.get('retail_price', product.get('base_price', 0))
                quantity = product.get('quantity', product.get('stock', 0))
                
                product_cost = cost_price * quantity
                product_value = retail_price * quantity
                product_profit = product_value - product_cost
                
                inventory_cost += product_cost
                inventory_value += product_value
                
                if quantity > 0:
                    profit_margin = ((retail_price - cost_price) / retail_price * 100) if retail_price > 0 else 0
                    profit_details.append({
                        'name': product.get('name', 'غير محدد'),
                        'cost': cost_price,
                        'price': retail_price,
                        'quantity': quantity,
                        'profit_per_unit': retail_price - cost_price,
                        'profit_margin': profit_margin,
                        'total_profit': product_profit
                    })
            
            # حساب أرباح المبيعات
            sales_profit = 0
            sales_by_date = {}
            for sale in sales:
                sale_total = sale.get('total', 0)
                sale_date = sale.get('date', 'غير محدد')[:10]
                
                # تقدير التكلفة (70% من سعر البيع كمتوسط)
                estimated_cost = sale_total * 0.7
                sale_profit = sale_total - estimated_cost
                
                sales_profit += sale_profit
                
                if sale_date not in sales_by_date:
                    sales_by_date[sale_date] = {
                        'revenue': 0,
                        'estimated_cost': 0,
                        'profit': 0,
                        'count': 0
                    }
                
                sales_by_date[sale_date]['revenue'] += sale_total
                sales_by_date[sale_date]['estimated_cost'] += estimated_cost
                sales_by_date[sale_date]['profit'] += sale_profit
                sales_by_date[sale_date]['count'] += 1
            
            # إجمالي الأرباح
            total_inventory_profit = inventory_value - inventory_cost
            overall_profit_margin = ((inventory_value - inventory_cost) / inventory_value * 100) if inventory_value > 0 else 0
            
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            profits_text = f"""
💰 تقرير الأرباح والتواريخ المفصل
Detailed Profit & Dates Report
{'='*60}

📅 تاريخ التقرير: {current_time}
Report Date: {current_time}

💰 ملخص الأرباح الإجمالي:
Overall Profit Summary:
{'='*40}

📦 أرباح المخزون:
Inventory Profits:
   • إجمالي التكلفة: ${inventory_cost:,.2f}
   • إجمالي القيمة: ${inventory_value:,.2f}
   • إجمالي الربح: ${total_inventory_profit:,.2f}
   • نسبة الربح: {overall_profit_margin:.1f}%
   • Total Cost: ${inventory_cost:,.2f}
   • Total Value: ${inventory_value:,.2f}
   • Total Profit: ${total_inventory_profit:,.2f}
   • Profit Margin: {overall_profit_margin:.1f}%

💰 أرباح المبيعات:
Sales Profits:
   • إجمالي المبيعات: ${sum(s.get('total', 0) for s in sales):,.2f}
   • الربح المقدر: ${sales_profit:,.2f}
   • Total Sales: ${sum(s.get('total', 0) for s in sales):,.2f}
   • Estimated Profit: ${sales_profit:,.2f}

📊 تفاصيل الأرباح حسب المنتج:
Product Profit Details:
{'='*40}
"""
            
            # أفضل المنتجات ربحية
            profit_details.sort(key=lambda x: x['profit_margin'], reverse=True)
            for i, product in enumerate(profit_details[:10], 1):
                profits_text += f"""
📦 منتج رقم {i}:
   الاسم: {product['name']}
   التكلفة: ${product['cost']:.2f}
   السعر: ${product['price']:.2f}
   الكمية: {product['quantity']}
   الربح للوحدة: ${product['profit_per_unit']:.2f}
   نسبة الربح: {product['profit_margin']:.1f}%
   إجمالي الربح: ${product['total_profit']:.2f}
   {'-'*30}
"""
            
            if len(profit_details) > 10:
                profits_text += f"\\n... و {len(profit_details) - 10} منتج آخر\\n"
            
            # الأرباح حسب التاريخ
            profits_text += f"""
📅 الأرباح حسب التاريخ:
Profits by Date:
{'='*40}
"""
            
            # ترتيب التواريخ
            sorted_dates = sorted(sales_by_date.keys(), reverse=True)
            for date in sorted_dates[:15]:  # آخر 15 يوم
                data = sales_by_date[date]
                profit_margin = (data['profit'] / data['revenue'] * 100) if data['revenue'] > 0 else 0
                
                profits_text += f"""
📅 تاريخ: {date}
   عدد الفواتير: {data['count']} فاتورة
   إجمالي المبيعات: ${data['revenue']:,.2f}
   التكلفة المقدرة: ${data['estimated_cost']:,.2f}
   الربح المقدر: ${data['profit']:,.2f}
   نسبة الربح: {profit_margin:.1f}%
   {'-'*25}
"""
            
            if len(sorted_dates) > 15:
                profits_text += f"\\n... و {len(sorted_dates) - 15} يوم آخر\\n"
            
            # تحليل الاتجاهات
            profits_text += f"""
📈 تحليل الاتجاهات:
Trend Analysis:
{'='*40}

💡 أفضل المنتجات ربحية:
Top Profitable Products:
"""
            
            top_3_products = profit_details[:3]
            for i, product in enumerate(top_3_products, 1):
                profits_text += f"   {i}. {product['name']} - {product['profit_margin']:.1f}% ربح\\n"
            
            # أفضل الأيام مبيعات
            if sorted_dates:
                best_day = max(sales_by_date.keys(), key=lambda x: sales_by_date[x]['revenue'])
                best_day_data = sales_by_date[best_day]
                
                profits_text += f"""
🏆 أفضل يوم مبيعات:
Best Sales Day:
   التاريخ: {best_day}
   المبيعات: ${best_day_data['revenue']:,.2f}
   الربح: ${best_day_data['profit']:,.2f}
   نسبة الربح: {(best_day_data['profit']/best_day_data['revenue']*100):.1f}%

💰 متوسط الربح اليومي:
Average Daily Profit:
   ${sum(data['profit'] for data in sales_by_date.values()) / len(sales_by_date):,.2f}
"""
            
            profits_text += f"""
🎯 توصيات لزيادة الأرباح:
Recommendations to Increase Profits:
   • ركز على المنتجات عالية الربحية
   • راجع أسعار المنتجات منخفضة الربح
   • تابع الاتجاهات اليومية للمبيعات
   • Focus on high-margin products
   • Review pricing for low-margin products
   • Monitor daily sales trends

✅ تقرير الأرباح محدث ودقيق
Profit report is updated and accurate
            """
            
            self.reports_text.delete(1.0, tk.END)
            self.reports_text.insert(1.0, profits_text)
            
        except Exception as e:
            print(f"❌ خطأ في عرض تقرير الأرباح: {e}")
            self.show_error_message("خطأ في عرض تقرير الأرباح")
    
    def show_monthly_profits(self):
        """عرض الأرباح الشهرية"""
        try:
            self.report_title.config(text="📅 الأرباح الشهرية")
            
            sales = getattr(self, 'sales', [])
            monthly_data = {}
            
            for sale in sales:
                sale_date = sale.get('date', '')
                if len(sale_date) >= 7:  # YYYY-MM format
                    month = sale_date[:7]  # YYYY-MM
                    sale_total = sale.get('total', 0)
                    estimated_cost = sale_total * 0.7
                    profit = sale_total - estimated_cost
                    
                    if month not in monthly_data:
                        monthly_data[month] = {
                            'revenue': 0,
                            'cost': 0,
                            'profit': 0,
                            'count': 0
                        }
                    
                    monthly_data[month]['revenue'] += sale_total
                    monthly_data[month]['cost'] += estimated_cost
                    monthly_data[month]['profit'] += profit
                    monthly_data[month]['count'] += 1
            
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            monthly_text = f"""
📅 تقرير الأرباح الشهرية
Monthly Profit Report
{'='*50}

📅 تاريخ التقرير: {current_time}

📊 الأرباح حسب الشهر:
Profits by Month:
{'='*30}
"""
            
            # ترتيب الشهور
            sorted_months = sorted(monthly_data.keys(), reverse=True)
            for month in sorted_months:
                data = monthly_data[month]
                profit_margin = (data['profit'] / data['revenue'] * 100) if data['revenue'] > 0 else 0
                
                monthly_text += f"""
📅 شهر: {month}
   عدد الفواتير: {data['count']} فاتورة
   إجمالي المبيعات: ${data['revenue']:,.2f}
   التكلفة المقدرة: ${data['cost']:,.2f}
   الربح: ${data['profit']:,.2f}
   نسبة الربح: {profit_margin:.1f}%
   {'-'*25}
"""
            
            if not monthly_data:
                monthly_text += "\\nلا توجد بيانات مبيعات شهرية\\nNo monthly sales data available\\n"
            
            self.reports_text.delete(1.0, tk.END)
            self.reports_text.insert(1.0, monthly_text)
            
        except Exception as e:
            print(f"❌ خطأ في عرض الأرباح الشهرية: {e}")
    
    def show_error_message(self, message):
        """عرض رسالة خطأ"""
        try:
            if hasattr(self, 'reports_text'):
                error_text = f"""
❌ خطأ في التقرير
Error in Report
{'='*30}

الرسالة: {message}
Message: {message}

🔧 يرجى المحاولة مرة أخرى
Please try again

📞 إذا استمر الخطأ، تواصل مع الدعم الفني
If error persists, contact technical support
                """
                self.reports_text.delete(1.0, tk.END)
                self.reports_text.insert(1.0, error_text)
        except:
            print(f"❌ {message}")'''
        
        # البحث عن مكان إضافة الدوال الجديدة
        last_method = content.rfind("\n    def ")
        if last_method != -1:
            content = content[:last_method] + profits_method + content[last_method:]
        
        # كتابة الملف المحدث
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة دوال الأرباح مع التواريخ")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة الأرباح مع التواريخ: {e}")
        return False

def update_reports_buttons():
    """تحديث أزرار التقارير لتشمل الأرباح"""
    try:
        print("🔧 تحديث أزرار التقارير...")

        protech_file = r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_working.py"

        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False

        # قراءة الملف
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # البحث عن دالة create_reports_buttons وتحديثها
        if "def create_reports_buttons(" in content:
            # العثور على بداية ونهاية الدالة
            method_start = content.find("def create_reports_buttons(")
            method_end = content.find("\n    def ", method_start + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", method_start + 1)
            if method_end == -1:
                method_end = len(content)

            # الدالة المحدثة مع أزرار الأرباح
            updated_buttons_method = '''
    def create_reports_buttons(self, parent):
        """إنشاء أزرار التقارير المحدثة"""
        try:
            # عنوان الشريط الجانبي
            sidebar_title = tk.Label(
                parent,
                text="📋 اختر التقرير\\nSelect Report",
                font=('Arial', 14, 'bold'),
                bg='#ecf0f1',
                fg='#2c3e50',
                justify='center'
            )
            sidebar_title.pack(pady=15)

            # أزرار التقارير المحدثة مع الأرباح
            reports_buttons = [
                ("📊 إحصائيات عامة", self.show_general_stats, '#3498db'),
                ("📈 تقرير المبيعات", self.show_sales_report_safe, '#2ecc71'),
                ("📦 تقرير المخزون", self.show_inventory_report_safe, '#e74c3c'),
                ("👥 تقرير العملاء", self.show_customers_report_safe, '#f39c12'),
                ("🏢 تقرير الموردين", self.show_suppliers_report_safe, '#9b59b6'),
                ("💰 الأرباح والتواريخ", self.show_profits_with_dates, '#e67e22'),
                ("📅 الأرباح الشهرية", self.show_monthly_profits, '#8e44ad'),
                ("📋 تقرير شامل", self.show_comprehensive_report, '#1abc9c')
            ]

            for text, command, color in reports_buttons:
                btn = tk.Button(
                    parent,
                    text=text,
                    command=command,
                    font=('Arial', 11, 'bold'),
                    bg=color,
                    fg='white',
                    relief='raised',
                    bd=2,
                    padx=10,
                    pady=8,
                    cursor='hand2',
                    width=20
                )
                btn.pack(pady=5, padx=10, fill='x')

        except Exception as e:
            print(f"❌ خطأ في إنشاء أزرار التقارير: {e}")'''

            # استبدال الدالة
            content = content[:method_start] + updated_buttons_method.strip() + content[method_end:]
            print("✅ تم تحديث أزرار التقارير")

        # كتابة الملف المحدث
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم تحديث أزرار التقارير بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث أزرار التقارير: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("💰 إضافة نسبة الأرباح مع التواريخ لنظام ProTech")
    print("💰 Adding Profit Margins with Dates to ProTech System")
    print("=" * 60)

    try:
        # الخطوة 1: إضافة دوال الأرباح
        print("💰 الخطوة 1: إضافة دوال الأرباح مع التواريخ...")
        if not add_profits_with_dates():
            print("❌ فشل في إضافة دوال الأرباح")
            return False

        # الخطوة 2: تحديث أزرار التقارير
        print("🔧 الخطوة 2: تحديث أزرار التقارير...")
        if not update_reports_buttons():
            print("❌ فشل في تحديث أزرار التقارير")
            return False

        print("\n" + "=" * 60)
        print("✅ تم إضافة نسبة الأرباح مع التواريخ بنجاح!")
        print("✅ Profit margins with dates added successfully!")
        print("=" * 60)

        print("\n💰 الميزات الجديدة المضافة:")
        print("• تقرير الأرباح المفصل مع التواريخ")
        print("• تحليل الأرباح حسب المنتج")
        print("• الأرباح اليومية مع التواريخ")
        print("• الأرباح الشهرية")
        print("• نسب الربح لكل منتج")
        print("• أفضل المنتجات ربحية")
        print("• أفضل الأيام مبيعات")
        print("• توصيات لزيادة الأرباح")

        print("\n📊 التقارير الجديدة:")
        print("• 💰 الأرباح والتواريخ - تقرير مفصل للأرباح مع التواريخ")
        print("• 📅 الأرباح الشهرية - تحليل الأرباح حسب الشهر")

        print("\n🎯 المعلومات المتوفرة:")
        print("• إجمالي التكلفة والقيمة")
        print("• نسبة الربح لكل منتج")
        print("• الأرباح حسب التاريخ")
        print("• متوسط الربح اليومي")
        print("• اتجاهات الأرباح")
        print("• توصيات للتحسين")

        print("\n🚀 كيفية الاستخدام:")
        print("1. اذهب إلى صفحة التقارير في البرنامج")
        print("2. اضغط على '💰 الأرباح والتواريخ' للتقرير المفصل")
        print("3. أو اضغط على '📅 الأرباح الشهرية' للتحليل الشهري")
        print("4. استمتع بتقارير الأرباح المفصلة!")

        print("\n🎉 تم إضافة تقارير الأرباح بنجاح!")

        return True

    except Exception as e:
        print(f"❌ خطأ عام في إضافة الأرباح: {e}")
        return False

if __name__ == "__main__":
    main()
