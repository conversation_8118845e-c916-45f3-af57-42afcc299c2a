#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Unicode Error - ProTech
إصلاح خطأ الترميز - ProTech
"""

import os
import shutil
from datetime import datetime

def fix_unicode_error():
    """Fix Unicode encoding error in ProTech"""
    try:
        print("🔧 إصلاح خطأ الترميز...")
        
        # Create backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.unicode_error_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        
        # Read the file
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix Unicode issues
        print("🔧 إصلاح مشاكل الترميز...")
        
        # Replace problematic Unicode characters
        fixes = [
            ('\\U0001f680', '🚀'),  # Rocket emoji
            ('\\u274c', '❌'),      # Cross mark
            ('\\u2705', '✅'),      # Check mark
            ('\\U0001f4be', '💾'),  # Floppy disk
            ('\\U0001f4ca', '📊'),  # Bar chart
            ('\\U0001f4e6', '📦'),  # Package
            ('\\U0001f465', '👥'),  # People
            ('\\U0001f3ea', '🏪'),  # Store
            ('\\U0001f4b0', '💰'),  # Money bag
            ('\\U0001f9ee', '🧮'),  # Calculator
        ]
        
        for old, new in fixes:
            content = content.replace(old, new)
        
        # Fix print statements with Unicode
        lines = content.split('\n')
        fixed_lines = []
        
        for line in lines:
            # Fix problematic print statements
            if 'print(' in line and ('\\u' in line or '\\U' in line):
                # Replace with safe ASCII version
                if 'بدء تشغيل نظام ProTech' in line:
                    line = '    print("تشغيل ProTech...")'
                elif 'خطأ في بدء التشغيل' in line:
                    line = '    print("خطأ في التشغيل:", str(e))'
                elif any(emoji in line for emoji in ['🚀', '❌', '✅', '💾', '📊', '📦', '👥', '🏪', '💰', '🧮']):
                    # Remove emojis from print statements
                    for emoji in ['🚀', '❌', '✅', '💾', '📊', '📦', '👥', '🏪', '💰', '🧮']:
                        line = line.replace(emoji, '')
            
            fixed_lines.append(line)
        
        # Write fixed content
        fixed_content = '\n'.join(fixed_lines)
        
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("✅ تم إصلاح مشاكل الترميز")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def create_safe_launcher():
    """Create safe launcher that handles encoding"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Safe Launcher
مشغل ProTech الآمن
"""

import os
import sys
import subprocess

def main():
    """Launch ProTech safely"""
    try:
        print("تشغيل ProTech...")
        
        # Set UTF-8 encoding
        if sys.platform == 'win32':
            os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Launch with proper encoding
        result = subprocess.run([
            sys.executable, 'protech_simple_working.py'
        ], env=os.environ.copy())
        
        if result.returncode != 0:
            print("خطأ في التشغيل")
            input("اضغط Enter للخروج...")
        
    except Exception as e:
        print(f"خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
    
    with open('launch_protech_safe.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل آمن: launch_protech_safe.py")

def test_fixed_version():
    """Test the fixed version"""
    try:
        import subprocess
        import sys
        
        print("🧪 اختبار النسخة المصلحة...")
        
        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التركيب نجح")
            
            # Test basic execution
            result = subprocess.run([sys.executable, '-c', 'import protech_simple_working'], 
                                  capture_output=True, text=True, timeout=5)
            
            if result.returncode == 0:
                print("✅ اختبار الاستيراد نجح")
                return True
            else:
                print(f"❌ خطأ في الاستيراد: {result.stderr}")
                return False
        else:
            print(f"❌ خطأ في التركيب: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح فشل تشغيل ProTech")
    print("🔧 Fix ProTech Launch Failure")
    print("=" * 50)
    
    try:
        # Step 1: Fix Unicode error
        if fix_unicode_error():
            print("✅ تم إصلاح خطأ الترميز")
        
        # Step 2: Test fixed version
        if test_fixed_version():
            print("✅ الاختبار نجح")
        
        # Step 3: Create safe launcher
        create_safe_launcher()
        
        print("\n" + "=" * 50)
        print("✅ تم إصلاح فشل التشغيل!")
        print("✅ Launch failure fixed!")
        print("=" * 50)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إصلاح خطأ ترميز Unicode")
        print("• إزالة الرموز التعبيرية المشكلة")
        print("• إنشاء مشغل آمن")
        
        print("\n🚀 الآن يمكنك:")
        print("1. تشغيل launch_protech_safe.py")
        print("2. النقر المزدوج على protech_simple_working.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
