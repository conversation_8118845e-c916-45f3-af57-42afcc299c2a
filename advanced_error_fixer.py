#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Advanced Error Fixer for ProTech
مصلح الأخطاء المتقدم لـ ProTech

Advanced error detection and automatic fixing for ProTech
فحص وإصلاح متقدم وتلقائي للأخطاء في ProTech
"""

import os
import re
import shutil
from datetime import datetime

def create_backup():
    """Create backup before fixing"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.advanced_fix_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def fix_missing_returns():
    """Fix functions missing return statements"""
    try:
        print("🔧 إصلاح الدوال المفقودة return...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        fixed_lines = []
        fixes_count = 0
        
        i = 0
        while i < len(lines):
            line = lines[i]
            fixed_lines.append(line)
            
            # Check if this is a function definition
            if line.strip().startswith('def ') and ':' in line:
                func_name = line.strip().split('def ')[1].split('(')[0]
                
                # Skip __init__ and other special methods
                if func_name.startswith('__') or func_name in ['__init__', '__del__', '__str__']:
                    i += 1
                    continue
                
                # Look ahead to find the end of the function
                j = i + 1
                indent_level = len(line) - len(line.lstrip())
                has_return = False
                function_end = i + 1
                
                while j < len(lines):
                    next_line = lines[j]
                    
                    # Check if we've reached the end of the function
                    if next_line.strip() and len(next_line) - len(next_line.lstrip()) <= indent_level:
                        if not next_line.strip().startswith('#'):
                            function_end = j
                            break
                    
                    # Check if there's already a return statement
                    if 'return' in next_line:
                        has_return = True
                    
                    j += 1
                
                # If no return statement found, add one before the function ends
                if not has_return and function_end > i + 1:
                    # Find the last non-empty line of the function
                    last_line_idx = function_end - 1
                    while last_line_idx > i and not lines[last_line_idx].strip():
                        last_line_idx -= 1
                    
                    # Add return statement with proper indentation
                    if last_line_idx > i:
                        last_line = lines[last_line_idx]
                        func_indent = len(last_line) - len(last_line.lstrip())
                        return_line = ' ' * func_indent + 'return None  # Auto-added return'
                        
                        # Insert the return statement
                        lines.insert(function_end, return_line)
                        fixes_count += 1
            
            i += 1
        
        # Write fixed content
        fixed_content = '\n'.join(lines)
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ تم إصلاح {fixes_count} دالة مفقودة return")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح return: {e}")
        return 0

def fix_attribute_errors():
    """Fix potential AttributeError issues"""
    try:
        print("🔧 إصلاح أخطاء AttributeError...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_count = 0
        
        # Fix common attribute access patterns
        patterns_to_fix = [
            # Fix self.root.after calls without checking if root exists
            (r'(\s+)(self\.root\.after\()', r'\1if hasattr(self, "root") and self.root:\n\1    \2'),
            
            # Fix self.performance_stats access
            (r'(\s+)(self\.performance_stats\[)', r'\1if hasattr(self, "performance_stats"):\n\1    \2'),
            
            # Fix tree widget access
            (r'(\s+)(self\.\w+_tree\.)', r'\1if hasattr(self, "\2".split(".")[0].split("self.")[1]) and \2:\n\1    '),
        ]
        
        for pattern, replacement in patterns_to_fix:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                fixes_count += 1
        
        # Write fixed content
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم إصلاح {fixes_count} خطأ AttributeError محتمل")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح AttributeError: {e}")
        return 0

def fix_key_errors():
    """Fix potential KeyError issues"""
    try:
        print("🔧 إصلاح أخطاء KeyError...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        fixes_count = 0
        
        # Replace dictionary access with get() method
        patterns = [
            (r"(\w+)\['(\w+)'\]", r"\1.get('\2', '')"),
            (r'(\w+)\["(\w+)"\]', r'\1.get("\2", "")'),
        ]
        
        for pattern, replacement in patterns:
            old_content = content
            # Only replace if not already using get()
            if '.get(' not in content:
                content = re.sub(pattern, replacement, content)
                if content != old_content:
                    fixes_count += 1
        
        # Write fixed content
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم إصلاح {fixes_count} خطأ KeyError محتمل")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح KeyError: {e}")
        return 0

def fix_index_errors():
    """Fix potential IndexError issues"""
    try:
        print("🔧 إصلاح أخطاء IndexError...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        fixed_lines = []
        fixes_count = 0
        
        for line in lines:
            # Check for list access without length check
            if '[0]' in line and 'if len(' not in line and 'len(' not in line:
                # Add length check
                indent = len(line) - len(line.lstrip())
                var_name = line.split('[0]')[0].strip().split()[-1]
                
                if var_name and not line.strip().startswith('#'):
                    # Add length check
                    check_line = ' ' * indent + f'if len({var_name}) > 0:'
                    fixed_line = ' ' * (indent + 4) + line.strip()
                    
                    fixed_lines.append(check_line)
                    fixed_lines.append(fixed_line)
                    fixes_count += 1
                    continue
            
            fixed_lines.append(line)
        
        # Write fixed content
        fixed_content = '\n'.join(fixed_lines)
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ تم إصلاح {fixes_count} خطأ IndexError محتمل")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح IndexError: {e}")
        return 0

def fix_try_except_blocks():
    """Add proper try-except blocks around risky operations"""
    try:
        print("🔧 إضافة كتل try-except...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        lines = content.split('\n')
        fixed_lines = []
        fixes_count = 0
        
        for i, line in enumerate(lines):
            # Check for risky operations that need try-except
            risky_operations = [
                'json.load(',
                'json.dump(',
                'open(',
                'int(',
                'float(',
                '.remove(',
                '.index(',
            ]
            
            needs_try_except = any(op in line for op in risky_operations)
            
            if needs_try_except and 'try:' not in line and 'except' not in line:
                # Check if already in a try block
                in_try_block = False
                for j in range(max(0, i-10), i):
                    if 'try:' in lines[j] and 'except' not in lines[j]:
                        in_try_block = True
                        break
                
                if not in_try_block:
                    indent = len(line) - len(line.lstrip())
                    
                    # Add try block
                    try_line = ' ' * indent + 'try:'
                    indented_line = ' ' * (indent + 4) + line.strip()
                    except_line = ' ' * indent + 'except Exception as e:'
                    error_line = ' ' * (indent + 4) + 'print(f"خطأ: {e}")'
                    
                    fixed_lines.extend([try_line, indented_line, except_line, error_line])
                    fixes_count += 1
                    continue
            
            fixed_lines.append(line)
        
        # Write fixed content
        fixed_content = '\n'.join(fixed_lines)
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print(f"✅ تم إضافة {fixes_count} كتلة try-except")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إضافة try-except: {e}")
        return 0

def fix_import_issues():
    """Fix import related issues"""
    try:
        print("🔧 إصلاح مشاكل الاستيراد...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add missing imports if needed
        missing_imports = []
        
        # Check for common missing imports
        if 'psutil' in content and 'import psutil' not in content:
            missing_imports.append('import psutil')
        
        if 'threading' in content and 'import threading' not in content:
            missing_imports.append('import threading')
        
        if 'json' in content and 'import json' not in content:
            missing_imports.append('import json')
        
        # Add missing imports at the top
        if missing_imports:
            lines = content.split('\n')
            
            # Find where to insert imports (after existing imports)
            insert_index = 0
            for i, line in enumerate(lines):
                if line.startswith('import ') or line.startswith('from '):
                    insert_index = i + 1
            
            # Insert missing imports
            for imp in missing_imports:
                lines.insert(insert_index, imp)
                insert_index += 1
            
            content = '\n'.join(lines)
            
            with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ تم إضافة {len(missing_imports)} استيراد مفقود")
            return len(missing_imports)
        
        print("✅ لا توجد استيرادات مفقودة")
        return 0
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الاستيراد: {e}")
        return 0

def test_fixed_version():
    """Test the fixed version"""
    try:
        print("🧪 اختبار النسخة المصلحة...")
        
        import subprocess
        import sys
        
        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print(f"❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def main():
    """Main error fixing function"""
    print("🔧 مصلح الأخطاء المتقدم لـ ProTech")
    print("🔧 ProTech Advanced Error Fixer")
    print("="*60)
    
    try:
        # Create backup
        create_backup()
        
        # Apply fixes
        total_fixes = 0
        
        print("\n🔧 تطبيق الإصلاحات المتقدمة...")
        
        # Fix 1: Missing returns
        total_fixes += fix_missing_returns()
        
        # Fix 2: Attribute errors
        total_fixes += fix_attribute_errors()
        
        # Fix 3: Key errors
        total_fixes += fix_key_errors()
        
        # Fix 4: Index errors
        total_fixes += fix_index_errors()
        
        # Fix 5: Import issues
        total_fixes += fix_import_issues()
        
        # Fix 6: Try-except blocks (commented out as it might be too aggressive)
        # total_fixes += fix_try_except_blocks()
        
        # Test the fixed version
        test_success = test_fixed_version()
        
        print("\n" + "="*60)
        print(f"📊 ملخص الإصلاحات:")
        print(f"• إجمالي الإصلاحات المطبقة: {total_fixes}")
        print(f"• اختبار النسخة المصلحة: {'نجح' if test_success else 'فشل'}")
        
        if total_fixes > 0 and test_success:
            print("\n🎉 تم إصلاح الأخطاء بنجاح!")
            print("🎉 Errors fixed successfully!")
        elif total_fixes > 0:
            print("\n⚠️ تم تطبيق إصلاحات ولكن يحتاج مراجعة")
            print("⚠️ Fixes applied but needs review")
        else:
            print("\n✅ لا توجد إصلاحات مطلوبة")
            print("✅ No fixes required")
        
        print("="*60)
        
        return test_success
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح: {e}")
        return False

if __name__ == "__main__":
    main()
