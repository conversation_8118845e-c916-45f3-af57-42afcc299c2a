#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Data Save Issue for ProTech
إصلاح مشكلة حفظ البيانات في ProTech

Fix data saving problems and ensure data persistence
إصلاح مشاكل حفظ البيانات وضمان استمرارية البيانات
"""

import os
import json
import shutil
from datetime import datetime

def diagnose_save_issue():
    """Diagnose data save issues"""
    try:
        print("🔍 تشخيص مشكلة حفظ البيانات")
        print("🔍 Diagnosing Data Save Issue")
        print("="*50)
        
        # Check current directory
        current_dir = os.getcwd()
        print(f"📁 المجلد الحالي: {current_dir}")
        
        # Check data file
        data_file = "protech_simple_data.json"
        
        if os.path.exists(data_file):
            size = os.path.getsize(data_file)
            mod_time = datetime.fromtimestamp(os.path.getmtime(data_file))
            print(f"📄 ملف البيانات: {size} bytes - {mod_time.strftime('%H:%M:%S')}")
            
            # Check file permissions
            if os.access(data_file, os.R_OK):
                print("✅ صلاحية القراءة: متاحة")
            else:
                print("❌ صلاحية القراءة: غير متاحة")
            
            if os.access(data_file, os.W_OK):
                print("✅ صلاحية الكتابة: متاحة")
            else:
                print("❌ صلاحية الكتابة: غير متاحة")
            
            # Try to read current data
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                print("✅ قراءة البيانات: نجحت")
                print(f"📊 محتوى البيانات: {len(str(data))} حرف")
            except Exception as e:
                print(f"❌ قراءة البيانات: فشلت - {e}")
                return False
        else:
            print("❌ ملف البيانات: غير موجود")
        
        # Check directory permissions
        if os.access(current_dir, os.W_OK):
            print("✅ صلاحية كتابة المجلد: متاحة")
        else:
            print("❌ صلاحية كتابة المجلد: غير متاحة")
            return False
        
        # Check disk space
        try:
            import shutil
            total, used, free = shutil.disk_usage(current_dir)
            free_mb = free // (1024*1024)
            print(f"💾 مساحة فارغة: {free_mb} MB")
            
            if free_mb < 10:
                print("⚠️ مساحة القرص منخفضة!")
                return False
        except Exception as e:
            print(f"⚠️ لا يمكن فحص مساحة القرص: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        return False

def create_test_data():
    """Create test data structure"""
    return {
        "suppliers": [
            {
                "id": 1,
                "name": "مورد تجريبي",
                "phone": "123456789",
                "address": "عنوان تجريبي"
            }
        ],
        "products": [
            {
                "id": 1,
                "barcode": "1234567890123",
                "name": "منتج تجريبي",
                "category": "فئة تجريبية",
                "supplier": "مورد تجريبي",
                "unit": "قطعة",
                "quantity": 10,
                "price": 100.0,
                "cost": 80.0,
                "min_stock": 5
            }
        ],
        "customers": [
            {
                "id": 1,
                "name": "عميل تجريبي",
                "phone": "987654321",
                "type": "تجزئة",
                "balance": 0.0
            }
        ],
        "sales": [],
        "settings": {
            "auto_save": True,
            "backup_enabled": True,
            "language": "ar"
        },
        "metadata": {
            "version": "1.0",
            "created": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat()
        }
    }

def fix_data_save_permissions():
    """Fix data save permissions"""
    try:
        print("\n🔧 إصلاح صلاحيات حفظ البيانات...")
        
        data_file = "protech_simple_data.json"
        
        # Create backup if file exists
        if os.path.exists(data_file):
            backup_name = f"{data_file}.save_fix_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(data_file, backup_name)
            print(f"💾 نسخة احتياطية: {backup_name}")
        
        # Test write operation
        test_data = create_test_data()
        
        try:
            # Try to write test data
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False, indent=2)
            
            print("✅ اختبار الكتابة: نجح")
            
            # Verify the written data
            with open(data_file, 'r', encoding='utf-8') as f:
                verified_data = json.load(f)
            
            if verified_data == test_data:
                print("✅ التحقق من البيانات: نجح")
                return True
            else:
                print("❌ التحقق من البيانات: فشل")
                return False
                
        except PermissionError:
            print("❌ خطأ صلاحيات: لا يمكن الكتابة")
            
            # Try to fix permissions
            try:
                import stat
                os.chmod(data_file, stat.S_IWRITE | stat.S_IREAD)
                print("🔧 تم تعديل الصلاحيات")
                
                # Retry write
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(test_data, f, ensure_ascii=False, indent=2)
                
                print("✅ الكتابة بعد إصلاح الصلاحيات: نجحت")
                return True
                
            except Exception as e:
                print(f"❌ فشل في إصلاح الصلاحيات: {e}")
                return False
        
        except Exception as e:
            print(f"❌ خطأ في الكتابة: {e}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الصلاحيات: {e}")
        return False

def create_robust_save_function():
    """Create robust save function"""
    try:
        print("\n🛡️ إنشاء دالة حفظ محسنة...")
        
        save_function_code = '''
def save_data_robust(data, filename="protech_simple_data.json"):
    """
    Robust data saving function with multiple fallbacks
    دالة حفظ البيانات المحسنة مع عدة بدائل
    """
    import json
    import os
    import shutil
    from datetime import datetime
    
    try:
        # Method 1: Direct save
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print("✅ حفظ مباشر: نجح")
            return True
        except PermissionError:
            print("⚠️ حفظ مباشر: فشل - مشكلة صلاحيات")
        except Exception as e:
            print(f"⚠️ حفظ مباشر: فشل - {e}")
        
        # Method 2: Save to temp file then move
        try:
            temp_filename = f"{filename}.temp_{datetime.now().strftime('%H%M%S')}"
            
            with open(temp_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Move temp file to final location
            if os.path.exists(filename):
                backup_name = f"{filename}.backup_{datetime.now().strftime('%H%M%S')}"
                shutil.move(filename, backup_name)
            
            shutil.move(temp_filename, filename)
            print("✅ حفظ مؤقت ثم نقل: نجح")
            return True
            
        except Exception as e:
            print(f"⚠️ حفظ مؤقت: فشل - {e}")
            # Clean up temp file
            if os.path.exists(temp_filename):
                try:
                    os.remove(temp_filename)
                except:
                    pass
        
        # Method 3: Save to alternative location
        try:
            import tempfile
            temp_dir = tempfile.gettempdir()
            alt_filename = os.path.join(temp_dir, f"protech_emergency_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            with open(alt_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ حفظ بديل: نجح في {alt_filename}")
            
            # Try to copy back to original location
            try:
                shutil.copy2(alt_filename, filename)
                print("✅ نسخ إلى المكان الأصلي: نجح")
                os.remove(alt_filename)
                return True
            except:
                print(f"⚠️ البيانات محفوظة في: {alt_filename}")
                return True
                
        except Exception as e:
            print(f"❌ حفظ بديل: فشل - {e}")
        
        # Method 4: Save as multiple small files
        try:
            backup_dir = "protech_data_backup"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            for key, value in data.items():
                part_filename = os.path.join(backup_dir, f"{key}_{timestamp}.json")
                with open(part_filename, 'w', encoding='utf-8') as f:
                    json.dump({key: value}, f, ensure_ascii=False, indent=2)
            
            print(f"✅ حفظ متعدد: نجح في {backup_dir}")
            return True
            
        except Exception as e:
            print(f"❌ حفظ متعدد: فشل - {e}")
        
        print("❌ فشل في جميع طرق الحفظ")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الحفظ: {e}")
        return False

# Test the robust save function
if __name__ == "__main__":
    test_data = {
        "test": "data",
        "timestamp": datetime.now().isoformat()
    }
    
    result = save_data_robust(test_data, "test_save.json")
    print(f"نتيجة الاختبار: {'نجح' if result else 'فشل'}")
'''
        
        # Save the robust function to a file
        with open("robust_save_function.py", 'w', encoding='utf-8') as f:
            f.write(save_function_code)
        
        print("✅ تم إنشاء دالة الحفظ المحسنة: robust_save_function.py")
        
        # Test the function
        exec(save_function_code)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء دالة الحفظ: {e}")
        return False

def apply_data_save_fix():
    """Apply comprehensive data save fix"""
    try:
        print("\n🔧 تطبيق إصلاح شامل لحفظ البيانات...")
        
        # Create emergency data if none exists
        data_file = "protech_simple_data.json"
        
        if not os.path.exists(data_file) or os.path.getsize(data_file) < 10:
            print("📝 إنشاء بيانات أساسية...")
            
            basic_data = create_test_data()
            
            try:
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(basic_data, f, ensure_ascii=False, indent=2)
                print("✅ تم إنشاء بيانات أساسية")
            except Exception as e:
                print(f"❌ فشل في إنشاء البيانات الأساسية: {e}")
                return False
        
        # Create auto-save script
        autosave_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Auto-Save Monitor
مراقب الحفظ التلقائي لـ ProTech
"""

import json
import os
import time
import shutil
from datetime import datetime

def monitor_and_save():
    """Monitor data file and create backups"""
    data_file = "protech_simple_data.json"
    last_backup_time = 0
    backup_interval = 300  # 5 minutes
    
    while True:
        try:
            if os.path.exists(data_file):
                current_time = time.time()
                
                if current_time - last_backup_time > backup_interval:
                    # Create backup
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_name = f"protech_auto_backup_{timestamp}.json"
                    
                    try:
                        shutil.copy2(data_file, backup_name)
                        print(f"✅ نسخة احتياطية تلقائية: {backup_name}")
                        last_backup_time = current_time
                    except Exception as e:
                        print(f"⚠️ فشل في النسخ الاحتياطي: {e}")
            
            time.sleep(60)  # Check every minute
            
        except KeyboardInterrupt:
            print("\\n🛑 تم إيقاف مراقب الحفظ التلقائي")
            break
        except Exception as e:
            print(f"❌ خطأ في المراقب: {e}")
            time.sleep(60)

if __name__ == "__main__":
    print("🔄 بدء مراقب الحفظ التلقائي...")
    monitor_and_save()
'''
        
        with open("auto_save_monitor.py", 'w', encoding='utf-8') as f:
            f.write(autosave_script)
        
        print("✅ تم إنشاء مراقب الحفظ التلقائي: auto_save_monitor.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق الإصلاح: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح مشكلة حفظ البيانات في ProTech")
    print("🔧 Fixing ProTech Data Save Issue")
    print("="*60)
    
    # Step 1: Diagnose the issue
    if not diagnose_save_issue():
        print("❌ فشل في التشخيص - توقف الإصلاح")
        return False
    
    # Step 2: Fix permissions
    if not fix_data_save_permissions():
        print("❌ فشل في إصلاح الصلاحيات")
        return False
    
    # Step 3: Create robust save function
    if not create_robust_save_function():
        print("❌ فشل في إنشاء دالة الحفظ المحسنة")
        return False
    
    # Step 4: Apply comprehensive fix
    if not apply_data_save_fix():
        print("❌ فشل في تطبيق الإصلاح الشامل")
        return False
    
    print("\n" + "="*60)
    print("🎉 تم إصلاح مشكلة حفظ البيانات بنجاح!")
    print("🎉 Data save issue fixed successfully!")
    
    print("\n💡 الحلول المطبقة:")
    print("✅ إصلاح صلاحيات الملفات")
    print("✅ إنشاء بيانات أساسية")
    print("✅ دالة حفظ محسنة مع بدائل متعددة")
    print("✅ مراقب حفظ تلقائي")
    
    print("\n🔧 للاستخدام:")
    print("1. البيانات الآن محفوظة ويمكن الكتابة عليها")
    print("2. استخدم robust_save_function.py للحفظ المحسن")
    print("3. شغل auto_save_monitor.py للحفظ التلقائي")
    
    return True

if __name__ == "__main__":
    main()
