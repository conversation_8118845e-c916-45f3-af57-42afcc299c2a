// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  password  String
  firstName String
  lastName  String
  role      UserRole @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  createdInvoices     Invoice[]     @relation("CreatedBy")
  createdPurchases    Purchase[]    @relation("CreatedBy")
  inventoryMovements  InventoryMovement[]
  
  @@map("users")
}

enum UserRole {
  ADMIN
  MANAGER
  USER
}

// Customer Management
model Customer {
  id            String          @id @default(cuid())
  code          String          @unique
  name          String
  email         String?
  phone         String?
  address       String?
  city          String?
  country       String?
  taxNumber     String?
  creditLimit   Decimal         @default(0)
  currentBalance Decimal        @default(0)
  category      CustomerCategory @default(RETAIL)
  priceLevel    Int             @default(1) // 1-4 for different price levels
  isActive      Boolean         @default(true)
  notes         String?
  createdAt     DateTime        @default(now())
  updatedAt     DateTime        @updatedAt

  // Relations
  invoices      Invoice[]
  payments      Payment[]
  
  @@map("customers")
}

enum CustomerCategory {
  RETAIL
  WHOLESALE
  DISTRIBUTOR
  VIP
}

// Supplier Management
model Supplier {
  id            String   @id @default(cuid())
  code          String   @unique
  name          String
  email         String?
  phone         String?
  address       String?
  city          String?
  country       String?
  taxNumber     String?
  paymentTerms  String?
  isActive      Boolean  @default(true)
  notes         String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  purchases     Purchase[]
  products      Product[]
  
  @@map("suppliers")
}

// Product Management
model Category {
  id          String    @id @default(cuid())
  name        String
  description String?
  parentId    String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  parent      Category? @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    Category[] @relation("CategoryHierarchy")
  products    Product[]
  
  @@map("categories")
}

model Product {
  id              String    @id @default(cuid())
  code            String    @unique
  name            String
  description     String?
  barcode         String?   @unique
  categoryId      String?
  supplierId      String?
  unit            String    @default("PCS")
  costPrice       Decimal   @default(0)
  basePrice       Decimal   @default(0)
  priceLevel1     Decimal   @default(0) // Base + 5%
  priceLevel2     Decimal   @default(0) // Base + 15%
  priceLevel3     Decimal   @default(0) // Base + 20%
  priceLevel4     Decimal   @default(0) // Base + 30%
  currentStock    Int       @default(0)
  minStock        Int       @default(0)
  maxStock        Int       @default(0)
  location        String?
  isActive        Boolean   @default(true)
  hasVariants     Boolean   @default(false)
  trackInventory  Boolean   @default(true)
  allowNegative   Boolean   @default(false)
  weight          Decimal?
  dimensions      String?
  imageUrl        String?
  notes           String?
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt

  // Relations
  category        Category? @relation(fields: [categoryId], references: [id])
  supplier        Supplier? @relation(fields: [supplierId], references: [id])
  invoiceItems    InvoiceItem[]
  purchaseItems   PurchaseItem[]
  inventoryMovements InventoryMovement[]
  variants        ProductVariant[]
  
  @@map("products")
}

model ProductVariant {
  id          String   @id @default(cuid())
  productId   String
  name        String
  sku         String   @unique
  barcode     String?  @unique
  costPrice   Decimal  @default(0)
  basePrice   Decimal  @default(0)
  priceLevel1 Decimal  @default(0)
  priceLevel2 Decimal  @default(0)
  priceLevel3 Decimal  @default(0)
  priceLevel4 Decimal  @default(0)
  currentStock Int     @default(0)
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  product     Product  @relation(fields: [productId], references: [id], onDelete: Cascade)
  
  @@map("product_variants")
}

// Inventory Management
model InventoryMovement {
  id            String            @id @default(cuid())
  productId     String
  userId        String
  type          MovementType
  quantity      Int
  unitCost      Decimal?
  totalCost     Decimal?
  reason        String?
  referenceType String?           // 'invoice', 'purchase', 'adjustment'
  referenceId   String?
  notes         String?
  createdAt     DateTime          @default(now())

  // Relations
  product       Product           @relation(fields: [productId], references: [id])
  user          User              @relation(fields: [userId], references: [id])
  
  @@map("inventory_movements")
}

enum MovementType {
  IN
  OUT
  ADJUSTMENT
  TRANSFER
}

// Sales Management
model Invoice {
  id              String        @id @default(cuid())
  number          String        @unique
  customerId      String
  userId          String
  date            DateTime      @default(now())
  dueDate         DateTime?
  status          InvoiceStatus @default(DRAFT)
  subtotal        Decimal       @default(0)
  taxAmount       Decimal       @default(0)
  discountAmount  Decimal       @default(0)
  totalAmount     Decimal       @default(0)
  paidAmount      Decimal       @default(0)
  balanceAmount   Decimal       @default(0)
  notes           String?
  terms           String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  customer        Customer      @relation(fields: [customerId], references: [id])
  createdBy       User          @relation("CreatedBy", fields: [userId], references: [id])
  items           InvoiceItem[]
  payments        Payment[]

  @@map("invoices")
}

enum InvoiceStatus {
  DRAFT
  SENT
  PAID
  OVERDUE
  CANCELLED
  REFUNDED
}

model InvoiceItem {
  id          String   @id @default(cuid())
  invoiceId   String
  productId   String
  description String?
  quantity    Int
  unitPrice   Decimal
  discount    Decimal  @default(0)
  taxRate     Decimal  @default(0)
  lineTotal   Decimal
  createdAt   DateTime @default(now())

  // Relations
  invoice     Invoice  @relation(fields: [invoiceId], references: [id], onDelete: Cascade)
  product     Product  @relation(fields: [productId], references: [id])

  @@map("invoice_items")
}

// Purchase Management
model Purchase {
  id              String         @id @default(cuid())
  number          String         @unique
  supplierId      String
  userId          String
  date            DateTime       @default(now())
  expectedDate    DateTime?
  receivedDate    DateTime?
  status          PurchaseStatus @default(DRAFT)
  subtotal        Decimal        @default(0)
  taxAmount       Decimal        @default(0)
  discountAmount  Decimal        @default(0)
  totalAmount     Decimal        @default(0)
  paidAmount      Decimal        @default(0)
  balanceAmount   Decimal        @default(0)
  notes           String?
  terms           String?
  createdAt       DateTime       @default(now())
  updatedAt       DateTime       @updatedAt

  // Relations
  supplier        Supplier       @relation(fields: [supplierId], references: [id])
  createdBy       User           @relation("CreatedBy", fields: [userId], references: [id])
  items           PurchaseItem[]

  @@map("purchases")
}

enum PurchaseStatus {
  DRAFT
  SENT
  CONFIRMED
  RECEIVED
  CANCELLED
}

model PurchaseItem {
  id            String   @id @default(cuid())
  purchaseId    String
  productId     String
  description   String?
  quantity      Int
  receivedQty   Int      @default(0)
  unitCost      Decimal
  discount      Decimal  @default(0)
  taxRate       Decimal  @default(0)
  lineTotal     Decimal
  createdAt     DateTime @default(now())

  // Relations
  purchase      Purchase @relation(fields: [purchaseId], references: [id], onDelete: Cascade)
  product       Product  @relation(fields: [productId], references: [id])

  @@map("purchase_items")
}

// Payment Management
model Payment {
  id            String      @id @default(cuid())
  customerId    String?
  invoiceId     String?
  amount        Decimal
  method        PaymentMethod
  reference     String?
  notes         String?
  date          DateTime    @default(now())
  createdAt     DateTime    @default(now())

  // Relations
  customer      Customer?   @relation(fields: [customerId], references: [id])
  invoice       Invoice?    @relation(fields: [invoiceId], references: [id])

  @@map("payments")
}

enum PaymentMethod {
  CASH
  CARD
  BANK_TRANSFER
  CHECK
  CREDIT
}

// System Settings
model Setting {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  type      String   @default("string")
  category  String   @default("general")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}
