
def save_data_robust(data, filename="protech_simple_data.json"):
    """
    Robust data saving function with multiple fallbacks
    دالة حفظ البيانات المحسنة مع عدة بدائل
    """
    import json
    import os
    import shutil
    from datetime import datetime
    
    try:
        # Method 1: Direct save
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print("✅ حفظ مباشر: نجح")
            return True
        except PermissionError:
            print("⚠️ حفظ مباشر: فشل - مشكلة صلاحيات")
        except Exception as e:
            print(f"⚠️ حفظ مباشر: فشل - {e}")
        
        # Method 2: Save to temp file then move
        try:
            temp_filename = f"{filename}.temp_{datetime.now().strftime('%H%M%S')}"
            
            with open(temp_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Move temp file to final location
            if os.path.exists(filename):
                backup_name = f"{filename}.backup_{datetime.now().strftime('%H%M%S')}"
                shutil.move(filename, backup_name)
            
            shutil.move(temp_filename, filename)
            print("✅ حفظ مؤقت ثم نقل: نجح")
            return True
            
        except Exception as e:
            print(f"⚠️ حفظ مؤقت: فشل - {e}")
            # Clean up temp file
            if os.path.exists(temp_filename):
                try:
                    os.remove(temp_filename)
                except:
                    pass
        
        # Method 3: Save to alternative location
        try:
            import tempfile
            temp_dir = tempfile.gettempdir()
            alt_filename = os.path.join(temp_dir, f"protech_emergency_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
            with open(alt_filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ حفظ بديل: نجح في {alt_filename}")
            
            # Try to copy back to original location
            try:
                shutil.copy2(alt_filename, filename)
                print("✅ نسخ إلى المكان الأصلي: نجح")
                os.remove(alt_filename)
                return True
            except:
                print(f"⚠️ البيانات محفوظة في: {alt_filename}")
                return True
                
        except Exception as e:
            print(f"❌ حفظ بديل: فشل - {e}")
        
        # Method 4: Save as multiple small files
        try:
            backup_dir = "protech_data_backup"
            if not os.path.exists(backup_dir):
                os.makedirs(backup_dir)
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            for key, value in data.items():
                part_filename = os.path.join(backup_dir, f"{key}_{timestamp}.json")
                with open(part_filename, 'w', encoding='utf-8') as f:
                    json.dump({key: value}, f, ensure_ascii=False, indent=2)
            
            print(f"✅ حفظ متعدد: نجح في {backup_dir}")
            return True
            
        except Exception as e:
            print(f"❌ حفظ متعدد: فشل - {e}")
        
        print("❌ فشل في جميع طرق الحفظ")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام في الحفظ: {e}")
        return False

# Test the robust save function
if __name__ == "__main__":
    from datetime import datetime
    test_data = {
        "test": "data",
        "timestamp": datetime.now().isoformat()
    }
    
    result = save_data_robust(test_data, "test_save.json")
    print(f"نتيجة الاختبار: {'نجح' if result else 'فشل'}")
