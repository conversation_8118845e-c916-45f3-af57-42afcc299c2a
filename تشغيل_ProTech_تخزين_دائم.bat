@echo off
title ProTech with Persistent Storage - نظام ProTech مع التخزين الدائم المنظم
color 0A

echo.
echo ================================================================
echo    💾 نظام ProTech مع التخزين الدائم المنظم
echo    💾 ProTech with Organized Persistent Storage
echo ================================================================
echo.

echo [1/7] فحص متطلبات النظام / Checking system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت / Python not installed
    echo يرجى تثبيت Python 3.8+ من https://python.org
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر / Python available
)

echo.
echo [2/7] فحص المكتبات المطلوبة / Checking required libraries...
python -c "import tkinter, json, os, sqlite3, datetime, threading, time, hashlib, gzip, shutil, logging, traceback" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبات مطلوبة مفقودة / Required libraries missing
    echo جميع المكتبات المطلوبة مدمجة مع Python / All required libraries are built-in with Python
    echo يرجى التأكد من تثبيت Python بشكل صحيح / Please ensure Python is properly installed
    pause
    exit /b 1
) else (
    echo ✅ جميع المكتبات متوفرة / All libraries available
)

echo.
echo [3/7] إنشاء مجلدات التخزين المنظم / Creating organized storage directories...
if not exist "data" mkdir data
if not exist "backups" mkdir backups
if not exist "archives" mkdir archives
if not exist "temp" mkdir temp
if not exist "logs" mkdir logs
if not exist "exports" mkdir exports
echo ✅ تم إنشاء مجلدات التخزين المنظم / Organized storage directories created

echo.
echo [4/7] فحص ملفات النظام / Checking system files...
if exist "persistent_storage_manager.py" (
    echo ✅ مدير التخزين الدائم موجود / Persistent storage manager found
) else (
    echo ❌ مدير التخزين الدائم مفقود / Persistent storage manager missing
    echo يرجى التأكد من وجود ملف persistent_storage_manager.py
    pause
    exit /b 1
)

if exist "protech_persistent_integration.py" (
    echo ✅ وحدة التكامل موجودة / Integration module found
) else (
    echo ❌ وحدة التكامل مفقودة / Integration module missing
    echo يرجى التأكد من وجود ملف protech_persistent_integration.py
    pause
    exit /b 1
)

if exist "protech_simple_working.py" (
    echo ✅ النظام الأساسي موجود / Base system found
) else (
    echo ❌ النظام الأساسي مفقود / Base system missing
    echo يرجى التأكد من وجود ملف protech_simple_working.py
    pause
    exit /b 1
)

echo.
echo [5/7] اختبار نظام التخزين الدائم / Testing persistent storage system...
python -c "
from persistent_storage_manager import PersistentStorageManager
try:
    storage = PersistentStorageManager()
    print('✅ تم اختبار نظام التخزين الدائم بنجاح')
    print('✅ Persistent storage system test successful')
    storage.close()
except Exception as e:
    print(f'❌ خطأ في نظام التخزين: {e}')
    import sys
    sys.exit(1)
" 2>nul

if errorlevel 1 (
    echo ❌ فشل في اختبار نظام التخزين / Persistent storage test failed
    echo.
    echo 🔧 خطوات استكشاف الأخطاء / Troubleshooting steps:
    echo    1. تأكد من صحة ملفات النظام / Verify system files integrity
    echo    2. تحقق من أذونات الملفات / Check file permissions
    echo    3. تأكد من وجود مساحة كافية / Ensure sufficient disk space
    echo.
    pause
    exit /b 1
)

echo.
echo [6/7] فحص البيانات الموجودة / Checking existing data...
if exist "protech_simple_data.json" (
    echo ✅ ملف البيانات JSON موجود / JSON data file found
    python -c "
import json
try:
    with open('protech_simple_data.json', 'r', encoding='utf-8') as f:
        data = json.load(f)
    products = len(data.get('products', []))
    customers = len(data.get('customers', []))
    suppliers = len(data.get('suppliers', []))
    print(f'📊 البيانات الموجودة: {products} منتج، {customers} عميل، {suppliers} مورد')
    print(f'📊 Existing data: {products} products, {customers} customers, {suppliers} suppliers')
    print('🔄 سيتم ترحيل البيانات إلى نظام التخزين الدائم')
    print('🔄 Data will be migrated to persistent storage system')
except Exception as e:
    print(f'⚠️ خطأ في قراءة البيانات: {e}')
" 2>nul
) else (
    echo 📝 لا توجد بيانات سابقة - سيبدأ النظام فارغاً / No previous data - system will start empty
)

if exist "data\protech_data.db" (
    echo ✅ قاعدة بيانات SQLite موجودة / SQLite database found
    python -c "
import sqlite3
import os
try:
    db_size = os.path.getsize('data/protech_data.db')
    print(f'📊 حجم قاعدة البيانات: {db_size:,} بايت ({db_size/(1024*1024):.2f} MB)')
    print(f'📊 Database size: {db_size:,} bytes ({db_size/(1024*1024):.2f} MB)')
except Exception as e:
    print(f'⚠️ خطأ في قراءة قاعدة البيانات: {e}')
" 2>nul
) else (
    echo 📝 سيتم إنشاء قاعدة بيانات جديدة / New database will be created
)

echo.
echo [7/7] تشغيل نظام ProTech مع التخزين الدائم / Starting ProTech with Persistent Storage...
echo.
echo 🎯 مميزات التخزين الدائم المنظم / Organized Persistent Storage Features:
echo    • تخزين SQLite محسن مع فهرسة متقدمة / Optimized SQLite with advanced indexing
echo    • نسخ احتياطية تلقائية مضغوطة كل 5 دقائق / Automatic compressed backups every 5 minutes
echo    • حفظ تلقائي للبيانات كل 30 ثانية / Automatic data saving every 30 seconds
echo    • ترحيل تلقائي من JSON إلى SQLite / Automatic migration from JSON to SQLite
echo    • تنظيم هيكلي للملفات والمجلدات / Structured file and folder organization
echo    • تتبع شامل للإحصائيات والأداء / Comprehensive statistics and performance tracking
echo    • تصدير واستيراد محسن للبيانات / Enhanced data export and import
echo    • حماية البيانات من الفقدان / Data loss protection
echo    • سجلات مفصلة لجميع العمليات / Detailed logs for all operations
echo    • تحقق من سلامة البيانات / Data integrity verification
echo    • تنظيف تلقائي للنسخ القديمة / Automatic cleanup of old backups
echo    • دعم التشفير والضغط / Encryption and compression support
echo.

echo 🚀 بدء التشغيل مع التخزين الدائم / Starting with persistent storage...
echo.

python -c "
import sys
import os

# Add current directory to Python path
sys.path.insert(0, '.')

try:
    # Import required modules
    from protech_persistent_integration import add_persistent_storage_to_app
    
    print('🔄 تحميل النظام الأساسي...')
    print('🔄 Loading base system...')
    
    # Import and run the original ProTech system with persistent storage
    import subprocess
    import threading
    
    def run_protech_with_storage():
        try:
            # Run the original ProTech system
            subprocess.run([sys.executable, 'protech_simple_working.py'], check=True)
        except Exception as e:
            print(f'خطأ في تشغيل النظام: {e}')
    
    # Start ProTech in a separate thread
    protech_thread = threading.Thread(target=run_protech_with_storage)
    protech_thread.start()
    
    print('✅ تم تشغيل النظام مع التخزين الدائم')
    print('✅ System started with persistent storage')
    
    # Wait for the thread to complete
    protech_thread.join()
    
except Exception as e:
    print(f'❌ خطأ في تشغيل النظام: {e}')
    import traceback
    traceback.print_exc()
    sys.exit(1)
"

echo.
if errorlevel 1 (
    echo ❌ حدث خطأ في تشغيل النظام مع التخزين الدائم / Error in running system with persistent storage
    echo.
    echo 🔧 خطوات استكشاف الأخطاء المتقدمة / Advanced troubleshooting steps:
    echo    1. تحقق من سجل الأخطاء في logs/storage.log
    echo    2. تأكد من سلامة قاعدة البيانات في data/protech_data.db
    echo    3. جرب حذف مجلد temp وإعادة التشغيل
    echo    4. تأكد من وجود مساحة كافية على القرص الصلب
    echo    5. جرب تشغيل النظام كمدير
    echo.
    echo    1. Check error log in logs/storage.log
    echo    2. Verify database integrity in data/protech_data.db
    echo    3. Try deleting temp folder and restart
    echo    4. Ensure sufficient disk space
    echo    5. Try running as administrator
    echo.
    echo 📁 الملفات المهمة / Important files:
    echo    - persistent_storage_manager.py (مدير التخزين / Storage manager)
    echo    - protech_persistent_integration.py (وحدة التكامل / Integration module)
    echo    - data/ (مجلد البيانات / Data folder)
    echo    - backups/ (النسخ الاحتياطية / Backups)
    echo    - logs/ (السجلات / Logs)
    echo.
) else (
    echo ✅ تم إغلاق النظام مع التخزين الدائم بنجاح / System with persistent storage closed successfully
    echo.
    echo 📊 معلومات التخزين الدائم / Persistent Storage Information:
    echo    • تم حفظ البيانات في data/protech_data.db
    echo    • تم حفظ النسخ الاحتياطية في backups/
    echo    • تم حفظ السجلات في logs/
    echo    • تم حفظ الصادرات في exports/
    echo    • Data saved in data/protech_data.db
    echo    • Backups saved in backups/
    echo    • Logs saved in logs/
    echo    • Exports saved in exports/
    echo.
    echo 🔄 لإعادة التشغيل، انقر نقرة مزدوجة على هذا الملف مرة أخرى
    echo 🔄 To restart, double-click this file again
    echo.
    echo 📈 إحصائيات سريعة / Quick Statistics:
    python -c "
import os
try:
    if os.path.exists('data/protech_data.db'):
        db_size = os.path.getsize('data/protech_data.db')
        print(f'    📊 حجم قاعدة البيانات: {db_size:,} بايت')
        print(f'    📊 Database size: {db_size:,} bytes')
    
    if os.path.exists('backups'):
        backup_files = [f for f in os.listdir('backups') if f.startswith('protech_backup_')]
        print(f'    💾 عدد النسخ الاحتياطية: {len(backup_files)}')
        print(f'    💾 Backup files count: {len(backup_files)}')
    
    if os.path.exists('logs'):
        log_files = [f for f in os.listdir('logs') if f.endswith('.log')]
        print(f'    📝 ملفات السجلات: {len(log_files)}')
        print(f'    📝 Log files: {len(log_files)}')
        
except Exception as e:
    print(f'    ⚠️ خطأ في قراءة الإحصائيات: {e}')
" 2>nul
)

echo.
echo ================================================================
echo    شكراً لاستخدام نظام ProTech مع التخزين الدائم المنظم
echo    Thank you for using ProTech with Organized Persistent Storage
echo    
echo    💾 نظام تخزين متقدم - Advanced Storage System
echo    🏢 نظام محاسبة متكامل - Integrated Accounting System
echo    🔒 حماية البيانات المتقدمة - Advanced Data Protection
echo ================================================================
echo.

pause
