#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Smart Code Analyzer
محلل الكود الذكي لـ ProTech

Smart analysis of ProTech code to identify real issues without breaking functionality
تحليل ذكي لكود ProTech لتحديد المشاكل الحقيقية دون كسر الوظائف
"""

import os
import re
import ast
import json
import shutil
from datetime import datetime

class ProTechSmartAnalyzer:
    """Smart analyzer for ProTech code"""
    
    def __init__(self):
        self.code_file = "protech_simple_working.py"
        self.analysis_results = {
            'syntax_errors': [],
            'logical_issues': [],
            'performance_issues': [],
            'redundancy_issues': [],
            'real_bugs': [],
            'suggestions': []
        }
        
    def create_backup(self):
        """Create backup before analysis"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f'{self.code_file}.smart_analysis_backup_{timestamp}'
            shutil.copy2(self.code_file, backup_name)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            return backup_name
        except Exception as e:
            print(f"❌ خطأ في النسخة الاحتياطية: {e}")
            return None
    
    def analyze_syntax_issues(self):
        """Analyze real syntax issues"""
        try:
            print("🔍 تحليل مشاكل التركيب الحقيقية...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test actual syntax
            try:
                ast.parse(content)
                print("✅ لا توجد أخطاء تركيب حقيقية")
                return True
            except SyntaxError as e:
                self.analysis_results['syntax_errors'].append({
                    'line': e.lineno,
                    'message': e.msg,
                    'text': e.text
                })
                print(f"❌ خطأ تركيب حقيقي في السطر {e.lineno}: {e.msg}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في تحليل التركيب: {e}")
            return False
    
    def analyze_real_logical_issues(self):
        """Analyze real logical issues that affect functionality"""
        try:
            print("🔍 تحليل المشاكل المنطقية الحقيقية...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            real_issues = []
            
            for i, line in enumerate(lines, 1):
                line_stripped = line.strip()
                
                # Real issue 1: Infinite loops
                if 'while True:' in line and 'break' not in ''.join(lines[i:i+10]):
                    real_issues.append({
                        'line': i,
                        'type': 'infinite_loop',
                        'message': 'حلقة لا نهائية محتملة بدون break',
                        'severity': 'high'
                    })
                
                # Real issue 2: Division by zero without check
                if '/' in line and 'len(' in line and 'if len(' not in line:
                    real_issues.append({
                        'line': i,
                        'type': 'division_by_zero',
                        'message': 'قسمة محتملة على صفر',
                        'severity': 'medium'
                    })
                
                # Real issue 3: Undefined variables
                if re.search(r'self\.(\w+)\s*=.*self\.(\w+)', line):
                    # This is usually fine, skip
                    pass
                
                # Real issue 4: Missing return in functions that should return
                if line_stripped.startswith('def ') and 'get_' in line:
                    # Check if function has return statement
                    func_lines = []
                    j = i
                    while j < len(lines) and (lines[j].startswith('    ') or not lines[j].strip()):
                        func_lines.append(lines[j])
                        j += 1
                    
                    if not any('return' in func_line for func_line in func_lines):
                        real_issues.append({
                            'line': i,
                            'type': 'missing_return',
                            'message': 'دالة get_ بدون return',
                            'severity': 'medium'
                        })
            
            self.analysis_results['logical_issues'] = real_issues
            print(f"🔍 تم العثور على {len(real_issues)} مشكلة منطقية حقيقية")
            
            return len(real_issues) == 0
            
        except Exception as e:
            print(f"❌ خطأ في تحليل المشاكل المنطقية: {e}")
            return False
    
    def analyze_performance_bottlenecks(self):
        """Analyze real performance bottlenecks"""
        try:
            print("🔍 تحليل اختناقات الأداء الحقيقية...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            performance_issues = []
            
            # Issue 1: Too many threads
            thread_count = content.count('threading.Thread')
            if thread_count > 10:
                performance_issues.append({
                    'type': 'too_many_threads',
                    'count': thread_count,
                    'message': f'عدد كبير من الخيوط: {thread_count}',
                    'severity': 'high'
                })
            
            # Issue 2: Excessive caching
            cache_count = content.count('cache')
            if cache_count > 50:
                performance_issues.append({
                    'type': 'excessive_caching',
                    'count': cache_count,
                    'message': f'استخدام مفرط للذاكرة المؤقتة: {cache_count}',
                    'severity': 'medium'
                })
            
            # Issue 3: Too many performance monitors
            monitor_count = content.count('performance_stats')
            if monitor_count > 30:
                performance_issues.append({
                    'type': 'excessive_monitoring',
                    'count': monitor_count,
                    'message': f'مراقبة أداء مفرطة: {monitor_count}',
                    'severity': 'medium'
                })
            
            # Issue 4: Large file size
            file_size = len(content)
            if file_size > 500000:  # 500KB
                performance_issues.append({
                    'type': 'large_file',
                    'size': file_size,
                    'message': f'حجم ملف كبير: {file_size/1000:.1f}KB',
                    'severity': 'high'
                })
            
            self.analysis_results['performance_issues'] = performance_issues
            print(f"⚡ تم العثور على {len(performance_issues)} مشكلة أداء حقيقية")
            
            return len(performance_issues) == 0
            
        except Exception as e:
            print(f"❌ خطأ في تحليل الأداء: {e}")
            return False
    
    def analyze_code_redundancy(self):
        """Analyze code redundancy and duplication"""
        try:
            print("🔍 تحليل التكرار في الكود...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            redundancy_issues = []
            
            # Find duplicate function definitions
            function_names = []
            for i, line in enumerate(lines, 1):
                if line.strip().startswith('def '):
                    func_name = line.strip().split('def ')[1].split('(')[0]
                    if func_name in function_names:
                        redundancy_issues.append({
                            'line': i,
                            'type': 'duplicate_function',
                            'function': func_name,
                            'message': f'دالة مكررة: {func_name}',
                            'severity': 'medium'
                        })
                    function_names.append(func_name)
            
            # Find similar code blocks
            code_blocks = {}
            current_block = []
            
            for i, line in enumerate(lines, 1):
                if line.strip() and not line.strip().startswith('#'):
                    current_block.append(line.strip())
                    if len(current_block) >= 5:
                        block_key = ''.join(current_block[-5:])
                        if block_key in code_blocks:
                            redundancy_issues.append({
                                'line': i,
                                'type': 'similar_code',
                                'message': 'كود مشابه موجود',
                                'severity': 'low'
                            })
                        code_blocks[block_key] = i
                else:
                    current_block = []
            
            self.analysis_results['redundancy_issues'] = redundancy_issues
            print(f"🔄 تم العثور على {len(redundancy_issues)} مشكلة تكرار")
            
            return len(redundancy_issues) == 0
            
        except Exception as e:
            print(f"❌ خطأ في تحليل التكرار: {e}")
            return False
    
    def identify_real_bugs(self):
        """Identify real bugs that affect functionality"""
        try:
            print("🐛 تحديد الأخطاء الحقيقية...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            real_bugs = []
            
            # Bug 1: Using undefined variables
            undefined_vars = re.findall(r'self\.(\w+)(?!\s*=)', content)
            defined_vars = re.findall(r'self\.(\w+)\s*=', content)
            
            for var in set(undefined_vars):
                if var not in defined_vars and var not in ['root', 'content_frame']:
                    real_bugs.append({
                        'type': 'undefined_variable',
                        'variable': var,
                        'message': f'متغير غير معرف: self.{var}',
                        'severity': 'high'
                    })
            
            # Bug 2: Incorrect exception handling
            empty_excepts = re.findall(r'except[^:]*:\s*pass', content)
            if len(empty_excepts) > 5:
                real_bugs.append({
                    'type': 'empty_exception_handling',
                    'count': len(empty_excepts),
                    'message': f'معالجة استثناءات فارغة: {len(empty_excepts)}',
                    'severity': 'medium'
                })
            
            self.analysis_results['real_bugs'] = real_bugs
            print(f"🐛 تم العثور على {len(real_bugs)} خطأ حقيقي")
            
            return len(real_bugs) == 0
            
        except Exception as e:
            print(f"❌ خطأ في تحديد الأخطاء: {e}")
            return False
    
    def generate_smart_suggestions(self):
        """Generate smart improvement suggestions"""
        try:
            print("💡 إنشاء اقتراحات التحسين الذكية...")
            
            suggestions = []
            
            # Suggestion 1: Code organization
            suggestions.append({
                'type': 'organization',
                'priority': 'high',
                'title': 'تنظيم الكود',
                'description': 'تقسيم الكود إلى ملفات منفصلة حسب الوظيفة',
                'benefit': 'سهولة الصيانة والتطوير'
            })
            
            # Suggestion 2: Performance optimization
            if self.analysis_results['performance_issues']:
                suggestions.append({
                    'type': 'performance',
                    'priority': 'high',
                    'title': 'تحسين الأداء',
                    'description': 'تقليل عدد الخيوط ومراقبة الأداء',
                    'benefit': 'تحسين سرعة التطبيق واستهلاك الذاكرة'
                })
            
            # Suggestion 3: Error handling
            suggestions.append({
                'type': 'error_handling',
                'priority': 'medium',
                'title': 'تحسين معالجة الأخطاء',
                'description': 'استخدام معالجة أخطاء محددة بدلاً من العامة',
                'benefit': 'تشخيص أفضل للمشاكل'
            })
            
            # Suggestion 4: Code simplification
            suggestions.append({
                'type': 'simplification',
                'priority': 'medium',
                'title': 'تبسيط الكود',
                'description': 'إزالة التعقيدات غير الضرورية',
                'benefit': 'كود أسهل للفهم والصيانة'
            })
            
            self.analysis_results['suggestions'] = suggestions
            print(f"💡 تم إنشاء {len(suggestions)} اقتراح تحسين")
            
            return suggestions
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الاقتراحات: {e}")
            return []
    
    def run_comprehensive_analysis(self):
        """Run comprehensive smart analysis"""
        try:
            print("🧠 بدء التحليل الذكي الشامل لـ ProTech")
            print("🧠 Starting ProTech Smart Comprehensive Analysis")
            print("="*60)
            
            # Create backup
            self.create_backup()
            
            # Run all analyses
            syntax_ok = self.analyze_syntax_issues()
            logical_ok = self.analyze_real_logical_issues()
            performance_ok = self.analyze_performance_bottlenecks()
            redundancy_ok = self.analyze_code_redundancy()
            bugs_ok = self.identify_real_bugs()
            
            # Generate suggestions
            suggestions = self.generate_smart_suggestions()
            
            # Calculate overall health score
            health_score = sum([syntax_ok, logical_ok, performance_ok, redundancy_ok, bugs_ok]) * 20
            
            # Generate report
            self.generate_analysis_report(health_score)
            
            print("\n" + "="*60)
            print(f"📊 نتيجة التحليل الذكي: {health_score}%")
            print(f"📊 Smart Analysis Score: {health_score}%")
            
            if health_score >= 80:
                print("🎉 الكود في حالة جيدة!")
                print("🎉 Code is in good condition!")
            elif health_score >= 60:
                print("⚠️ الكود يحتاج تحسينات بسيطة")
                print("⚠️ Code needs minor improvements")
            else:
                print("🔧 الكود يحتاج تحسينات مهمة")
                print("🔧 Code needs significant improvements")
            
            print("="*60)
            
            return health_score >= 60
            
        except Exception as e:
            print(f"❌ خطأ في التحليل الشامل: {e}")
            return False
    
    def generate_analysis_report(self, health_score):
        """Generate detailed analysis report"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"protech_smart_analysis_report_{timestamp}.json"
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'health_score': health_score,
                'analysis_results': self.analysis_results,
                'summary': {
                    'syntax_errors': len(self.analysis_results['syntax_errors']),
                    'logical_issues': len(self.analysis_results['logical_issues']),
                    'performance_issues': len(self.analysis_results['performance_issues']),
                    'redundancy_issues': len(self.analysis_results['redundancy_issues']),
                    'real_bugs': len(self.analysis_results['real_bugs']),
                    'suggestions': len(self.analysis_results['suggestions'])
                }
            }
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"📄 تم إنشاء تقرير التحليل: {report_file}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير: {e}")

def main():
    """Main analysis function"""
    analyzer = ProTechSmartAnalyzer()
    success = analyzer.run_comprehensive_analysis()
    
    if success:
        print("\n✅ التحليل الذكي مكتمل بنجاح!")
        print("✅ Smart analysis completed successfully!")
    else:
        print("\n⚠️ التحليل الذكي مكتمل مع تحذيرات")
        print("⚠️ Smart analysis completed with warnings")
    
    return success

if __name__ == "__main__":
    main()
