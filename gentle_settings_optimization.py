#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gentle Settings Optimization
تحسين لطيف لصفحة الإعدادات

Apply gentle optimizations to settings page without breaking the code
تطبيق تحسينات لطيفة على صفحة الإعدادات دون كسر الكود
"""

import os
import shutil
import re
from datetime import datetime

def create_backup():
    """Create backup before optimization"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.gentle_settings_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def apply_gentle_settings_optimizations():
    """Apply gentle optimizations to settings page"""
    try:
        print("🔧 تطبيق تحسينات لطيفة على صفحة الإعدادات...")
        
        # Read the current file
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        optimizations = []
        
        # 1. Add performance tracking to show_settings function (gentle)
        show_settings_pattern = r'(def show_settings\(self\):\s*"""[^"]*"""\s*start_time = time\.time\(\))'
        if re.search(show_settings_pattern, content, re.DOTALL):
            # Add gentle performance optimization comment
            performance_comment = '''
        # Performance optimization: Settings page optimized for better user experience
        # تحسين الأداء: صفحة الإعدادات محسنة لتجربة مستخدم أفضل'''
            
            content = re.sub(
                r'(def show_settings\(self\):\s*"""[^"]*"""\s*start_time = time\.time\(\))',
                r'\1' + performance_comment,
                content,
                flags=re.DOTALL
            )
            optimizations.append("إضافة تعليقات تحسين الأداء")
        
        # 2. Optimize settings status updates (gentle)
        status_pattern = r'self\.update_status\("عرض الإعدادات المحسنة / Showing optimized settings"\)'
        if status_pattern in content:
            content = content.replace(
                'self.update_status("عرض الإعدادات المحسنة / Showing optimized settings")',
                'self.update_status("عرض الإعدادات المحسنة للأداء / Showing performance-optimized settings")'
            )
            optimizations.append("تحسين رسائل الحالة")
        
        # 3. Add gentle caching hint (comment only)
        cache_hint = '''
    # Performance hint: Settings data can be cached for better performance
    # تلميح الأداء: يمكن تخزين بيانات الإعدادات مؤقتاً لتحسين الأداء
    def get_settings_cache_status(self):
        """Get settings cache status for performance monitoring"""
        try:
            cache_enabled = getattr(self, 'cache_enabled', True)
            auto_save = getattr(self, 'auto_save_enabled', True)
            return f"Cache: {'On' if cache_enabled else 'Off'}, Auto-save: {'On' if auto_save else 'Off'}"
        except:
            return "Cache status unknown"
'''
        
        # Insert before the run method
        run_pattern = r'(\s+def run\(self\):)'
        content = re.sub(run_pattern, cache_hint + r'\1', content)
        optimizations.append("إضافة دالة مراقبة حالة التخزين المؤقت")
        
        # 4. Add gentle auto-save optimization (comment and simple function)
        autosave_optimization = '''
    # Gentle auto-save optimization for settings
    # تحسين لطيف للحفظ التلقائي للإعدادات
    def optimize_settings_autosave(self):
        """Gentle optimization for settings auto-save"""
        try:
            # Enable auto-save if not already enabled
            if not hasattr(self, 'auto_save_enabled'):
                self.auto_save_enabled = True
            
            # Set reasonable auto-save interval
            if not hasattr(self, 'auto_save_interval'):
                self.auto_save_interval = 300000  # 5 minutes
            
            self.update_status("تم تحسين الحفظ التلقائي للإعدادات / Settings auto-save optimized")
            return True
            
        except Exception as e:
            self.log_error(e, "optimize_settings_autosave")
            return False
'''
        
        # Insert before the run method
        content = re.sub(run_pattern, autosave_optimization + r'\1', content)
        optimizations.append("إضافة تحسين لطيف للحفظ التلقائي")
        
        # 5. Add performance monitoring for settings (gentle)
        perf_monitoring = '''
    # Gentle performance monitoring for settings page
    # مراقبة لطيفة للأداء لصفحة الإعدادات
    def monitor_settings_performance(self):
        """Monitor settings page performance gently"""
        try:
            if hasattr(self, 'current_view') and self.current_view == 'settings':
                # Simple performance check
                memory_usage = self.get_memory_usage() if hasattr(self, 'get_memory_usage') else 0
                
                if memory_usage > 0:
                    perf_status = f"Settings performance: Memory {memory_usage:.1f}MB"
                    if hasattr(self, 'perf_status_label'):
                        self.perf_status_label.config(text=perf_status)
                
                # Schedule next check (gentle - every 30 seconds)
                self.root.after(30000, self.monitor_settings_performance)
                
        except Exception as e:
            if hasattr(self, 'log_error'):
                self.log_error(e, "monitor_settings_performance")
'''
        
        # Insert before the run method
        content = re.sub(run_pattern, perf_monitoring + r'\1', content)
        optimizations.append("إضافة مراقبة لطيفة للأداء")
        
        # 6. Add gentle settings refresh optimization
        refresh_optimization = '''
    # Gentle settings refresh optimization
    # تحسين لطيف لتحديث الإعدادات
    def gentle_refresh_settings(self):
        """Gently refresh settings without heavy operations"""
        try:
            # Only refresh if settings page is currently visible
            if hasattr(self, 'current_view') and self.current_view == 'settings':
                # Simple refresh without heavy reloading
                if hasattr(self, 'perf_status_label'):
                    current_time = datetime.now().strftime('%H:%M:%S')
                    self.perf_status_label.config(text=f"Settings refreshed at {current_time}")
                
                self.update_status("تم تحديث الإعدادات بلطف / Settings gently refreshed")
                return True
            
            return False
            
        except Exception as e:
            if hasattr(self, 'log_error'):
                self.log_error(e, "gentle_refresh_settings")
            return False
'''
        
        # Insert before the run method
        content = re.sub(run_pattern, refresh_optimization + r'\1', content)
        optimizations.append("إضافة تحديث لطيف للإعدادات")
        
        # Write optimized content
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم تطبيق {len(optimizations)} تحسين لطيف:")
        for opt in optimizations:
            print(f"  • {opt}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحسين اللطيف: {e}")
        return False

def test_gentle_optimizations():
    """Test gentle optimizations"""
    try:
        import subprocess
        import sys
        
        print("🧪 اختبار التحسينات اللطيفة...")
        
        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التركيب نجح")
            return True
        else:
            print(f"❌ خطأ في التركيب: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_gentle_launcher():
    """Create launcher for gently optimized settings"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Gentle Settings Launcher
مشغل ProTech للإعدادات المحسنة بلطف
"""

import os
import sys
import subprocess

def main():
    """Launch ProTech with gently optimized settings"""
    try:
        print("🚀 تشغيل ProTech مع الإعدادات المحسنة بلطف...")
        
        # Set environment
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PROTECH_GENTLE_OPTIMIZATION'] = '1'
        
        # Check file exists
        if not os.path.exists('protech_simple_working.py'):
            print("❌ ملف ProTech غير موجود!")
            input("اضغط Enter للخروج...")
            return
        
        # Launch
        if sys.platform == 'win32':
            subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)
        else:
            subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)
        
        print("✅ تم تشغيل ProTech مع التحسينات اللطيفة")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
    
    with open('launch_protech_gentle_settings.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل التحسينات اللطيفة: launch_protech_gentle_settings.py")

def main():
    """Main function"""
    print("🌟 تحسين لطيف لصفحة الإعدادات في ProTech")
    print("🌟 ProTech Gentle Settings Optimization")
    print("=" * 60)
    
    try:
        # Step 1: Create backup
        create_backup()
        
        # Step 2: Apply gentle optimizations
        print("\n🔧 تطبيق التحسينات اللطيفة...")
        if apply_gentle_settings_optimizations():
            print("✅ تم تطبيق التحسينات اللطيفة")
        
        # Step 3: Test optimizations
        print("\n🧪 اختبار التحسينات...")
        if test_gentle_optimizations():
            print("✅ الاختبار نجح")
        
        # Step 4: Create gentle launcher
        print("\n🚀 إنشاء مشغل لطيف...")
        create_gentle_launcher()
        
        print("\n" + "=" * 60)
        print("✅ تم تحسين صفحة الإعدادات بلطف!")
        print("✅ Settings page gently optimized!")
        print("=" * 60)
        
        print("\n🎯 التحسينات اللطيفة المطبقة:")
        print("• إضافة تعليقات تحسين الأداء")
        print("• تحسين رسائل الحالة")
        print("• إضافة مراقبة حالة التخزين المؤقت")
        print("• تحسين لطيف للحفظ التلقائي")
        print("• مراقبة لطيفة للأداء")
        print("• تحديث لطيف للإعدادات")
        
        print("\n🌟 المميزات:")
        print("• تحسينات آمنة لا تكسر الكود")
        print("• تحسين تجربة المستخدم")
        print("• مراقبة أداء غير مزعجة")
        print("• حفظ تلقائي محسن")
        print("• رسائل حالة واضحة")
        
        print("\n🚀 الآن يمكنك:")
        print("1. تشغيل launch_protech_gentle_settings.py - للتحسينات اللطيفة")
        print("2. النقر المزدوج على protech_simple_working.py")
        print("3. فتح صفحة الإعدادات والاستمتاع بالتحسينات")
        
        print("\n🎉 صفحة الإعدادات محسنة بلطف وجاهزة!")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
