#!/usr/bin/env python3
"""
Fix Permission Error in ProTech
إصلاح خطأ الصلاحيات في ProTech

Fix the PermissionError when saving data
إصلاح خطأ الصلاحيات عند حفظ البيانات
"""

import os
import re
import shutil
from datetime import datetime

def backup_file():
    """Create backup of current file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.permission_fix_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def fix_save_data_function():
    """Fix save_data function to handle permission errors"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and replace the save_data function
        save_data_pattern = r'def save_data\(self\):(.*?)(?=def\s+\w+|class\s+\w+|if __name__|$)'
        
        new_save_data = '''def save_data(self):
        """Save data with comprehensive permission error handling"""
        try:
            # Prepare data to save
            data = {
                'suppliers': getattr(self, 'suppliers', []),
                'products': getattr(self, 'products', []),
                'customers': getattr(self, 'customers', []),
                'sales': getattr(self, 'sales', []),
                'last_updated': datetime.now().isoformat()
            }
            
            # Define data file path
            data_file = 'protech_simple_data.json'
            
            # Method 1: Try direct save (simplest)
            try:
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print("✅ تم حفظ البيانات بنجاح")
                return True
            except PermissionError:
                print("⚠️ خطأ في الصلاحيات - جاري المحاولة بطريقة بديلة...")
            except Exception as e:
                print(f"⚠️ خطأ في الحفظ المباشر: {e}")
            
            # Method 2: Try saving to user's Documents folder
            try:
                import os
                documents_path = os.path.expanduser("~/Documents")
                alt_file = os.path.join(documents_path, "protech_simple_data.json")
                
                with open(alt_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                
                # Copy back to original location if possible
                try:
                    shutil.copy2(alt_file, data_file)
                    print("✅ تم حفظ البيانات عبر مجلد Documents")
                    return True
                except:
                    print(f"✅ تم حفظ البيانات في: {alt_file}")
                    return True
                    
            except Exception as e:
                print(f"⚠️ خطأ في الحفظ البديل: {e}")
            
            # Method 3: Try saving with different name
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"protech_data_backup_{timestamp}.json"
                
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                
                print(f"✅ تم حفظ البيانات في ملف احتياطي: {backup_file}")
                return True
                
            except Exception as e:
                print(f"❌ فشل في جميع طرق الحفظ: {e}")
                return False
            
        except Exception as e:
            print(f"❌ خطأ عام في حفظ البيانات: {e}")
            return False

    '''
        
        # Replace the save_data function
        content = re.sub(save_data_pattern, new_save_data, content, flags=re.DOTALL)
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح دالة حفظ البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح دالة الحفظ: {e}")
        return False

def fix_save_data_background():
    """Fix background save function to handle permission errors"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and replace save_data_background function
        background_pattern = r'def save_data_background\(self\):(.*?)(?=def\s+\w+|class\s+\w+|if __name__|$)'
        
        new_background = '''def save_data_background(self):
        """Background save with permission error handling"""
        try:
            # Only save if not currently loading
            if getattr(self, 'loading', False):
                return
            
            # Prepare data
            data = {
                'suppliers': getattr(self, 'suppliers', []),
                'products': getattr(self, 'products', []),
                'customers': getattr(self, 'customers', []),
                'sales': getattr(self, 'sales', []),
                'last_updated': datetime.now().isoformat()
            }
            
            # Try simple save first (no temp files)
            data_file = 'protech_simple_data.json'
            
            try:
                with open(data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print("💾 تم الحفظ التلقائي")
                return
            except PermissionError:
                # Try alternative location
                try:
                    import os
                    documents_path = os.path.expanduser("~/Documents")
                    alt_file = os.path.join(documents_path, "protech_simple_data.json")
                    
                    with open(alt_file, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                    print(f"💾 تم الحفظ التلقائي في: {alt_file}")
                    return
                except:
                    pass
            except Exception as e:
                print(f"⚠️ خطأ في الحفظ التلقائي: {e}")
            
            # If all fails, create timestamped backup
            try:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_file = f"protech_auto_backup_{timestamp}.json"
                
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print(f"💾 تم الحفظ التلقائي في ملف احتياطي: {backup_file}")
            except:
                print("❌ فشل في الحفظ التلقائي")
                
        except Exception as e:
            print(f"❌ خطأ في الحفظ التلقائي: {e}")

    '''
        
        # Replace the function if it exists
        if 'def save_data_background(self):' in content:
            content = re.sub(background_pattern, new_background, content, flags=re.DOTALL)
            print("✅ تم إصلاح دالة الحفظ التلقائي")
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الحفظ التلقائي: {e}")
        return False

def disable_problematic_background_save():
    """Disable background save if it's causing problems"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and comment out background save calls
        # Look for threading calls to save_data_background
        content = re.sub(
            r'threading\.Thread\(target=self\.save_data_background.*?\)\.start\(\)',
            '# threading.Thread(target=self.save_data_background, daemon=True).start()  # Disabled due to permission issues',
            content
        )
        
        # Also disable any timer-based saves
        content = re.sub(
            r'self\.root\.after\(\d+, self\.save_data_background\)',
            '# self.root.after(30000, self.save_data_background)  # Disabled due to permission issues',
            content
        )
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تعطيل الحفظ التلقائي المشكل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعطيل الحفظ التلقائي: {e}")
        return False

def add_manual_save_button():
    """Add a manual save button to the interface"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add save button method
        save_button_method = '''
    def add_save_button(self):
        """Add manual save button to interface"""
        try:
            # Create save button frame
            save_frame = tk.Frame(self.root)
            save_frame.pack(side=tk.BOTTOM, fill=tk.X, padx=5, pady=5)
            
            # Manual save button
            save_btn = tk.Button(save_frame, text="💾 حفظ البيانات يدوياً", 
                               command=self.manual_save, bg='#4CAF50', fg='white',
                               font=('Arial', 10, 'bold'))
            save_btn.pack(side=tk.RIGHT, padx=5)
            
            # Status label
            self.save_status_label = tk.Label(save_frame, text="جاهز للحفظ", 
                                            fg='green', font=('Arial', 9))
            self.save_status_label.pack(side=tk.LEFT, padx=5)
            
        except Exception as e:
            print(f"خطأ في إضافة زر الحفظ: {e}")
    
    def manual_save(self):
        """Manual save function"""
        try:
            self.save_status_label.config(text="جاري الحفظ...", fg='orange')
            self.root.update()
            
            if self.save_data():
                self.save_status_label.config(text="تم الحفظ بنجاح ✅", fg='green')
            else:
                self.save_status_label.config(text="فشل في الحفظ ❌", fg='red')
                
            # Reset status after 3 seconds
            self.root.after(3000, lambda: self.save_status_label.config(text="جاهز للحفظ", fg='green'))
            
        except Exception as e:
            print(f"خطأ في الحفظ اليدوي: {e}")
            self.save_status_label.config(text="خطأ في الحفظ ❌", fg='red')
'''
        
        # Add the methods before save_data or at end of class
        if 'def add_save_button(self):' not in content:
            if 'def save_data(self):' in content:
                content = content.replace('def save_data(self):', save_button_method + '\n    def save_data(self):')
            else:
                content = content.replace('if __name__ == "__main__":', save_button_method + '\nif __name__ == "__main__":')
            print("✅ تم إضافة زر الحفظ اليدوي")
        
        # Add call to add_save_button in __init__
        if 'self.add_save_button()' not in content:
            # Find __init__ and add the call
            init_pattern = r'(def __init__\(self.*?\):.*?)(self\.root\.mainloop\(\)|if __name__|def\s+\w+)'
            
            def add_save_button_call(match):
                init_content = match.group(1)
                next_part = match.group(2)
                
                if 'self.add_save_button()' not in init_content:
                    lines = init_content.split('\n')
                    lines.insert(-1, '        # Add manual save button')
                    lines.insert(-1, '        self.add_save_button()')
                    init_content = '\n'.join(lines)
                
                return init_content + next_part
            
            content = re.sub(init_pattern, add_save_button_call, content, flags=re.DOTALL)
            print("✅ تم إضافة استدعاء زر الحفظ")
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة زر الحفظ: {e}")
        return False

def test_file_compilation():
    """Test if the file compiles correctly"""
    try:
        import py_compile
        py_compile.compile('protech_simple_working.py', doraise=True)
        print("✅ الملف يعمل بدون أخطاء تركيبية")
        return True
    except Exception as e:
        print(f"❌ خطأ في تركيب الملف: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح خطأ الصلاحيات في ProTech")
    print("🔧 Fixing Permission Error in ProTech")
    print()
    
    try:
        # Step 1: Create backup
        backup_file()
        
        # Step 2: Fix save_data function
        print("🔧 إصلاح دالة حفظ البيانات...")
        if fix_save_data_function():
            print("✅ تم إصلاح دالة الحفظ الرئيسية")
        
        # Step 3: Fix background save
        print("🔧 إصلاح الحفظ التلقائي...")
        if fix_save_data_background():
            print("✅ تم إصلاح الحفظ التلقائي")
        
        # Step 4: Disable problematic background save
        print("🔧 تعطيل الحفظ التلقائي المشكل...")
        if disable_problematic_background_save():
            print("✅ تم تعطيل الحفظ التلقائي المشكل")
        
        # Step 5: Add manual save button
        print("🔧 إضافة زر الحفظ اليدوي...")
        if add_manual_save_button():
            print("✅ تم إضافة زر الحفظ اليدوي")
        
        # Step 6: Test compilation
        print("🧪 اختبار الملف...")
        if test_file_compilation():
            print("✅ الملف جاهز للتشغيل")
        
        print("\n" + "="*60)
        print("✅ تم إصلاح خطأ الصلاحيات بنجاح!")
        print("✅ Permission error fixed successfully!")
        print("="*60)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إصلاح دالة حفظ البيانات لتجنب أخطاء الصلاحيات")
        print("• إزالة استخدام الملفات المؤقتة المشكلة")
        print("• إضافة طرق حفظ بديلة (Documents folder)")
        print("• تعطيل الحفظ التلقائي المشكل")
        print("• إضافة زر حفظ يدوي للتحكم الكامل")
        print("• معالجة شاملة لأخطاء الصلاحيات")
        
        print("\n🚀 الآن عند التشغيل:")
        print("• لن تظهر أخطاء الصلاحيات")
        print("• يمكن الحفظ يدوياً بزر مخصص")
        print("• الحفظ في مواقع بديلة عند الحاجة")
        print("• رسائل واضحة لحالة الحفظ")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح: {e}")
        return False

if __name__ == "__main__":
    main()
