
import os
import tempfile

class AlternativePathManager:
    """Manage alternative file paths"""
    
    def __init__(self):
        self.paths = [
            os.getcwd(),
            os.path.expanduser("~/Documents"),
            os.path.expanduser("~/Desktop"),
            tempfile.gettempdir()
        ]
    
    def get_writable_path(self, filename):
        """Get first writable path"""
        for path in self.paths:
            try:
                test_file = os.path.join(path, f"test_{filename}")
                with open(test_file, 'w') as f:
                    f.write("test")
                os.remove(test_file)
                return path
            except:
                continue
        return tempfile.gettempdir()
    
    def save_with_alternatives(self, data, filename):
        """Save using alternative paths"""
        import json
        
        for path in self.paths:
            try:
                full_path = os.path.join(path, filename)
                with open(full_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                print(f"✅ حفظ في: {full_path}")
                return full_path
            except:
                continue
        
        raise Exception("فشل في الحفظ في جميع المسارات")

# Global instance
alt_manager = AlternativePathManager()
