#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Console Encoding for ProTech
إصلاح ترميز وحدة التحكم لـ ProTech

Fix console encoding issues with emojis and Arabic text
إصلاح مشاكل ترميز وحدة التحكم مع الإيموجي والنص العربي
"""

import os
import re
import shutil
from datetime import datetime

def fix_console_encoding():
    """Fix console encoding issues in ProTech"""
    try:
        print("🔧 إصلاح مشاكل ترميز وحدة التحكم...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        code_file = "protech_simple_working.py"
        code_path = os.path.join(data_dir, code_file)
        
        if not os.path.exists(code_path):
            print("❌ ملف البرنامج غير موجود!")
            return False
        
        # Create backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{code_path}.console_encoding_fix_{timestamp}"
        shutil.copy2(code_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}")
        
        # Read current code
        with open(code_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix console encoding issues
        fixes_applied = 0
        
        # Fix 1: Add console encoding setup at the beginning
        encoding_setup = '''
# Console encoding setup for Windows
import sys
import os

# Set console encoding to UTF-8
if sys.platform == 'win32':
    try:
        # Try to set console to UTF-8
        os.system('chcp 65001 > nul 2>&1')
        
        # Set environment variables for UTF-8
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # Reconfigure stdout and stderr
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
        if hasattr(sys.stderr, 'reconfigure'):
            sys.stderr.reconfigure(encoding='utf-8', errors='replace')
            
    except Exception:
        # Fallback: use safe printing
        pass

def safe_print(*args, **kwargs):
    """Safe print function that handles encoding issues"""
    try:
        print(*args, **kwargs)
    except UnicodeEncodeError:
        # Remove emojis and problematic characters
        safe_args = []
        for arg in args:
            if isinstance(arg, str):
                # Remove emojis and keep only basic characters
                safe_arg = re.sub(r'[^\\x00-\\x7F\\u0600-\\u06FF\\u0750-\\u077F\\u08A0-\\u08FF\\uFB50-\\uFDFF\\uFE70-\\uFEFF]', '', str(arg))
                safe_args.append(safe_arg)
            else:
                safe_args.append(str(arg))
        print(*safe_args, **kwargs)

'''
        
        # Insert encoding setup after imports
        import_end = content.find('class ProTechApp')
        if import_end != -1:
            content = content[:import_end] + encoding_setup + content[import_end:]
            fixes_applied += 1
        
        # Fix 2: Replace problematic print statements
        problematic_patterns = [
            (r'print\("🚀', 'safe_print("تشغيل'),
            (r'print\("❌', 'safe_print("خطأ:'),
            (r'print\("✅', 'safe_print("نجح:'),
            (r'print\("⚠️', 'safe_print("تحذير:'),
            (r'print\("📊', 'safe_print("إحصائيات:'),
            (r'print\("💾', 'safe_print("حفظ:'),
            (r'print\("🔧', 'safe_print("إصلاح:'),
            (r'print\("🎉', 'safe_print("مبروك:'),
            (r'print\(f"🚀', 'safe_print(f"تشغيل:'),
            (r'print\(f"❌', 'safe_print(f"خطأ:'),
            (r'print\(f"✅', 'safe_print(f"نجح:'),
            (r'print\(f"⚠️', 'safe_print(f"تحذير:'),
        ]
        
        for pattern, replacement in problematic_patterns:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                fixes_applied += 1
        
        # Fix 3: Replace emoji characters in strings
        emoji_replacements = [
            ('🚀', 'تشغيل'),
            ('❌', 'خطأ'),
            ('✅', 'نجح'),
            ('⚠️', 'تحذير'),
            ('📊', 'إحصائيات'),
            ('💾', 'حفظ'),
            ('🔧', 'إصلاح'),
            ('🎉', 'مبروك'),
            ('📁', 'مجلد'),
            ('📄', 'ملف'),
            ('🔍', 'بحث'),
            ('⚡', 'سريع'),
            ('🛡️', 'أمان'),
            ('💡', 'فكرة'),
        ]
        
        for emoji, replacement in emoji_replacements:
            if emoji in content:
                content = content.replace(emoji, replacement)
                fixes_applied += 1
        
        # Fix 4: Add safe main function
        main_function_fix = '''
def main():
    """Main function with safe console handling"""
    try:
        safe_print("بدء تشغيل نظام ProTech البسيط للمحاسبة")
        safe_print("Starting ProTech Simple Accounting System")
        
        # Create and run the application
        app = ProTechApp()
        app.run()
        
    except KeyboardInterrupt:
        safe_print("تم إيقاف البرنامج بواسطة المستخدم")
        safe_print("Program stopped by user")
    except Exception as e:
        safe_print(f"خطأ في بدء التشغيل: {e}")
        safe_print(f"Startup error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            # Cleanup
            pass
        except:
            pass

if __name__ == "__main__":
    main()
'''
        
        # Replace the main function
        main_pattern = r'def main\(\):.*?if __name__ == "__main__":.*?main\(\)'
        content = re.sub(main_pattern, main_function_fix.strip(), content, flags=re.DOTALL)
        fixes_applied += 1
        
        # Write fixed content
        with open(code_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم تطبيق {fixes_applied} إصلاح لترميز وحدة التحكم")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح ترميز وحدة التحكم: {e}")
        return False

def test_console_encoding():
    """Test console encoding after fix"""
    try:
        print("🧪 اختبار ترميز وحدة التحكم...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        code_file = "protech_simple_working.py"
        code_path = os.path.join(data_dir, code_file)
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', code_path], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            
            # Test basic execution
            test_code = f'''
import sys
import os
sys.path.append(r"{data_dir}")

# Set UTF-8 encoding
os.environ['PYTHONIOENCODING'] = 'utf-8'

try:
    # Test safe_print function
    exec(open(r"{code_path}").read())
    print("Test: Console encoding fix successful")
except Exception as e:
    print(f"Test failed: {{e}}")
'''
            
            test_result = subprocess.run([sys.executable, '-c', test_code], 
                                       capture_output=True, text=True, 
                                       env={**os.environ, 'PYTHONIOENCODING': 'utf-8'})
            
            if test_result.returncode == 0:
                print("✅ اختبار ترميز وحدة التحكم: نجح")
                return True
            else:
                print(f"❌ اختبار ترميز وحدة التحكم: فشل")
                if test_result.stderr:
                    print(f"الخطأ: {test_result.stderr}")
                return False
        else:
            print(f"❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار ترميز وحدة التحكم: {e}")
        return False

def create_safe_launcher():
    """Create safe launcher with proper encoding"""
    try:
        print("🚀 إنشاء مشغل آمن...")
        
        launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Safe Launcher with Console Encoding Fix
مشغل ProTech الآمن مع إصلاح ترميز وحدة التحكم
"""

import os
import sys
import subprocess

def setup_console_encoding():
    """Setup console encoding for Windows"""
    if sys.platform == 'win32':
        try:
            # Set console to UTF-8
            os.system('chcp 65001 > nul 2>&1')
            
            # Set environment variables
            os.environ['PYTHONIOENCODING'] = 'utf-8'
            os.environ['PYTHONUTF8'] = '1'
            
            # Reconfigure stdout and stderr
            if hasattr(sys.stdout, 'reconfigure'):
                sys.stdout.reconfigure(encoding='utf-8', errors='replace')
            if hasattr(sys.stderr, 'reconfigure'):
                sys.stderr.reconfigure(encoding='utf-8', errors='replace')
                
        except Exception:
            pass

def main():
    """Main launcher function"""
    try:
        print("تشغيل ProTech مع إصلاح ترميز وحدة التحكم...")
        print("Starting ProTech with console encoding fix...")
        
        # Setup encoding
        setup_console_encoding()
        
        # Check if ProTech file exists
        if not os.path.exists('protech_simple_working.py'):
            print("خطأ: ملف ProTech غير موجود!")
            print("Error: ProTech file not found!")
            input("اضغط Enter للخروج...")
            return
        
        # Test compilation first
        print("اختبار التجميع...")
        test_result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                                   capture_output=True, text=True)
        
        if test_result.returncode != 0:
            print("خطأ في التجميع:")
            print("Compilation error:")
            print(test_result.stderr)
            input("اضغط Enter للخروج...")
            return
        
        print("نجح التجميع!")
        print("Compilation successful!")
        
        # Launch ProTech
        print("تشغيل ProTech...")
        print("Launching ProTech...")
        
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'
        
        subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)
        
        print("تم تشغيل ProTech بنجاح!")
        print("ProTech launched successfully!")
        
    except Exception as e:
        print(f"خطأ في التشغيل: {e}")
        print(f"Launch error: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        launcher_path = os.path.join(data_dir, "launch_protech_safe_encoding.py")
        
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        print(f"✅ تم إنشاء المشغل الآمن: launch_protech_safe_encoding.py")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المشغل الآمن: {e}")
        return False

def main():
    """Main console encoding fix function"""
    print("🔧 مصلح ترميز وحدة التحكم لـ ProTech")
    print("🔧 ProTech Console Encoding Fixer")
    print("="*50)
    
    try:
        # Fix console encoding
        if fix_console_encoding():
            print("\n✅ تم إصلاح ترميز وحدة التحكم بنجاح!")
            
            # Test the fix
            if test_console_encoding():
                print("✅ اختبار الإصلاح نجح")
            
            # Create safe launcher
            if create_safe_launcher():
                print("✅ تم إنشاء مشغل آمن")
            
            print("\n🎉 إصلاح ترميز وحدة التحكم مكتمل!")
            print("🎉 Console encoding fix completed!")
            
            return True
        else:
            print("\n❌ فشل في إصلاح ترميز وحدة التحكم")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في إصلاح ترميز وحدة التحكم: {e}")
        return False

if __name__ == "__main__":
    main()
