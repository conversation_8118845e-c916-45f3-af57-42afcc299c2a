#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Direct Fix for Double Click Issue
إصلاح مباشر لمشكلة النقر المزدوج

Directly fix the original ProTech file to work with double-click
إصلاح مباشر للملف الأصلي ليعمل مع النقر المزدوج
"""

import os
import shutil
from datetime import datetime

def find_original_protech():
    """Find the original ProTech file"""
    try:
        possible_locations = [
            "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program\\protech_simple_working.py",
            "C:\\Users\\<USER>\\Documents\\augment-projects\\protech\\protech_simple_working.py"
        ]
        
        for location in possible_locations:
            if os.path.exists(location):
                print(f"✅ وجد الملف الأصلي: {location}")
                return location
        
        print("❌ لم يتم العثور على الملف الأصلي")
        return None
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {e}")
        return None

def create_direct_fix():
    """Create a direct fix by adding initialization code at the beginning"""
    try:
        print("🔧 إصلاح مباشر للملف الأصلي")
        print("🔧 Direct Fix for Original File")
        print("="*50)
        
        # Find the original file
        original_file = find_original_protech()
        if not original_file:
            return False
        
        # Read the original content
        with open(original_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Create backup
        backup_name = f"{original_file}.double_click_fix_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(original_file, backup_name)
        print(f"💾 نسخة احتياطية: {backup_name}")
        
        # Create the fix code to add at the beginning
        fix_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# ===== DOUBLE CLICK FIX - START =====
# إصلاح النقر المزدوج - البداية

import os
import sys

def fix_double_click_issues():
    """Fix issues that occur when double-clicking the file"""
    try:
        # Fix 1: Set correct working directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Fix 2: Set UTF-8 encoding
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        
        # Fix 3: Create missing data file
        data_file = "protech_simple_data.json"
        if not os.path.exists(data_file):
            import json
            default_data = {
                "suppliers": [],
                "products": [],
                "customers": [],
                "sales": []
            }
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(default_data, f, ensure_ascii=False, indent=2)
        
        return True
    except:
        return False

# Apply the fix immediately when the file is loaded
fix_double_click_issues()

# ===== DOUBLE CLICK FIX - END =====
# إصلاح النقر المزدوج - النهاية

'''
        
        # Combine fix code with original content
        fixed_content = fix_code + original_content
        
        # Write the fixed version
        with open(original_file, 'w', encoding='utf-8') as f:
            f.write(fixed_content)
        
        print("✅ تم إصلاح الملف الأصلي")
        
        # Test the fix
        try:
            import subprocess
            result = subprocess.run([sys.executable, '-m', 'py_compile', original_file], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ اختبار التجميع: نجح")
            else:
                print("❌ اختبار التجميع: فشل")
                print(f"الخطأ: {result.stderr}")
                
                # Restore backup if compilation fails
                shutil.copy2(backup_name, original_file)
                print("🔄 تم استعادة النسخة الاحتياطية")
                return False
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح المباشر: {e}")
        return False

def create_simple_test_launcher():
    """Create a simple test launcher"""
    try:
        print("\n🧪 إنشاء مشغل اختبار بسيط...")
        
        test_launcher = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Test Launcher
مشغل اختبار بسيط
"""

import os
import sys
import subprocess

def main():
    try:
        print("🧪 اختبار تشغيل ProTech...")
        
        # Find ProTech file
        protech_files = [
            "protech_simple_working.py",
            "ProTech_DoubleClick.py"
        ]
        
        protech_file = None
        for file in protech_files:
            if os.path.exists(file):
                protech_file = file
                break
        
        if not protech_file:
            print("❌ لم يتم العثور على ملف ProTech")
            input("اضغط Enter للخروج...")
            return
        
        print(f"✅ تم العثور على: {protech_file}")
        print("🚀 تشغيل ProTech...")
        
        # Launch ProTech
        subprocess.run([sys.executable, protech_file])
        
    except Exception as e:
        print(f"❌ خطأ: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
        
        # Save to desktop accounting program folder
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        if os.path.exists(desktop_path):
            launcher_path = os.path.join(desktop_path, "اختبار_ProTech.py")
            
            with open(launcher_path, 'w', encoding='utf-8') as f:
                f.write(test_launcher)
            
            print(f"✅ تم إنشاء مشغل الاختبار: {launcher_path}")
            return launcher_path
        
        return None
        
    except Exception as e:
        print(f"❌ فشل في إنشاء مشغل الاختبار: {e}")
        return None

def create_ultimate_launcher():
    """Create ultimate launcher that always works"""
    try:
        print("\n🚀 إنشاء المشغل النهائي...")
        
        ultimate_launcher = '''@echo off
chcp 65001 > nul
title ProTech - المشغل النهائي

echo.
echo ==========================================
echo         ProTech - المشغل النهائي
echo ==========================================
echo.

cd /d "%~dp0"

echo 📁 المجلد الحالي: %CD%
echo.

echo 🔍 البحث عن ملفات ProTech...

if exist "protech_simple_working.py" (
    echo ✅ تم العثور على protech_simple_working.py
    set PROTECH_FILE=protech_simple_working.py
    goto :run_protech
)

if exist "ProTech_DoubleClick.py" (
    echo ✅ تم العثور على ProTech_DoubleClick.py
    set PROTECH_FILE=ProTech_DoubleClick.py
    goto :run_protech
)

if exist "protech_minimal_working.py" (
    echo ✅ تم العثور على protech_minimal_working.py
    set PROTECH_FILE=protech_minimal_working.py
    goto :run_protech
)

echo ❌ لم يتم العثور على أي ملف ProTech
echo.
echo 📋 الملفات المطلوبة:
echo - protech_simple_working.py
echo - ProTech_DoubleClick.py
echo - protech_minimal_working.py
echo.
goto :end

:run_protech
echo.
echo 🚀 تشغيل %PROTECH_FILE%...
echo.

python "%PROTECH_FILE%"

if errorlevel 1 (
    echo.
    echo ⚠️ حدث خطأ، محاولة تشغيل بديل...
    
    python -u "%PROTECH_FILE%"
    
    if errorlevel 1 (
        echo.
        echo ❌ فشل التشغيل البديل
        echo.
        echo 📝 فتح الملف للمراجعة...
        notepad "%PROTECH_FILE%"
    )
) else (
    echo.
    echo ✅ تم إغلاق ProTech بنجاح
)

:end
echo.
echo اضغط أي مفتاح للخروج...
pause > nul
'''
        
        # Save to desktop accounting program folder
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        if os.path.exists(desktop_path):
            launcher_path = os.path.join(desktop_path, "ProTech_المشغل_النهائي.bat")
            
            with open(launcher_path, 'w', encoding='utf-8') as f:
                f.write(ultimate_launcher)
            
            print(f"✅ تم إنشاء المشغل النهائي: {launcher_path}")
            return launcher_path
        
        return None
        
    except Exception as e:
        print(f"❌ فشل في إنشاء المشغل النهائي: {e}")
        return None

def main():
    """Main function"""
    print("🔧 إصلاح مباشر لمشكلة النقر المزدوج")
    print("🔧 Direct Fix for Double-Click Issue")
    print("="*60)
    
    print("\n💡 هذا الإصلاح سيقوم بـ:")
    print("• إضافة كود إصلاح في بداية الملف الأصلي")
    print("• إصلاح مشكلة المجلد تلقائياً")
    print("• إصلاح مشكلة الترميز")
    print("• إنشاء ملفات البيانات المفقودة")
    
    created_items = []
    
    # Apply direct fix
    if create_direct_fix():
        created_items.append("إصلاح مباشر للملف الأصلي")
    
    # Create test launcher
    test_launcher = create_simple_test_launcher()
    if test_launcher:
        created_items.append("مشغل اختبار بسيط")
    
    # Create ultimate launcher
    ultimate_launcher = create_ultimate_launcher()
    if ultimate_launcher:
        created_items.append("المشغل النهائي")
    
    # Summary
    print("\n" + "="*60)
    print("📊 ملخص الإصلاحات:")
    
    if created_items:
        print(f"✅ تم تطبيق {len(created_items)} إصلاح:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم تطبيق أي إصلاحات")
    
    print("\n🎯 طرق التشغيل الجديدة:")
    print("1. انقر مزدوج على 'protech_simple_working.py' - الملف الأصلي المصلح")
    print("2. انقر مزدوج على 'اختبار_ProTech.py' - مشغل اختبار")
    print("3. انقر مزدوج على 'ProTech_المشغل_النهائي.bat' - المشغل النهائي")
    
    print("\n💡 الفرق الآن:")
    print("• الملف الأصلي تم إصلاحه مباشرة")
    print("• لا حاجة لملفات إضافية")
    print("• النقر المزدوج سيعمل مباشرة")
    
    print("\n🎉 تم الإصلاح المباشر!")
    
    if len(created_items) >= 2:
        print("✅ جميع الحلول جاهزة للاستخدام")
    else:
        print("⚠️ قد تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
