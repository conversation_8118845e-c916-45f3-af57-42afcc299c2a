#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Reports Button
إصلاح زر التقارير

Fix the reports button to show Table Reports and use the correct method
إصلاح زر التقارير لإظهار Table Reports واستخدام الطريقة الصحيحة
"""

import os
import shutil
from datetime import datetime

def fix_reports_button():
    """إصلاح زر التقارير"""
    try:
        print("🔧 إصلاح زر التقارير")
        print("🔧 Fixing Reports Button")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.fix_button_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and replace the reports button
        old_patterns = [
            'btn5 = tk.Button(sidebar, text="التقارير", font=("Arial", 12), bg=\'#9b59b6\', fg=\'white\', width=18, height=2, command=self.show_reports)',
            'btn5 = tk.Button(sidebar, text="التقارير"',
            'command=self.show_reports)'
        ]
        
        # Check what's currently in the file
        if 'btn5 = tk.Button(sidebar, text="التقارير"' in content:
            # Find the complete button definition
            start_pos = content.find('btn5 = tk.Button(sidebar, text="التقارير"')
            if start_pos != -1:
                # Find the end of this button definition
                end_pos = content.find(')', start_pos)
                if end_pos != -1:
                    end_pos = content.find('\n', end_pos)
                    if end_pos != -1:
                        old_button = content[start_pos:end_pos]
                        new_button = 'btn5 = tk.Button(sidebar, text="Table Reports", font=("Arial", 12), bg=\'#9b59b6\', fg=\'white\', width=18, height=2, command=self.create_reports_table_page)'
                        
                        content = content.replace(old_button, new_button)
                        print("✅ تم تحديث زر التقارير")
                    else:
                        print("❌ لم يتم العثور على نهاية تعريف الزر")
                        return False
                else:
                    print("❌ لم يتم العثور على نهاية تعريف الزر")
                    return False
            else:
                print("❌ لم يتم العثور على زر التقارير")
                return False
        else:
            print("❌ لم يتم العثور على زر التقارير بالنص المتوقع")
            # Let's search for any btn5 definition
            if 'btn5 = tk.Button(' in content:
                print("🔍 وجد btn5 بتعريف مختلف")
                # Find all btn5 definitions
                import re
                btn5_matches = re.finditer(r'btn5 = tk\.Button\([^)]+\)', content)
                for match in btn5_matches:
                    print(f"Found: {match.group()}")
                    if 'التقارير' in match.group() or 'Reports' in match.group():
                        old_button = match.group()
                        new_button = 'btn5 = tk.Button(sidebar, text="Table Reports", font=("Arial", 12), bg=\'#9b59b6\', fg=\'white\', width=18, height=2, command=self.create_reports_table_page)'
                        content = content.replace(old_button, new_button)
                        print("✅ تم تحديث زر التقارير")
                        break
                else:
                    print("❌ لم يتم العثور على زر التقارير المناسب")
                    return False
            else:
                print("❌ لم يتم العثور على btn5")
                return False
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح زر التقارير")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح زر التقارير: {e}")
        return False

def verify_changes():
    """التحقق من التغييرات"""
    try:
        print("\n🔍 التحقق من التغييرات")
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for the new button text
        if 'Table Reports' in content:
            print("✅ النص 'Table Reports' موجود")
        else:
            print("❌ النص 'Table Reports' غير موجود")
        
        # Check for the new method call
        if 'create_reports_table_page' in content:
            print("✅ الطريقة 'create_reports_table_page' موجودة")
        else:
            print("❌ الطريقة 'create_reports_table_page' غير موجودة")
        
        # Check for the method definition
        if 'def create_reports_table_page(self):' in content:
            print("✅ تعريف الطريقة 'create_reports_table_page' موجود")
        else:
            print("❌ تعريف الطريقة 'create_reports_table_page' غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح زر التقارير في ProTech")
    print("🔧 Fixing Reports Button in ProTech")
    print("="*50)
    
    if fix_reports_button():
        print("\n🎉 تم إصلاح زر التقارير بنجاح!")
        
        # Verify changes
        verify_changes()
        
        print("\n📊 الآن يجب أن يظهر:")
        print("• زر 'Table Reports' في الشريط الجانبي")
        print("• صفحة التقارير الجديدة عند النقر على الزر")
        print("• 5 تقارير مختلفة في جداول")
        print("• إمكانية التصدير")
        
        print("\n🎯 كيفية الاختبار:")
        print("1. افتح برنامج ProTech")
        print("2. ابحث عن زر 'Table Reports' في الشريط الجانبي")
        print("3. انقر على الزر")
        print("4. جرب الأزرار المختلفة للتقارير")
        
    else:
        print("\n❌ فشل في إصلاح زر التقارير")
    
    print("\n🔧 تم الانتهاء من إصلاح الزر")

if __name__ == "__main__":
    main()
