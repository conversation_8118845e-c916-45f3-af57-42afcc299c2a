#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

def test_data_loading():
    """Test data loading functionality"""
    print("🔍 Testing data loading...")
    
    data_file = "protech_simple_data.json"
    print(f"📁 Data file: {data_file}")
    print(f"📁 File exists: {os.path.exists(data_file)}")
    
    if os.path.exists(data_file):
        print(f"📊 File size: {os.path.getsize(data_file)} bytes")
        
        try:
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            print(f"✅ JSON loaded successfully")
            print(f"🔍 Data keys: {list(data.keys())}")
            
            suppliers = data.get('suppliers', [])
            products = data.get('products', [])
            customers = data.get('customers', [])
            sales = data.get('sales', [])
            
            print(f"📊 Data counts:")
            print(f"  - Suppliers: {len(suppliers)}")
            print(f"  - Products: {len(products)}")
            print(f"  - Customers: {len(customers)}")
            print(f"  - Sales: {len(sales)}")
            
            if len(products) > 0:
                print(f"🔍 First product: {products[0].get('name', 'NO_NAME')}")
                print(f"🔍 Product details: {products[0]}")
            
            if len(customers) > 0:
                print(f"🔍 First customer: {customers[0].get('name', 'NO_NAME')}")
            
            print(f"🕒 Last updated: {data.get('last_updated', 'UNKNOWN')}")
            
        except Exception as e:
            print(f"❌ Error loading JSON: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("❌ Data file not found")

if __name__ == "__main__":
    test_data_loading()
