import { db } from './db';
import { Prisma } from '@prisma/client';
import { 
  Product, 
  Customer, 
  Supplier, 
  Invoice, 
  Purchase, 
  PaginatedResponse,
  ApiResponse 
} from '@/types';

// Generic pagination function
export async function paginate<T>(
  model: any,
  page: number = 1,
  limit: number = 10,
  where?: any,
  include?: any,
  orderBy?: any
): Promise<PaginatedResponse<T>> {
  const skip = (page - 1) * limit;
  
  const [data, total] = await Promise.all([
    model.findMany({
      where,
      include,
      orderBy,
      skip,
      take: limit,
    }),
    model.count({ where }),
  ]);

  const totalPages = Math.ceil(total / limit);

  return {
    data,
    total,
    page,
    limit,
    totalPages,
  };
}

// Product operations
export const productOperations = {
  async getAll(page: number = 1, limit: number = 10, search?: string) {
    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { code: { contains: search, mode: 'insensitive' as const } },
            { barcode: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : undefined;

    return paginate<Product>(
      db.product,
      page,
      limit,
      where,
      {
        category: true,
        supplier: true,
      },
      { name: 'asc' }
    );
  },

  async getById(id: string) {
    return db.product.findUnique({
      where: { id },
      include: {
        category: true,
        supplier: true,
        variants: true,
      },
    });
  },

  async getByBarcode(barcode: string) {
    return db.product.findUnique({
      where: { barcode },
      include: {
        category: true,
        supplier: true,
      },
    });
  },

  async create(data: Prisma.ProductCreateInput) {
    return db.product.create({
      data,
      include: {
        category: true,
        supplier: true,
      },
    });
  },

  async update(id: string, data: Prisma.ProductUpdateInput) {
    return db.product.update({
      where: { id },
      data,
      include: {
        category: true,
        supplier: true,
      },
    });
  },

  async delete(id: string) {
    return db.product.delete({
      where: { id },
    });
  },

  async getLowStock(threshold?: number) {
    const lowStockThreshold = threshold || 10;
    return db.product.findMany({
      where: {
        AND: [
          { trackInventory: true },
          { currentStock: { lte: lowStockThreshold } },
          { isActive: true },
        ],
      },
      include: {
        category: true,
        supplier: true,
      },
      orderBy: { currentStock: 'asc' },
    });
  },

  async updateStock(id: string, quantity: number, type: 'IN' | 'OUT' | 'ADJUSTMENT') {
    const product = await db.product.findUnique({ where: { id } });
    if (!product) throw new Error('Product not found');

    let newStock = product.currentStock;
    
    switch (type) {
      case 'IN':
        newStock += quantity;
        break;
      case 'OUT':
        newStock -= quantity;
        break;
      case 'ADJUSTMENT':
        newStock = quantity;
        break;
    }

    if (newStock < 0 && !product.allowNegative) {
      throw new Error('Insufficient stock');
    }

    return db.product.update({
      where: { id },
      data: { currentStock: newStock },
    });
  },
};

// Customer operations
export const customerOperations = {
  async getAll(page: number = 1, limit: number = 10, search?: string) {
    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { code: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : undefined;

    return paginate<Customer>(
      db.customer,
      page,
      limit,
      where,
      undefined,
      { name: 'asc' }
    );
  },

  async getById(id: string) {
    return db.customer.findUnique({
      where: { id },
      include: {
        invoices: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        payments: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
      },
    });
  },

  async create(data: Prisma.CustomerCreateInput) {
    return db.customer.create({ data });
  },

  async update(id: string, data: Prisma.CustomerUpdateInput) {
    return db.customer.update({
      where: { id },
      data,
    });
  },

  async delete(id: string) {
    return db.customer.delete({
      where: { id },
    });
  },

  async updateBalance(id: string, amount: number) {
    return db.customer.update({
      where: { id },
      data: {
        currentBalance: {
          increment: amount,
        },
      },
    });
  },
};

// Supplier operations
export const supplierOperations = {
  async getAll(page: number = 1, limit: number = 10, search?: string) {
    const where = search
      ? {
          OR: [
            { name: { contains: search, mode: 'insensitive' as const } },
            { code: { contains: search, mode: 'insensitive' as const } },
            { email: { contains: search, mode: 'insensitive' as const } },
          ],
        }
      : undefined;

    return paginate<Supplier>(
      db.supplier,
      page,
      limit,
      where,
      undefined,
      { name: 'asc' }
    );
  },

  async getById(id: string) {
    return db.supplier.findUnique({
      where: { id },
      include: {
        purchases: {
          take: 10,
          orderBy: { createdAt: 'desc' },
        },
        products: {
          take: 10,
          orderBy: { name: 'asc' },
        },
      },
    });
  },

  async create(data: Prisma.SupplierCreateInput) {
    return db.supplier.create({ data });
  },

  async update(id: string, data: Prisma.SupplierUpdateInput) {
    return db.supplier.update({
      where: { id },
      data,
    });
  },

  async delete(id: string) {
    return db.supplier.delete({
      where: { id },
    });
  },
};

// Invoice operations
export const invoiceOperations = {
  async getAll(page: number = 1, limit: number = 10, search?: string) {
    const where = search
      ? {
          OR: [
            { number: { contains: search, mode: 'insensitive' as const } },
            { customer: { name: { contains: search, mode: 'insensitive' as const } } },
          ],
        }
      : undefined;

    return paginate<Invoice>(
      db.invoice,
      page,
      limit,
      where,
      {
        customer: true,
        createdBy: true,
        items: {
          include: {
            product: true,
          },
        },
      },
      { createdAt: 'desc' }
    );
  },

  async getById(id: string) {
    return db.invoice.findUnique({
      where: { id },
      include: {
        customer: true,
        createdBy: true,
        items: {
          include: {
            product: true,
          },
        },
        payments: true,
      },
    });
  },

  async create(data: Prisma.InvoiceCreateInput) {
    return db.invoice.create({
      data,
      include: {
        customer: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });
  },

  async update(id: string, data: Prisma.InvoiceUpdateInput) {
    return db.invoice.update({
      where: { id },
      data,
      include: {
        customer: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });
  },

  async delete(id: string) {
    return db.invoice.delete({
      where: { id },
    });
  },

  async generateNumber(): Promise<string> {
    const prefix = 'INV';
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    const lastInvoice = await db.invoice.findFirst({
      where: {
        number: {
          startsWith: `${prefix}-${year}${month}`,
        },
      },
      orderBy: {
        number: 'desc',
      },
    });

    let sequence = 1;
    if (lastInvoice) {
      const lastSequence = parseInt(lastInvoice.number.split('-').pop() || '0');
      sequence = lastSequence + 1;
    }

    return `${prefix}-${year}${month}-${String(sequence).padStart(4, '0')}`;
  },
};

// Purchase operations
export const purchaseOperations = {
  async getAll(page: number = 1, limit: number = 10, search?: string) {
    const where = search
      ? {
          OR: [
            { number: { contains: search, mode: 'insensitive' as const } },
            { supplier: { name: { contains: search, mode: 'insensitive' as const } } },
          ],
        }
      : undefined;

    return paginate<Purchase>(
      db.purchase,
      page,
      limit,
      where,
      {
        supplier: true,
        createdBy: true,
        items: {
          include: {
            product: true,
          },
        },
      },
      { createdAt: 'desc' }
    );
  },

  async getById(id: string) {
    return db.purchase.findUnique({
      where: { id },
      include: {
        supplier: true,
        createdBy: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });
  },

  async create(data: Prisma.PurchaseCreateInput) {
    return db.purchase.create({
      data,
      include: {
        supplier: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });
  },

  async update(id: string, data: Prisma.PurchaseUpdateInput) {
    return db.purchase.update({
      where: { id },
      data,
      include: {
        supplier: true,
        items: {
          include: {
            product: true,
          },
        },
      },
    });
  },

  async delete(id: string) {
    return db.purchase.delete({
      where: { id },
    });
  },

  async generateNumber(): Promise<string> {
    const prefix = 'PO';
    const year = new Date().getFullYear();
    const month = String(new Date().getMonth() + 1).padStart(2, '0');
    
    const lastPurchase = await db.purchase.findFirst({
      where: {
        number: {
          startsWith: `${prefix}-${year}${month}`,
        },
      },
      orderBy: {
        number: 'desc',
      },
    });

    let sequence = 1;
    if (lastPurchase) {
      const lastSequence = parseInt(lastPurchase.number.split('-').pop() || '0');
      sequence = lastSequence + 1;
    }

    return `${prefix}-${year}${month}-${String(sequence).padStart(4, '0')}`;
  },
};

// Settings operations
export const settingsOperations = {
  async get(key: string) {
    const setting = await db.setting.findUnique({
      where: { key },
    });
    return setting?.value;
  },

  async set(key: string, value: string, category: string = 'general') {
    return db.setting.upsert({
      where: { key },
      update: { value },
      create: { key, value, category },
    });
  },

  async getByCategory(category: string) {
    return db.setting.findMany({
      where: { category },
    });
  },

  async getAll() {
    return db.setting.findMany({
      orderBy: [{ category: 'asc' }, { key: 'asc' }],
    });
  },
};
