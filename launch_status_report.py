#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Launch Status Report
تقرير حالة تشغيل ProTech

Generate status report for ProTech launch from original location
إنشاء تقرير حالة لتشغيل ProTech من المكان الأصلي
"""

import os
import json
import subprocess
import sys
from datetime import datetime

def generate_launch_status_report():
    """Generate launch status report"""
    try:
        print("🚀 تقرير حالة تشغيل ProTech")
        print("🚀 ProTech Launch Status Report")
        print("="*50)
        
        # Check current directory
        current_dir = os.getcwd()
        print(f"📁 المجلد الحالي: {current_dir}")
        
        # Check if we're in the right location
        expected_dir = "C:\\Users\\<USER>\\Documents\\augment-projects\\protech"
        if current_dir == expected_dir:
            print("✅ المكان صحيح - المجلد الأصلي")
        else:
            print(f"⚠️ المكان مختلف - المتوقع: {expected_dir}")
        
        # Check essential files
        print("\\n📋 فحص الملفات الأساسية:")
        
        essential_files = [
            "protech_simple_working.py",
            "protech_simple_data.json",
            "protech_backup.json"
        ]
        
        files_status = {}
        
        for file in essential_files:
            if os.path.exists(file):
                size = os.path.getsize(file) / 1024
                mod_time = datetime.fromtimestamp(os.path.getmtime(file))
                files_status[file] = {
                    'exists': True,
                    'size_kb': round(size, 1),
                    'modified': mod_time.strftime('%H:%M:%S')
                }
                print(f"✅ {file}: {size:.1f} KB - {mod_time.strftime('%H:%M:%S')}")
            else:
                files_status[file] = {'exists': False}
                print(f"❌ {file}: غير موجود")
        
        # Test compilation
        print("\\n🧪 اختبار التجميع:")
        
        try:
            result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ التجميع: نجح")
                compilation_status = "success"
            else:
                print("❌ التجميع: فشل")
                print(f"   الخطأ: {result.stderr[:100]}...")
                compilation_status = "failed"
        except Exception as e:
            print(f"❌ خطأ في اختبار التجميع: {e}")
            compilation_status = "error"
        
        # Check data integrity
        print("\\n💾 فحص سلامة البيانات:")
        
        data_status = {}
        
        if os.path.exists('protech_simple_data.json'):
            try:
                with open('protech_simple_data.json', 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                data_status = {
                    'valid': True,
                    'suppliers': len(data.get('suppliers', [])),
                    'products': len(data.get('products', [])),
                    'customers': len(data.get('customers', [])),
                    'sales': len(data.get('sales', []))
                }
                
                print("✅ البيانات: سليمة")
                print(f"📊 الموردين: {data_status['suppliers']}")
                print(f"📦 المنتجات: {data_status['products']}")
                print(f"👥 العملاء: {data_status['customers']}")
                print(f"💰 المبيعات: {data_status['sales']}")
                
            except Exception as e:
                print(f"❌ خطأ في البيانات: {e}")
                data_status = {'valid': False, 'error': str(e)}
        else:
            print("❌ ملف البيانات غير موجود")
            data_status = {'valid': False, 'error': 'File not found'}
        
        # Check running processes
        print("\\n🔍 فحص العمليات الجارية:")
        
        try:
            # Check if ProTech is running
            result = subprocess.run(['tasklist', '/FI', 'IMAGENAME eq python.exe'], 
                                  capture_output=True, text=True)
            
            if 'python.exe' in result.stdout:
                python_processes = result.stdout.count('python.exe')
                print(f"🐍 عمليات Python نشطة: {python_processes}")
                
                # Check if our process is likely running
                if python_processes > 0:
                    print("✅ ProTech يبدو أنه يعمل")
                    process_status = "running"
                else:
                    print("⚠️ لا توجد عمليات Python")
                    process_status = "not_running"
            else:
                print("❌ لا توجد عمليات Python")
                process_status = "not_running"
                
        except Exception as e:
            print(f"❌ خطأ في فحص العمليات: {e}")
            process_status = "error"
        
        # Check for GUI window
        print("\\n🖥️ فحص واجهة المستخدم:")
        
        try:
            # Try to detect if tkinter window is open
            result = subprocess.run(['tasklist', '/FI', 'WINDOWTITLE eq ProTech*'], 
                                  capture_output=True, text=True)
            
            if 'python.exe' in result.stdout or 'ProTech' in result.stdout:
                print("✅ نافذة ProTech مفتوحة")
                gui_status = "open"
            else:
                print("⚠️ لا يمكن اكتشاف نافذة ProTech")
                gui_status = "unknown"
                
        except Exception as e:
            print(f"❌ خطأ في فحص الواجهة: {e}")
            gui_status = "error"
        
        # Overall status assessment
        print("\\n📊 تقييم الحالة العامة:")
        
        status_score = 0
        max_score = 4
        
        if files_status.get('protech_simple_working.py', {}).get('exists', False):
            status_score += 1
        
        if compilation_status == "success":
            status_score += 1
        
        if data_status.get('valid', False):
            status_score += 1
        
        if process_status == "running":
            status_score += 1
        
        success_rate = (status_score / max_score) * 100
        
        if success_rate >= 75:
            overall_status = "ممتاز"
            status_color = "🟢"
        elif success_rate >= 50:
            overall_status = "جيد"
            status_color = "🟡"
        else:
            overall_status = "يحتاج مراجعة"
            status_color = "🔴"
        
        print(f"{status_color} الحالة العامة: {overall_status} ({success_rate:.0f}%)")
        
        # Generate summary report
        report = {
            'timestamp': datetime.now().isoformat(),
            'location': current_dir,
            'files_status': files_status,
            'compilation_status': compilation_status,
            'data_status': data_status,
            'process_status': process_status,
            'gui_status': gui_status,
            'overall_status': overall_status,
            'success_rate': success_rate
        }
        
        # Save report
        report_file = f"launch_status_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            print(f"\\n📄 تم حفظ التقرير: {report_file}")
        except Exception as e:
            print(f"\\n❌ فشل في حفظ التقرير: {e}")
        
        # Recommendations
        print("\\n💡 التوصيات:")
        
        if compilation_status != "success":
            print("• إصلاح أخطاء التجميع")
        
        if not data_status.get('valid', False):
            print("• فحص وإصلاح ملف البيانات")
        
        if process_status != "running":
            print("• التأكد من تشغيل البرنامج")
        
        if gui_status == "unknown":
            print("• فحص نافذة البرنامج على الشاشة")
        
        if success_rate >= 75:
            print("• البرنامج يعمل بشكل مثالي!")
        
        print("\\n" + "="*50)
        
        return success_rate >= 50
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء تقرير التشغيل: {e}")
        return False

def main():
    """Main function"""
    success = generate_launch_status_report()
    
    if success:
        print("\\n🎉 تقرير حالة التشغيل مكتمل!")
        print("🎉 Launch status report completed!")
    else:
        print("\\n❌ فشل في إنشاء تقرير حالة التشغيل")
        print("❌ Failed to generate launch status report")

if __name__ == "__main__":
    main()
