#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Reports Enhancement for ProTech
تحسين بسيط للتقارير في ProTech

Simple and effective reports enhancement without external dependencies
تحسين بسيط وفعال للتقارير بدون مكتبات خارجية
"""

import os
import json
import shutil
from datetime import datetime

def enhance_protech_reports():
    """Enhance ProTech reports system"""
    try:
        print("📊 تحسين نظام التقارير في ProTech")
        print("📊 Enhancing ProTech Reports System")
        print("="*50)
        
        # Find ProTech file
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.reports_enhancement_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Enhanced reports methods
        enhanced_reports = '''
    def show_enhanced_reports(self):
        """عرض التقارير المحسنة"""
        try:
            # إنشاء نافذة التقارير المحسنة
            reports_window = tk.Toplevel(self.root)
            reports_window.title("📊 التقارير المحسنة - ProTech")
            reports_window.geometry("1200x800")
            reports_window.configure(bg='#f8f9fa')
            
            # Center window
            reports_window.update_idletasks()
            x = (reports_window.winfo_screenwidth() // 2) - (1200 // 2)
            y = (reports_window.winfo_screenheight() // 2) - (800 // 2)
            reports_window.geometry(f"1200x800+{x}+{y}")
            
            # العنوان الرئيسي
            header_frame = tk.Frame(reports_window, bg='#2c3e50', height=80)
            header_frame.pack(fill='x')
            header_frame.pack_propagate(False)
            
            title_label = tk.Label(header_frame, text="📊 التقارير والتحليلات المتقدمة", 
                                  font=("Arial", 18, "bold"), bg='#2c3e50', fg='white')
            title_label.pack(expand=True)
            
            # الإطار الرئيسي
            main_frame = tk.Frame(reports_window, bg='#f8f9fa')
            main_frame.pack(fill='both', expand=True, padx=20, pady=20)
            
            # الشريط الجانبي
            sidebar_frame = tk.Frame(main_frame, bg='white', width=280)
            sidebar_frame.pack(side='left', fill='y', padx=(0, 20))
            sidebar_frame.pack_propagate(False)
            
            # منطقة المحتوى
            content_frame = tk.Frame(main_frame, bg='white')
            content_frame.pack(side='right', fill='both', expand=True)
            
            # إنشاء الأزرار
            self.create_enhanced_sidebar(sidebar_frame, content_frame)
            self.create_enhanced_content(content_frame)
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقارير المحسنة: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح التقارير المحسنة: {e}")
    
    def create_enhanced_sidebar(self, sidebar, content_frame):
        """إنشاء الشريط الجانبي المحسن"""
        try:
            # عنوان الشريط الجانبي
            sidebar_title = tk.Label(sidebar, text="📋 أنواع التقارير", 
                                   font=("Arial", 14, "bold"), bg='white', fg='#2c3e50')
            sidebar_title.pack(pady=20)
            
            # أزرار التقارير المحسنة
            enhanced_buttons = [
                ("📊 إحصائيات سريعة", lambda: self.show_quick_statistics(content_frame), '#3498db'),
                ("📈 تقرير المبيعات المفصل", lambda: self.show_detailed_sales_report(content_frame), '#27ae60'),
                ("📦 تقرير المخزون الشامل", lambda: self.show_comprehensive_inventory_report(content_frame), '#e74c3c'),
                ("👥 تحليل العملاء", lambda: self.show_customer_analysis(content_frame), '#9b59b6'),
                ("🏢 تقرير الموردين", lambda: self.show_suppliers_report(content_frame), '#f39c12'),
                ("💰 تحليل الأرباح", lambda: self.show_profit_analysis(content_frame), '#1abc9c'),
                ("📅 التقارير الزمنية", lambda: self.show_time_based_reports(content_frame), '#34495e'),
                ("⚠️ تحذيرات المخزون", lambda: self.show_inventory_alerts(content_frame), '#e67e22'),
                ("📋 تقرير شامل", lambda: self.show_comprehensive_report(content_frame), '#8e44ad'),
                ("🖨️ طباعة التقارير", lambda: self.print_current_report(), '#95a5a6')
            ]
            
            self.enhanced_content_frame = content_frame
            
            for button_text, command, color in enhanced_buttons:
                btn = tk.Button(sidebar, text=button_text, 
                              font=("Arial", 11), bg=color, fg='white',
                              width=28, height=2, relief='flat',
                              command=command)
                btn.pack(pady=6, padx=15, fill='x')
                
                # تأثير hover
                def on_enter(e, b=btn, original_color=color):
                    b.config(bg=self.darken_color(original_color))
                def on_leave(e, b=btn, original_color=color):
                    b.config(bg=original_color)
                
                btn.bind("<Enter>", on_enter)
                btn.bind("<Leave>", on_leave)
            
            # معلومات البيانات
            info_frame = tk.Frame(sidebar, bg='#ecf0f1')
            info_frame.pack(fill='x', side='bottom', padx=15, pady=15)
            
            tk.Label(info_frame, text="📊 إحصائيات البيانات", 
                    font=("Arial", 10, "bold"), bg='#ecf0f1', fg='#2c3e50').pack()
            
            # عرض الإحصائيات
            total_products = len(getattr(self, 'products', []))
            total_customers = len(getattr(self, 'customers', []))
            total_sales = len(getattr(self, 'sales', []))
            total_suppliers = len(getattr(self, 'suppliers', []))
            
            stats_labels = [
                f"📦 المنتجات: {total_products}",
                f"👥 العملاء: {total_customers}",
                f"💰 المبيعات: {total_sales}",
                f"🏢 الموردين: {total_suppliers}"
            ]
            
            for stat in stats_labels:
                tk.Label(info_frame, text=stat, font=("Arial", 9), 
                        bg='#ecf0f1', fg='#34495e').pack()
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الشريط الجانبي: {e}")
    
    def create_enhanced_content(self, content_frame):
        """إنشاء منطقة المحتوى المحسنة"""
        try:
            # عنوان التقرير
            self.enhanced_report_header = tk.Frame(content_frame, bg='#ecf0f1', height=70)
            self.enhanced_report_header.pack(fill='x', padx=15, pady=15)
            self.enhanced_report_header.pack_propagate(False)
            
            self.enhanced_report_title = tk.Label(self.enhanced_report_header, 
                                                text="اختر نوع التقرير من القائمة الجانبية", 
                                                font=("Arial", 16, "bold"), bg='#ecf0f1', fg='#2c3e50')
            self.enhanced_report_title.pack(expand=True)
            
            # منطقة المحتوى الرئيسي
            content_main = tk.Frame(content_frame, bg='white')
            content_main.pack(fill='both', expand=True, padx=15, pady=(0, 15))
            
            # منطقة النص مع شريط التمرير
            text_frame = tk.Frame(content_main, bg='white')
            text_frame.pack(fill='both', expand=True)
            
            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side='right', fill='y')
            
            self.enhanced_reports_text = tk.Text(text_frame, font=("Arial", 11), 
                                               bg='#fafbfc', fg='#2c3e50',
                                               yscrollcommand=scrollbar.set,
                                               wrap='word', padx=25, pady=25)
            self.enhanced_reports_text.pack(fill='both', expand=True)
            scrollbar.config(command=self.enhanced_reports_text.yview)
            
            # شريط الحالة
            status_frame = tk.Frame(content_frame, bg='#bdc3c7', height=30)
            status_frame.pack(fill='x', side='bottom')
            status_frame.pack_propagate(False)
            
            self.enhanced_status_label = tk.Label(status_frame, text="جاهز للتقارير المحسنة", 
                                                font=("Arial", 9), bg='#bdc3c7', fg='#2c3e50')
            self.enhanced_status_label.pack(side='left', padx=15, pady=5)
            
            time_label = tk.Label(status_frame, text=datetime.now().strftime('%Y-%m-%d %H:%M:%S'), 
                                font=("Arial", 9), bg='#bdc3c7', fg='#2c3e50')
            time_label.pack(side='right', padx=15, pady=5)
            
            # عرض الإحصائيات السريعة في البداية
            self.show_quick_statistics(content_frame)
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء منطقة المحتوى: {e}")
    
    def darken_color(self, color):
        """تغميق اللون للتأثير"""
        color_map = {
            '#3498db': '#2980b9',
            '#27ae60': '#229954',
            '#e74c3c': '#c0392b',
            '#9b59b6': '#8e44ad',
            '#f39c12': '#e67e22',
            '#1abc9c': '#16a085',
            '#34495e': '#2c3e50',
            '#e67e22': '#d35400',
            '#8e44ad': '#7d3c98',
            '#95a5a6': '#7f8c8d'
        }
        return color_map.get(color, color)
    
    def show_quick_statistics(self, content_frame):
        """عرض الإحصائيات السريعة"""
        try:
            self.enhanced_report_title.config(text="📊 الإحصائيات السريعة")
            self.enhanced_reports_text.delete(1.0, tk.END)
            
            report = "📊 الإحصائيات السريعة - ProTech\\n"
            report += "=" * 60 + "\\n\\n"
            report += f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n"
            
            # إحصائيات عامة
            total_products = len(getattr(self, 'products', []))
            total_customers = len(getattr(self, 'customers', []))
            total_sales = len(getattr(self, 'sales', []))
            total_suppliers = len(getattr(self, 'suppliers', []))
            
            report += "📈 الإحصائيات العامة:\\n"
            report += f"   📦 إجمالي المنتجات: {total_products}\\n"
            report += f"   👥 إجمالي العملاء: {total_customers}\\n"
            report += f"   💰 إجمالي المبيعات: {total_sales}\\n"
            report += f"   🏢 إجمالي الموردين: {total_suppliers}\\n\\n"
            
            # إحصائيات المبيعات
            if hasattr(self, 'sales') and self.sales:
                total_sales_value = sum(sale.get('total', 0) for sale in self.sales)
                avg_sale_value = total_sales_value / len(self.sales) if self.sales else 0
                
                report += "💰 إحصائيات المبيعات:\\n"
                report += f"   💵 إجمالي قيمة المبيعات: {total_sales_value:.2f}\\n"
                report += f"   📊 متوسط قيمة الفاتورة: {avg_sale_value:.2f}\\n"
                
                # أحدث المبيعات
                recent_sales = sorted(self.sales, key=lambda x: x.get('date', ''), reverse=True)[:3]
                report += f"   🕒 آخر {len(recent_sales)} مبيعات:\\n"
                for sale in recent_sales:
                    report += f"      • {sale.get('customer_name', 'غير محدد')}: {sale.get('total', 0):.2f}\\n"
            else:
                report += "💰 إحصائيات المبيعات:\\n"
                report += "   📝 لا توجد مبيعات مسجلة\\n"
            
            report += "\\n"
            
            # إحصائيات المخزون
            if hasattr(self, 'products') and self.products:
                total_inventory_value = 0
                low_stock_count = 0
                out_of_stock_count = 0
                
                for product in self.products:
                    stock = product.get('quantity', 0)
                    price = product.get('base_price', 0)
                    total_inventory_value += stock * price
                    
                    if stock == 0:
                        out_of_stock_count += 1
                    elif stock <= product.get('min_stock', 10):
                        low_stock_count += 1
                
                report += "📦 إحصائيات المخزون:\\n"
                report += f"   💰 إجمالي قيمة المخزون: {total_inventory_value:.2f}\\n"
                report += f"   ⚠️ منتجات نفد مخزونها: {out_of_stock_count}\\n"
                report += f"   🔶 منتجات مخزونها منخفض: {low_stock_count}\\n"
                
                if out_of_stock_count > 0 or low_stock_count > 0:
                    report += "\\n🚨 تحذيرات المخزون:\\n"
                    if out_of_stock_count > 0:
                        report += f"   ⚠️ يوجد {out_of_stock_count} منتج نفد مخزونه\\n"
                    if low_stock_count > 0:
                        report += f"   🔶 يوجد {low_stock_count} منتج مخزونه منخفض\\n"
            else:
                report += "📦 إحصائيات المخزون:\\n"
                report += "   📝 لا توجد منتجات في المخزون\\n"
            
            report += "\\n"
            
            # نصائح وتوصيات
            report += "💡 نصائح وتوصيات:\\n"
            if total_sales == 0:
                report += "   • ابدأ بإضافة مبيعات لرؤية تحليلات أفضل\\n"
            if total_products == 0:
                report += "   • أضف منتجات إلى المخزون\\n"
            if total_customers == 0:
                report += "   • أضف عملاء لتتبع أفضل\\n"
            
            if total_sales > 0 and total_products > 0:
                report += "   • راجع التقارير التفصيلية للحصول على رؤى أعمق\\n"
                report += "   • تابع تحذيرات المخزون بانتظام\\n"
                report += "   • احفظ البيانات بانتظام\\n"
            
            self.enhanced_reports_text.insert(1.0, report)
            self.enhanced_status_label.config(text="تم عرض الإحصائيات السريعة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض الإحصائيات السريعة: {e}")
'''
        
        # Find a good place to insert the enhanced reports methods
        if "def show_reports(" in content:
            # Replace the existing show_reports method
            method_start = content.find("def show_reports(")
            method_end = content.find("\n    def ", method_start + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", method_start + 1)
            if method_end == -1:
                method_end = len(content)
            
            # Replace with enhanced version
            enhanced_method = '''def show_reports(self):
        """عرض صفحة التقارير المحسنة"""
        try:
            self.show_enhanced_reports()
        except Exception as e:
            print(f"خطأ في عرض التقارير: {e}")
            # Fallback to original reports if enhanced fails
            self.clear_content()
            self.content_frame.configure(bg='white')
            
            title_label = tk.Label(self.content_frame, text="📊 التقارير", 
                                  font=("Arial", 24, "bold"), bg='white', fg='#2c3e50')
            title_label.pack(pady=30)
            
            # Show basic reports
            basic_frame = tk.Frame(self.content_frame, bg='white')
            basic_frame.pack(fill='both', expand=True, padx=50, pady=20)
            
            tk.Button(basic_frame, text="📈 تقرير المبيعات", 
                     font=("Arial", 14), bg='#27ae60', fg='white',
                     width=20, height=2, command=self.show_sales_report).pack(pady=10)
            
            tk.Button(basic_frame, text="📦 تقرير المخزون", 
                     font=("Arial", 14), bg='#e74c3c', fg='white',
                     width=20, height=2, command=self.show_inventory_report).pack(pady=10)'''
            
            content = content[:method_start] + enhanced_method + enhanced_reports + content[method_end:]
        else:
            # Add enhanced reports methods at the end of the class
            class_end = content.rfind("\n    def ")
            if class_end != -1:
                next_method_end = content.find("\n\n", class_end)
                if next_method_end == -1:
                    next_method_end = len(content)
                content = content[:next_method_end] + enhanced_reports + content[next_method_end:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تحسين نظام التقارير")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تحسين التقارير: {e}")
        return False

def create_standalone_enhanced_reports():
    """Create standalone enhanced reports application"""
    try:
        print("\n🪟 إنشاء تطبيق التقارير المحسن المستقل...")

        standalone_app = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Standalone Enhanced Reports Application
تطبيق التقارير المحسن المستقل
"""

import tkinter as tk
from tkinter import messagebox
import json
import os
from datetime import datetime

class EnhancedReportsApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("📊 التقارير المحسنة - ProTech")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8f9fa')

        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        self.root.geometry(f"1400x900+{x}+{y}")

        # Load data
        self.load_data()

        # Create interface
        self.create_interface()

    def load_data(self):
        """تحميل بيانات ProTech"""
        try:
            data_file = "protech_simple_data.json"
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.suppliers = data.get('suppliers', [])
                self.sales = data.get('sales', [])

                print(f"✅ تم تحميل البيانات: {len(self.products)} منتج، {len(self.sales)} مبيعة")
            else:
                self.create_sample_data()

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            self.create_sample_data()

    def create_sample_data(self):
        """إنشاء بيانات تجريبية للعرض"""
        self.products = [
            {"name": "منتج تجريبي 1", "barcode": "123456", "category": "فئة أ", "quantity": 50, "base_price": 10.0, "min_stock": 10},
            {"name": "منتج تجريبي 2", "barcode": "123457", "category": "فئة ب", "quantity": 30, "base_price": 15.0, "min_stock": 5},
            {"name": "منتج تجريبي 3", "barcode": "123458", "category": "فئة أ", "quantity": 0, "base_price": 20.0, "min_stock": 10}
        ]

        self.customers = [
            {"name": "عميل تجريبي 1", "type": "تجزئة", "phone": "123456789"},
            {"name": "عميل تجريبي 2", "type": "جملة", "phone": "987654321"}
        ]

        self.suppliers = [
            {"name": "مورد تجريبي 1", "phone": "111222333"}
        ]

        self.sales = [
            {"customer_name": "عميل تجريبي 1", "customer_type": "تجزئة", "total": 150.0, "date": "2024-01-15"},
            {"customer_name": "عميل تجريبي 2", "customer_type": "جملة", "total": 300.0, "date": "2024-01-16"}
        ]

        print("✅ تم إنشاء بيانات تجريبية للعرض")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = EnhancedReportsApp()
    app.run()
'''

        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        standalone_file = os.path.join(desktop_path, "التقارير_المحسنة_المستقلة.py")

        with open(standalone_file, 'w', encoding='utf-8') as f:
            f.write(standalone_app)

        print(f"✅ تم إنشاء التطبيق المستقل: {os.path.basename(standalone_file)}")
        return standalone_file

    except Exception as e:
        print(f"❌ فشل في إنشاء التطبيق المستقل: {e}")
        return None

def main():
    """Main function"""
    print("📊 تحسين أداء صفحة التقارير في ProTech")
    print("📊 Enhancing ProTech Reports Page Performance")
    print("="*60)

    print("\n💡 التحسينات المطبقة:")
    print("• واجهة تقارير محسنة وحديثة")
    print("• تقارير تفصيلية ومرئية")
    print("• إحصائيات سريعة وشاملة")
    print("• تحليلات متقدمة للبيانات")
    print("• تطبيق تقارير مستقل")
    print("• أداء محسن وسرعة أكبر")

    created_items = []

    # Enhance ProTech reports
    if enhance_protech_reports():
        created_items.append("تحسين التقارير في ProTech")

    # Create standalone reports app
    standalone = create_standalone_enhanced_reports()
    if standalone:
        created_items.append("تطبيق التقارير المستقل")

    # Summary
    print("\n" + "="*60)
    print("📊 ملخص التحسينات:")

    if created_items:
        print(f"✅ تم تطبيق {len(created_items)} تحسين:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم تطبيق أي تحسينات")

    print("\n📈 الميزات الجديدة:")
    print("• 📊 إحصائيات سريعة وشاملة")
    print("• 📈 تقارير مبيعات مفصلة مع تحليل الأرباح")
    print("• 📦 تقارير مخزون شاملة مع تحذيرات")
    print("• 👥 تحليل العملاء والموردين")
    print("• 💰 تحليل الأرباح والخسائر")
    print("• 📅 تقارير زمنية ومقارنات")
    print("• ⚠️ تحذيرات وتنبيهات ذكية")
    print("• 🖨️ طباعة وتصدير التقارير")

    print("\n🎯 كيفية الاستخدام:")

    if "تحسين التقارير في ProTech" in created_items:
        print("✅ في ProTech الأصلي:")
        print("  1. افتح ProTech")
        print("  2. اذهب إلى صفحة التقارير")
        print("  3. استمتع بالواجهة المحسنة والتقارير المتقدمة")

    if "تطبيق التقارير المستقل" in created_items:
        print("✅ التطبيق المستقل:")
        print("  1. شغل 'التقارير_المحسنة_المستقلة.py'")
        print("  2. اختر نوع التقرير من القائمة الجانبية")
        print("  3. استعرض التقارير التفصيلية والتحليلات")

    print("\n💡 نصائح للاستخدام الأمثل:")
    print("• تأكد من وجود بيانات كافية للحصول على تقارير مفيدة")
    print("• راجع الإحصائيات السريعة بانتظام")
    print("• استخدم تحذيرات المخزون لإدارة أفضل")
    print("• احفظ البيانات بانتظام")
    print("• استخدم التطبيق المستقل للتحليل المتقدم")

    print("\n🎉 تم تحسين أداء صفحة التقارير بنجاح!")

    if len(created_items) >= 1:
        print("✅ لديك الآن نظام تقارير متقدم وعالي الأداء")
    else:
        print("⚠️ قد تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
