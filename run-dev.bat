@echo off
echo Starting ProTech Accounting Development Server...
echo.

REM Add Node.js to PATH for this session
set PATH=%PATH%;C:\Program Files\nodejs

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js not found in PATH
    echo Please ensure Node.js is installed and accessible
    pause
    exit /b 1
)

echo Node.js version:
node --version

echo.
echo Starting development server...
echo The application will be available at: http://localhost:3000
echo.
echo Press Ctrl+C to stop the server
echo.

npm run dev

pause
