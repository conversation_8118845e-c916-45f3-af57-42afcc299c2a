#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Data Persistence Issue
إصلاح مشكلة استمرارية البيانات

Fix the issue where data is lost when closing and reopening ProTech
إصلاح مشكلة فقدان البيانات عند إغلاق وإعادة فتح ProTech
"""

import os
import json
import shutil
from datetime import datetime

def diagnose_data_persistence_issue():
    """Diagnose data persistence issue"""
    try:
        print("🔍 تشخيص مشكلة استمرارية البيانات")
        print("🔍 Diagnosing Data Persistence Issue")
        print("="*50)
        
        # Check data file location
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = os.path.join(desktop_path, "protech_simple_data.json")
        
        print(f"📁 مسار ملف البيانات: {data_file}")
        
        if os.path.exists(data_file):
            size = os.path.getsize(data_file)
            mod_time = datetime.fromtimestamp(os.path.getmtime(data_file))
            print(f"✅ ملف البيانات موجود: {size} bytes")
            print(f"🕐 آخر تعديل: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
            
            # Check file content
            try:
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print("📊 محتوى ملف البيانات:")
                print(f"  📦 المنتجات: {len(data.get('products', []))}")
                print(f"  👥 العملاء: {len(data.get('customers', []))}")
                print(f"  🏢 الموردين: {len(data.get('suppliers', []))}")
                print(f"  💰 المبيعات: {len(data.get('sales', []))}")
                
                # Check if data is empty
                total_items = (len(data.get('products', [])) + 
                             len(data.get('customers', [])) + 
                             len(data.get('suppliers', [])) + 
                             len(data.get('sales', [])))
                
                if total_items == 0:
                    print("⚠️ ملف البيانات فارغ!")
                    return "empty_data"
                else:
                    print(f"✅ ملف البيانات يحتوي على {total_items} عنصر")
                    return "data_exists"
                    
            except json.JSONDecodeError as e:
                print(f"❌ خطأ في تنسيق JSON: {e}")
                return "json_error"
            except Exception as e:
                print(f"❌ خطأ في قراءة البيانات: {e}")
                return "read_error"
        else:
            print("❌ ملف البيانات غير موجود")
            return "no_file"
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        return "error"

def fix_data_persistence():
    """Fix data persistence by modifying the ProTech file"""
    try:
        print("\n🔧 إصلاح مشكلة استمرارية البيانات...")
        
        # Find ProTech file
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.persistence_fix_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add persistence fix code
        persistence_fix = '''
# ===== DATA PERSISTENCE FIX - START =====
# إصلاح استمرارية البيانات - البداية

import atexit
import threading
import time

class DataPersistenceManager:
    """Manage data persistence and auto-save"""
    
    def __init__(self, app_instance):
        self.app = app_instance
        self.auto_save_enabled = True
        self.save_interval = 30  # Save every 30 seconds
        self.setup_persistence()
    
    def setup_persistence(self):
        """Setup data persistence mechanisms"""
        try:
            # Register exit handler
            atexit.register(self.save_on_exit)
            
            # Start auto-save thread
            if self.auto_save_enabled:
                self.start_auto_save()
            
            # Override window close event
            if hasattr(self.app, 'root'):
                self.app.root.protocol("WM_DELETE_WINDOW", self.on_window_close)
            
        except Exception as e:
            print(f"تحذير: مشكلة في إعداد استمرارية البيانات: {e}")
    
    def save_on_exit(self):
        """Save data when program exits"""
        try:
            if hasattr(self.app, 'save_data'):
                self.app.save_data()
                print("✅ تم حفظ البيانات عند الخروج")
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات عند الخروج: {e}")
    
    def on_window_close(self):
        """Handle window close event"""
        try:
            # Save data before closing
            if hasattr(self.app, 'save_data'):
                self.app.save_data()
                print("✅ تم حفظ البيانات عند إغلاق النافذة")
            
            # Close the window
            if hasattr(self.app, 'root'):
                self.app.root.destroy()
                
        except Exception as e:
            print(f"❌ خطأ في إغلاق النافذة: {e}")
            # Force close if there's an error
            try:
                self.app.root.destroy()
            except:
                pass
    
    def start_auto_save(self):
        """Start auto-save thread"""
        try:
            def auto_save_worker():
                while self.auto_save_enabled:
                    try:
                        time.sleep(self.save_interval)
                        if hasattr(self.app, 'save_data') and self.auto_save_enabled:
                            self.app.save_data()
                            print(f"🔄 حفظ تلقائي: {datetime.now().strftime('%H:%M:%S')}")
                    except Exception as e:
                        print(f"❌ خطأ في الحفظ التلقائي: {e}")
                        break
            
            auto_save_thread = threading.Thread(target=auto_save_worker, daemon=True)
            auto_save_thread.start()
            print("🔄 تم تفعيل الحفظ التلقائي")
            
        except Exception as e:
            print(f"❌ فشل في تفعيل الحفظ التلقائي: {e}")
    
    def stop_auto_save(self):
        """Stop auto-save"""
        self.auto_save_enabled = False

# Enhanced save_data method
def enhanced_save_data(self):
    """Enhanced save data method with better error handling"""
    try:
        # Prepare data
        data = {
            'suppliers': getattr(self, 'suppliers', []),
            'products': getattr(self, 'products', []),
            'customers': getattr(self, 'customers', []),
            'sales': getattr(self, 'sales', []),
            'settings': getattr(self, 'settings', {}),
            'last_saved': datetime.now().isoformat()
        }
        
        # Save to main file
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Create backup copy
            backup_file = f"{self.data_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            # Keep only last 5 backups
            self.cleanup_old_backups()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            
            # Try alternative save location
            try:
                alt_file = f"protech_emergency_save_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                with open(alt_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                print(f"✅ تم الحفظ في مكان بديل: {alt_file}")
                return True
            except Exception as e2:
                print(f"❌ فشل الحفظ البديل: {e2}")
                return False
    
    except Exception as e:
        print(f"❌ خطأ عام في حفظ البيانات: {e}")
        return False

def cleanup_old_backups(self):
    """Clean up old backup files"""
    try:
        import glob
        backup_pattern = f"{self.data_file}.backup_*"
        backup_files = glob.glob(backup_pattern)
        
        if len(backup_files) > 5:
            # Sort by modification time and keep only the 5 newest
            backup_files.sort(key=os.path.getmtime, reverse=True)
            for old_backup in backup_files[5:]:
                try:
                    os.remove(old_backup)
                except:
                    pass
    except:
        pass

# ===== DATA PERSISTENCE FIX - END =====
# إصلاح استمرارية البيانات - النهاية

'''
        
        # Find the class definition and add persistence manager
        if "class " in content:
            # Find the __init__ method and add persistence manager
            init_pos = content.find("def __init__(self")
            if init_pos != -1:
                # Find the end of __init__ method
                init_end = content.find("\n    def ", init_pos + 1)
                if init_end == -1:
                    init_end = content.find("\nclass ", init_pos + 1)
                if init_end == -1:
                    init_end = len(content)
                
                # Add persistence manager initialization
                persistence_init = '''
        
        # Initialize data persistence manager
        try:
            self.persistence_manager = DataPersistenceManager(self)
        except Exception as e:
            print(f"تحذير: فشل في تهيئة مدير استمرارية البيانات: {e}")
'''
                
                content = content[:init_end] + persistence_init + content[init_end:]
        
        # Replace the save_data method if it exists
        if "def save_data(self" in content:
            # Find and replace the save_data method
            save_start = content.find("def save_data(self")
            if save_start != -1:
                # Find the end of the method
                save_end = content.find("\n    def ", save_start + 1)
                if save_end == -1:
                    save_end = content.find("\nclass ", save_start + 1)
                if save_end == -1:
                    save_end = len(content)
                
                # Replace with enhanced save method
                enhanced_save = '''def save_data(self):
        """Enhanced save data method"""
        return enhanced_save_data(self)
    
    def cleanup_old_backups(self):
        """Clean up old backup files"""
        return cleanup_old_backups(self)'''
                
                content = content[:save_start] + enhanced_save + content[save_end:]
        
        # Add the persistence fix code at the beginning (after existing fixes)
        if "DOUBLE CLICK FIX - END" in content:
            insert_pos = content.find("DOUBLE CLICK FIX - END") + len("DOUBLE CLICK FIX - END")
            content = content[:insert_pos] + persistence_fix + content[insert_pos:]
        else:
            # Add at the beginning if no double click fix exists
            content = persistence_fix + content
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة إصلاح استمرارية البيانات")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح استمرارية البيانات: {e}")
        return False

def create_data_recovery_tool():
    """Create data recovery tool"""
    try:
        print("\n🛠️ إنشاء أداة استعادة البيانات...")
        
        recovery_tool = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Data Recovery Tool
أداة استعادة بيانات ProTech
"""

import os
import json
import glob
from datetime import datetime

def recover_data():
    """Recover data from backup files"""
    try:
        print("🔍 البحث عن ملفات النسخ الاحتياطية...")
        
        # Look for backup files
        backup_patterns = [
            "protech_simple_data.json.backup_*",
            "protech_emergency_save_*.json",
            "protech_backup.json"
        ]
        
        all_backups = []
        for pattern in backup_patterns:
            backups = glob.glob(pattern)
            all_backups.extend(backups)
        
        if not all_backups:
            print("❌ لم يتم العثور على ملفات نسخ احتياطية")
            return False
        
        print(f"✅ تم العثور على {len(all_backups)} ملف نسخ احتياطي")
        
        # Sort by modification time (newest first)
        all_backups.sort(key=os.path.getmtime, reverse=True)
        
        print("\\n📋 ملفات النسخ الاحتياطية المتاحة:")
        for i, backup in enumerate(all_backups[:5], 1):
            mod_time = datetime.fromtimestamp(os.path.getmtime(backup))
            size = os.path.getsize(backup)
            print(f"  {i}. {backup} - {size} bytes - {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Try to restore from the newest backup
        newest_backup = all_backups[0]
        
        try:
            with open(newest_backup, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # Check data content
            total_items = (len(data.get('products', [])) + 
                         len(data.get('customers', [])) + 
                         len(data.get('suppliers', [])) + 
                         len(data.get('sales', [])))
            
            print(f"\\n📊 محتوى النسخة الاحتياطية:")
            print(f"  📦 المنتجات: {len(data.get('products', []))}")
            print(f"  👥 العملاء: {len(data.get('customers', []))}")
            print(f"  🏢 الموردين: {len(data.get('suppliers', []))}")
            print(f"  💰 المبيعات: {len(data.get('sales', []))}")
            print(f"  📊 إجمالي العناصر: {total_items}")
            
            if total_items > 0:
                # Restore the data
                main_data_file = "protech_simple_data.json"
                
                # Create backup of current file if it exists
                if os.path.exists(main_data_file):
                    backup_current = f"{main_data_file}.before_recovery_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    import shutil
                    shutil.copy2(main_data_file, backup_current)
                    print(f"💾 نسخة احتياطية من الملف الحالي: {backup_current}")
                
                # Restore data
                with open(main_data_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                print(f"\\n✅ تم استعادة البيانات من: {newest_backup}")
                print("✅ يمكنك الآن تشغيل ProTech لرؤية البيانات المستعادة")
                return True
            else:
                print("⚠️ النسخة الاحتياطية فارغة")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في استعادة البيانات: {e}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في أداة الاستعادة: {e}")
        return False

def main():
    """Main function"""
    print("🛠️ أداة استعادة بيانات ProTech")
    print("🛠️ ProTech Data Recovery Tool")
    print("="*50)
    
    if recover_data():
        print("\\n🎉 تم استعادة البيانات بنجاح!")
    else:
        print("\\n❌ فشل في استعادة البيانات")
    
    input("\\nاضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        recovery_file = os.path.join(desktop_path, "استعادة_بيانات_ProTech.py")
        
        with open(recovery_file, 'w', encoding='utf-8') as f:
            f.write(recovery_tool)
        
        print(f"✅ تم إنشاء أداة الاستعادة: {os.path.basename(recovery_file)}")
        return recovery_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء أداة الاستعادة: {e}")
        return None

def main():
    """Main function"""
    print("🔧 إصلاح مشكلة استمرارية البيانات في ProTech")
    print("🔧 Fixing ProTech Data Persistence Issue")
    print("="*60)
    
    print("\n💡 المشكلة:")
    print("عند إغلاق البرنامج وإعادة تشغيله، تختفي جميع البيانات")
    print("هذا يحدث لأن البرنامج لا يحفظ البيانات تلقائياً")
    
    # Diagnose the issue
    issue_type = diagnose_data_persistence_issue()
    
    created_items = []
    
    # Apply persistence fix
    if fix_data_persistence():
        created_items.append("إصلاح استمرارية البيانات")
    
    # Create recovery tool
    recovery_tool = create_data_recovery_tool()
    if recovery_tool:
        created_items.append("أداة استعادة البيانات")
    
    # Summary
    print("\n" + "="*60)
    print("📊 ملخص الإصلاحات:")
    
    if created_items:
        print(f"✅ تم تطبيق {len(created_items)} إصلاح:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم تطبيق أي إصلاحات")
    
    print("\n🔧 الميزات المضافة:")
    print("• حفظ تلقائي كل 30 ثانية")
    print("• حفظ عند إغلاق النافذة")
    print("• حفظ عند خروج البرنامج")
    print("• نسخ احتياطية متعددة")
    print("• أداة استعادة البيانات")
    
    print("\n💡 كيفية الاستخدام:")
    print("1. شغل ProTech كالمعتاد")
    print("2. أدخل البيانات (منتجات، عملاء، إلخ)")
    print("3. البيانات ستُحفظ تلقائياً كل 30 ثانية")
    print("4. عند إغلاق البرنامج، ستُحفظ البيانات تلقائياً")
    print("5. عند إعادة التشغيل، ستجد جميع البيانات محفوظة")
    
    print("\n🛠️ في حالة فقدان البيانات:")
    print("• شغل 'استعادة_بيانات_ProTech.py'")
    print("• ستجد النسخ الاحتياطية وتستعيد البيانات")
    
    print("\n🎉 تم إصلاح مشكلة استمرارية البيانات!")
    
    if len(created_items) >= 1:
        print("✅ البيانات الآن ستُحفظ تلقائياً")
    else:
        print("⚠️ قد تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
