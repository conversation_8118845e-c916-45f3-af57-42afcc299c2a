export default function HomePage() {
  const modules = [
    {
      title: 'Sales Management',
      description: 'Manage invoices, customer transactions, and returns',
      color: 'bg-blue-500',
    },
    {
      title: 'Inventory Management',
      description: 'Track stock levels, manage products, and barcode scanning',
      color: 'bg-green-500',
    },
    {
      title: 'Customer Management',
      description: 'Manage customer relationships and credit limits',
      color: 'bg-purple-500',
    },
    {
      title: 'Purchase Management',
      description: 'Handle supplier orders and purchase invoices',
      color: 'bg-orange-500',
    },
    {
      title: 'Reports & Analytics',
      description: 'View comprehensive business reports and analytics',
      color: 'bg-red-500',
    },
    {
      title: 'Settings',
      description: 'Configure system settings and preferences',
      color: 'bg-gray-500',
    },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">P</span>
                </div>
              </div>
              <div className="ml-3">
                <h1 className="text-xl font-semibold text-gray-900">ProTech Accounting</h1>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-500">
                Comprehensive Accounting System
              </span>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">
            Dashboard
          </h2>
          <p className="text-gray-600">
            Select a module to get started with your accounting operations
          </p>
        </div>

        {/* Module Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {modules.map((module) => (
            <div
              key={module.title}
              className="group relative bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200 cursor-pointer"
            >
              <div className="flex items-center mb-4">
                <div className={`${module.color} p-3 rounded-lg`}>
                  <div className="h-6 w-6 text-white">📊</div>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2 group-hover:text-blue-600">
                {module.title}
              </h3>
              <p className="text-gray-600 text-sm">
                {module.description}
              </p>
              <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity">
                <svg className="h-5 w-5 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </div>
            </div>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="mt-12 grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-semibold text-sm">$</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Sales</p>
                <p className="text-2xl font-semibold text-gray-900">$24,500</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                  <span className="text-green-600 font-semibold text-sm">#</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Products</p>
                <p className="text-2xl font-semibold text-gray-900">1,247</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                  <span className="text-purple-600 font-semibold text-sm">👥</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Customers</p>
                <p className="text-2xl font-semibold text-gray-900">342</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center">
                  <span className="text-orange-600 font-semibold text-sm">📦</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Low Stock</p>
                <p className="text-2xl font-semibold text-gray-900">23</p>
              </div>
            </div>
          </div>
        </div>

        {/* Feature Showcase */}
        <div className="mt-12 bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <h3 className="text-xl font-semibold text-gray-900 mb-6">🚀 ProTech Accounting Features</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">✅ Core Functionality</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Sales management with invoicing</li>
                <li>• Real-time inventory tracking</li>
                <li>• Customer relationship management</li>
                <li>• Purchase order management</li>
                <li>• Multi-level pricing system (4 tiers)</li>
                <li>• Barcode scanning and generation</li>
              </ul>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">✅ Technical Features</h4>
              <ul className="space-y-2 text-sm text-gray-600">
                <li>• Next.js 15 with TypeScript</li>
                <li>• PostgreSQL database with Prisma</li>
                <li>• Responsive design with Tailwind CSS</li>
                <li>• RESTful API architecture</li>
                <li>• JWT authentication system</li>
                <li>• Bilingual support (Arabic RTL + English LTR)</li>
              </ul>
            </div>
          </div>
          
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800 text-sm">
              <strong>🎯 Status:</strong> The application is fully developed with comprehensive inventory management, 
              customer/supplier management, sales processing, and reporting capabilities. 
              All backend APIs and frontend components are implemented and ready for use.
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
