#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Targeted Error Fix for ProTech
إصلاح مستهدف للأخطاء في ProTech

Fix specific indentation and syntax errors
إصلاح أخطاء المسافات البادئة والتركيب المحددة
"""

import os
import shutil
from datetime import datetime

def create_backup():
    """Create backup before fixing"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.targeted_fix_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def fix_indentation_errors():
    """Fix specific indentation errors"""
    try:
        print("🔧 إصلاح أخطاء المسافات البادئة المحددة...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        fixes_count = 0
        
        # Fix line by line
        for i in range(len(lines)):
            line = lines[i]
            
            # Fix empty except blocks
            if line.strip().startswith('except') and ':' in line:
                # Check if next line is empty or has wrong indentation
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    
                    # If next line is empty or doesn't have proper indentation
                    if not next_line.strip() or not next_line.startswith('    '):
                        # Get the indentation of the except line
                        except_indent = len(line) - len(line.lstrip())
                        
                        # Add proper pass statement
                        pass_line = ' ' * (except_indent + 4) + 'pass  # Auto-fixed\n'
                        
                        # Insert the pass line
                        if i + 1 < len(lines):
                            lines[i + 1] = pass_line
                        else:
                            lines.append(pass_line)
                        
                        fixes_count += 1
            
            # Fix try blocks without content
            elif line.strip().startswith('try:'):
                # Check if next line has proper indentation
                if i + 1 < len(lines):
                    next_line = lines[i + 1]
                    
                    if not next_line.strip():
                        # Get the indentation of the try line
                        try_indent = len(line) - len(line.lstrip())
                        
                        # Add proper pass statement
                        pass_line = ' ' * (try_indent + 4) + 'pass  # Auto-fixed\n'
                        lines[i + 1] = pass_line
                        fixes_count += 1
        
        # Write fixed content
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم إصلاح {fixes_count} خطأ مسافات بادئة")
        return fixes_count
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح المسافات البادئة: {e}")
        return 0

def fix_specific_line_164():
    """Fix specific error on line 164"""
    try:
        print("🔧 إصلاح الخطأ المحدد في السطر 164...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        # Check line 164 (index 163)
        if len(lines) > 164:
            line_164 = lines[163]  # Line 164 (0-based index)
            line_165 = lines[164] if len(lines) > 164 else ""
            
            print(f"السطر 164: {line_164.strip()}")
            print(f"السطر 165: {line_165.strip()}")
            
            # If line 164 is except and line 165 is empty or wrong
            if 'except' in line_164 and ':' in line_164:
                if not line_165.strip() or not line_165.startswith('    '):
                    # Get indentation
                    indent = len(line_164) - len(line_164.lstrip())
                    
                    # Fix line 165
                    lines[164] = ' ' * (indent + 4) + 'pass  # Fixed line 165\n'
                    
                    print("✅ تم إصلاح السطر 165")
                    
                    # Write fixed content
                    with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                        f.writelines(lines)
                    
                    return True
        
        print("ℹ️ لم يتم العثور على مشكلة في السطر 164")
        return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح السطر 164: {e}")
        return False

def test_syntax():
    """Test syntax after fixes"""
    try:
        print("🧪 اختبار التركيب...")
        
        import subprocess
        import sys
        
        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التركيب: نجح")
            return True
        else:
            print(f"❌ اختبار التركيب: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Extract line number from error
            if "line" in result.stderr:
                import re
                line_match = re.search(r'line (\d+)', result.stderr)
                if line_match:
                    error_line = int(line_match.group(1))
                    print(f"🎯 الخطأ في السطر: {error_line}")
                    
                    # Try to fix this specific line
                    fix_specific_line(error_line)
            
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التركيب: {e}")
        return False

def fix_specific_line(line_number):
    """Fix specific line number"""
    try:
        print(f"🔧 إصلاح السطر {line_number}...")
        
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if line_number <= len(lines):
            line_index = line_number - 1
            problem_line = lines[line_index]
            
            print(f"السطر المشكل: {problem_line.strip()}")
            
            # Common fixes
            if problem_line.strip() == "":
                # Empty line after except/try
                if line_index > 0:
                    prev_line = lines[line_index - 1]
                    if 'except' in prev_line or 'try:' in prev_line:
                        indent = len(prev_line) - len(prev_line.lstrip())
                        lines[line_index] = ' ' * (indent + 4) + 'pass  # Fixed empty block\n'
                        
                        # Write fixed content
                        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                            f.writelines(lines)
                        
                        print(f"✅ تم إصلاح السطر {line_number}")
                        return True
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح السطر {line_number}: {e}")
        return False

def create_working_launcher():
    """Create launcher for working version"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Working Launcher
مشغل ProTech العامل
"""

import os
import sys
import subprocess

def main():
    """Launch ProTech working version"""
    try:
        print("🚀 تشغيل ProTech العامل...")
        
        # Set environment
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        
        # Check file exists
        if not os.path.exists('protech_simple_working.py'):
            print("❌ ملف ProTech غير موجود!")
            input("اضغط Enter للخروج...")
            return
        
        # Test syntax first
        print("🧪 اختبار التركيب...")
        test_result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                                   capture_output=True, text=True)
        
        if test_result.returncode != 0:
            print(f"❌ خطأ في التركيب:")
            print(test_result.stderr)
            print("\\n🔧 محاولة إصلاح تلقائي...")
            
            # Try automatic fix
            try:
                exec(open('targeted_error_fix.py').read())
            except:
                pass
            
            input("اضغط Enter للخروج...")
            return
        
        print("✅ التركيب سليم")
        
        # Launch
        if sys.platform == 'win32':
            subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)
        else:
            subprocess.Popen([sys.executable, 'protech_simple_working.py'], env=env)
        
        print("✅ تم تشغيل ProTech بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''
    
    with open('launch_protech_working.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل عامل: launch_protech_working.py")

def main():
    """Main targeted fixing function"""
    print("🎯 إصلاح مستهدف للأخطاء في ProTech")
    print("🎯 ProTech Targeted Error Fix")
    print("="*50)
    
    try:
        # Create backup
        create_backup()
        
        # Apply targeted fixes
        total_fixes = 0
        
        print("\n🔧 تطبيق الإصلاحات المستهدفة...")
        
        # Fix 1: Specific line 164
        if fix_specific_line_164():
            total_fixes += 1
        
        # Fix 2: General indentation errors
        total_fixes += fix_indentation_errors()
        
        # Test syntax multiple times with fixes
        max_attempts = 3
        for attempt in range(max_attempts):
            print(f"\n🧪 محاولة اختبار {attempt + 1}/{max_attempts}...")
            
            if test_syntax():
                print("✅ نجح الاختبار!")
                break
            
            if attempt < max_attempts - 1:
                print("🔧 محاولة إصلاح إضافي...")
                total_fixes += fix_indentation_errors()
        
        # Create launcher
        create_working_launcher()
        
        print("\n" + "="*50)
        print(f"📊 ملخص الإصلاحات المستهدفة:")
        print(f"• إجمالي الإصلاحات: {total_fixes}")
        
        # Final test
        final_test = test_syntax()
        print(f"• الاختبار النهائي: {'نجح' if final_test else 'فشل'}")
        
        if final_test:
            print("\n🎉 تم إصلاح الأخطاء بنجاح!")
            print("🎉 Errors fixed successfully!")
            print("\n🚀 يمكنك الآن:")
            print("1. تشغيل launch_protech_working.py")
            print("2. النقر المزدوج على protech_simple_working.py")
        else:
            print("\n⚠️ لا يزال يحتاج إصلاح يدوي")
            print("⚠️ Still needs manual fixing")
        
        print("="*50)
        
        return final_test
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح المستهدف: {e}")
        return False

if __name__ == "__main__":
    main()
