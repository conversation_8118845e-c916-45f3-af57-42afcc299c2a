'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import { formatCurrency } from '@/lib/utils';
import { 
  CubeIcon, 
  CurrencyDollarIcon, 
  ExclamationTriangleIcon,
  XCircleIcon 
} from '@heroicons/react/24/outline';

interface InventoryStats {
  totalProducts: number;
  totalQuantity: number;
  totalValue: number;
  averageValue: number;
  lowStockCount: number;
  outOfStockCount: number;
  categoryBreakdown: Array<{
    category: string;
    products: number;
    quantity: number;
    value: number;
  }>;
  topValueProducts: Array<{
    productId: string;
    productCode: string;
    productName: string;
    quantity: number;
    unitPrice: number;
    totalValue: number;
  }>;
}

interface InventoryDashboardProps {
  refreshTrigger?: number;
}

export default function InventoryDashboard({ refreshTrigger }: InventoryDashboardProps) {
  const [stats, setStats] = useState<InventoryStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchInventoryStats();
  }, [refreshTrigger]);

  const fetchInventoryStats = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/inventory/valuation');
      const data = await response.json();

      if (data.success) {
        setStats({
          totalProducts: data.data.summary.totalProducts,
          totalQuantity: data.data.summary.totalQuantity,
          totalValue: data.data.summary.totalValue,
          averageValue: data.data.summary.averageValue,
          lowStockCount: 0, // This would come from a separate endpoint
          outOfStockCount: data.data.analysis.zeroStockProducts.length,
          categoryBreakdown: data.data.analysis.categoryBreakdown,
          topValueProducts: data.data.analysis.topValueProducts.slice(0, 5),
        });
      } else {
        setError(data.error || 'Failed to fetch inventory statistics');
      }
    } catch (err) {
      setError('Failed to fetch inventory statistics');
      console.error('Error fetching inventory stats:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error || !stats) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center">
            <ExclamationTriangleIcon className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CubeIcon className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Products</p>
                <p className="text-2xl font-semibold text-gray-900">{stats.totalProducts}</p>
                <p className="text-xs text-gray-500">{stats.totalQuantity} total units</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CurrencyDollarIcon className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Value</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {formatCurrency(stats.totalValue)}
                </p>
                <p className="text-xs text-gray-500">
                  Avg: {formatCurrency(stats.averageValue)}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <ExclamationTriangleIcon className="h-8 w-8 text-orange-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Low Stock</p>
                <p className="text-2xl font-semibold text-orange-600">{stats.lowStockCount}</p>
                <p className="text-xs text-gray-500">Items below minimum</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircleIcon className="h-8 w-8 text-red-500" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Out of Stock</p>
                <p className="text-2xl font-semibold text-red-600">{stats.outOfStockCount}</p>
                <p className="text-xs text-gray-500">Items with zero stock</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Category Breakdown and Top Products */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Category Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle>Inventory by Category</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.categoryBreakdown.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No categories found</p>
            ) : (
              <div className="space-y-4">
                {stats.categoryBreakdown.map((category, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-900">
                          {category.category}
                        </span>
                        <span className="text-sm text-gray-500">
                          {formatCurrency(category.value)}
                        </span>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <span>{category.products} products</span>
                        <span>{category.quantity} units</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Top Value Products */}
        <Card>
          <CardHeader>
            <CardTitle>Top Value Products</CardTitle>
          </CardHeader>
          <CardContent>
            {stats.topValueProducts.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No products found</p>
            ) : (
              <div className="space-y-4">
                {stats.topValueProducts.map((product, index) => (
                  <div key={product.productId} className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <span className="text-xs font-medium text-gray-400 w-4">
                          #{index + 1}
                        </span>
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {product.productName}
                          </div>
                          <div className="text-xs text-gray-500">
                            {product.productCode} • {product.quantity} units
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {formatCurrency(product.totalValue)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {formatCurrency(product.unitPrice)}/unit
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
