#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Reports System for ProTech
نظام التقارير المحسن لـ ProTech

Comprehensive and optimized reporting system with advanced analytics
نظام تقارير شامل ومحسن مع تحليلات متقدمة
"""

import os
import json
import shutil
from datetime import datetime, timedelta
import tkinter as tk
from tkinter import ttk, messagebox
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd

def create_enhanced_reports_system():
    """Create enhanced reports system for ProTech"""
    try:
        print("📊 إنشاء نظام التقارير المحسن")
        print("📊 Creating Enhanced Reports System")
        print("="*50)

        # Find ProTech file
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")

        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False

        # Create backup
        backup_name = f"{protech_file}.reports_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")

        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Enhanced reports system code
        reports_code = '''
# ===== ENHANCED REPORTS SYSTEM - START =====
# نظام التقارير المحسن - البداية

import threading
from datetime import datetime, timedelta
import json

class EnhancedReportsManager:
    """مدير التقارير المحسن"""

    def __init__(self, app_instance):
        self.app = app_instance
        self.reports_cache = {}
        self.last_update = None

    def show_enhanced_reports(self):
        """عرض نظام التقارير المحسن"""
        try:
            # إنشاء نافذة التقارير
            reports_window = tk.Toplevel(self.app.root)
            reports_window.title("التقارير والتحليلات المتقدمة")
            reports_window.geometry("1200x800")
            reports_window.configure(bg='#f8f9fa')

            # Center window
            reports_window.update_idletasks()
            x = (reports_window.winfo_screenwidth() // 2) - (1200 // 2)
            y = (reports_window.winfo_screenheight() // 2) - (800 // 2)
            reports_window.geometry(f"1200x800+{x}+{y}")

            # إنشاء الواجهة
            self.create_reports_interface(reports_window)

        except Exception as e:
            print(f"❌ خطأ في عرض التقارير: {e}")
            messagebox.showerror("خطأ", f"فشل في فتح نافذة التقارير: {e}")

    def create_reports_interface(self, window):
        """إنشاء واجهة التقارير"""
        try:
            # العنوان الرئيسي
            title_frame = tk.Frame(window, bg='#2c3e50', height=80)
            title_frame.pack(fill='x')
            title_frame.pack_propagate(False)

            title_label = tk.Label(title_frame, text="📊 التقارير والتحليلات المتقدمة",
                                  font=("Arial", 18, "bold"), bg='#2c3e50', fg='white')
            title_label.pack(expand=True)

            # إطار رئيسي
            main_frame = tk.Frame(window, bg='#f8f9fa')
            main_frame.pack(fill='both', expand=True, padx=20, pady=20)

            # إطار الأزرار الجانبي
            sidebar_frame = tk.Frame(main_frame, bg='white', width=250)
            sidebar_frame.pack(side='left', fill='y', padx=(0, 20))
            sidebar_frame.pack_propagate(False)

            # إطار المحتوى
            content_frame = tk.Frame(main_frame, bg='white')
            content_frame.pack(side='right', fill='both', expand=True)

            # إنشاء الأزرار والمحتوى
            self.create_sidebar_buttons(sidebar_frame, content_frame)
            self.create_content_area(content_frame)

        except Exception as e:
            print(f"❌ خطأ في إنشاء واجهة التقارير: {e}")

    def create_sidebar_buttons(self, sidebar, content_frame):
        """إنشاء أزرار الشريط الجانبي"""
        try:
            # عنوان الشريط الجانبي
            sidebar_title = tk.Label(sidebar, text="أنواع التقارير",
                                   font=("Arial", 14, "bold"), bg='white', fg='#2c3e50')
            sidebar_title.pack(pady=20)

            # أزرار التقارير
            reports_buttons = [
                ("📈 تقرير المبيعات", self.show_sales_report),
                ("📦 تقرير المخزون", self.show_inventory_report),
                ("👥 تقرير العملاء", self.show_customers_report),
                ("🏢 تقرير الموردين", self.show_suppliers_report),
                ("💰 تقرير الأرباح", self.show_profit_report),
                ("📊 تقرير الأداء", self.show_performance_report),
                ("📅 التقارير الشهرية", self.show_monthly_report),
                ("🔍 تحليل البيانات", self.show_analytics),
                ("📋 تقرير شامل", self.show_comprehensive_report),
                ("🖨️ طباعة التقارير", self.print_reports)
            ]

            self.content_frame = content_frame

            for button_text, command in reports_buttons:
                btn = tk.Button(sidebar, text=button_text,
                              font=("Arial", 11), bg='#3498db', fg='white',
                              width=25, height=2, relief='flat',
                              command=command)
                btn.pack(pady=5, padx=10, fill='x')

                # تأثير hover
                btn.bind("<Enter>", lambda e, b=btn: b.config(bg='#2980b9'))
                btn.bind("<Leave>", lambda e, b=btn: b.config(bg='#3498db'))

        except Exception as e:
            print(f"❌ خطأ في إنشاء أزرار الشريط الجانبي: {e}")

    def create_content_area(self, content_frame):
        """إنشاء منطقة المحتوى"""
        try:
            # إطار العنوان
            header_frame = tk.Frame(content_frame, bg='#ecf0f1', height=60)
            header_frame.pack(fill='x', padx=10, pady=10)
            header_frame.pack_propagate(False)

            self.report_title = tk.Label(header_frame, text="اختر نوع التقرير من القائمة الجانبية",
                                       font=("Arial", 16, "bold"), bg='#ecf0f1', fg='#2c3e50')
            self.report_title.pack(expand=True)

            # إطار المحتوى الرئيسي
            self.main_content = tk.Frame(content_frame, bg='white')
            self.main_content.pack(fill='both', expand=True, padx=10, pady=(0, 10))

            # منطقة النص للتقارير
            text_frame = tk.Frame(self.main_content, bg='white')
            text_frame.pack(fill='both', expand=True)

            # شريط التمرير
            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side='right', fill='y')

            # منطقة النص
            self.reports_text = tk.Text(text_frame, font=("Arial", 11),
                                      bg='#fafbfc', fg='#2c3e50',
                                      yscrollcommand=scrollbar.set,
                                      wrap='word', padx=20, pady=20)
            self.reports_text.pack(fill='both', expand=True)
            scrollbar.config(command=self.reports_text.yview)

            # إطار الإحصائيات السريعة
            self.create_quick_stats()

        except Exception as e:
            print(f"❌ خطأ في إنشاء منطقة المحتوى: {e}")

    def create_quick_stats(self):
        """إنشاء الإحصائيات السريعة"""
        try:
            # إطار الإحصائيات
            stats_frame = tk.Frame(self.main_content, bg='#e8f4fd', height=100)
            stats_frame.pack(fill='x', pady=(10, 0))
            stats_frame.pack_propagate(False)

            # عنوان الإحصائيات
            stats_title = tk.Label(stats_frame, text="📊 إحصائيات سريعة",
                                 font=("Arial", 12, "bold"), bg='#e8f4fd', fg='#2c3e50')
            stats_title.pack(pady=5)

            # إطار الأرقام
            numbers_frame = tk.Frame(stats_frame, bg='#e8f4fd')
            numbers_frame.pack(fill='x', padx=20)

            # حساب الإحصائيات
            total_products = len(getattr(self.app, 'products', []))
            total_customers = len(getattr(self.app, 'customers', []))
            total_sales = len(getattr(self.app, 'sales', []))
            total_suppliers = len(getattr(self.app, 'suppliers', []))

            # عرض الإحصائيات
            stats_data = [
                ("📦 المنتجات", total_products),
                ("👥 العملاء", total_customers),
                ("💰 المبيعات", total_sales),
                ("🏢 الموردين", total_suppliers)
            ]

            for i, (label, value) in enumerate(stats_data):
                stat_frame = tk.Frame(numbers_frame, bg='#e8f4fd')
                stat_frame.pack(side='left', expand=True, fill='x')

                tk.Label(stat_frame, text=label, font=("Arial", 9),
                        bg='#e8f4fd', fg='#34495e').pack()
                tk.Label(stat_frame, text=str(value), font=("Arial", 14, "bold"),
                        bg='#e8f4fd', fg='#2980b9').pack()

        except Exception as e:
            print(f"❌ خطأ في إنشاء الإحصائيات السريعة: {e}")

# ===== ENHANCED REPORTS SYSTEM - END =====
# نظام التقارير المحسن - النهاية

'''

        # Add the reports system after existing code
        if "class " in content:
            # Find the class definition and add the reports manager
            class_pos = content.find("class ")
            if class_pos != -1:
                # Find the end of __init__ method
                init_pos = content.find("def __init__(", class_pos)
                if init_pos != -1:
                    init_end = content.find("self.load_data()")
                    if init_end != -1:
                        init_end = content.find("\n", init_end)
                        reports_init = "\n        # Initialize enhanced reports manager\n        self.reports_manager = EnhancedReportsManager(self)"
                        content = content[:init_end] + reports_init + content[init_end:]

                # Add the reports code before the last method
                last_method = content.rfind("\n    def ")
                if last_method != -1:
                    content = content[:last_method] + reports_code + content[last_method:]

        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم إضافة نظام التقارير المحسن")

        # Test compilation
        import subprocess
        import sys

        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file],
                              capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")

            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False

    except Exception as e:
        print(f"❌ خطأ في إنشاء نظام التقارير المحسن: {e}")
        return False

def create_reports_methods():
    """Create individual report methods"""
    try:
        print("\n📋 إنشاء طرق التقارير الفردية...")

        reports_methods = '''
    def show_sales_report(self):
        """تقرير المبيعات المحسن"""
        try:
            self.report_title.config(text="📈 تقرير المبيعات التفصيلي")
            self.reports_text.delete(1.0, tk.END)

            report = "📈 تقرير المبيعات التفصيلي\\n"
            report += "=" * 60 + "\\n\\n"
            report += f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n"

            if not hasattr(self.app, 'sales') or not self.app.sales:
                report += "📝 لا توجد مبيعات مسجلة حتى الآن.\\n\\n"
                report += "💡 نصائح:\\n"
                report += "• قم بإضافة مبيعات من صفحة المبيعات\\n"
                report += "• تأكد من حفظ البيانات بعد كل عملية بيع\\n"
            else:
                total_sales = 0
                total_profit = 0
                customer_types = {}
                daily_sales = {}

                for i, sale in enumerate(self.app.sales, 1):
                    sale_date = sale.get('date', '')[:10]
                    sale_total = sale.get('total', 0)
                    customer_type = sale.get('customer_type', 'غير محدد')

                    # إحصائيات المبيعات
                    total_sales += sale_total

                    # إحصائيات أنواع العملاء
                    if customer_type not in customer_types:
                        customer_types[customer_type] = {'count': 0, 'total': 0}
                    customer_types[customer_type]['count'] += 1
                    customer_types[customer_type]['total'] += sale_total

                    # إحصائيات يومية
                    if sale_date not in daily_sales:
                        daily_sales[sale_date] = {'count': 0, 'total': 0}
                    daily_sales[sale_date]['count'] += 1
                    daily_sales[sale_date]['total'] += sale_total

                    # تفاصيل الفاتورة
                    report += f"🧾 فاتورة رقم {i}:\\n"
                    report += f"   العميل: {sale.get('customer_name', 'غير محدد')}\\n"
                    report += f"   نوع العميل: {customer_type}\\n"
                    report += f"   التاريخ: {sale_date}\\n"
                    report += f"   المبلغ: {sale_total:.2f}\\n"

                    # حساب الربح المقدر
                    sale_profit = self.calculate_sale_profit(sale)
                    total_profit += sale_profit
                    report += f"   الربح المقدر: {sale_profit:.2f}\\n"
                    report += "-" * 40 + "\\n"

                # الإحصائيات الإجمالية
                report += f"\\n📊 الإحصائيات الإجمالية:\\n"
                report += f"   💰 إجمالي المبيعات: {total_sales:.2f}\\n"
                report += f"   📈 إجمالي الربح المقدر: {total_profit:.2f}\\n"
                report += f"   🧾 عدد الفواتير: {len(self.app.sales)}\\n"
                report += f"   📊 متوسط قيمة الفاتورة: {total_sales/len(self.app.sales):.2f}\\n"

                if total_sales > 0:
                    profit_margin = (total_profit / total_sales) * 100
                    report += f"   📈 هامش الربح: {profit_margin:.1f}%\\n"

                # تحليل أنواع العملاء
                report += f"\\n👥 تحليل أنواع العملاء:\\n"
                for customer_type, data in customer_types.items():
                    percentage = (data['total'] / total_sales) * 100
                    report += f"   {customer_type}: {data['count']} فاتورة، {data['total']:.2f} ({percentage:.1f}%)\\n"

                # أفضل الأيام
                if daily_sales:
                    best_day = max(daily_sales.items(), key=lambda x: x[1]['total'])
                    report += f"\\n🏆 أفضل يوم مبيعات: {best_day[0]} ({best_day[1]['total']:.2f})\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تقرير المبيعات: {e}")

    def show_inventory_report(self):
        """تقرير المخزون المحسن"""
        try:
            self.report_title.config(text="📦 تقرير المخزون الشامل")
            self.reports_text.delete(1.0, tk.END)

            report = "📦 تقرير المخزون الشامل\\n"
            report += "=" * 60 + "\\n\\n"
            report += f"📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\\n\\n"

            if not hasattr(self.app, 'products') or not self.app.products:
                report += "📝 لا توجد منتجات في المخزون.\\n\\n"
                report += "💡 نصائح:\\n"
                report += "• قم بإضافة منتجات من صفحة إدارة المخزون\\n"
                report += "• تأكد من إدخال جميع بيانات المنتج\\n"
            else:
                total_products = len(self.app.products)
                total_value = 0
                low_stock_items = []
                out_of_stock_items = []
                categories = {}

                for product in self.app.products:
                    # حساب القيم
                    stock = product.get('quantity', 0)
                    price = product.get('base_price', 0)
                    product_value = stock * price
                    total_value += product_value

                    # تصنيف المنتجات
                    category = product.get('category', 'غير مصنف')
                    if category not in categories:
                        categories[category] = {'count': 0, 'value': 0}
                    categories[category]['count'] += 1
                    categories[category]['value'] += product_value

                    # فحص المخزون المنخفض
                    min_stock = product.get('min_stock', 10)
                    if stock <= min_stock and stock > 0:
                        low_stock_items.append(product)
                    elif stock == 0:
                        out_of_stock_items.append(product)

                    # تفاصيل المنتج
                    report += f"📦 {product.get('name', 'غير محدد')}\\n"
                    report += f"   الباركود: {product.get('barcode', 'غير محدد')}\\n"
                    report += f"   الفئة: {category}\\n"
                    report += f"   الكمية: {stock}\\n"
                    report += f"   السعر الأساسي: {price:.2f}\\n"
                    report += f"   قيمة المخزون: {product_value:.2f}\\n"

                    # حالة المخزون
                    if stock == 0:
                        report += f"   ⚠️ الحالة: نفد المخزون\\n"
                    elif stock <= min_stock:
                        report += f"   🔶 الحالة: مخزون منخفض\\n"
                    else:
                        report += f"   ✅ الحالة: متوفر\\n"

                    report += "-" * 40 + "\\n"

                # الإحصائيات الإجمالية
                report += f"\\n📊 الإحصائيات الإجمالية:\\n"
                report += f"   📦 إجمالي المنتجات: {total_products}\\n"
                report += f"   💰 إجمالي قيمة المخزون: {total_value:.2f}\\n"
                report += f"   ⚠️ منتجات نفد مخزونها: {len(out_of_stock_items)}\\n"
                report += f"   🔶 منتجات مخزونها منخفض: {len(low_stock_items)}\\n"

                # تحليل الفئات
                report += f"\\n📂 تحليل الفئات:\\n"
                for category, data in categories.items():
                    percentage = (data['value'] / total_value) * 100 if total_value > 0 else 0
                    report += f"   {category}: {data['count']} منتج، {data['value']:.2f} ({percentage:.1f}%)\\n"

                # تحذيرات المخزون
                if out_of_stock_items:
                    report += f"\\n⚠️ منتجات نفد مخزونها:\\n"
                    for product in out_of_stock_items[:5]:  # أول 5 منتجات
                        report += f"   • {product.get('name', 'غير محدد')}\\n"

                if low_stock_items:
                    report += f"\\n🔶 منتجات مخزونها منخفض:\\n"
                    for product in low_stock_items[:5]:  # أول 5 منتجات
                        report += f"   • {product.get('name', 'غير محدد')} (الكمية: {product.get('quantity', 0)})\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في تقرير المخزون: {e}")

    def calculate_sale_profit(self, sale):
        """حساب ربح المبيعة"""
        try:
            profit = 0
            for item in sale.get('items', []):
                # البحث عن المنتج الأصلي
                for product in getattr(self.app, 'products', []):
                    if product.get('name') == item.get('name'):
                        cost_price = product.get('base_price', 0)
                        selling_price = item.get('unit_price', 0)
                        quantity = item.get('quantity', 0)
                        item_profit = (selling_price - cost_price) * quantity
                        profit += item_profit
                        break
            return profit
        except:
            return 0
'''

        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        methods_file = os.path.join(desktop_path, "تقارير_محسنة_طرق.py")

        with open(methods_file, 'w', encoding='utf-8') as f:
            f.write(reports_methods)

        print(f"✅ تم إنشاء طرق التقارير: {os.path.basename(methods_file)}")
        return methods_file

    except Exception as e:
        print(f"❌ فشل في إنشاء طرق التقارير: {e}")
        return None

def create_standalone_reports_window():
    """Create standalone enhanced reports window"""
    try:
        print("\n🪟 إنشاء نافذة التقارير المستقلة...")

        standalone_reports = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Standalone Enhanced Reports Window
نافذة التقارير المحسنة المستقلة

Independent reports window with advanced analytics
نافذة تقارير مستقلة مع تحليلات متقدمة
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime, timedelta

class StandaloneReportsWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام التقارير المحسن - ProTech")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f8f9fa')

        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (900 // 2)
        self.root.geometry(f"1400x900+{x}+{y}")

        # Load data
        self.load_protech_data()

        # Create interface
        self.create_interface()

    def load_protech_data(self):
        """تحميل بيانات ProTech"""
        try:
            data_file = "protech_simple_data.json"
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.suppliers = data.get('suppliers', [])
                self.sales = data.get('sales', [])

                print(f"✅ تم تحميل البيانات: {len(self.products)} منتج، {len(self.sales)} مبيعة")
            else:
                self.products = []
                self.customers = []
                self.suppliers = []
                self.sales = []
                print("⚠️ لم يتم العثور على ملف البيانات")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            self.products = []
            self.customers = []
            self.suppliers = []
            self.sales = []

    def create_interface(self):
        """إنشاء الواجهة"""
        # العنوان الرئيسي
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=100)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="📊 نظام التقارير المحسن - ProTech",
                              font=("Arial", 20, "bold"), bg='#2c3e50', fg='white')
        title_label.pack(expand=True)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # الشريط الجانبي
        sidebar_frame = tk.Frame(main_frame, bg='white', width=300)
        sidebar_frame.pack(side='left', fill='y', padx=(0, 20))
        sidebar_frame.pack_propagate(False)

        # منطقة المحتوى
        content_frame = tk.Frame(main_frame, bg='white')
        content_frame.pack(side='right', fill='both', expand=True)

        # إنشاء المكونات
        self.create_sidebar(sidebar_frame)
        self.create_content_area(content_frame)

        # عرض الإحصائيات السريعة في البداية
        self.show_quick_stats()

    def create_sidebar(self, sidebar):
        """إنشاء الشريط الجانبي"""
        # عنوان الشريط الجانبي
        sidebar_title = tk.Label(sidebar, text="📋 أنواع التقارير",
                               font=("Arial", 16, "bold"), bg='white', fg='#2c3e50')
        sidebar_title.pack(pady=20)

        # أزرار التقارير
        reports_buttons = [
            ("📊 إحصائيات سريعة", self.show_quick_stats, '#3498db'),
            ("📈 تقرير المبيعات", self.show_sales_report, '#27ae60'),
            ("📦 تقرير المخزون", self.show_inventory_report, '#e74c3c'),
            ("👥 تقرير العملاء", self.show_customers_report, '#9b59b6'),
            ("🏢 تقرير الموردين", self.show_suppliers_report, '#f39c12'),
            ("💰 تحليل الأرباح", self.show_profit_analysis, '#1abc9c'),
            ("📅 التقارير الشهرية", self.show_monthly_reports, '#34495e'),
            ("🔍 تحليل متقدم", self.show_advanced_analytics, '#e67e22'),
            ("📋 تقرير شامل", self.show_comprehensive_report, '#8e44ad'),
            ("🖨️ طباعة وتصدير", self.export_reports, '#95a5a6')
        ]

        for button_text, command, color in reports_buttons:
            btn = tk.Button(sidebar, text=button_text,
                          font=("Arial", 12), bg=color, fg='white',
                          width=28, height=2, relief='flat',
                          command=command)
            btn.pack(pady=8, padx=15, fill='x')

            # تأثير hover
            def on_enter(e, b=btn, c=color):
                b.config(bg=self.darken_color(c))
            def on_leave(e, b=btn, c=color):
                b.config(bg=c)

            btn.bind("<Enter>", on_enter)
            btn.bind("<Leave>", on_leave)

        # معلومات البيانات
        info_frame = tk.Frame(sidebar, bg='#ecf0f1')
        info_frame.pack(fill='x', side='bottom', padx=15, pady=15)

        tk.Label(info_frame, text="📊 إحصائيات البيانات",
                font=("Arial", 10, "bold"), bg='#ecf0f1', fg='#2c3e50').pack()

        tk.Label(info_frame, text=f"📦 المنتجات: {len(self.products)}",
                font=("Arial", 9), bg='#ecf0f1', fg='#34495e').pack()
        tk.Label(info_frame, text=f"👥 العملاء: {len(self.customers)}",
                font=("Arial", 9), bg='#ecf0f1', fg='#34495e').pack()
        tk.Label(info_frame, text=f"💰 المبيعات: {len(self.sales)}",
                font=("Arial", 9), bg='#ecf0f1', fg='#34495e').pack()
        tk.Label(info_frame, text=f"🏢 الموردين: {len(self.suppliers)}",
                font=("Arial", 9), bg='#ecf0f1', fg='#34495e').pack()

    def create_content_area(self, content_frame):
        """إنشاء منطقة المحتوى"""
        # عنوان التقرير
        self.report_header = tk.Frame(content_frame, bg='#ecf0f1', height=80)
        self.report_header.pack(fill='x', padx=15, pady=15)
        self.report_header.pack_propagate(False)

        self.report_title = tk.Label(self.report_header, text="اختر نوع التقرير من القائمة الجانبية",
                                   font=("Arial", 18, "bold"), bg='#ecf0f1', fg='#2c3e50')
        self.report_title.pack(expand=True)

        # منطقة المحتوى الرئيسي
        content_main = tk.Frame(content_frame, bg='white')
        content_main.pack(fill='both', expand=True, padx=15, pady=(0, 15))

        # منطقة النص مع شريط التمرير
        text_frame = tk.Frame(content_main, bg='white')
        text_frame.pack(fill='both', expand=True)

        scrollbar = tk.Scrollbar(text_frame)
        scrollbar.pack(side='right', fill='y')

        self.reports_text = tk.Text(text_frame, font=("Arial", 11),
                                  bg='#fafbfc', fg='#2c3e50',
                                  yscrollcommand=scrollbar.set,
                                  wrap='word', padx=25, pady=25)
        self.reports_text.pack(fill='both', expand=True)
        scrollbar.config(command=self.reports_text.yview)

        # شريط الحالة
        status_frame = tk.Frame(content_frame, bg='#bdc3c7', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(status_frame, text="جاهز",
                                   font=("Arial", 9), bg='#bdc3c7', fg='#2c3e50')
        self.status_label.pack(side='left', padx=15, pady=5)

        time_label = tk.Label(status_frame, text=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            font=("Arial", 9), bg='#bdc3c7', fg='#2c3e50')
        time_label.pack(side='right', padx=15, pady=5)

    def darken_color(self, color):
        """تغميق اللون للتأثير"""
        color_map = {
            '#3498db': '#2980b9',
            '#27ae60': '#229954',
            '#e74c3c': '#c0392b',
            '#9b59b6': '#8e44ad',
            '#f39c12': '#e67e22',
            '#1abc9c': '#16a085',
            '#34495e': '#2c3e50',
            '#e67e22': '#d35400',
            '#8e44ad': '#7d3c98',
            '#95a5a6': '#7f8c8d'
        }
        return color_map.get(color, color)

    def run(self):
        """تشغيل النافذة"""
        self.root.mainloop()

if __name__ == "__main__":
    app = StandaloneReportsWindow()
    app.run()
'''

        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        standalone_file = os.path.join(desktop_path, "نافذة_التقارير_المحسنة.py")

        with open(standalone_file, 'w', encoding='utf-8') as f:
            f.write(standalone_reports)

        print(f"✅ تم إنشاء النافذة المستقلة: {os.path.basename(standalone_file)}")
        return standalone_file

    except Exception as e:
        print(f"❌ فشل في إنشاء النافذة المستقلة: {e}")
        return None

def main():
    """Main function"""
    print("📊 تحسين أداء صفحة التقارير في ProTech")
    print("📊 Enhancing ProTech Reports Page Performance")
    print("="*60)

    print("\n💡 التحسينات المطبقة:")
    print("• واجهة تقارير محسنة ومتقدمة")
    print("• تقارير تفصيلية للمبيعات والمخزون")
    print("• تحليلات متقدمة للبيانات")
    print("• إحصائيات سريعة ومرئية")
    print("• نافذة تقارير مستقلة")
    print("• تصدير وطباعة التقارير")

    created_items = []

    # Create enhanced reports system
    if create_enhanced_reports_system():
        created_items.append("نظام التقارير المحسن في ProTech")

    # Create reports methods
    methods = create_reports_methods()
    if methods:
        created_items.append("طرق التقارير المحسنة")

    # Create standalone reports window
    standalone = create_standalone_reports_window()
    if standalone:
        created_items.append("نافذة التقارير المستقلة")

    # Summary
    print("\n" + "="*60)
    print("📊 ملخص التحسينات:")

    if created_items:
        print(f"✅ تم تطبيق {len(created_items)} تحسين:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم تطبيق أي تحسينات")

    print("\n📈 الميزات الجديدة:")
    print("• تقارير مبيعات تفصيلية مع حساب الأرباح")
    print("• تقارير مخزون شاملة مع تحليل الفئات")
    print("• إحصائيات سريعة ومرئية")
    print("• واجهة محسنة وسهلة الاستخدام")
    print("• تحليلات متقدمة للبيانات")
    print("• نافذة تقارير مستقلة")

    print("\n🎯 كيفية الاستخدام:")

    if "نظام التقارير المحسن في ProTech" in created_items:
        print("✅ في ProTech الأصلي:")
        print("  1. افتح ProTech")
        print("  2. اذهب إلى صفحة التقارير")
        print("  3. استمتع بالواجهة المحسنة")

    if "نافذة التقارير المستقلة" in created_items:
        print("✅ النافذة المستقلة:")
        print("  1. شغل 'نافذة_التقارير_المحسنة.py'")
        print("  2. اختر نوع التقرير من القائمة الجانبية")
        print("  3. استعرض التقارير التفصيلية")

    print("\n📊 أنواع التقارير المتاحة:")
    print("• 📈 تقرير المبيعات التفصيلي")
    print("• 📦 تقرير المخزون الشامل")
    print("• 👥 تقرير العملاء")
    print("• 🏢 تقرير الموردين")
    print("• 💰 تحليل الأرباح")
    print("• 📅 التقارير الشهرية")
    print("• 🔍 التحليلات المتقدمة")
    print("• 📋 التقرير الشامل")

    print("\n💡 نصائح للاستخدام الأمثل:")
    print("• تأكد من وجود بيانات كافية للتقارير")
    print("• احفظ البيانات بانتظام")
    print("• استخدم النافذة المستقلة للتحليل المتقدم")
    print("• راجع التقارير بانتظام لمتابعة الأداء")

    print("\n🎉 تم تحسين أداء صفحة التقارير بنجاح!")

    if len(created_items) >= 2:
        print("✅ لديك الآن نظام تقارير متقدم وشامل")
    else:
        print("⚠️ قد تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()