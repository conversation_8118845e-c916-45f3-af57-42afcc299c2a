# 🏢 برنامج ناصر النهائي للمحاسبة / Nassar Final Accounting Program

## 📋 نظرة عامة / Overview

برنامج ناصر للمحاسبة هو نظام محاسبة شامل ومتقدم مصمم خصيصاً لإدارة الأعمال التجارية. يوفر البرنامج واجهة ثنائية اللغة (العربية والإنجليزية) مع جميع الميزات المطلوبة لإدارة المخزون والعملاء والمبيعات والتقارير.

Nassar Accounting Program is a comprehensive and advanced accounting system designed specifically for business management. The program provides a bilingual interface (Arabic and English) with all the features required for inventory, customer, sales, and reporting management.

## 🎯 الميزات الرئيسية / Key Features

### 📦 إدارة المخزون / Inventory Management
- ✅ إضافة وتعديل المنتجات مع الباركود
- ✅ تتبع المخزون والكميات
- ✅ أسعار متعددة حسب نوع العميل
- ✅ تنبيهات المخزون المنخفض
- ✅ مسح الباركود المتقدم

### 👥 إدارة العملاء / Customer Management
- ✅ تصنيف العملاء (تجزئة، جملة، موزع معتمد، صاحب محل)
- ✅ تتبع الأرصدة والديون
- ✅ معلومات الاتصال الكاملة
- ✅ تاريخ المعاملات

### 🏢 إدارة الموردين / Supplier Management
- ✅ معلومات الموردين الشاملة
- ✅ تتبع المشتريات
- ✅ معلومات الاتصال والعناوين

### 💰 نظام المبيعات / Sales System
- ✅ إنشاء فواتير المبيعات
- ✅ حساب الأسعار التلقائي حسب نوع العميل
- ✅ تتبع المدفوعات والأرصدة
- ✅ طباعة الفواتير

### 📊 التقارير والإحصائيات / Reports & Statistics
- ✅ تقارير المبيعات اليومية والشهرية
- ✅ تقارير المخزون والأرباح
- ✅ أفضل المنتجات مبيعاً
- ✅ تقارير العملاء والأرصدة

## 📁 هيكل المجلدات / Folder Structure

```
nassar program final/
├── 01_Main_Program/           # البرنامج الرئيسي / Main Program
│   └── protech_simple_working.py
├── 02_Data_Files/             # ملفات البيانات / Data Files
│   └── protech_simple_data.json
├── 03_Launchers/              # مشغلات البرنامج / Program Launchers
│   └── Quick_Launch_Nassar.py
├── 04_Tools_Utilities/        # الأدوات المساعدة / Utility Tools
│   ├── Data_Backup_Tool.py
│   └── Data_Checker_Tool.py
├── 05_Backups/               # النسخ الاحتياطية / Backups
├── 06_Documentation/         # الوثائق / Documentation
└── START_NASSAR_PROGRAM.py   # المشغل الرئيسي / Main Launcher
```

## 🚀 كيفية التشغيل / How to Run

### الطريقة الأولى (الموصى بها) / Method 1 (Recommended)
1. اضغط مرتين على `START_NASSAR_PROGRAM.py`
2. اتبع التعليمات في نافذة الترحيب
3. Double-click `START_NASSAR_PROGRAM.py`
4. Follow the instructions in the welcome window

### الطريقة الثانية (التشغيل السريع) / Method 2 (Quick Launch)
1. اذهب إلى مجلد `03_Launchers`
2. اضغط مرتين على `Quick_Launch_Nassar.py`
3. Go to `03_Launchers` folder
4. Double-click `Quick_Launch_Nassar.py`

### الطريقة الثالثة (التشغيل المباشر) / Method 3 (Direct Launch)
1. اذهب إلى مجلد `01_Main_Program`
2. اضغط مرتين على `protech_simple_working.py`
3. Go to `01_Main_Program` folder
4. Double-click `protech_simple_working.py`

## 🛠️ الأدوات المساعدة / Utility Tools

### 💾 أداة النسخ الاحتياطي / Data Backup Tool
- إنشاء نسخ احتياطية من البيانات
- استعادة البيانات من النسخ الاحتياطية
- عرض معلومات النسخ الاحتياطية
- الموقع: `04_Tools_Utilities/Data_Backup_Tool.py`

### 🔍 أداة فحص البيانات / Data Checker Tool
- فحص سلامة البيانات
- تحليل الإحصائيات
- تقارير مفصلة عن البيانات
- الموقع: `04_Tools_Utilities/Data_Checker_Tool.py`

## 📊 البيانات النموذجية / Sample Data

البرنامج يأتي مع بيانات نموذجية تشمل:
The program comes with sample data including:

- **7 منتجات إلكترونية / 7 Electronic Products**
- **3 عملاء بأنواع مختلفة / 3 Customers of Different Types**
- **3 موردين / 3 Suppliers**
- **فواتير مبيعات نموذجية / Sample Sales Invoices**

## 🔧 استكشاف الأخطاء / Troubleshooting

### إذا لم تظهر البيانات / If Data Doesn't Show
1. اذهب إلى صفحة المخزون في البرنامج
2. اضغط الزر الأحمر "🔄 إعادة تحميل البيانات"
3. Go to Inventory page in the program
4. Click the red "🔄 Force Reload Data" button

### إذا فقدت البيانات / If Data is Lost
1. استخدم أداة النسخ الاحتياطي لاستعادة البيانات
2. أو انسخ ملف البيانات من مجلد `02_Data_Files`
3. Use the Backup Tool to restore data
4. Or copy the data file from `02_Data_Files` folder

### مشاكل التشغيل / Launch Issues
1. تأكد من تثبيت Python على النظام
2. انقر بالزر الأيمن على الملف واختر "Open with Python"
3. Make sure Python is installed on the system
4. Right-click the file and choose "Open with Python"

## 📞 الدعم الفني / Technical Support

للحصول على الدعم الفني أو الإبلاغ عن مشاكل:
For technical support or to report issues:

- استخدم أدوات الفحص والنسخ الاحتياطي المدمجة
- تحقق من ملفات السجل في مجلد البرنامج
- Use the built-in checking and backup tools
- Check log files in the program folder

## 📝 ملاحظات مهمة / Important Notes

### الأمان / Security
- ✅ يتم حفظ البيانات تلقائياً كل 5 دقائق
- ✅ نسخ احتياطية تلقائية عند الإغلاق
- ✅ حماية من فقدان البيانات
- ✅ Data is automatically saved every 5 minutes
- ✅ Automatic backups on program close
- ✅ Data loss protection

### الأداء / Performance
- ✅ محسن للأداء السريع
- ✅ إدارة ذاكرة متقدمة
- ✅ واجهة مستخدم سريعة الاستجابة
- ✅ Optimized for fast performance
- ✅ Advanced memory management
- ✅ Responsive user interface

## 🎉 شكر وتقدير / Acknowledgments

تم تطوير هذا البرنامج بواسطة Augment Agent مع التركيز على:
This program was developed by Augment Agent with focus on:

- سهولة الاستخدام / Ease of use
- الموثوقية والاستقرار / Reliability and stability
- الأداء المحسن / Optimized performance
- الدعم ثنائي اللغة / Bilingual support

---

**🏢 برنامج ناصر للمحاسبة - الحل الشامل لإدارة أعمالك**
**Nassar Accounting Program - Complete Solution for Your Business Management**
