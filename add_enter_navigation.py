#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Enter Navigation to Product Form
إضافة التنقل بالإنتر في نموذج المنتج

Add Enter key navigation between fields in add product window
إضافة التنقل بمفتاح الإنتر بين الحقول في نافذة إضافة المنتج
"""

import os
import shutil
from datetime import datetime

def add_enter_navigation_to_protech():
    """Add Enter key navigation to ProTech product form"""
    try:
        print("⌨️ إضافة التنقل بالإنتر في نموذج المنتج")
        print("⌨️ Adding Enter Navigation to Product Form")
        print("="*50)
        
        # Find ProTech file
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.enter_navigation_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Enter navigation code
        enter_navigation_code = '''
# ===== ENTER NAVIGATION SYSTEM - START =====
# نظام التنقل بالإنتر - البداية

def setup_enter_navigation(self, window, entry_widgets):
    """Setup Enter key navigation between entry widgets"""
    try:
        # Create list of entry widgets in order
        if not isinstance(entry_widgets, list):
            entry_widgets = list(entry_widgets)
        
        def on_enter_key(event, current_index):
            """Handle Enter key press"""
            try:
                # Move to next field
                next_index = (current_index + 1) % len(entry_widgets)
                next_widget = entry_widgets[next_index]
                
                # Focus on next widget
                next_widget.focus_set()
                
                # If it's an Entry widget, select all text
                if hasattr(next_widget, 'select_range'):
                    next_widget.select_range(0, 'end')
                
                return "break"  # Prevent default Enter behavior
                
            except Exception as e:
                print(f"خطأ في التنقل: {e}")
                return None
        
        # Bind Enter key to each entry widget
        for i, entry_widget in enumerate(entry_widgets):
            if entry_widget and hasattr(entry_widget, 'bind'):
                entry_widget.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
                entry_widget.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))  # Numpad Enter
        
        # Also bind Tab key for additional navigation
        def on_tab_key(event, current_index):
            """Handle Tab key press"""
            try:
                next_index = (current_index + 1) % len(entry_widgets)
                entry_widgets[next_index].focus_set()
                return "break"
            except:
                return None
        
        for i, entry_widget in enumerate(entry_widgets):
            if entry_widget and hasattr(entry_widget, 'bind'):
                entry_widget.bind('<Tab>', lambda event, idx=i: on_tab_key(event, idx))
        
        # Focus on first widget
        if entry_widgets and entry_widgets[0]:
            entry_widgets[0].focus_set()
        
        print("✅ تم تفعيل التنقل بالإنتر")
        
    except Exception as e:
        print(f"❌ خطأ في إعداد التنقل بالإنتر: {e}")

def setup_form_shortcuts(self, window, save_button=None, cancel_button=None):
    """Setup keyboard shortcuts for form"""
    try:
        # Ctrl+S to save
        if save_button:
            window.bind('<Control-s>', lambda event: save_button.invoke())
            window.bind('<Control-S>', lambda event: save_button.invoke())
        
        # Escape to cancel
        if cancel_button:
            window.bind('<Escape>', lambda event: cancel_button.invoke())
        
        # F1 for help (optional)
        def show_help(event):
            help_text = """
مفاتيح الاختصار:
• Enter: الانتقال للحقل التالي
• Tab: الانتقال للحقل التالي
• Ctrl+S: حفظ
• Escape: إلغاء
• F1: عرض هذه المساعدة
            """
            import tkinter.messagebox as messagebox
            messagebox.showinfo("مساعدة", help_text)
        
        window.bind('<F1>', show_help)
        
        print("✅ تم تفعيل اختصارات لوحة المفاتيح")
        
    except Exception as e:
        print(f"❌ خطأ في إعداد الاختصارات: {e}")

def enhanced_add_product_window(self):
    """Enhanced add product window with Enter navigation"""
    try:
        # Create window
        add_window = tk.Toplevel(self.root)
        add_window.title("إضافة منتج جديد")
        add_window.geometry("500x600")
        add_window.resizable(False, False)
        add_window.transient(self.root)
        add_window.grab_set()
        
        # Center window
        add_window.update_idletasks()
        x = (add_window.winfo_screenwidth() // 2) - (500 // 2)
        y = (add_window.winfo_screenheight() // 2) - (600 // 2)
        add_window.geometry(f"500x600+{x}+{y}")
        
        # Main frame
        main_frame = tk.Frame(add_window, bg='white', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="إضافة منتج جديد", 
                              font=("Arial", 16, "bold"), bg='white', fg='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # Entry widgets list for navigation
        entry_widgets = []
        
        # Barcode field
        barcode_frame = tk.Frame(main_frame, bg='white')
        barcode_frame.pack(fill='x', pady=5)
        tk.Label(barcode_frame, text="الباركود:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        barcode_entry = tk.Entry(barcode_frame, font=("Arial", 10), width=30)
        barcode_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(barcode_entry)
        
        # Product name field
        name_frame = tk.Frame(main_frame, bg='white')
        name_frame.pack(fill='x', pady=5)
        tk.Label(name_frame, text="اسم المنتج:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        name_entry = tk.Entry(name_frame, font=("Arial", 10), width=30)
        name_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(name_entry)
        
        # Category field
        category_frame = tk.Frame(main_frame, bg='white')
        category_frame.pack(fill='x', pady=5)
        tk.Label(category_frame, text="الفئة:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        category_entry = tk.Entry(category_frame, font=("Arial", 10), width=30)
        category_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(category_entry)
        
        # Supplier field
        supplier_frame = tk.Frame(main_frame, bg='white')
        supplier_frame.pack(fill='x', pady=5)
        tk.Label(supplier_frame, text="المورد:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        supplier_entry = tk.Entry(supplier_frame, font=("Arial", 10), width=30)
        supplier_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(supplier_entry)
        
        # Phone field
        phone_frame = tk.Frame(main_frame, bg='white')
        phone_frame.pack(fill='x', pady=5)
        tk.Label(phone_frame, text="رقم الهاتف:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        phone_entry = tk.Entry(phone_frame, font=("Arial", 10), width=30)
        phone_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(phone_entry)
        
        # Unit field
        unit_frame = tk.Frame(main_frame, bg='white')
        unit_frame.pack(fill='x', pady=5)
        tk.Label(unit_frame, text="الوحدة:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        unit_entry = tk.Entry(unit_frame, font=("Arial", 10), width=30)
        unit_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(unit_entry)
        
        # Quantity field
        quantity_frame = tk.Frame(main_frame, bg='white')
        quantity_frame.pack(fill='x', pady=5)
        tk.Label(quantity_frame, text="الكمية:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        quantity_entry = tk.Entry(quantity_frame, font=("Arial", 10), width=30)
        quantity_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(quantity_entry)
        
        # Base price field
        base_price_frame = tk.Frame(main_frame, bg='white')
        base_price_frame.pack(fill='x', pady=5)
        tk.Label(base_price_frame, text="السعر الأساسي:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        base_price_entry = tk.Entry(base_price_frame, font=("Arial", 10), width=30)
        base_price_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(base_price_entry)
        
        # Customer type prices
        shop_owner_frame = tk.Frame(main_frame, bg='white')
        shop_owner_frame.pack(fill='x', pady=5)
        tk.Label(shop_owner_frame, text="سعر صاحب محل:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        shop_owner_entry = tk.Entry(shop_owner_frame, font=("Arial", 10), width=30)
        shop_owner_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(shop_owner_entry)
        
        distributor_frame = tk.Frame(main_frame, bg='white')
        distributor_frame.pack(fill='x', pady=5)
        tk.Label(distributor_frame, text="سعر موزع معتمد:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        distributor_entry = tk.Entry(distributor_frame, font=("Arial", 10), width=30)
        distributor_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(distributor_entry)
        
        wholesale_frame = tk.Frame(main_frame, bg='white')
        wholesale_frame.pack(fill='x', pady=5)
        tk.Label(wholesale_frame, text="سعر جملة:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        wholesale_entry = tk.Entry(wholesale_frame, font=("Arial", 10), width=30)
        wholesale_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(wholesale_entry)
        
        retail_frame = tk.Frame(main_frame, bg='white')
        retail_frame.pack(fill='x', pady=5)
        tk.Label(retail_frame, text="سعر تجزئة:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        retail_entry = tk.Entry(retail_frame, font=("Arial", 10), width=30)
        retail_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(retail_entry)
        
        # Notes field
        notes_frame = tk.Frame(main_frame, bg='white')
        notes_frame.pack(fill='x', pady=5)
        tk.Label(notes_frame, text="ملاحظات:", font=("Arial", 10), 
                bg='white', width=15, anchor='e').pack(side='right')
        notes_entry = tk.Entry(notes_frame, font=("Arial", 10), width=30)
        notes_entry.pack(side='right', padx=(10, 0))
        entry_widgets.append(notes_entry)
        
        # Buttons frame
        buttons_frame = tk.Frame(main_frame, bg='white')
        buttons_frame.pack(fill='x', pady=20)
        
        # Save button
        save_button = tk.Button(buttons_frame, text="حفظ المنتج", 
                               font=("Arial", 12, "bold"), bg='#27ae60', fg='white',
                               width=15, height=2,
                               command=lambda: self.save_new_product(
                                   add_window, barcode_entry, name_entry, category_entry,
                                   supplier_entry, phone_entry, unit_entry, quantity_entry,
                                   base_price_entry, shop_owner_entry, distributor_entry,
                                   wholesale_entry, retail_entry, notes_entry))
        save_button.pack(side='left', padx=10)
        
        # Cancel button
        cancel_button = tk.Button(buttons_frame, text="إلغاء", 
                                 font=("Arial", 12), bg='#e74c3c', fg='white',
                                 width=15, height=2,
                                 command=add_window.destroy)
        cancel_button.pack(side='right', padx=10)
        
        # Setup Enter navigation
        self.setup_enter_navigation(add_window, entry_widgets)
        
        # Setup keyboard shortcuts
        self.setup_form_shortcuts(add_window, save_button, cancel_button)
        
        # Instructions label
        instructions = tk.Label(main_frame, 
                               text="استخدم Enter للانتقال بين الحقول • Ctrl+S للحفظ • Escape للإلغاء • F1 للمساعدة",
                               font=("Arial", 8), bg='white', fg='#7f8c8d')
        instructions.pack(pady=(10, 0))
        
        return add_window
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نافذة إضافة المنتج: {e}")
        return None

# ===== ENTER NAVIGATION SYSTEM - END =====
# نظام التنقل بالإنتر - النهاية

'''
        
        # Add the navigation code after existing fixes
        if "DOUBLE CLICK FIX - END" in content:
            insert_pos = content.find("DOUBLE CLICK FIX - END") + len("DOUBLE CLICK FIX - END")
            content = content[:insert_pos] + enter_navigation_code + content[insert_pos:]
        else:
            content = enter_navigation_code + content
        
        # Replace the add_product method if it exists
        if "def add_product(" in content:
            # Find and replace the add_product method
            method_start = content.find("def add_product(")
            if method_start != -1:
                # Find the end of the method
                method_end = content.find("\n    def ", method_start + 1)
                if method_end == -1:
                    method_end = content.find("\nclass ", method_start + 1)
                if method_end == -1:
                    method_end = len(content)
                
                # Replace with enhanced version
                enhanced_method = '''def add_product(self):
        """Enhanced add product with Enter navigation"""
        try:
            self.enhanced_add_product_window()
        except Exception as e:
            print(f"خطأ في إضافة المنتج: {e}")
            import tkinter.messagebox as messagebox
            messagebox.showerror("خطأ", f"فشل في فتح نافذة إضافة المنتج: {e}")'''
                
                content = content[:method_start] + enhanced_method + content[method_end:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة التنقل بالإنتر")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة التنقل بالإنتر: {e}")
        return False

def create_navigation_test_window():
    """Create a test window to demonstrate Enter navigation"""
    try:
        print("\n🧪 إنشاء نافذة اختبار التنقل...")
        
        test_window_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enter Navigation Test Window
نافذة اختبار التنقل بالإنتر
"""

import tkinter as tk
from tkinter import messagebox

class NavigationTestWindow:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("اختبار التنقل بالإنتر")
        self.root.geometry("400x500")
        self.root.resizable(False, False)
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.root.winfo_screenheight() // 2) - (500 // 2)
        self.root.geometry(f"400x500+{x}+{y}")
        
        self.create_widgets()
        self.setup_navigation()
    
    def create_widgets(self):
        """Create test widgets"""
        main_frame = tk.Frame(self.root, bg='white', padx=20, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # Title
        title_label = tk.Label(main_frame, text="اختبار التنقل بالإنتر", 
                              font=("Arial", 16, "bold"), bg='white')
        title_label.pack(pady=(0, 20))
        
        # Entry widgets
        self.entry_widgets = []
        
        fields = [
            "الحقل الأول", "الحقل الثاني", "الحقل الثالث", 
            "الحقل الرابع", "الحقل الخامس", "الحقل السادس"
        ]
        
        for field_name in fields:
            frame = tk.Frame(main_frame, bg='white')
            frame.pack(fill='x', pady=5)
            
            label = tk.Label(frame, text=f"{field_name}:", 
                           font=("Arial", 10), bg='white', width=12, anchor='e')
            label.pack(side='right')
            
            entry = tk.Entry(frame, font=("Arial", 10), width=25)
            entry.pack(side='right', padx=(10, 0))
            self.entry_widgets.append(entry)
        
        # Instructions
        instructions = tk.Label(main_frame, 
                               text="استخدم Enter أو Tab للانتقال بين الحقول\\nCtrl+S للحفظ • Escape للإغلاق",
                               font=("Arial", 9), bg='white', fg='blue')
        instructions.pack(pady=20)
        
        # Buttons
        button_frame = tk.Frame(main_frame, bg='white')
        button_frame.pack(pady=10)
        
        self.save_button = tk.Button(button_frame, text="حفظ", 
                                   font=("Arial", 12), bg='green', fg='white',
                                   command=self.save_data)
        self.save_button.pack(side='left', padx=10)
        
        self.cancel_button = tk.Button(button_frame, text="إغلاق", 
                                     font=("Arial", 12), bg='red', fg='white',
                                     command=self.root.destroy)
        self.cancel_button.pack(side='right', padx=10)
    
    def setup_navigation(self):
        """Setup Enter key navigation"""
        def on_enter_key(event, current_index):
            try:
                next_index = (current_index + 1) % len(self.entry_widgets)
                next_widget = self.entry_widgets[next_index]
                next_widget.focus_set()
                next_widget.select_range(0, 'end')
                return "break"
            except Exception as e:
                print(f"خطأ في التنقل: {e}")
                return None
        
        # Bind Enter key to each entry
        for i, entry in enumerate(self.entry_widgets):
            entry.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
            entry.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))
        
        # Keyboard shortcuts
        self.root.bind('<Control-s>', lambda event: self.save_data())
        self.root.bind('<Control-S>', lambda event: self.save_data())
        self.root.bind('<Escape>', lambda event: self.root.destroy())
        
        # Focus on first entry
        if self.entry_widgets:
            self.entry_widgets[0].focus_set()
    
    def save_data(self):
        """Save test data"""
        values = [entry.get() for entry in self.entry_widgets]
        if any(values):
            messagebox.showinfo("حفظ", f"تم حفظ البيانات:\\n" + "\\n".join(f"الحقل {i+1}: {v}" for i, v in enumerate(values) if v))
        else:
            messagebox.showwarning("تحذير", "لا توجد بيانات للحفظ")
    
    def run(self):
        """Run the test window"""
        self.root.mainloop()

if __name__ == "__main__":
    app = NavigationTestWindow()
    app.run()
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        test_file = os.path.join(desktop_path, "اختبار_التنقل_بالإنتر.py")
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_window_code)
        
        print(f"✅ تم إنشاء نافذة الاختبار: {os.path.basename(test_file)}")
        return test_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء نافذة الاختبار: {e}")
        return None

def main():
    """Main function"""
    print("⌨️ إضافة التنقل بالإنتر في نموذج المنتج")
    print("⌨️ Adding Enter Navigation to Product Form")
    print("="*60)
    
    print("\n💡 الميزات المضافة:")
    print("• التنقل بين الحقول باستخدام Enter")
    print("• التنقل بين الحقول باستخدام Tab")
    print("• Ctrl+S للحفظ السريع")
    print("• Escape للإلغاء")
    print("• F1 لعرض المساعدة")
    print("• تحديد النص تلقائياً عند الانتقال")
    
    created_items = []
    
    # Add Enter navigation to ProTech
    if add_enter_navigation_to_protech():
        created_items.append("التنقل بالإنتر في ProTech")
    
    # Create test window
    test_window = create_navigation_test_window()
    if test_window:
        created_items.append("نافذة اختبار التنقل")
    
    # Summary
    print("\n" + "="*60)
    print("📊 ملخص التحسينات:")
    
    if created_items:
        print(f"✅ تم إضافة {len(created_items)} تحسين:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم إضافة أي تحسينات")
    
    print("\n⌨️ كيفية الاستخدام:")
    print("1. افتح ProTech")
    print("2. اذهب إلى إضافة منتج جديد")
    print("3. استخدم Enter للانتقال بين الحقول")
    print("4. استخدم Ctrl+S للحفظ السريع")
    print("5. استخدم Escape للإلغاء")
    
    print("\n🧪 لاختبار الميزة:")
    print("• شغل 'اختبار_التنقل_بالإنتر.py' لاختبار التنقل")
    print("• جرب جميع المفاتيح المختلفة")
    
    print("\n🎉 تم إضافة التنقل بالإنتر بنجاح!")
    
    if len(created_items) >= 1:
        print("✅ يمكنك الآن استخدام Enter للتنقل في النماذج")
    else:
        print("⚠️ قد تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()
