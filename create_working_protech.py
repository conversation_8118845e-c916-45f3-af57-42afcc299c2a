#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Working ProTech - Final Solution
إنشاء ProTech العامل - الحل النهائي

Create a completely working version of ProTech from scratch
إنشاء نسخة عاملة تماماً من ProTech من الصفر
"""

import os
import shutil
from datetime import datetime

def create_backup():
    """Create backup of broken file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.broken_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية للملف المكسور: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def create_working_protech():
    """Create a completely working ProTech from scratch"""

    working_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Simple Working - Accounting System
نظام ProTech للمحاسبة - النسخة العاملة

A complete accounting system with inventory, sales, customers, and reports
نظام محاسبة شامل مع المخزون والمبيعات والعملاء والتقارير
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import json
import os
from datetime import datetime
import threading
import time

class ProTechSimpleWorking:
    """ProTech Simple Working Accounting System"""

    def __init__(self):
        """Initialize the application"""
        try:
            # Initialize data structures
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []
            self.loading = False
            self.data_file = "protech_simple_data.json"

            # Create main window
            self.root = tk.Tk()
            self.root.title("🧮 ProTech Accounting System")
            self.root.geometry("1200x800")
            self.root.configure(bg='#f0f8ff')

            # Try to set calculator icon
            try:
                self.root.iconbitmap(default='calculator.ico')
            except:
                pass

            # Create interface
            self.create_interface()

            # Load data after interface is ready
            self.root.after(500, self.load_data)

        except Exception as e:
            print(f"❌ خطأ في التهيئة: {e}")
            # Create minimal interface
            self.root = tk.Tk()
            self.root.title("ProTech - Safe Mode")
            self.suppliers = []
            self.products = []
            self.customers = []
            self.sales = []

    def create_interface(self):
        """Create the main interface"""
        try:
            # Create notebook for tabs
            self.notebook = ttk.Notebook(self.root)
            self.notebook.pack(fill='both', expand=True, padx=10, pady=10)

            # Create tabs
            self.create_suppliers_tab()
            self.create_products_tab()
            self.create_customers_tab()
            self.create_sales_tab()
            self.create_reports_tab()

            # Create status bar
            self.create_status_bar()

        except Exception as e:
            print(f"❌ خطأ في إنشاء الواجهة: {e}")

    def create_suppliers_tab(self):
        """Create suppliers management tab"""
        # Suppliers tab
        suppliers_frame = ttk.Frame(self.notebook)
        self.notebook.add(suppliers_frame, text="🏪 الموردين")

        # Input frame
        input_frame = ttk.LabelFrame(suppliers_frame, text="إضافة مورد جديد")
        input_frame.pack(fill='x', padx=10, pady=5)

        # Supplier name
        ttk.Label(input_frame, text="اسم المورد:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        self.supplier_name = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.supplier_name, width=30).grid(row=0, column=1, padx=5, pady=5)

        # Supplier phone
        ttk.Label(input_frame, text="رقم الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
        self.supplier_phone = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.supplier_phone, width=20).grid(row=0, column=3, padx=5, pady=5)

        # Add button
        ttk.Button(input_frame, text="إضافة مورد", command=self.add_supplier).grid(row=0, column=4, padx=10, pady=5)

        # Suppliers list
        list_frame = ttk.LabelFrame(suppliers_frame, text="قائمة الموردين")
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Treeview for suppliers
        self.suppliers_tree = ttk.Treeview(list_frame, columns=('name', 'phone'), show='headings')
        self.suppliers_tree.heading('name', text='اسم المورد')
        self.suppliers_tree.heading('phone', text='رقم الهاتف')
        self.suppliers_tree.pack(fill='both', expand=True, padx=5, pady=5)

    def create_products_tab(self):
        """Create products management tab"""
        # Products tab
        products_frame = ttk.Frame(self.notebook)
        self.notebook.add(products_frame, text="📦 المنتجات")

        # Input frame
        input_frame = ttk.LabelFrame(products_frame, text="إضافة منتج جديد")
        input_frame.pack(fill='x', padx=10, pady=5)

        # Product name
        ttk.Label(input_frame, text="اسم المنتج:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        self.product_name = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.product_name, width=25).grid(row=0, column=1, padx=5, pady=5)

        # Barcode
        ttk.Label(input_frame, text="الباركود:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
        self.product_barcode = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.product_barcode, width=15).grid(row=0, column=3, padx=5, pady=5)

        # Price
        ttk.Label(input_frame, text="السعر:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        self.product_price = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.product_price, width=15).grid(row=1, column=1, padx=5, pady=5)

        # Stock
        ttk.Label(input_frame, text="المخزون:").grid(row=1, column=2, padx=5, pady=5, sticky='e')
        self.product_stock = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.product_stock, width=15).grid(row=1, column=3, padx=5, pady=5)

        # Add button
        ttk.Button(input_frame, text="إضافة منتج", command=self.add_product).grid(row=1, column=4, padx=10, pady=5)

        # Products list
        list_frame = ttk.LabelFrame(products_frame, text="قائمة المنتجات")
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Treeview for products
        self.products_tree = ttk.Treeview(list_frame, columns=('name', 'barcode', 'price', 'stock'), show='headings')
        self.products_tree.heading('name', text='اسم المنتج')
        self.products_tree.heading('barcode', text='الباركود')
        self.products_tree.heading('price', text='السعر')
        self.products_tree.heading('stock', text='المخزون')
        self.products_tree.pack(fill='both', expand=True, padx=5, pady=5)

    def create_customers_tab(self):
        """Create customers management tab"""
        # Customers tab
        customers_frame = ttk.Frame(self.notebook)
        self.notebook.add(customers_frame, text="👥 العملاء")

        # Input frame
        input_frame = ttk.LabelFrame(customers_frame, text="إضافة عميل جديد")
        input_frame.pack(fill='x', padx=10, pady=5)

        # Customer name
        ttk.Label(input_frame, text="اسم العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        self.customer_name = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)

        # Customer phone
        ttk.Label(input_frame, text="رقم الهاتف:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
        self.customer_phone = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.customer_phone, width=20).grid(row=0, column=3, padx=5, pady=5)

        # Customer type
        ttk.Label(input_frame, text="نوع العميل:").grid(row=1, column=0, padx=5, pady=5, sticky='e')
        self.customer_type = tk.StringVar()
        customer_type_combo = ttk.Combobox(input_frame, textvariable=self.customer_type, width=22)
        customer_type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
        customer_type_combo.grid(row=1, column=1, padx=5, pady=5)
        customer_type_combo.set('تجزئة')

        # Add button
        ttk.Button(input_frame, text="إضافة عميل", command=self.add_customer).grid(row=1, column=3, padx=10, pady=5)

        # Customers list
        list_frame = ttk.LabelFrame(customers_frame, text="قائمة العملاء")
        list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Treeview for customers
        self.customers_tree = ttk.Treeview(list_frame, columns=('name', 'phone', 'type'), show='headings')
        self.customers_tree.heading('name', text='اسم العميل')
        self.customers_tree.heading('phone', text='رقم الهاتف')
        self.customers_tree.heading('type', text='نوع العميل')
        self.customers_tree.pack(fill='both', expand=True, padx=5, pady=5)

    def create_sales_tab(self):
        """Create sales management tab"""
        # Sales tab
        sales_frame = ttk.Frame(self.notebook)
        self.notebook.add(sales_frame, text="💰 المبيعات")

        # Customer info frame
        customer_frame = ttk.LabelFrame(sales_frame, text="معلومات العميل")
        customer_frame.pack(fill='x', padx=10, pady=5)

        # Customer name
        ttk.Label(customer_frame, text="اسم العميل:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        self.sale_customer_name = tk.StringVar()
        ttk.Entry(customer_frame, textvariable=self.sale_customer_name, width=25).grid(row=0, column=1, padx=5, pady=5)

        # Customer type
        ttk.Label(customer_frame, text="نوع العميل:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
        self.sale_customer_type = tk.StringVar()
        sale_customer_type_combo = ttk.Combobox(customer_frame, textvariable=self.sale_customer_type, width=15)
        sale_customer_type_combo['values'] = ('تجزئة', 'جملة', 'موزع معتمد', 'صاحب محل')
        sale_customer_type_combo.grid(row=0, column=3, padx=5, pady=5)
        sale_customer_type_combo.set('تجزئة')

        # Product input frame
        product_frame = ttk.LabelFrame(sales_frame, text="إضافة منتج للفاتورة")
        product_frame.pack(fill='x', padx=10, pady=5)

        # Barcode input
        ttk.Label(product_frame, text="الباركود:").grid(row=0, column=0, padx=5, pady=5, sticky='e')
        self.sale_barcode = tk.StringVar()
        barcode_entry = ttk.Entry(product_frame, textvariable=self.sale_barcode, width=20)
        barcode_entry.grid(row=0, column=1, padx=5, pady=5)
        barcode_entry.bind('<Return>', self.add_product_to_sale)

        # Quantity
        ttk.Label(product_frame, text="الكمية:").grid(row=0, column=2, padx=5, pady=5, sticky='e')
        self.sale_quantity = tk.StringVar()
        self.sale_quantity.set('1')
        ttk.Entry(product_frame, textvariable=self.sale_quantity, width=10).grid(row=0, column=3, padx=5, pady=5)

        # Add button
        ttk.Button(product_frame, text="إضافة", command=self.add_product_to_sale).grid(row=0, column=4, padx=10, pady=5)

        # Sale products list
        sale_list_frame = ttk.LabelFrame(sales_frame, text="منتجات الفاتورة")
        sale_list_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Treeview for sale products
        self.sale_tree = ttk.Treeview(sale_list_frame, columns=('name', 'quantity', 'price', 'total'), show='headings')
        self.sale_tree.heading('name', text='اسم المنتج')
        self.sale_tree.heading('quantity', text='الكمية')
        self.sale_tree.heading('price', text='السعر')
        self.sale_tree.heading('total', text='المجموع')
        self.sale_tree.pack(fill='both', expand=True, padx=5, pady=5)

        # Total and buttons frame
        total_frame = ttk.Frame(sales_frame)
        total_frame.pack(fill='x', padx=10, pady=5)

        # Total label
        self.total_label = ttk.Label(total_frame, text="المجموع الكلي: 0.00", font=('Arial', 12, 'bold'))
        self.total_label.pack(side='left', padx=10)

        # Buttons
        ttk.Button(total_frame, text="حفظ الفاتورة", command=self.save_sale).pack(side='right', padx=5)
        ttk.Button(total_frame, text="مسح الكل", command=self.clear_sale).pack(side='right', padx=5)

    def create_reports_tab(self):
        """Create reports tab"""
        # Reports tab
        reports_frame = ttk.Frame(self.notebook)
        self.notebook.add(reports_frame, text="📊 التقارير")

        # Reports buttons
        buttons_frame = ttk.LabelFrame(reports_frame, text="التقارير المتاحة")
        buttons_frame.pack(fill='x', padx=10, pady=10)

        ttk.Button(buttons_frame, text="تقرير المبيعات", command=self.show_sales_report).pack(side='left', padx=10, pady=10)
        ttk.Button(buttons_frame, text="تقرير المخزون", command=self.show_inventory_report).pack(side='left', padx=10, pady=10)
        ttk.Button(buttons_frame, text="تقرير العملاء", command=self.show_customers_report).pack(side='left', padx=10, pady=10)

        # Reports display area
        self.reports_text = tk.Text(reports_frame, wrap='word', font=('Arial', 10))
        self.reports_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Scrollbar for reports
        scrollbar = ttk.Scrollbar(self.reports_text)
        scrollbar.pack(side='right', fill='y')
        self.reports_text.config(yscrollcommand=scrollbar.set)
        scrollbar.config(command=self.reports_text.yview)

    def create_status_bar(self):
        """Create status bar"""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side='bottom', fill='x')

        self.status_label = ttk.Label(self.status_bar, text="جاهز | Ready")
        self.status_label.pack(side='left', padx=10, pady=5)

        # Save button
        ttk.Button(self.status_bar, text="💾 حفظ البيانات", command=self.save_data).pack(side='right', padx=10, pady=5)

    def add_supplier(self):
        """Add new supplier"""
        try:
            name = self.supplier_name.get().strip()
            phone = self.supplier_phone.get().strip()

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المورد")
                return

            supplier = {
                'name': name,
                'phone': phone,
                'date_added': datetime.now().isoformat()
            }

            self.suppliers.append(supplier)
            self.update_suppliers_display()

            # Clear inputs
            self.supplier_name.set('')
            self.supplier_phone.set('')

            self.status_label.config(text=f"تم إضافة المورد: {name}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المورد: {e}")

    def add_product(self):
        """Add new product"""
        try:
            name = self.product_name.get().strip()
            barcode = self.product_barcode.get().strip()
            price = float(self.product_price.get() or 0)
            stock = int(self.product_stock.get() or 0)

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم المنتج")
                return

            product = {
                'name': name,
                'barcode': barcode,
                'price': price,
                'stock': stock,
                'date_added': datetime.now().isoformat()
            }

            self.products.append(product)
            self.update_products_display()

            # Clear inputs
            self.product_name.set('')
            self.product_barcode.set('')
            self.product_price.set('')
            self.product_stock.set('')

            self.status_label.config(text=f"تم إضافة المنتج: {name}")

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال قيم صحيحة للسعر والمخزون")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المنتج: {e}")

    def add_customer(self):
        """Add new customer"""
        try:
            name = self.customer_name.get().strip()
            phone = self.customer_phone.get().strip()
            customer_type = self.customer_type.get()

            if not name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            customer = {
                'name': name,
                'phone': phone,
                'type': customer_type,
                'date_added': datetime.now().isoformat()
            }

            self.customers.append(customer)
            self.update_customers_display()

            # Clear inputs
            self.customer_name.set('')
            self.customer_phone.set('')
            self.customer_type.set('تجزئة')

            self.status_label.config(text=f"تم إضافة العميل: {name}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة العميل: {e}")'''

    try:
        # Create backup of broken file
        create_backup()

        # Write the working code
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(working_code)

        print("✅ تم إنشاء الجزء الأول من ProTech العامل")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف: {e}")
        return False

def add_remaining_methods():
    """Add the remaining methods to complete ProTech"""

    remaining_code = '''
    def add_product_to_sale(self, event=None):
        """Add product to current sale"""
        try:
            barcode = self.sale_barcode.get().strip()
            quantity = int(self.sale_quantity.get() or 1)

            if not barcode:
                messagebox.showerror("خطأ", "يرجى إدخال الباركود")
                return

            # Find product by barcode
            product = None
            for p in self.products:
                if p.get('barcode') == barcode:
                    product = p
                    break

            if not product:
                messagebox.showerror("خطأ", "المنتج غير موجود")
                return

            if product['stock'] < quantity:
                messagebox.showerror("خطأ", "المخزون غير كافي")
                return

            # Calculate price based on customer type
            base_price = product['price']
            customer_type = self.sale_customer_type.get()

            # Price margins
            margins = {
                'تجزئة': 1.30,      # +30%
                'جملة': 1.20,       # +20%
                'موزع معتمد': 1.15, # +15%
                'صاحب محل': 1.05   # +5%
            }

            final_price = base_price * margins.get(customer_type, 1.30)
            total = final_price * quantity

            # Add to sale tree
            self.sale_tree.insert('', 'end', values=(
                product['name'],
                quantity,
                f"{final_price:.2f}",
                f"{total:.2f}"
            ))

            # Update total
            self.update_sale_total()

            # Clear barcode input
            self.sale_barcode.set('')
            self.sale_quantity.set('1')

        except ValueError:
            messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة")
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إضافة المنتج: {e}")

    def update_sale_total(self):
        """Update sale total"""
        try:
            total = 0
            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                total += float(values[3])  # Total column

            self.total_label.config(text=f"المجموع الكلي: {total:.2f}")

        except Exception as e:
            print(f"خطأ في حساب المجموع: {e}")

    def save_sale(self):
        """Save current sale"""
        try:
            customer_name = self.sale_customer_name.get().strip()
            customer_type = self.sale_customer_type.get()

            if not customer_name:
                messagebox.showerror("خطأ", "يرجى إدخال اسم العميل")
                return

            # Get sale items
            items = []
            total = 0

            for item in self.sale_tree.get_children():
                values = self.sale_tree.item(item)['values']
                items.append({
                    'name': values[0],
                    'quantity': int(values[1]),
                    'price': float(values[2]),
                    'total': float(values[3])
                })
                total += float(values[3])

                # Update product stock
                for product in self.products:
                    if product['name'] == values[0]:
                        product['stock'] -= int(values[1])
                        break

            if not items:
                messagebox.showerror("خطأ", "لا توجد منتجات في الفاتورة")
                return

            # Create sale record
            sale = {
                'customer_name': customer_name,
                'customer_type': customer_type,
                'items': items,
                'total': total,
                'date': datetime.now().isoformat()
            }

            self.sales.append(sale)

            # Clear sale
            self.clear_sale()

            # Update displays
            self.update_products_display()

            messagebox.showinfo("نجح", f"تم حفظ الفاتورة بمبلغ {total:.2f}")
            self.status_label.config(text=f"تم حفظ فاتورة بمبلغ {total:.2f}")

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في حفظ الفاتورة: {e}")

    def clear_sale(self):
        """Clear current sale"""
        try:
            # Clear tree
            for item in self.sale_tree.get_children():
                self.sale_tree.delete(item)

            # Clear inputs
            self.sale_customer_name.set('')
            self.sale_customer_type.set('تجزئة')
            self.sale_barcode.set('')
            self.sale_quantity.set('1')

            # Reset total
            self.total_label.config(text="المجموع الكلي: 0.00")

        except Exception as e:
            print(f"خطأ في مسح الفاتورة: {e}")

    def update_suppliers_display(self):
        """Update suppliers display"""
        try:
            # Clear tree
            for item in self.suppliers_tree.get_children():
                self.suppliers_tree.delete(item)

            # Add suppliers
            for supplier in self.suppliers:
                self.suppliers_tree.insert('', 'end', values=(
                    supplier['name'],
                    supplier.get('phone', '')
                ))

        except Exception as e:
            print(f"خطأ في تحديث عرض الموردين: {e}")

    def update_products_display(self):
        """Update products display"""
        try:
            # Clear tree
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # Add products
            for product in self.products:
                self.products_tree.insert('', 'end', values=(
                    product['name'],
                    product.get('barcode', ''),
                    f"{product['price']:.2f}",
                    product['stock']
                ))

        except Exception as e:
            print(f"خطأ في تحديث عرض المنتجات: {e}")

    def update_customers_display(self):
        """Update customers display"""
        try:
            # Clear tree
            for item in self.customers_tree.get_children():
                self.customers_tree.delete(item)

            # Add customers
            for customer in self.customers:
                self.customers_tree.insert('', 'end', values=(
                    customer['name'],
                    customer.get('phone', ''),
                    customer.get('type', 'تجزئة')
                ))

        except Exception as e:
            print(f"خطأ في تحديث عرض العملاء: {e}")

    def show_sales_report(self):
        """Show sales report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "تقرير المبيعات\\n"
            report += "=" * 50 + "\\n\\n"

            total_sales = 0
            for i, sale in enumerate(self.sales, 1):
                report += f"فاتورة رقم {i}:\\n"
                report += f"العميل: {sale['customer_name']}\\n"
                report += f"نوع العميل: {sale['customer_type']}\\n"
                report += f"التاريخ: {sale['date'][:10]}\\n"
                report += f"المبلغ: {sale['total']:.2f}\\n"
                report += "-" * 30 + "\\n"
                total_sales += sale['total']

            report += f"\\nإجمالي المبيعات: {total_sales:.2f}\\n"
            report += f"عدد الفواتير: {len(self.sales)}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير: {e}")

    def show_inventory_report(self):
        """Show inventory report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "تقرير المخزون\\n"
            report += "=" * 50 + "\\n\\n"

            total_value = 0
            for product in self.products:
                value = product['price'] * product['stock']
                total_value += value

                report += f"المنتج: {product['name']}\\n"
                report += f"الباركود: {product.get('barcode', 'غير محدد')}\\n"
                report += f"السعر: {product['price']:.2f}\\n"
                report += f"المخزون: {product['stock']}\\n"
                report += f"القيمة: {value:.2f}\\n"
                report += "-" * 30 + "\\n"

            report += f"\\nإجمالي قيمة المخزون: {total_value:.2f}\\n"
            report += f"عدد المنتجات: {len(self.products)}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير: {e}")

    def show_customers_report(self):
        """Show customers report"""
        try:
            self.reports_text.delete(1.0, tk.END)

            report = "تقرير العملاء\\n"
            report += "=" * 50 + "\\n\\n"

            # Count customers by type
            customer_types = {}
            for customer in self.customers:
                customer_type = customer.get('type', 'تجزئة')
                customer_types[customer_type] = customer_types.get(customer_type, 0) + 1

            for customer in self.customers:
                report += f"العميل: {customer['name']}\\n"
                report += f"الهاتف: {customer.get('phone', 'غير محدد')}\\n"
                report += f"النوع: {customer.get('type', 'تجزئة')}\\n"
                report += "-" * 30 + "\\n"

            report += f"\\nإجمالي العملاء: {len(self.customers)}\\n"
            report += "\\nتوزيع العملاء حسب النوع:\\n"
            for customer_type, count in customer_types.items():
                report += f"{customer_type}: {count}\\n"

            self.reports_text.insert(1.0, report)

        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في عرض التقرير: {e}")'''

    try:
        # Read current file
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # Add remaining methods before the last part
        content = content.replace('        except Exception as e:\n            messagebox.showerror("خطأ", f"خطأ في إضافة العميل: {e}")',
                                remaining_code + '\n        except Exception as e:\n            messagebox.showerror("خطأ", f"خطأ في إضافة العميل: {e}")')

        # Write back
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم إضافة الطرق المتبقية")
        return True

    except Exception as e:
        print(f"❌ خطأ في إضافة الطرق: {e}")
        return False

def add_final_methods():
    """Add final methods and main function"""

    final_code = '''
    def load_data(self):
        """Load data from file"""
        try:
            self.loading = True

            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])

                print(f"📥 تم تحميل البيانات: {len(self.suppliers)} موردين، {len(self.products)} منتجات، {len(self.customers)} عملاء، {len(self.sales)} مبيعات")

                # Update displays
                self.update_suppliers_display()
                self.update_products_display()
                self.update_customers_display()

                self.status_label.config(text="تم تحميل البيانات بنجاح")
            else:
                print("📝 لا يوجد ملف بيانات، سيتم البدء بملف فارغ")
                self.status_label.config(text="بدء جديد - لا توجد بيانات سابقة")

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في تحميل البيانات: {e}")
        finally:
            self.loading = False

    def save_data(self):
        """Save data to file"""
        try:
            if self.loading:
                return

            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat()
            }

            # Save to main file
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            print("💾 تم حفظ البيانات بنجاح")
            self.status_label.config(text="تم حفظ البيانات بنجاح")

            # Create backup
            backup_file = f"protech_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            messagebox.showerror("خطأ", f"خطأ في حفظ البيانات: {e}")
            return False

    def run(self):
        """Run the application"""
        try:
            print("🚀 تشغيل ProTech...")
            print("🧮 ProTech Accounting System")
            print("=" * 50)

            # Setup window close handler
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

            # Start the main loop
            self.root.mainloop()

        except Exception as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج: {e}")

    def on_closing(self):
        """Handle window closing"""
        try:
            # Save data before closing
            self.save_data()
            print("👋 إغلاق ProTech...")
            self.root.destroy()
        except Exception as e:
            print(f"خطأ عند الإغلاق: {e}")
            self.root.destroy()

def main():
    """Main function"""
    try:
        print("🧮 ProTech Simple Working - Accounting System")
        print("نظام ProTech للمحاسبة - النسخة العاملة")
        print("=" * 60)

        # Create and run the application
        app = ProTechSimpleWorking()
        app.run()

    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''

    try:
        # Read current file
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()

        # Add final methods and main function
        content += final_code

        # Write back
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم إضافة الطرق النهائية ودالة main")
        return True

    except Exception as e:
        print(f"❌ خطأ في إضافة الطرق النهائية: {e}")
        return False

def test_working_protech():
    """Test the working ProTech"""
    try:
        import subprocess
        import sys

        print("🧪 اختبار ProTech العامل...")

        # Test compilation
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'],
                              capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ اختبار التركيب نجح")

            # Test basic execution
            result = subprocess.run([sys.executable, '-c', 'import protech_simple_working'],
                                  capture_output=True, text=True, timeout=5)

            if result.returncode == 0:
                print("✅ اختبار الاستيراد نجح")
                return True
            else:
                print(f"❌ خطأ في الاستيراد: {result.stderr}")
                return False
        else:
            print(f"❌ خطأ في التركيب: {result.stderr}")
            return False

    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_simple_launcher():
    """Create simple launcher"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Simple Launcher
مشغل ProTech البسيط
"""

import os
import sys
import subprocess

def main():
    """Launch ProTech"""
    try:
        print("🚀 تشغيل ProTech...")

        if not os.path.exists('protech_simple_working.py'):
            print("❌ ملف ProTech غير موجود!")
            input("اضغط Enter للخروج...")
            return

        # Launch ProTech
        subprocess.run([sys.executable, 'protech_simple_working.py'])

    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''

    with open('launch_protech.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)

    print("✅ تم إنشاء مشغل بسيط: launch_protech.py")

def main():
    """Main function"""
    print("🔧 إنشاء ProTech العامل - الحل النهائي")
    print("🔧 Create Working ProTech - Final Solution")
    print("=" * 60)

    try:
        # Step 1: Create working ProTech
        print("📝 إنشاء ProTech العامل...")
        if create_working_protech():
            print("✅ تم إنشاء الجزء الأول")

        # Step 2: Add remaining methods
        print("🔧 إضافة الطرق المتبقية...")
        if add_remaining_methods():
            print("✅ تم إضافة الطرق المتبقية")

        # Step 3: Add final methods
        print("🔧 إضافة الطرق النهائية...")
        if add_final_methods():
            print("✅ تم إضافة الطرق النهائية")

        # Step 4: Test the result
        print("🧪 اختبار النتيجة...")
        if test_working_protech():
            print("✅ الاختبار نجح")

        # Step 5: Create launcher
        print("🚀 إنشاء المشغل...")
        create_simple_launcher()

        print("\n" + "=" * 60)
        print("✅ تم إنشاء ProTech العامل بنجاح!")
        print("✅ Working ProTech created successfully!")
        print("=" * 60)

        print("\n🎯 الملفات المنشأة:")
        print("• protech_simple_working.py - البرنامج الرئيسي العامل")
        print("• launch_protech.py - مشغل بسيط")
        print("• protech_simple_working.py.broken_* - نسخة احتياطية للملف المكسور")

        print("\n🚀 طرق التشغيل:")
        print("1. النقر المزدوج على protech_simple_working.py")
        print("2. النقر المزدوج على launch_protech.py")
        print("3. من سطر الأوامر: python protech_simple_working.py")

        print("\n🎉 ProTech جاهز للعمل!")

        return True

    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()