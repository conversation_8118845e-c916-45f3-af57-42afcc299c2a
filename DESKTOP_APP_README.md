# نظام ProTech للمحاسبة - تطبيق سطح المكتب
# ProTech Accounting System - Desktop Application

## 🖥️ تطبيق سطح المكتب / Desktop Application

تطبيق سطح مكتب سهل الاستخدام لنظام ProTech للمحاسبة مع واجهة رسومية بسيطة وفعالة.

Easy-to-use desktop application for ProTech Accounting System with simple and effective graphical interface.

## 🚀 طرق التشغيل / How to Run

### الطريقة الأولى: ملف التشغيل المباشر / Method 1: Direct Run File
```
انقر نقراً مزدوجاً على: ProTech Desktop App.bat
Double-click on: ProTech Desktop App.bat
```

### الطريقة الثانية: سطر الأوامر / Method 2: Command Line
```bash
python protech_desktop_simple.py
```

### الطريقة الثالثة: التطبيق المتقدم / Method 3: Advanced App
```bash
python protech_desktop.py
```

## 📋 المتطلبات / Requirements

- **Python 3.7+** مثبت على النظام / installed on system
- **tkinter** (مدمج مع Python / included with Python)
- **basic_flask.py** في نفس المجلد / in the same folder

## 🎯 كيفية الاستخدام / How to Use

### خطوات التشغيل / Steps to Run:

1. **تشغيل التطبيق / Start Application**
   - انقر نقراً مزدوجاً على `ProTech Desktop App.bat`
   - أو شغل `python protech_desktop_simple.py`

2. **بدء النظام / Start System**
   - اضغط زر "🚀 تشغيل النظام / Start System"
   - انتظر حتى تظهر رسالة "🟢 النظام يعمل / System Running"

3. **فتح التطبيق / Open Application**
   - اضغط زر "🌐 فتح التطبيق / Open Application"
   - سيفتح المتصفح تلقائياً على `http://localhost:5000`

4. **استخدام النظام / Use System**
   - استخدم النظام من خلال المتصفح
   - جميع المميزات متاحة

5. **إيقاف النظام / Stop System**
   - اضغط زر "🛑 إيقاف النظام / Stop System"
   - أو أغلق نافذة التطبيق

## 🌟 مميزات تطبيق سطح المكتب / Desktop App Features

### ✅ المميزات الأساسية / Basic Features
- **واجهة سهلة الاستخدام** / Easy-to-use interface
- **تحكم كامل في النظام** / Full system control
- **فتح المتصفح تلقائياً** / Automatic browser opening
- **مراقبة حالة النظام** / System status monitoring
- **إيقاف آمن للنظام** / Safe system shutdown

### ✅ المميزات المتقدمة / Advanced Features
- **سجل نشاط مفصل** / Detailed activity log
- **معلومات النظام** / System information
- **تبويبات منظمة** / Organized tabs
- **رسائل تأكيد** / Confirmation messages
- **معالجة الأخطاء** / Error handling

## 🔧 استكشاف الأخطاء / Troubleshooting

### مشكلة: Python غير موجود / Problem: Python not found
```
الحل / Solution:
1. ثبت Python من python.org
2. تأكد من إضافة Python إلى PATH
3. أعد تشغيل الكمبيوتر
```

### مشكلة: tkinter غير موجود / Problem: tkinter not found
```
الحل / Solution:
- على Windows: tkinter مدمج مع Python
- على Linux: sudo apt-get install python3-tk
- على macOS: brew install python-tk
```

### مشكلة: الخادم لا يبدأ / Problem: Server won't start
```
الحل / Solution:
1. تأكد من وجود basic_flask.py
2. تأكد من أن المنفذ 5000 غير مستخدم
3. أعد تشغيل التطبيق
```

## 📁 ملفات التطبيق / Application Files

```
📁 ProTech Desktop App/
├── 🖥️ ProTech Desktop App.bat          # ملف التشغيل المباشر
├── 🐍 protech_desktop_simple.py        # التطبيق البسيط
├── 🐍 protech_desktop.py               # التطبيق المتقدم
├── 🐍 basic_flask.py                   # خادم Flask
├── 📋 run_desktop_app.bat              # ملف تشغيل متقدم
└── 📖 DESKTOP_APP_README.md            # هذا الملف
```

## 🌐 الروابط المتاحة / Available URLs

عند تشغيل النظام، ستكون الروابط التالية متاحة:

When the system is running, the following URLs will be available:

- **🏠 الرئيسية / Home**: http://localhost:5000
- **📦 المخزون / Inventory**: http://localhost:5000/inventory
- **👥 العملاء / Customers**: http://localhost:5000/customers
- **🏢 الموردين / Suppliers**: http://localhost:5000/suppliers
- **💰 المبيعات / Sales**: http://localhost:5000/sales
- **📈 التقارير / Reports**: http://localhost:5000/reports
- **🧪 الاختبار / Test**: http://localhost:5000/test

## 📞 الدعم / Support

للحصول على المساعدة أو الإبلاغ عن مشاكل:
For help or to report issues:

- **📧 البريد الإلكتروني / Email**: <EMAIL>
- **🌐 الموقع / Website**: www.protech.com
- **📱 الهاتف / Phone**: +966-11-123-4567

## 📄 الترخيص / License

© 2024 ProTech Accounting System. جميع الحقوق محفوظة / All rights reserved.

---

## 🎉 استمتع باستخدام نظام ProTech للمحاسبة!
## 🎉 Enjoy using ProTech Accounting System!
