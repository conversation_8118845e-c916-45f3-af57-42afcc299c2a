#!/usr/bin/env python3
"""
ProTech Persistent Integration - تكامل التخزين الدائم مع ProTech
دمج نظام التخزين الدائم المنظم مع نظام ProTech الحالي

Integration of persistent storage system with existing ProTech system
تكامل نظام التخزين الدائم مع نظام ProTech الموجود
"""

import os
import sys
import json
import shutil
import threading
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import traceback

# Import the persistent storage manager
from persistent_storage_manager import PersistentStorageManager

class ProTechPersistentIntegration:
    """Integration layer for ProTech with persistent storage"""
    
    def __init__(self, original_app_instance=None):
        """Initialize persistent integration"""
        self.original_app = original_app_instance
        self.storage_manager = None
        self.integration_active = False
        
        # Data mapping for ProTech
        self.data_mappings = {
            'suppliers': 'suppliers',
            'products': 'products', 
            'customers': 'customers',
            'sales': 'sales',
            'invoices': 'invoices',
            'settings': 'settings',
            'categories': 'categories'
        }
        
        # Auto-save settings
        self.auto_save_enabled = True
        self.auto_save_interval = 30  # 30 seconds
        self.last_save_time = time.time()
        
        # Initialize integration
        self.init_integration()

    def init_integration(self):
        """Initialize the integration system"""
        try:
            print("🔄 تهيئة تكامل التخزين الدائم...")
            print("🔄 Initializing persistent storage integration...")
            
            # Initialize storage manager
            self.storage_manager = PersistentStorageManager(
                base_path=".", 
                app_name="ProTech"
            )
            
            # Check for existing data
            self.migrate_existing_data()
            
            # Start auto-save if enabled
            if self.auto_save_enabled:
                self.start_auto_save()
            
            self.integration_active = True
            
            print("✅ تم تهيئة تكامل التخزين الدائم بنجاح")
            print("✅ Persistent storage integration initialized successfully")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة التكامل: {e}")
            traceback.print_exc()

    def migrate_existing_data(self):
        """Migrate existing JSON data to persistent storage"""
        try:
            # Look for existing ProTech data files
            existing_files = [
                "protech_simple_data.json",
                "protech_data.json", 
                "data.json"
            ]
            
            migrated_any = False
            
            for filename in existing_files:
                if os.path.exists(filename):
                    print(f"📥 ترحيل البيانات من {filename}...")
                    
                    with open(filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Migrate each data type
                    for data_type, items in data.items():
                        if data_type in self.data_mappings and items:
                            self.storage_manager.save_data(data_type, items)
                            print(f"  ✅ تم ترحيل {data_type}: {len(items) if isinstance(items, list) else 1} عنصر")
                    
                    # Create backup of original file
                    backup_name = f"{filename}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    shutil.copy2(filename, backup_name)
                    print(f"  💾 تم إنشاء نسخة احتياطية: {backup_name}")
                    
                    migrated_any = True
                    break  # Use first found file
            
            if not migrated_any:
                print("📝 لم يتم العثور على بيانات موجودة - سيبدأ النظام فارغاً")
                print("📝 No existing data found - system will start empty")
                
        except Exception as e:
            print(f"❌ خطأ في ترحيل البيانات: {e}")

    def save_app_data(self, data_type: str, data: Any) -> bool:
        """Save application data using persistent storage"""
        try:
            if not self.integration_active or not self.storage_manager:
                return False
            
            # Map data type if needed
            storage_type = self.data_mappings.get(data_type, data_type)
            
            # Save to persistent storage
            success = self.storage_manager.save_data(storage_type, data)
            
            if success:
                self.last_save_time = time.time()
                print(f"💾 تم حفظ {data_type}: {len(data) if isinstance(data, (list, dict)) else 1} عنصر")
            
            return success
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            return False

    def load_app_data(self, data_type: str, default_value: Any = None) -> Any:
        """Load application data from persistent storage"""
        try:
            if not self.integration_active or not self.storage_manager:
                return default_value
            
            # Map data type if needed
            storage_type = self.data_mappings.get(data_type, data_type)
            
            # Load from persistent storage
            data = self.storage_manager.load_data(storage_type, default_value=default_value)
            
            if data is not None and data != default_value:
                print(f"📥 تم تحميل {data_type}: {len(data) if isinstance(data, (list, dict)) else 1} عنصر")
            
            return data
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            return default_value

    def create_backup(self, backup_name: str = None) -> str:
        """Create backup using persistent storage manager"""
        try:
            if not self.integration_active or not self.storage_manager:
                return ""
            
            backup_path = self.storage_manager.create_backup(backup_name)
            print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
            
            return backup_path
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            return ""

    def export_data(self, export_path: str = None) -> str:
        """Export all data to JSON file"""
        try:
            if not self.integration_active or not self.storage_manager:
                return ""
            
            export_path = self.storage_manager.export_all_data(export_path)
            print(f"📤 تم تصدير البيانات: {export_path}")
            
            return export_path
            
        except Exception as e:
            print(f"❌ خطأ في تصدير البيانات: {e}")
            return ""

    def get_storage_statistics(self) -> Dict[str, Any]:
        """Get storage statistics"""
        try:
            if not self.integration_active or not self.storage_manager:
                return {}
            
            return self.storage_manager.get_storage_stats()
            
        except Exception as e:
            print(f"❌ خطأ في الحصول على الإحصائيات: {e}")
            return {}

    def start_auto_save(self):
        """Start automatic saving thread"""
        try:
            self.auto_save_thread = threading.Thread(target=self.auto_save_worker, daemon=True)
            self.auto_save_thread.start()
            print("✅ تم تشغيل الحفظ التلقائي")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل الحفظ التلقائي: {e}")

    def auto_save_worker(self):
        """Background worker for automatic saving"""
        while self.integration_active:
            try:
                time.sleep(self.auto_save_interval)
                
                # Check if we have an original app instance to save from
                if self.original_app and hasattr(self.original_app, 'save_all_data'):
                    self.original_app.save_all_data()
                elif self.original_app:
                    # Try to save common data attributes
                    self.save_common_app_data()
                
            except Exception as e:
                print(f"❌ خطأ في الحفظ التلقائي: {e}")

    def save_common_app_data(self):
        """Save common application data attributes"""
        try:
            if not self.original_app:
                return
            
            # Common data attributes in ProTech applications
            common_attributes = [
                'suppliers', 'products', 'customers', 'sales', 
                'invoices', 'categories', 'settings'
            ]
            
            saved_count = 0
            
            for attr_name in common_attributes:
                if hasattr(self.original_app, attr_name):
                    data = getattr(self.original_app, attr_name)
                    if data:  # Only save if data exists
                        if self.save_app_data(attr_name, data):
                            saved_count += 1
            
            if saved_count > 0:
                print(f"💾 تم حفظ {saved_count} نوع من البيانات تلقائياً")
                
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات المشتركة: {e}")

    def integrate_with_app(self, app_instance):
        """Integrate with existing ProTech application instance"""
        try:
            self.original_app = app_instance
            
            # Override save methods if they exist
            if hasattr(app_instance, 'save_data'):
                original_save = app_instance.save_data
                
                def enhanced_save_data():
                    # Call original save method
                    result = original_save()
                    
                    # Also save using persistent storage
                    self.save_common_app_data()
                    
                    return result
                
                app_instance.save_data = enhanced_save_data
            
            # Override load methods if they exist
            if hasattr(app_instance, 'load_data'):
                original_load = app_instance.load_data
                
                def enhanced_load_data():
                    # Try to load from persistent storage first
                    self.load_common_app_data()
                    
                    # Then call original load method as fallback
                    return original_load()
                
                app_instance.load_data = enhanced_load_data
            
            print("✅ تم دمج التخزين الدائم مع التطبيق")
            
        except Exception as e:
            print(f"❌ خطأ في دمج التطبيق: {e}")

    def load_common_app_data(self):
        """Load common application data attributes"""
        try:
            if not self.original_app:
                return
            
            # Common data attributes in ProTech applications
            common_attributes = [
                'suppliers', 'products', 'customers', 'sales', 
                'invoices', 'categories', 'settings'
            ]
            
            loaded_count = 0
            
            for attr_name in common_attributes:
                # Load data from persistent storage
                data = self.load_app_data(attr_name, default_value=[])
                
                if data:  # Only set if data exists
                    setattr(self.original_app, attr_name, data)
                    loaded_count += 1
            
            if loaded_count > 0:
                print(f"📥 تم تحميل {loaded_count} نوع من البيانات")
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات المشتركة: {e}")

    def create_integration_wrapper(self, original_app_class):
        """Create a wrapper class that integrates persistent storage"""
        
        class PersistentProTechApp(original_app_class):
            def __init__(self, *args, **kwargs):
                # Initialize persistent integration
                self.persistent_integration = ProTechPersistentIntegration()
                
                # Initialize original app
                super().__init__(*args, **kwargs)
                
                # Integrate with persistent storage
                self.persistent_integration.integrate_with_app(self)
                
                # Load existing data
                self.load_persistent_data()
            
            def load_persistent_data(self):
                """Load data from persistent storage"""
                try:
                    self.persistent_integration.load_common_app_data()
                except Exception as e:
                    print(f"خطأ في تحميل البيانات الدائمة: {e}")
            
            def save_persistent_data(self):
                """Save data to persistent storage"""
                try:
                    self.persistent_integration.save_common_app_data()
                except Exception as e:
                    print(f"خطأ في حفظ البيانات الدائمة: {e}")
            
            def create_backup(self):
                """Create backup using persistent storage"""
                return self.persistent_integration.create_backup()
            
            def export_data(self):
                """Export data using persistent storage"""
                return self.persistent_integration.export_data()
            
            def get_storage_stats(self):
                """Get storage statistics"""
                return self.persistent_integration.get_storage_statistics()
        
        return PersistentProTechApp

    def close(self):
        """Close integration and cleanup"""
        try:
            self.integration_active = False
            
            # Save final data
            if self.original_app:
                self.save_common_app_data()
            
            # Close storage manager
            if self.storage_manager:
                self.storage_manager.close()
            
            print("✅ تم إغلاق تكامل التخزين الدائم")
            
        except Exception as e:
            print(f"❌ خطأ في إغلاق التكامل: {e}")

# Utility functions for easy integration
def create_persistent_protech_app(original_app_class):
    """Create a ProTech app with persistent storage integration"""
    integration = ProTechPersistentIntegration()
    return integration.create_integration_wrapper(original_app_class)

def add_persistent_storage_to_app(app_instance):
    """Add persistent storage to existing ProTech app instance"""
    integration = ProTechPersistentIntegration(app_instance)
    integration.integrate_with_app(app_instance)
    return integration

# Example usage
if __name__ == "__main__":
    print("🧪 اختبار تكامل التخزين الدائم")
    print("🧪 Testing Persistent Storage Integration")
    
    try:
        # Test integration
        integration = ProTechPersistentIntegration()
        
        # Test data operations
        test_data = {
            'suppliers': [{'name': 'مورد تجريبي', 'phone': '123456789'}],
            'products': [{'name': 'منتج تجريبي', 'price': 100}],
            'customers': [{'name': 'عميل تجريبي', 'phone': '987654321'}]
        }
        
        # Save test data
        for data_type, data in test_data.items():
            integration.save_app_data(data_type, data)
        
        # Load test data
        for data_type in test_data.keys():
            loaded_data = integration.load_app_data(data_type, [])
            print(f"✅ تم تحميل {data_type}: {len(loaded_data)} عنصر")
        
        # Test backup
        backup_path = integration.create_backup("test_integration_backup")
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # Test export
        export_path = integration.export_data()
        print(f"✅ تم تصدير البيانات: {export_path}")
        
        # Show statistics
        stats = integration.get_storage_statistics()
        print(f"\n📊 إحصائيات التخزين:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Close integration
        integration.close()
        
        print("\n✅ اكتمل اختبار التكامل بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في اختبار التكامل: {e}")
        traceback.print_exc()
