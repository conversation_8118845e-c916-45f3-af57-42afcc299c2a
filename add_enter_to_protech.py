#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Enter Navigation to ProTech Product Form
إضافة التنقل بالإنتر في نموذج المنتج في ProTech

Direct modification to add Enter key navigation to existing ProTech
تعديل مباشر لإضافة التنقل بمفتاح الإنتر في ProTech الموجود
"""

import os
import shutil
from datetime import datetime

def find_protech_file():
    """Find the ProTech file"""
    try:
        possible_paths = [
            "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program\\protech_simple_working.py",
            "C:\\Users\\<USER>\\Documents\\augment-projects\\protech\\protech_simple_working.py"
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                print(f"✅ تم العثور على ProTech: {path}")
                return path
        
        print("❌ لم يتم العثور على ملف ProTech")
        return None
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {e}")
        return None

def add_enter_navigation_to_protech():
    """Add Enter navigation to ProTech product form"""
    try:
        print("⌨️ إضافة التنقل بالإنتر في نموذج المنتج")
        print("⌨️ Adding Enter Navigation to Product Form")
        print("="*50)
        
        # Find ProTech file
        protech_file = find_protech_file()
        if not protech_file:
            return False
        
        # Create backup
        backup_name = f"{protech_file}.enter_nav_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if navigation already exists
        if "setup_enter_navigation" in content:
            print("✅ التنقل بالإنتر موجود بالفعل")
            return True
        
        # Add Enter navigation functions
        navigation_code = '''
    def setup_enter_navigation(self, entry_widgets):
        """إعداد التنقل بالإنتر بين الحقول"""
        try:
            def on_enter_key(event, current_index):
                try:
                    # الانتقال للحقل التالي
                    next_index = (current_index + 1) % len(entry_widgets)
                    next_widget = entry_widgets[next_index]
                    next_widget.focus_set()
                    
                    # تحديد النص إذا كان حقل إدخال
                    if hasattr(next_widget, 'select_range'):
                        next_widget.select_range(0, 'end')
                    
                    return "break"  # منع السلوك الافتراضي للإنتر
                except Exception as e:
                    print(f"خطأ في التنقل: {e}")
                    return None
            
            # ربط مفتاح الإنتر بكل حقل
            for i, entry_widget in enumerate(entry_widgets):
                if entry_widget and hasattr(entry_widget, 'bind'):
                    entry_widget.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
                    entry_widget.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))
            
            # التركيز على أول حقل
            if entry_widgets and entry_widgets[0]:
                entry_widgets[0].focus_set()
            
            print("✅ تم تفعيل التنقل بالإنتر")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد التنقل: {e}")

    def setup_form_shortcuts(self, window, save_button=None, cancel_button=None):
        """إعداد اختصارات لوحة المفاتيح"""
        try:
            # Ctrl+S للحفظ
            if save_button:
                window.bind('<Control-s>', lambda event: save_button.invoke())
                window.bind('<Control-S>', lambda event: save_button.invoke())
            
            # Escape للإلغاء
            if cancel_button:
                window.bind('<Escape>', lambda event: cancel_button.invoke())
            
            print("✅ تم تفعيل اختصارات لوحة المفاتيح")
            
        except Exception as e:
            print(f"❌ خطأ في إعداد الاختصارات: {e}")

'''
        
        # Find a good place to insert the navigation functions
        # Look for class definition
        class_pos = content.find("class ")
        if class_pos != -1:
            # Find the end of __init__ method
            init_pos = content.find("def __init__(", class_pos)
            if init_pos != -1:
                # Find the next method after __init__
                next_method_pos = content.find("\n    def ", init_pos + 1)
                if next_method_pos != -1:
                    # Insert navigation functions before the next method
                    content = content[:next_method_pos] + navigation_code + content[next_method_pos:]
                else:
                    # If no next method found, add at the end of the class
                    class_end = content.find("\nclass ", class_pos + 1)
                    if class_end == -1:
                        class_end = len(content)
                    content = content[:class_end] + navigation_code + content[class_end:]
        
        # Now find and modify the add_product method
        add_product_pos = content.find("def add_product(")
        if add_product_pos != -1:
            print("✅ تم العثور على دالة add_product")
            
            # Find the method content
            method_start = add_product_pos
            method_end = content.find("\n    def ", method_start + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", method_start + 1)
            if method_end == -1:
                method_end = len(content)
            
            method_content = content[method_start:method_end]
            
            # Look for entry widget creation patterns
            if "Entry(" in method_content:
                # Add navigation setup code before the end of the method
                # Find the last part of the method (usually before return or end)
                lines = method_content.split('\n')
                
                # Find where to insert navigation setup
                insert_line = -1
                for i, line in enumerate(lines):
                    if "pack(" in line and "Entry" in lines[max(0, i-3):i+1]:
                        insert_line = i + 1
                
                if insert_line > 0:
                    # Create entry widgets list
                    navigation_setup = '''
        # إعداد التنقل بالإنتر
        try:
            # جمع جميع حقول الإدخال
            entry_widgets = []
            for widget in add_window.winfo_children():
                for child in widget.winfo_children():
                    if isinstance(child, tk.Entry):
                        entry_widgets.append(child)
                    for grandchild in child.winfo_children():
                        if isinstance(grandchild, tk.Entry):
                            entry_widgets.append(grandchild)
            
            # تفعيل التنقل بالإنتر
            if entry_widgets:
                self.setup_enter_navigation(entry_widgets)
                
                # إضافة تعليمات
                instructions = tk.Label(add_window, 
                                       text="استخدم Enter للانتقال بين الحقول • Ctrl+S للحفظ • Escape للإلغاء",
                                       font=("Arial", 8), fg='gray')
                instructions.pack(pady=5)
            
        except Exception as e:
            print(f"تحذير: مشكلة في إعداد التنقل: {e}")
'''
                    
                    lines.insert(insert_line, navigation_setup)
                    modified_method = '\n'.join(lines)
                    content = content[:method_start] + modified_method + content[method_end:]
                    print("✅ تم إضافة التنقل للدالة")
                else:
                    print("⚠️ لم يتم العثور على مكان مناسب لإضافة التنقل")
            else:
                print("⚠️ لم يتم العثور على حقول إدخال في الدالة")
        else:
            print("⚠️ لم يتم العثور على دالة add_product")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تعديل الملف")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة التنقل: {e}")
        return False

def create_manual_navigation_patch():
    """Create manual patch for adding navigation"""
    try:
        print("\n📝 إنشاء رقعة يدوية للتنقل...")
        
        patch_code = '''# رقعة إضافة التنقل بالإنتر - نسخ والصق هذا الكود

# 1. أضف هذه الدوال في كلاس ProTech:

def setup_enter_navigation(self, entry_widgets):
    """إعداد التنقل بالإنتر بين الحقول"""
    try:
        def on_enter_key(event, current_index):
            try:
                next_index = (current_index + 1) % len(entry_widgets)
                next_widget = entry_widgets[next_index]
                next_widget.focus_set()
                if hasattr(next_widget, 'select_range'):
                    next_widget.select_range(0, 'end')
                return "break"
            except Exception as e:
                print(f"خطأ في التنقل: {e}")
                return None
        
        for i, entry_widget in enumerate(entry_widgets):
            if entry_widget and hasattr(entry_widget, 'bind'):
                entry_widget.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
                entry_widget.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))
        
        if entry_widgets and entry_widgets[0]:
            entry_widgets[0].focus_set()
        
    except Exception as e:
        print(f"خطأ في إعداد التنقل: {e}")

def setup_form_shortcuts(self, window, save_button=None, cancel_button=None):
    """إعداد اختصارات لوحة المفاتيح"""
    try:
        if save_button:
            window.bind('<Control-s>', lambda event: save_button.invoke())
            window.bind('<Control-S>', lambda event: save_button.invoke())
        if cancel_button:
            window.bind('<Escape>', lambda event: cancel_button.invoke())
    except Exception as e:
        print(f"خطأ في إعداد الاختصارات: {e}")

# 2. في دالة add_product، أضف هذا الكود في نهاية الدالة (قبل إغلاق النافذة):

# جمع حقول الإدخال
entry_widgets = []
# استبدل هذه الأسماء بأسماء حقول الإدخال الفعلية في نافذتك
entry_widgets = [
    barcode_entry,      # حقل الباركود
    name_entry,         # حقل اسم المنتج
    category_entry,     # حقل الفئة
    supplier_entry,     # حقل المورد
    phone_entry,        # حقل الهاتف
    unit_entry,         # حقل الوحدة
    quantity_entry,     # حقل الكمية
    price_entry,        # حقل السعر
    # أضف باقي الحقول هنا...
]

# تفعيل التنقل
self.setup_enter_navigation(entry_widgets)

# إضافة اختصارات (اختياري)
self.setup_form_shortcuts(add_window, save_button, cancel_button)

# إضافة تعليمات
instructions = tk.Label(add_window, 
                       text="استخدم Enter للانتقال • Ctrl+S للحفظ • Escape للإلغاء",
                       font=("Arial", 8), fg='gray')
instructions.pack(pady=5)

# 3. تأكد من استيراد tkinter في بداية الملف:
import tkinter as tk

# ملاحظات:
# - استبدل أسماء المتغيرات (barcode_entry, name_entry, إلخ) بالأسماء الفعلية في كودك
# - تأكد من أن جميع حقول الإدخال تم إنشاؤها قبل استدعاء setup_enter_navigation
# - يمكنك إضافة أو إزالة حقول من قائمة entry_widgets حسب الحاجة
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        patch_file = os.path.join(desktop_path, "رقعة_التنقل_بالإنتر.txt")
        
        with open(patch_file, 'w', encoding='utf-8') as f:
            f.write(patch_code)
        
        print(f"✅ تم إنشاء الرقعة اليدوية: {os.path.basename(patch_file)}")
        return patch_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الرقعة اليدوية: {e}")
        return None

def create_complete_example():
    """Create complete example of product form with navigation"""
    try:
        print("\n📋 إنشاء مثال كامل...")
        
        example_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مثال كامل لنموذج منتج مع التنقل بالإنتر
Complete Example of Product Form with Enter Navigation
"""

import tkinter as tk
from tkinter import messagebox

class ProductFormExample:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("مثال نموذج المنتج مع التنقل")
        self.root.geometry("500x700")
        self.root.configure(bg='#f0f8ff')
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"500x700+{x}+{y}")
        
        self.create_product_form()
    
    def setup_enter_navigation(self, entry_widgets):
        """إعداد التنقل بالإنتر بين الحقول"""
        try:
            def on_enter_key(event, current_index):
                try:
                    next_index = (current_index + 1) % len(entry_widgets)
                    next_widget = entry_widgets[next_index]
                    next_widget.focus_set()
                    if hasattr(next_widget, 'select_range'):
                        next_widget.select_range(0, 'end')
                    return "break"
                except Exception as e:
                    print(f"خطأ في التنقل: {e}")
                    return None
            
            for i, entry_widget in enumerate(entry_widgets):
                if entry_widget and hasattr(entry_widget, 'bind'):
                    entry_widget.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
                    entry_widget.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))
            
            if entry_widgets and entry_widgets[0]:
                entry_widgets[0].focus_set()
            
        except Exception as e:
            print(f"خطأ في إعداد التنقل: {e}")
    
    def setup_form_shortcuts(self, window, save_button=None, cancel_button=None):
        """إعداد اختصارات لوحة المفاتيح"""
        try:
            if save_button:
                window.bind('<Control-s>', lambda event: save_button.invoke())
                window.bind('<Control-S>', lambda event: save_button.invoke())
            if cancel_button:
                window.bind('<Escape>', lambda event: cancel_button.invoke())
        except Exception as e:
            print(f"خطأ في إعداد الاختصارات: {e}")
    
    def create_product_form(self):
        """إنشاء نموذج المنتج"""
        # العنوان
        title_label = tk.Label(self.root, text="إضافة منتج جديد", 
                              font=("Arial", 18, "bold"), bg='#f0f8ff', fg='#2c3e50')
        title_label.pack(pady=20)
        
        # إطار النموذج
        form_frame = tk.Frame(self.root, bg='#f0f8ff')
        form_frame.pack(fill='both', expand=True, padx=30, pady=10)
        
        # حقول الإدخال
        self.entries = {}
        entry_widgets = []
        
        fields = [
            ('barcode', 'الباركود'),
            ('name', 'اسم المنتج'),
            ('category', 'الفئة'),
            ('supplier', 'المورد'),
            ('phone', 'رقم الهاتف'),
            ('unit', 'الوحدة'),
            ('quantity', 'الكمية'),
            ('base_price', 'السعر الأساسي'),
            ('shop_price', 'سعر صاحب محل'),
            ('distributor_price', 'سعر موزع معتمد'),
            ('wholesale_price', 'سعر جملة'),
            ('retail_price', 'سعر تجزئة'),
            ('notes', 'ملاحظات')
        ]
        
        for field_key, field_label in fields:
            # إطار الحقل
            field_frame = tk.Frame(form_frame, bg='#f0f8ff')
            field_frame.pack(fill='x', pady=8)
            
            # التسمية
            label = tk.Label(field_frame, text=f"{field_label}:", 
                           font=("Arial", 11), bg='#f0f8ff', fg='#495057',
                           width=18, anchor='e')
            label.pack(side='right')
            
            # حقل الإدخال
            entry = tk.Entry(field_frame, font=("Arial", 11), width=30,
                           relief='solid', borderwidth=1)
            entry.pack(side='right', padx=(15, 0))
            
            self.entries[field_key] = entry
            entry_widgets.append(entry)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(self.root, bg='#f0f8ff')
        buttons_frame.pack(fill='x', padx=30, pady=20)
        
        # زر الحفظ
        save_button = tk.Button(buttons_frame, text="حفظ المنتج (Ctrl+S)", 
                               font=("Arial", 12, "bold"), bg='#28a745', fg='white',
                               width=20, height=2, command=self.save_product)
        save_button.pack(side='left', padx=10)
        
        # زر الإلغاء
        cancel_button = tk.Button(buttons_frame, text="إلغاء (Escape)", 
                                 font=("Arial", 12), bg='#dc3545', fg='white',
                                 width=20, height=2, command=self.root.destroy)
        cancel_button.pack(side='right', padx=10)
        
        # إعداد التنقل بالإنتر
        self.setup_enter_navigation(entry_widgets)
        
        # إعداد اختصارات لوحة المفاتيح
        self.setup_form_shortcuts(self.root, save_button, cancel_button)
        
        # تعليمات
        instructions = tk.Label(self.root, 
                               text="استخدم Enter للانتقال بين الحقول • Ctrl+S للحفظ • Escape للإلغاء",
                               font=("Arial", 9), bg='#f0f8ff', fg='#6c757d')
        instructions.pack(pady=10)
    
    def save_product(self):
        """حفظ المنتج"""
        # جمع البيانات
        data = {}
        for key, entry in self.entries.items():
            data[key] = entry.get().strip()
        
        # التحقق من البيانات الأساسية
        if not data['barcode'] or not data['name']:
            messagebox.showerror("خطأ", "يجب إدخال الباركود واسم المنتج على الأقل")
            return
        
        # عرض البيانات المحفوظة
        saved_data = "تم حفظ المنتج:\\n\\n"
        for key, value in data.items():
            if value:
                saved_data += f"{key}: {value}\\n"
        
        messagebox.showinfo("نجح الحفظ", saved_data)
        
        # مسح النموذج
        if messagebox.askyesno("منتج جديد", "هل تريد إضافة منتج آخر؟"):
            for entry in self.entries.values():
                entry.delete(0, 'end')
            # التركيز على أول حقل
            list(self.entries.values())[0].focus_set()
        else:
            self.root.destroy()
    
    def run(self):
        """تشغيل النموذج"""
        self.root.mainloop()

if __name__ == "__main__":
    app = ProductFormExample()
    app.run()
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        example_file = os.path.join(desktop_path, "مثال_نموذج_منتج_كامل.py")
        
        with open(example_file, 'w', encoding='utf-8') as f:
            f.write(example_code)
        
        print(f"✅ تم إنشاء المثال الكامل: {os.path.basename(example_file)}")
        return example_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء المثال الكامل: {e}")
        return None

def main():
    """Main function"""
    print("⌨️ إضافة التنقل بالإنتر في نموذج المنتج في ProTech")
    print("⌨️ Adding Enter Navigation to ProTech Product Form")
    print("="*60)
    
    print("\n💡 هذه الأداة ستقوم بـ:")
    print("• البحث عن ملف ProTech الأصلي")
    print("• إضافة دوال التنقل بالإنتر")
    print("• تعديل نافذة إضافة المنتج")
    print("• إنشاء نسخة احتياطية للأمان")
    
    created_items = []
    
    # Try to add navigation to ProTech
    if add_enter_navigation_to_protech():
        created_items.append("تعديل ProTech الأصلي")
    
    # Create manual patch
    patch = create_manual_navigation_patch()
    if patch:
        created_items.append("رقعة يدوية للتطبيق")
    
    # Create complete example
    example = create_complete_example()
    if example:
        created_items.append("مثال كامل للنموذج")
    
    # Summary
    print("\n" + "="*60)
    print("📊 ملخص النتائج:")
    
    if created_items:
        print(f"✅ تم إنشاء {len(created_items)} عنصر:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم إنشاء أي عناصر")
    
    print("\n⌨️ الميزات المضافة:")
    print("• Enter للانتقال للحقل التالي")
    print("• تحديد النص تلقائياً عند الانتقال")
    print("• Ctrl+S للحفظ السريع")
    print("• Escape للإلغاء")
    print("• التركيز على أول حقل تلقائياً")
    
    print("\n📖 كيفية الاستخدام:")
    
    if "تعديل ProTech الأصلي" in created_items:
        print("✅ تم تعديل ProTech الأصلي - جرب فتح نافذة إضافة المنتج")
    else:
        print("📝 استخدم الرقعة اليدوية:")
        print("  1. افتح 'رقعة_التنقل_بالإنتر.txt'")
        print("  2. انسخ الكود والصقه في ProTech")
        print("  3. عدل أسماء المتغيرات حسب كودك")
    
    print("\n🧪 للاختبار:")
    print("• شغل 'مثال_نموذج_منتج_كامل.py' لرؤية المثال العملي")
    print("• جرب جميع مفاتيح التنقل والاختصارات")
    
    print("\n🎉 تم إضافة التنقل بالإنتر!")
    
    if len(created_items) >= 2:
        print("✅ لديك الآن حلول متعددة للتطبيق")
    else:
        print("⚠️ قد تحتاج التطبيق اليدوي")

if __name__ == "__main__":
    main()
