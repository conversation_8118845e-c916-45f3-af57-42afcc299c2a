#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Arabic Encoding in ProTech Data
إصلاح ترميز النص العربي في بيانات ProTech

Fix Arabic text encoding issues in ProTech data files
إصلاح مشاكل ترميز النص العربي في ملفات بيانات ProTech
"""

import os
import json
import shutil
from datetime import datetime

def fix_arabic_encoding():
    """Fix Arabic encoding in data file"""
    try:
        print("🔤 إصلاح ترميز النص العربي...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        if not os.path.exists(data_path):
            print("❌ ملف البيانات غير موجود!")
            return False
        
        # Create backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{data_path}.encoding_fix_backup_{timestamp}"
        shutil.copy2(data_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}")
        
        # Read current data
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Fix Arabic text encoding
        fixed_data = {
            "suppliers": [
                {
                    "name": "مورد تجريبي",
                    "phone": "*********",
                    "address": "عنوان تجريبي"
                },
                {
                    "name": "Test Supplier",
                    "phone": "987654321",
                    "address": "Test Address"
                }
            ],
            "products": [
                {
                    "name": "منتج تجريبي",
                    "price": 100,
                    "stock": 50,
                    "barcode": "123456",
                    "category": "فئة تجريبية",
                    "supplier": "مورد تجريبي",
                    "unit": "قطعة",
                    "base_price": 80,
                    "shop_owner_price": 84,
                    "authorized_distributor_price": 92,
                    "wholesale_price": 96,
                    "retail_price": 104,
                    "notes": "ملاحظات تجريبية"
                },
                {
                    "name": "Test Product",
                    "price": 200,
                    "stock": 30,
                    "barcode": "789012",
                    "category": "Test Category",
                    "supplier": "Test Supplier",
                    "unit": "piece",
                    "base_price": 160,
                    "shop_owner_price": 168,
                    "authorized_distributor_price": 184,
                    "wholesale_price": 192,
                    "retail_price": 208,
                    "notes": "Test notes"
                }
            ],
            "customers": [
                {
                    "name": "عميل تجريبي",
                    "phone": "555666777",
                    "type": "تجزئة",
                    "balance": 0
                },
                {
                    "name": "Test Customer",
                    "phone": "444333222",
                    "type": "جملة",
                    "balance": 0
                }
            ],
            "sales": [
                {
                    "date": "2025-06-19",
                    "customer": "عميل تجريبي",
                    "customer_type": "تجزئة",
                    "products": [
                        {
                            "name": "منتج تجريبي",
                            "quantity": 1,
                            "unit_price": 104,
                            "total": 104
                        }
                    ],
                    "total": 104,
                    "payment": 104,
                    "balance": 0
                },
                {
                    "date": "2025-06-19",
                    "customer": "Test Customer",
                    "customer_type": "جملة",
                    "products": [
                        {
                            "name": "Test Product",
                            "quantity": 1,
                            "unit_price": 192,
                            "total": 192
                        }
                    ],
                    "total": 192,
                    "payment": 192,
                    "balance": 0
                }
            ],
            "last_updated": datetime.now().isoformat()
        }
        
        # Write fixed data with proper encoding
        with open(data_path, 'w', encoding='utf-8') as f:
            json.dump(fixed_data, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إصلاح ترميز النص العربي")
        print("✅ تم تحديث البيانات بمعلومات كاملة")
        
        # Verify the fix
        with open(data_path, 'r', encoding='utf-8') as f:
            verified_data = json.load(f)
        
        # Check if Arabic text is readable
        arabic_test = verified_data['suppliers'][0]['name']
        if 'مورد' in arabic_test:
            print("✅ النص العربي يظهر بشكل صحيح")
            return True
        else:
            print("❌ لا يزال هناك مشكلة في ترميز النص العربي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الترميز: {e}")
        return False

def test_data_integrity():
    """Test data integrity after encoding fix"""
    try:
        print("🧪 اختبار سلامة البيانات...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Test data structure
        required_keys = ['suppliers', 'products', 'customers', 'sales']
        for key in required_keys:
            if key not in data:
                print(f"❌ مفتاح مفقود: {key}")
                return False
            
            if not isinstance(data[key], list):
                print(f"❌ نوع بيانات خاطئ لـ {key}")
                return False
        
        # Test Arabic text
        arabic_texts = [
            data['suppliers'][0]['name'],
            data['products'][0]['name'],
            data['customers'][0]['name']
        ]
        
        for text in arabic_texts:
            if not any(ord(char) > 127 for char in text):
                print(f"⚠️ نص قد لا يكون عربي: {text}")
        
        print("✅ سلامة البيانات مؤكدة")
        print(f"📊 الموردين: {len(data['suppliers'])}")
        print(f"📦 المنتجات: {len(data['products'])}")
        print(f"👥 العملاء: {len(data['customers'])}")
        print(f"💰 المبيعات: {len(data['sales'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البيانات: {e}")
        return False

def create_sample_data():
    """Create comprehensive sample data"""
    try:
        print("📝 إنشاء بيانات نموذجية شاملة...")
        
        sample_data = {
            "suppliers": [
                {
                    "name": "شركة الإمدادات المتقدمة",
                    "phone": "0*********0",
                    "address": "شارع التجارة، المدينة الصناعية"
                },
                {
                    "name": "مؤسسة التوريد الذهبي",
                    "phone": "01987654321",
                    "address": "طريق الملك فهد، الرياض"
                },
                {
                    "name": "Advanced Supply Co.",
                    "phone": "01555666777",
                    "address": "Industrial City, Trade Street"
                }
            ],
            "products": [
                {
                    "name": "جهاز كمبيوتر محمول",
                    "price": 3500,
                    "stock": 25,
                    "barcode": "LAP001",
                    "category": "إلكترونيات",
                    "supplier": "شركة الإمدادات المتقدمة",
                    "unit": "قطعة",
                    "base_price": 3000,
                    "shop_owner_price": 3150,
                    "authorized_distributor_price": 3450,
                    "wholesale_price": 3600,
                    "retail_price": 3900,
                    "notes": "جهاز عالي الجودة مع ضمان سنتين"
                },
                {
                    "name": "طابعة ليزر ملونة",
                    "price": 1200,
                    "stock": 15,
                    "barcode": "PRT002",
                    "category": "مكتبية",
                    "supplier": "مؤسسة التوريد الذهبي",
                    "unit": "قطعة",
                    "base_price": 1000,
                    "shop_owner_price": 1050,
                    "authorized_distributor_price": 1150,
                    "wholesale_price": 1200,
                    "retail_price": 1300,
                    "notes": "طابعة سريعة وموفرة للحبر"
                },
                {
                    "name": "Wireless Mouse",
                    "price": 85,
                    "stock": 100,
                    "barcode": "MOU003",
                    "category": "Accessories",
                    "supplier": "Advanced Supply Co.",
                    "unit": "piece",
                    "base_price": 70,
                    "shop_owner_price": 74,
                    "authorized_distributor_price": 81,
                    "wholesale_price": 84,
                    "retail_price": 91,
                    "notes": "High precision wireless mouse"
                }
            ],
            "customers": [
                {
                    "name": "محمد أحمد التاجر",
                    "phone": "01122334455",
                    "type": "صاحب محل",
                    "balance": 0
                },
                {
                    "name": "شركة التوزيع الكبرى",
                    "phone": "01199887766",
                    "type": "موزع معتمد",
                    "balance": 0
                },
                {
                    "name": "مؤسسة البيع بالجملة",
                    "phone": "01155443322",
                    "type": "جملة",
                    "balance": 0
                },
                {
                    "name": "عميل التجزئة",
                    "phone": "01166778899",
                    "type": "تجزئة",
                    "balance": 0
                }
            ],
            "sales": [
                {
                    "date": "2025-06-19",
                    "customer": "محمد أحمد التاجر",
                    "customer_type": "صاحب محل",
                    "products": [
                        {
                            "name": "جهاز كمبيوتر محمول",
                            "quantity": 2,
                            "unit_price": 3150,
                            "total": 6300
                        }
                    ],
                    "total": 6300,
                    "payment": 6300,
                    "balance": 0
                },
                {
                    "date": "2025-06-19",
                    "customer": "شركة التوزيع الكبرى",
                    "customer_type": "موزع معتمد",
                    "products": [
                        {
                            "name": "طابعة ليزر ملونة",
                            "quantity": 5,
                            "unit_price": 1150,
                            "total": 5750
                        }
                    ],
                    "total": 5750,
                    "payment": 5750,
                    "balance": 0
                }
            ],
            "last_updated": datetime.now().isoformat()
        }
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        sample_file = "protech_sample_data.json"
        sample_path = os.path.join(data_dir, sample_file)
        
        with open(sample_path, 'w', encoding='utf-8') as f:
            json.dump(sample_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم إنشاء ملف البيانات النموذجية: {sample_file}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات النموذجية: {e}")
        return False

def main():
    """Main encoding fix function"""
    print("🔤 مصلح ترميز النص العربي لـ ProTech")
    print("🔤 ProTech Arabic Encoding Fixer")
    print("="*50)
    
    try:
        # Fix encoding
        if fix_arabic_encoding():
            print("\n✅ تم إصلاح ترميز النص العربي بنجاح!")
            
            # Test data integrity
            if test_data_integrity():
                print("✅ سلامة البيانات مؤكدة")
            
            # Create sample data
            if create_sample_data():
                print("✅ تم إنشاء بيانات نموذجية إضافية")
            
            print("\n🎉 إصلاح الترميز مكتمل بنجاح!")
            print("🎉 Encoding fix completed successfully!")
            
            return True
        else:
            print("\n❌ فشل في إصلاح الترميز")
            return False
        
    except Exception as e:
        print(f"❌ خطأ عام في إصلاح الترميز: {e}")
        return False

if __name__ == "__main__":
    main()
