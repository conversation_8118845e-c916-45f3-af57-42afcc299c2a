#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import subprocess

def main():
    """Launch ProTech from Desktop with guaranteed data"""
    print("🚀 ProTech Desktop Launcher")
    print("=" * 40)
    
    # Desktop path
    desktop_path = r"c:\Users\<USER>\OneDrive\Desktop\accounting program"
    
    print(f"📁 Desktop path: {desktop_path}")
    
    # Check if desktop folder exists
    if not os.path.exists(desktop_path):
        print("❌ Desktop folder not found!")
        input("Press Enter to exit...")
        return
    
    # Change to desktop directory
    os.chdir(desktop_path)
    print(f"✅ Changed to desktop directory")
    
    # Check for ProTech file
    protech_file = "protech_simple_working.py"
    if not os.path.exists(protech_file):
        print(f"❌ ProTech file not found: {protech_file}")
        input("Press Enter to exit...")
        return
    
    print(f"✅ ProTech file found: {protech_file}")
    
    # Check for data file
    data_file = "protech_simple_data.json"
    if os.path.exists(data_file):
        print(f"✅ Data file found: {data_file}")
    else:
        print(f"⚠️ Data file not found: {data_file}")
        print("   البرنامج سينشئ بيانات نموذجية")
        print("   Program will create sample data")
    
    print("\n🚀 Starting ProTech...")
    print("   إذا لم تظهر البيانات، اضغط زر 'إعادة تحميل البيانات' الأحمر")
    print("   If data doesn't show, click the red 'Reload Data' button")
    print()
    
    try:
        # Launch ProTech
        subprocess.run([sys.executable, protech_file])
        print("✅ ProTech closed successfully")
    except Exception as e:
        print(f"❌ Error launching ProTech: {e}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
