#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple Enter Navigation Solution
حل بسيط للتنقل بالإنتر

Simple and safe solution for Enter key navigation in forms
حل بسيط وآمن للتنقل بمفتاح الإنتر في النماذج
"""

import os
from datetime import datetime

def create_simple_enter_navigation_guide():
    """Create a simple guide for implementing Enter navigation"""
    try:
        print("📖 إنشاء دليل التنقل بالإنتر البسيط")
        print("📖 Creating Simple Enter Navigation Guide")
        print("="*50)
        
        # Create guide file
        guide_content = '''# دليل إضافة التنقل بالإنتر في ProTech
# Guide for Adding Enter Navigation in ProTech

## الكود المطلوب إضافته:

### 1. دالة إعداد التنقل بالإنتر:

```python
def setup_enter_navigation(self, entry_widgets):
    """إعداد التنقل بالإنتر بين الحقول"""
    def on_enter_key(event, current_index):
        try:
            # الانتقال للحقل التالي
            next_index = (current_index + 1) % len(entry_widgets)
            next_widget = entry_widgets[next_index]
            next_widget.focus_set()
            
            # تحديد النص إذا كان حقل إدخال
            if hasattr(next_widget, 'select_range'):
                next_widget.select_range(0, 'end')
            
            return "break"  # منع السلوك الافتراضي للإنتر
        except Exception as e:
            print(f"خطأ في التنقل: {e}")
            return None
    
    # ربط مفتاح الإنتر بكل حقل
    for i, entry_widget in enumerate(entry_widgets):
        if entry_widget and hasattr(entry_widget, 'bind'):
            entry_widget.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
            entry_widget.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))
    
    # التركيز على أول حقل
    if entry_widgets and entry_widgets[0]:
        entry_widgets[0].focus_set()
```

### 2. كيفية الاستخدام في نافذة إضافة المنتج:

```python
# بعد إنشاء جميع حقول الإدخال، أنشئ قائمة بها:
entry_widgets = [
    barcode_entry,
    name_entry,
    category_entry,
    supplier_entry,
    phone_entry,
    unit_entry,
    quantity_entry,
    base_price_entry,
    shop_owner_entry,
    distributor_entry,
    wholesale_entry,
    retail_entry,
    notes_entry
]

# ثم استدعي دالة إعداد التنقل:
self.setup_enter_navigation(entry_widgets)
```

### 3. إضافة اختصارات إضافية:

```python
def setup_form_shortcuts(self, window, save_button, cancel_button):
    """إعداد اختصارات لوحة المفاتيح"""
    # Ctrl+S للحفظ
    window.bind('<Control-s>', lambda event: save_button.invoke())
    window.bind('<Control-S>', lambda event: save_button.invoke())
    
    # Escape للإلغاء
    window.bind('<Escape>', lambda event: cancel_button.invoke())
```

## خطوات التطبيق:

1. افتح ملف protech_simple_working.py
2. ابحث عن دالة إضافة المنتج (add_product أو ما شابه)
3. أضف الكود أعلاه في بداية الكلاس
4. في نافذة إضافة المنتج، أنشئ قائمة بحقول الإدخال
5. استدعي setup_enter_navigation مع القائمة
6. احفظ الملف واختبر

## نصائح:

- تأكد من ترتيب الحقول في القائمة حسب التسلسل المطلوب
- يمكن إضافة المزيد من الاختصارات حسب الحاجة
- اختبر التنقل في نافذة الاختبار أولاً

## مثال كامل:

انظر ملف "اختبار_التنقل_بالإنتر.py" لمثال كامل وعملي
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        guide_file = os.path.join(desktop_path, "دليل_التنقل_بالإنتر.md")
        
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"✅ تم إنشاء الدليل: {os.path.basename(guide_file)}")
        return guide_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الدليل: {e}")
        return None

def create_enhanced_product_form():
    """Create enhanced product form with Enter navigation"""
    try:
        print("\n📝 إنشاء نموذج منتج محسن...")
        
        enhanced_form = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Enhanced Product Form with Enter Navigation
نموذج منتج محسن مع التنقل بالإنتر
"""

import tkinter as tk
from tkinter import ttk, messagebox

class EnhancedProductForm:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نموذج منتج محسن - مع التنقل بالإنتر")
        self.root.geometry("600x700")
        self.root.resizable(False, False)
        
        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (600 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"600x700+{x}+{y}")
        
        self.create_widgets()
        self.setup_navigation()
    
    def create_widgets(self):
        """إنشاء عناصر النموذج"""
        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg='#f8f9fa', padx=30, pady=20)
        main_frame.pack(fill='both', expand=True)
        
        # العنوان
        title_label = tk.Label(main_frame, text="إضافة منتج جديد", 
                              font=("Arial", 18, "bold"), bg='#f8f9fa', fg='#2c3e50')
        title_label.pack(pady=(0, 30))
        
        # قائمة حقول الإدخال
        self.entry_widgets = []
        
        # تعريف الحقول
        fields = [
            ("الباركود", "barcode"),
            ("اسم المنتج", "name"),
            ("الفئة", "category"),
            ("المورد", "supplier"),
            ("رقم الهاتف", "phone"),
            ("الوحدة (صندوق/كيلو/قطعة)", "unit"),
            ("الكمية", "quantity"),
            ("السعر الأساسي", "base_price"),
            ("سعر صاحب محل", "shop_owner_price"),
            ("سعر موزع معتمد", "distributor_price"),
            ("سعر جملة", "wholesale_price"),
            ("سعر تجزئة", "retail_price"),
            ("ملاحظات", "notes")
        ]
        
        # إنشاء الحقول
        self.entries = {}
        for field_name, field_key in fields:
            self.create_field(main_frame, field_name, field_key)
        
        # إطار الأزرار
        buttons_frame = tk.Frame(main_frame, bg='#f8f9fa')
        buttons_frame.pack(fill='x', pady=30)
        
        # زر الحفظ
        self.save_button = tk.Button(buttons_frame, text="حفظ المنتج (Ctrl+S)", 
                                   font=("Arial", 12, "bold"), bg='#28a745', fg='white',
                                   width=20, height=2, command=self.save_product)
        self.save_button.pack(side='left', padx=10)
        
        # زر الإلغاء
        self.cancel_button = tk.Button(buttons_frame, text="إلغاء (Escape)", 
                                     font=("Arial", 12), bg='#dc3545', fg='white',
                                     width=20, height=2, command=self.cancel)
        self.cancel_button.pack(side='right', padx=10)
        
        # زر المساعدة
        help_button = tk.Button(buttons_frame, text="مساعدة (F1)", 
                               font=("Arial", 10), bg='#17a2b8', fg='white',
                               width=15, height=1, command=self.show_help)
        help_button.pack(side='bottom', pady=10)
        
        # تعليمات
        instructions = tk.Label(main_frame, 
                               text="استخدم Enter أو Tab للانتقال • Ctrl+S للحفظ • Escape للإلغاء • F1 للمساعدة",
                               font=("Arial", 9), bg='#f8f9fa', fg='#6c757d')
        instructions.pack(pady=(10, 0))
    
    def create_field(self, parent, label_text, field_key):
        """إنشاء حقل إدخال"""
        # إطار الحقل
        field_frame = tk.Frame(parent, bg='#f8f9fa')
        field_frame.pack(fill='x', pady=8)
        
        # التسمية
        label = tk.Label(field_frame, text=f"{label_text}:", 
                        font=("Arial", 11), bg='#f8f9fa', fg='#495057',
                        width=20, anchor='e')
        label.pack(side='right')
        
        # حقل الإدخال
        entry = tk.Entry(field_frame, font=("Arial", 11), width=35,
                        relief='solid', borderwidth=1)
        entry.pack(side='right', padx=(15, 0))
        
        # إضافة للقوائم
        self.entries[field_key] = entry
        self.entry_widgets.append(entry)
    
    def setup_navigation(self):
        """إعداد التنقل بالإنتر"""
        def on_enter_key(event, current_index):
            try:
                # الانتقال للحقل التالي
                next_index = (current_index + 1) % len(self.entry_widgets)
                next_widget = self.entry_widgets[next_index]
                next_widget.focus_set()
                
                # تحديد النص
                if hasattr(next_widget, 'select_range'):
                    next_widget.select_range(0, 'end')
                
                return "break"
            except Exception as e:
                print(f"خطأ في التنقل: {e}")
                return None
        
        # ربط مفتاح الإنتر
        for i, entry_widget in enumerate(self.entry_widgets):
            entry_widget.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
            entry_widget.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))
        
        # اختصارات لوحة المفاتيح
        self.root.bind('<Control-s>', lambda event: self.save_product())
        self.root.bind('<Control-S>', lambda event: self.save_product())
        self.root.bind('<Escape>', lambda event: self.cancel())
        self.root.bind('<F1>', lambda event: self.show_help())
        
        # التركيز على أول حقل
        if self.entry_widgets:
            self.entry_widgets[0].focus_set()
    
    def save_product(self):
        """حفظ المنتج"""
        # جمع البيانات
        data = {}
        for key, entry in self.entries.items():
            data[key] = entry.get().strip()
        
        # التحقق من البيانات الأساسية
        if not data['barcode'] or not data['name']:
            messagebox.showerror("خطأ", "يجب إدخال الباركود واسم المنتج على الأقل")
            return
        
        # عرض البيانات المحفوظة
        saved_data = "تم حفظ المنتج:\\n\\n"
        for key, value in data.items():
            if value:
                field_names = {
                    'barcode': 'الباركود',
                    'name': 'اسم المنتج',
                    'category': 'الفئة',
                    'supplier': 'المورد',
                    'phone': 'رقم الهاتف',
                    'unit': 'الوحدة',
                    'quantity': 'الكمية',
                    'base_price': 'السعر الأساسي',
                    'shop_owner_price': 'سعر صاحب محل',
                    'distributor_price': 'سعر موزع معتمد',
                    'wholesale_price': 'سعر جملة',
                    'retail_price': 'سعر تجزئة',
                    'notes': 'ملاحظات'
                }
                saved_data += f"{field_names.get(key, key)}: {value}\\n"
        
        messagebox.showinfo("نجح الحفظ", saved_data)
        
        # مسح النموذج للمنتج التالي
        if messagebox.askyesno("منتج جديد", "هل تريد إضافة منتج آخر؟"):
            self.clear_form()
        else:
            self.root.destroy()
    
    def clear_form(self):
        """مسح النموذج"""
        for entry in self.entry_widgets:
            entry.delete(0, 'end')
        
        # التركيز على أول حقل
        if self.entry_widgets:
            self.entry_widgets[0].focus_set()
    
    def cancel(self):
        """إلغاء"""
        if messagebox.askyesno("تأكيد الإلغاء", "هل أنت متأكد من الإلغاء؟"):
            self.root.destroy()
    
    def show_help(self):
        """عرض المساعدة"""
        help_text = """
مساعدة نموذج المنتج:

⌨️ اختصارات لوحة المفاتيح:
• Enter: الانتقال للحقل التالي
• Tab: الانتقال للحقل التالي
• Shift+Tab: الانتقال للحقل السابق
• Ctrl+S: حفظ المنتج
• Escape: إلغاء
• F1: عرض هذه المساعدة

📝 الحقول المطلوبة:
• الباركود (مطلوب)
• اسم المنتج (مطلوب)

💡 نصائح:
• استخدم Enter للتنقل السريع
• يمكن ترك الحقول الاختيارية فارغة
• سيتم تحديد النص تلقائياً عند الانتقال
        """
        messagebox.showinfo("مساعدة", help_text)
    
    def run(self):
        """تشغيل النموذج"""
        self.root.mainloop()

if __name__ == "__main__":
    app = EnhancedProductForm()
    app.run()
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        form_file = os.path.join(desktop_path, "نموذج_منتج_محسن.py")
        
        with open(form_file, 'w', encoding='utf-8') as f:
            f.write(enhanced_form)
        
        print(f"✅ تم إنشاء النموذج المحسن: {os.path.basename(form_file)}")
        return form_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء النموذج المحسن: {e}")
        return None

def create_implementation_instructions():
    """Create step-by-step implementation instructions"""
    try:
        print("\n📋 إنشاء تعليمات التطبيق...")
        
        instructions = '''# تعليمات تطبيق التنقل بالإنتر في ProTech

## الخطوات المطلوبة:

### 1. نسخ الكود الأساسي:
انسخ الكود التالي وأضفه في ملف protech_simple_working.py:

```python
def setup_enter_navigation(self, entry_widgets):
    """إعداد التنقل بالإنتر بين الحقول"""
    def on_enter_key(event, current_index):
        try:
            next_index = (current_index + 1) % len(entry_widgets)
            next_widget = entry_widgets[next_index]
            next_widget.focus_set()
            if hasattr(next_widget, 'select_range'):
                next_widget.select_range(0, 'end')
            return "break"
        except Exception as e:
            print(f"خطأ في التنقل: {e}")
            return None
    
    for i, entry_widget in enumerate(entry_widgets):
        if entry_widget and hasattr(entry_widget, 'bind'):
            entry_widget.bind('<Return>', lambda event, idx=i: on_enter_key(event, idx))
            entry_widget.bind('<KP_Enter>', lambda event, idx=i: on_enter_key(event, idx))
    
    if entry_widgets and entry_widgets[0]:
        entry_widgets[0].focus_set()
```

### 2. تطبيق في نافذة إضافة المنتج:
في دالة إضافة المنتج، بعد إنشاء جميع حقول الإدخال:

```python
# إنشاء قائمة بحقول الإدخال بالترتيب المطلوب
entry_widgets = [
    barcode_entry,      # حقل الباركود
    name_entry,         # حقل اسم المنتج
    category_entry,     # حقل الفئة
    supplier_entry,     # حقل المورد
    phone_entry,        # حقل الهاتف
    unit_entry,         # حقل الوحدة
    quantity_entry,     # حقل الكمية
    base_price_entry,   # حقل السعر الأساسي
    # ... باقي الحقول
]

# تفعيل التنقل بالإنتر
self.setup_enter_navigation(entry_widgets)
```

### 3. إضافة اختصارات إضافية (اختياري):

```python
def setup_form_shortcuts(self, window, save_button, cancel_button):
    window.bind('<Control-s>', lambda event: save_button.invoke())
    window.bind('<Control-S>', lambda event: save_button.invoke())
    window.bind('<Escape>', lambda event: cancel_button.invoke())

# في نافذة إضافة المنتج:
self.setup_form_shortcuts(add_window, save_button, cancel_button)
```

## الاختبار:

1. شغل "نموذج_منتج_محسن.py" لرؤية المثال العملي
2. شغل "اختبار_التنقل_بالإنتر.py" لاختبار التنقل
3. طبق الكود في ProTech واختبر

## نصائح مهمة:

- تأكد من ترتيب الحقول في القائمة
- اختبر التنقل قبل التطبيق النهائي
- يمكن إضافة المزيد من الاختصارات
- احتفظ بنسخة احتياطية قبل التعديل

## في حالة المشاكل:

- تحقق من أسماء متغيرات حقول الإدخال
- تأكد من أن الحقول تم إنشاؤها قبل استدعاء setup_enter_navigation
- اختبر في النماذج المنفصلة أولاً
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        instructions_file = os.path.join(desktop_path, "تعليمات_تطبيق_التنقل.md")
        
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write(instructions)
        
        print(f"✅ تم إنشاء التعليمات: {os.path.basename(instructions_file)}")
        return instructions_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء التعليمات: {e}")
        return None

def main():
    """Main function"""
    print("⌨️ حل بسيط للتنقل بالإنتر في نماذج ProTech")
    print("⌨️ Simple Enter Navigation Solution for ProTech Forms")
    print("="*60)
    
    print("\n💡 الحل المقترح:")
    print("بدلاً من تعديل الملف الأصلي مباشرة، سنوفر:")
    print("• دليل شامل للتطبيق")
    print("• نماذج جاهزة للاختبار")
    print("• تعليمات خطوة بخطوة")
    print("• أمثلة عملية")
    
    created_items = []
    
    # Create guide
    guide = create_simple_enter_navigation_guide()
    if guide:
        created_items.append("دليل التنقل بالإنتر")
    
    # Create enhanced form
    enhanced_form = create_enhanced_product_form()
    if enhanced_form:
        created_items.append("نموذج منتج محسن")
    
    # Create implementation instructions
    instructions = create_implementation_instructions()
    if instructions:
        created_items.append("تعليمات التطبيق")
    
    # Summary
    print("\n" + "="*60)
    print("📊 ملخص الملفات المنشأة:")
    
    if created_items:
        print(f"✅ تم إنشاء {len(created_items)} ملف:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم إنشاء أي ملفات")
    
    print("\n📖 كيفية الاستخدام:")
    
    print("\n1️⃣ للاختبار والتعلم:")
    print("• شغل 'نموذج_منتج_محسن.py' - نموذج كامل مع التنقل")
    print("• شغل 'اختبار_التنقل_بالإنتر.py' - اختبار بسيط")
    
    print("\n2️⃣ للتطبيق في ProTech:")
    print("• اقرأ 'دليل_التنقل_بالإنتر.md' - الدليل الشامل")
    print("• اتبع 'تعليمات_تطبيق_التنقل.md' - خطوة بخطوة")
    
    print("\n3️⃣ الميزات المتاحة:")
    print("• Enter للانتقال للحقل التالي")
    print("• Tab للانتقال للحقل التالي")
    print("• Ctrl+S للحفظ السريع")
    print("• Escape للإلغاء")
    print("• F1 لعرض المساعدة")
    print("• تحديد النص تلقائياً")
    
    print("\n💡 نصائح:")
    print("• اختبر النماذج المنفصلة أولاً")
    print("• احتفظ بنسخة احتياطية قبل التعديل")
    print("• طبق التغييرات تدريجياً")
    
    print("\n🎉 تم إنشاء حل شامل للتنقل بالإنتر!")
    
    if len(created_items) >= 3:
        print("✅ لديك الآن جميع الأدوات اللازمة للتطبيق")
    else:
        print("⚠️ قد تحتاج إعادة تشغيل الأداة")

if __name__ == "__main__":
    main()
