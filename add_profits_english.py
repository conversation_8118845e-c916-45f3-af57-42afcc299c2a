#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Profits Report - English Version
إضافة تقرير الأرباح - النسخة الإنجليزية

Add profits and financial balance report with English text to avoid encoding issues
إضافة تقرير الأرباح والرصيد المالي بنص إنجليزي لتجنب مشاكل الترميز
"""

import os
import shutil
from datetime import datetime

def add_profits_english():
    """إضافة تقرير الأرباح بالإنجليزية"""
    try:
        print("💰 إضافة تقرير الأرباح بالإنجليزية")
        print("💰 Adding Profits Report in English")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.profits_english_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # English profits methods
        profits_methods = '''
    def calculate_store_profits(self):
        """Calculate store profits"""
        try:
            profits_data = {
                'total_revenue': 0,
                'total_cost': 0,
                'gross_profit': 0,
                'profit_margin': 0
            }
            
            sales = self.get_real_sales_data()
            products = self.get_real_products_data()
            
            # Calculate total revenue
            for sale in sales:
                sale_total = float(sale.get('total', 0))
                profits_data['total_revenue'] += sale_total
            
            # Calculate total cost
            for sale in sales:
                sale_cost = 0
                for item in sale.get('items', []):
                    item_name = item.get('name', '')
                    item_quantity = float(item.get('quantity', 0))
                    
                    # Find product cost
                    for product in products:
                        if product.get('name') == item_name:
                            base_price = float(product.get('base_price', 0))
                            sale_cost += item_quantity * base_price
                            break
                
                profits_data['total_cost'] += sale_cost
            
            # Calculate profits
            profits_data['gross_profit'] = profits_data['total_revenue'] - profits_data['total_cost']
            
            if profits_data['total_revenue'] > 0:
                profits_data['profit_margin'] = (profits_data['gross_profit'] / profits_data['total_revenue']) * 100
            
            return profits_data
            
        except Exception as e:
            print(f"Error calculating profits: {e}")
            return {}
    
    def calculate_financial_balance(self):
        """Calculate financial balance"""
        try:
            balance_data = {
                'inventory_value': 0,
                'accounts_receivable': 0,
                'cash_sales': 0,
                'customer_credits': 0,
                'net_worth': 0
            }
            
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            
            # Calculate inventory value
            for product in products:
                quantity = float(product.get('quantity', 0))
                base_price = float(product.get('base_price', 0))
                balance_data['inventory_value'] += quantity * base_price
            
            # Calculate customer balances
            for customer in customers:
                balance = float(customer.get('balance', 0))
                if balance > 0:
                    balance_data['accounts_receivable'] += balance
                else:
                    balance_data['customer_credits'] += abs(balance)
            
            # Calculate cash from sales
            balance_data['cash_sales'] = sum(float(sale.get('total', 0)) for sale in sales)
            
            # Calculate net worth
            balance_data['net_worth'] = (
                balance_data['inventory_value'] + 
                balance_data['accounts_receivable'] + 
                balance_data['cash_sales'] - 
                balance_data['customer_credits']
            )
            
            return balance_data
            
        except Exception as e:
            print(f"Error calculating balance: {e}")
            return {}
    
    def show_profits_and_balance(self):
        """Show profits and financial balance report"""
        try:
            self.report_title.config(text="Profits & Financial Balance")
            
            # Calculate data
            profits_data = self.calculate_store_profits()
            balance_data = self.calculate_financial_balance()
            
            # Get exchange rate
            exchange_rate = getattr(self, 'get_usd_exchange_rate', lambda: 89500)()
            
            # Create report
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            report = "PROFITS & FINANCIAL BALANCE REPORT\\n"
            report += "="*60 + "\\n\\n"
            report += f"Date: {current_time}\\n"
            report += f"Exchange Rate: 1 USD = {exchange_rate:,} LBP\\n\\n"
            
            # PROFITS SECTION
            if profits_data:
                total_revenue = profits_data.get('total_revenue', 0)
                total_cost = profits_data.get('total_cost', 0)
                gross_profit = profits_data.get('gross_profit', 0)
                profit_margin = profits_data.get('profit_margin', 0)
                
                report += "PROFITS ANALYSIS\\n"
                report += "="*40 + "\\n\\n"
                
                report += "Total Revenue:\\n"
                report += f"• {total_revenue:,.0f} LBP\\n"
                report += f"• ${total_revenue/exchange_rate:,.2f} USD\\n\\n"
                
                report += "Total Cost:\\n"
                report += f"• {total_cost:,.0f} LBP\\n"
                report += f"• ${total_cost/exchange_rate:,.2f} USD\\n\\n"
                
                report += "Gross Profit:\\n"
                report += f"• {gross_profit:,.0f} LBP\\n"
                report += f"• ${gross_profit/exchange_rate:,.2f} USD 💰\\n\\n"
                
                report += f"Profit Margin: {profit_margin:.1f}%\\n\\n"
                
                # Profit analysis
                if profit_margin >= 30:
                    report += "🟢 Excellent profit margin (30%+)\\n"
                elif profit_margin >= 20:
                    report += "🟡 Good profit margin (20-30%)\\n"
                elif profit_margin >= 10:
                    report += "🟠 Average profit margin (10-20%)\\n"
                else:
                    report += "🔴 Low profit margin (<10%)\\n"
            
            report += "\\n" + "-"*50 + "\\n\\n"
            
            # FINANCIAL BALANCE SECTION
            if balance_data:
                inventory_value = balance_data.get('inventory_value', 0)
                accounts_receivable = balance_data.get('accounts_receivable', 0)
                cash_sales = balance_data.get('cash_sales', 0)
                customer_credits = balance_data.get('customer_credits', 0)
                net_worth = balance_data.get('net_worth', 0)
                
                report += "FINANCIAL BALANCE\\n"
                report += "="*40 + "\\n\\n"
                
                report += "ASSETS:\\n"
                report += "-"*20 + "\\n"
                
                report += f"Inventory Value:\\n"
                report += f"• {inventory_value:,.0f} LBP\\n"
                report += f"• ${inventory_value/exchange_rate:,.2f} USD\\n\\n"
                
                report += f"Accounts Receivable:\\n"
                report += f"• {accounts_receivable:,.0f} LBP\\n"
                report += f"• ${accounts_receivable/exchange_rate:,.2f} USD\\n\\n"
                
                report += f"Cash from Sales:\\n"
                report += f"• {cash_sales:,.0f} LBP\\n"
                report += f"• ${cash_sales/exchange_rate:,.2f} USD\\n\\n"
                
                total_assets = inventory_value + accounts_receivable + cash_sales
                report += f"Total Assets:\\n"
                report += f"• {total_assets:,.0f} LBP\\n"
                report += f"• ${total_assets/exchange_rate:,.2f} USD\\n\\n"
                
                report += "LIABILITIES:\\n"
                report += "-"*20 + "\\n"
                
                report += f"Customer Credits:\\n"
                report += f"• {customer_credits:,.0f} LBP\\n"
                report += f"• ${customer_credits/exchange_rate:,.2f} USD\\n\\n"
                
                report += "NET WORTH:\\n"
                report += "-"*20 + "\\n"
                
                report += f"Store Net Worth:\\n"
                report += f"• {net_worth:,.0f} LBP\\n"
                report += f"• ${net_worth/exchange_rate:,.2f} USD 👑\\n\\n"
            
            # FINANCIAL RATIOS
            if profits_data and balance_data:
                report += "FINANCIAL RATIOS\\n"
                report += "="*40 + "\\n\\n"
                
                total_assets = balance_data.get('inventory_value', 0) + balance_data.get('accounts_receivable', 0) + balance_data.get('cash_sales', 0)
                gross_profit = profits_data.get('gross_profit', 0)
                total_revenue = profits_data.get('total_revenue', 0)
                net_worth = balance_data.get('net_worth', 0)
                
                if total_assets > 0:
                    roa = (gross_profit / total_assets) * 100
                    report += f"Return on Assets (ROA): {roa:.1f}%\\n"
                
                if net_worth > 0:
                    roe = (gross_profit / net_worth) * 100
                    report += f"Return on Equity (ROE): {roe:.1f}%\\n"
                
                if total_assets > 0:
                    asset_turnover = total_revenue / total_assets
                    report += f"Asset Turnover: {asset_turnover:.2f}\\n"
                
                report += "\\n"
            
            # RECOMMENDATIONS
            report += "RECOMMENDATIONS\\n"
            report += "="*40 + "\\n\\n"
            
            if profits_data and balance_data:
                profit_margin = profits_data.get('profit_margin', 0)
                net_worth_usd = balance_data.get('net_worth', 0) / exchange_rate
                
                if profit_margin < 15:
                    report += "• Improve profit margins\\n"
                    report += "• Review pricing strategy\\n"
                
                if net_worth_usd < 10000:
                    report += "• Increase working capital\\n"
                    report += "• Focus on profitable products\\n"
                
                report += "• Monitor cash flow daily\\n"
                report += "• Follow up on receivables\\n"
                report += "• Update prices with exchange rate\\n"
                report += "• Keep profit reserves\\n"
                report += "• Review monthly\\n"
            
            # SUMMARY
            report += "\\n" + "="*60 + "\\n"
            report += "EXECUTIVE SUMMARY\\n"
            report += "="*60 + "\\n\\n"
            
            if profits_data and balance_data:
                gross_profit_usd = profits_data.get('gross_profit', 0) / exchange_rate
                net_worth_usd = balance_data.get('net_worth', 0) / exchange_rate
                profit_margin = profits_data.get('profit_margin', 0)
                
                report += f"• Total Profit: ${gross_profit_usd:,.2f} USD\\n"
                report += f"• Net Worth: ${net_worth_usd:,.2f} USD\\n"
                report += f"• Profit Margin: {profit_margin:.1f}%\\n\\n"
                
                # Overall assessment
                if net_worth_usd >= 50000 and profit_margin >= 20:
                    report += "🏆 EXCELLENT financial position!\\n"
                elif net_worth_usd >= 25000 and profit_margin >= 15:
                    report += "✅ GOOD financial position\\n"
                elif net_worth_usd >= 10000 and profit_margin >= 10:
                    report += "⚠️ AVERAGE financial position\\n"
                else:
                    report += "🚨 NEEDS ATTENTION\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
            print("✅ تم عرض تقرير الأرباح والرصيد المالي")
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقرير: {e}")
            error_msg = "Error loading Profits & Financial Balance report\\n"
            error_msg += "Please check data and try again"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_msg)
'''
        
        # Add profits methods before the last method
        last_method = content.rfind("\n    def show_store_balance_usd(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + profits_methods + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + profits_methods + content[last_method:]
        
        # Add new button for profits report with English text
        if "btn6 = tk.Button(sidebar, text=\"رصيد المحل\"" in content:
            btn6_pos = content.find("btn6.pack(pady=3, padx=10, fill='x')")
            if btn6_pos != -1:
                btn6_end = content.find("\\n", btn6_pos) + 1
                
                new_button = '''
            btn_profits = tk.Button(sidebar, text="Profits & Balance", 
                                  font=("Arial", 10), bg='#27ae60', fg='white',
                                  width=18, height=2, command=self.show_profits_and_balance)
            btn_profits.pack(pady=3, padx=10, fill='x')
'''
                
                content = content[:btn6_end] + new_button + content[btn6_end:]
                print("✅ تم إضافة زر Profits & Balance")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة تقرير الأرباح والرصيد المالي")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة تقرير الأرباح: {e}")
        return False

def main():
    """Main function"""
    print("💰 إضافة تقرير الأرباح والرصيد المالي لـ ProTech")
    print("💰 Adding Profits and Financial Balance Report to ProTech")
    print("="*70)
    
    if add_profits_english():
        print("\n🎉 تم إضافة تقرير الأرباح والرصيد المالي بنجاح!")
        
        print("\n💰 الميزات الجديدة:")
        print("• 📊 تحليل الأرباح المفصل")
        print("• 💼 الرصيد المالي (الميزانية)")
        print("• 📈 النسب المالية")
        print("• 💡 التوصيات المالية")
        print("• 📋 الملخص التنفيذي")
        print("• 🔄 عرض بالدولار والليرة")
        
        print("\n📊 تحليل الأرباح:")
        print("• إجمالي الإيرادات")
        print("• إجمالي التكاليف")
        print("• إجمالي الأرباح")
        print("• هامش الربح")
        print("• تقييم مستوى الربحية")
        
        print("\n💼 الرصيد المالي:")
        print("• قيمة المخزون")
        print("• ذمم العملاء")
        print("• النقد من المبيعات")
        print("• أرصدة العملاء الدائنة")
        print("• صافي ثروة المحل")
        
        print("\n📈 النسب المالية:")
        print("• العائد على الأصول (ROA)")
        print("• العائد على حقوق الملكية (ROE)")
        print("• معدل دوران الأصول")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. انقر على زر 'Profits & Balance'")
        print("4. استعرض التقرير الشامل")
        
        print("\n🏆 مستويات التقييم:")
        print("• 🏆 ممتاز: +$50K و +20% ربح")
        print("• ✅ جيد: +$25K و +15% ربح")
        print("• ⚠️ متوسط: +$10K و +10% ربح")
        print("• 🚨 يحتاج اهتمام: أقل من ذلك")
        
    else:
        print("\n❌ فشل في إضافة تقرير الأرباح والرصيد المالي")
    
    print("\n🔧 تم الانتهاء من إضافة التقرير المالي")

if __name__ == "__main__":
    main()
