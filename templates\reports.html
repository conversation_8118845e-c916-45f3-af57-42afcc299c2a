{% extends "base.html" %}

{% block title %}Reports & Analytics - ProTech Accounting{% endblock %}
{% block page_title %}Reports & Analytics{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div>
        <h2 class="text-2xl font-bold text-gray-900">Reports & Analytics</h2>
        <p class="text-gray-600">Comprehensive business reports and performance analytics</p>
    </div>

    <!-- Report Categories -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Sales Reports -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="bg-green-100 p-3 rounded-lg">
                    <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                    </svg>
                </div>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Sales Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Track sales performance and trends</p>
            <div class="space-y-2">
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Daily Sales Summary</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Monthly Sales Report</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Customer Sales Analysis</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Product Performance</button>
            </div>
        </div>

        <!-- Inventory Reports -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="bg-blue-100 p-3 rounded-lg">
                    <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                    </svg>
                </div>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Inventory Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Monitor stock levels and movements</p>
            <div class="space-y-2">
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Stock Valuation Report</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Low Stock Alert Report</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Inventory Movement Log</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">ABC Analysis</button>
            </div>
        </div>

        <!-- Financial Reports -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <div class="flex items-center mb-4">
                <div class="bg-purple-100 p-3 rounded-lg">
                    <svg class="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                </div>
            </div>
            <h3 class="text-lg font-semibold text-gray-900 mb-2">Financial Reports</h3>
            <p class="text-gray-600 text-sm mb-4">Financial statements and analysis</p>
            <div class="space-y-2">
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Profit & Loss Statement</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Balance Sheet</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Cash Flow Statement</button>
                <button class="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md">Accounts Receivable</button>
            </div>
        </div>
    </div>

    <!-- Quick Analytics -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Performance Metrics -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Performance Metrics</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Total Revenue</span>
                    <span class="text-sm text-gray-900">${{ "%.2f"|format(stats.total_sales) }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Inventory Value</span>
                    <span class="text-sm text-gray-900">${{ "%.2f"|format(stats.total_inventory_value) }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Active Products</span>
                    <span class="text-sm text-gray-900">{{ stats.total_products }}</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">Active Customers</span>
                    <span class="text-sm text-gray-900">{{ stats.total_customers }}</span>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">System Status</h3>
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">🐍 Python Flask Server</span>
                    <span class="text-sm text-green-600 font-medium">Running</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">📊 Real-time Data</span>
                    <span class="text-sm text-green-600 font-medium">Active</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">🔄 API Endpoints</span>
                    <span class="text-sm text-green-600 font-medium">Operational</span>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-sm font-medium text-gray-900">📱 Responsive UI</span>
                    <span class="text-sm text-green-600 font-medium">Enabled</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Implementation Status -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">🐍 Python Implementation Features</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
                <h4 class="font-medium text-green-700 mb-3">✅ Completed Features</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Flask web application framework
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Jinja2 templating with responsive design
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        RESTful API endpoints
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Interactive inventory management
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Customer relationship management
                    </li>
                    <li class="flex items-center">
                        <svg class="h-4 w-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        Sales and invoice tracking
                    </li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium text-blue-700 mb-3">🚀 Technical Features</h4>
                <ul class="space-y-2 text-sm text-gray-600">
                    <li>• Python Flask backend</li>
                    <li>• Tailwind CSS styling</li>
                    <li>• AJAX API interactions</li>
                    <li>• Responsive mobile design</li>
                    <li>• Real-time data updates</li>
                    <li>• Interactive modals and forms</li>
                    <li>• Professional UI components</li>
                    <li>• Cross-browser compatibility</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}
