@echo off
title ProTech Accounting System - Standalone Desktop Application
color 0A

echo.
echo ============================================================
echo    🖥️ نظام ProTech للمحاسبة - تطبيق سطح مكتب مستقل
echo    🖥️ ProTech Accounting System - Standalone Desktop App
echo ============================================================
echo.

echo [1/2] فحص Python / Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير موجود / Python not found
    echo يرجى تثبيت Python أولاً / Please install Python first
    echo تحميل من / Download from: https://python.org
    pause
    exit /b 1
)
echo ✅ Python موجود / Python found

echo.
echo [2/2] فحص المكتبات / Checking libraries...
python -c "import tkinter, sqlite3" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ مكتبات مطلوبة غير موجودة / Required libraries not found
    pause
    exit /b 1
)
echo ✅ جميع المكتبات موجودة / All libraries found

echo.
echo ┌─────────────────────────────────────────────────────────┐
echo │  🖥️ تطبيق ProTech المستقل لسطح المكتب                 │
echo │  🖥️ ProTech Standalone Desktop Application             │
echo │                                                         │
echo │  🌟 المميزات / Features:                               │
echo │  ✅ تطبيق مستقل بالكامل - لا يحتاج متصفح               │
echo │  ✅ واجهة رسومية احترافية                             │
echo │  ✅ قاعدة بيانات SQLite مدمجة                         │
echo │  ✅ إدارة شاملة للمخزون والعملاء                      │
echo │  ✅ دعم ثنائي اللغة (عربي/إنجليزي)                   │
echo │  ✅ تصميم حديث وسهل الاستخدام                         │
echo │                                                         │
echo │  📋 الوظائف المتاحة / Available Functions:             │
echo │  📊 لوحة تحكم تفاعلية                                 │
echo │  📦 إدارة المخزون الكاملة                             │
echo │  👥 إدارة العملاء                                     │
echo │  🏢 إدارة الموردين                                    │
echo │  💰 نظام المبيعات                                     │
echo │  📈 التقارير والتحليلات                               │
echo │                                                         │
echo │  🚀 جاري التشغيل... / Starting...                     │
echo └─────────────────────────────────────────────────────────┘
echo.

python protech_standalone_app.py

echo.
echo 🛑 تم إغلاق التطبيق / Application closed
echo شكراً لاستخدام نظام ProTech / Thank you for using ProTech
pause
