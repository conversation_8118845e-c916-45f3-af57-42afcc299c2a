@echo off
chcp 65001 > nul
title مشغل ProTech بدون صلاحيات - Permission-Free ProTech Launcher

echo.
echo ════════════════════════════════════════════════════════════
echo    🔓 مشغل ProTech بدون صلاحيات
echo    🔓 Permission-Free ProTech Launcher
echo ════════════════════════════════════════════════════════════
echo.

echo 🔍 البحث عن ProTech في مواقع متعددة...
echo 🔍 Searching for ProTech in multiple locations...

REM Try location 1
if exist "C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_working.py" (
    echo ✅ تم العثور على ProTech في accounting program
    cd /d "C:\Users\<USER>\OneDrive\Desktop\accounting program"
    goto :run_protech
)

REM Try location 2
if exist "C:\Users\<USER>\Documents\accounting program\protech_simple_working.py" (
    echo ✅ تم العثور على ProTech في Documents
    cd /d "C:\Users\<USER>\Documents\accounting program"
    goto :run_protech
)

REM Try location 3
if exist "C:\Users\<USER>\OneDrive\Desktop\PROTECH_COMPLETE_STANDALONE.py" (
    echo ✅ تم العثور على النسخة المستقلة
    cd /d "C:\Users\<USER>\OneDrive\Desktop"
    python PROTECH_COMPLETE_STANDALONE.py
    goto :end
)

REM Try location 4
if exist "C:\Users\<USER>\OneDrive\Desktop\NASSAR_COMPLETE_SYSTEM\protech_simple_working.py" (
    echo ✅ تم العثور على ProTech في النظام الكامل
    cd /d "C:\Users\<USER>\OneDrive\Desktop\NASSAR_COMPLETE_SYSTEM"
    goto :run_protech
)

echo ❌ لم يتم العثور على ProTech في أي موقع
echo ❌ ProTech not found in any location
echo.
echo 💡 تأكد من وجود الملفات في المواقع التالية:
echo 💡 Make sure files exist in the following locations:
echo    • C:\Users\<USER>\OneDrive\Desktop\accounting program\
echo    • C:\Users\<USER>\OneDrive\Desktop\PROTECH_COMPLETE_STANDALONE.py
echo    • C:\Users\<USER>\OneDrive\Desktop\NASSAR_COMPLETE_SYSTEM\
pause
exit /b 1

:run_protech
echo.
echo 🚀 تشغيل ProTech...
echo 🚀 Starting ProTech...
echo.

REM Try different Python commands
python protech_simple_working.py
if %ERRORLEVEL% EQU 0 goto :success

echo 🔄 محاولة مع python3...
python3 protech_simple_working.py
if %ERRORLEVEL% EQU 0 goto :success

echo 🔄 محاولة مع py...
py protech_simple_working.py
if %ERRORLEVEL% EQU 0 goto :success

echo 🔄 محاولة مع المسار الكامل...
"C:\Python\python.exe" protech_simple_working.py
if %ERRORLEVEL% EQU 0 goto :success

echo 🔄 محاولة مع Python من Program Files...
"C:\Program Files\Python\python.exe" protech_simple_working.py
if %ERRORLEVEL% EQU 0 goto :success

echo ❌ فشل في تشغيل ProTech
echo ❌ Failed to run ProTech
echo.
echo 💡 تأكد من تثبيت Python على النظام
echo 💡 Make sure Python is installed on the system
echo.
echo 🔧 يمكنك تجربة:
echo 🔧 You can try:
echo    1. تثبيت Python من python.org
echo    2. إعادة تشغيل الكمبيوتر
echo    3. تشغيل الملف يدوياً
goto :end

:success
echo.
echo ✅ تم تشغيل ProTech بنجاح!
echo ✅ ProTech started successfully!

:end
echo.
echo 📝 انتهى التشغيل
echo 📝 Execution completed
pause
