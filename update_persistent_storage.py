#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Update Persistent Storage
تحديث التخزين الدائم

Enhanced persistent storage system for ProTech accounting program
نظام تخزين دائم محسن لبرنامج ProTech للمحاسبة
"""

import os
import shutil
import json
import sqlite3
from datetime import datetime

def update_persistent_storage():
    """تحديث نظام التخزين الدائم"""
    try:
        print("💾 تحديث نظام التخزين الدائم")
        print("💾 Updating Persistent Storage System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.persistent_storage_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add persistent storage methods
        storage_methods = '''
    def init_persistent_storage(self):
        """تهيئة نظام التخزين الدائم"""
        try:
            # إنشاء مجلدات التخزين
            self.storage_config = {
                'data_dir': 'protech_data',
                'db_dir': 'protech_data/database',
                'backup_dir': 'protech_data/backups',
                'export_dir': 'protech_data/exports',
                'import_dir': 'protech_data/imports',
                'logs_dir': 'protech_data/logs',
                'reports_dir': 'protech_data/reports',
                'settings_dir': 'protech_data/settings'
            }
            
            # إنشاء جميع المجلدات
            for dir_path in self.storage_config.values():
                os.makedirs(dir_path, exist_ok=True)
            
            # تهيئة قاعدة البيانات الدائمة
            self.init_persistent_database()
            
            # تهيئة نظام النسخ الاحتياطي
            self.init_backup_system()
            
            # تهيئة نظام الإعدادات
            self.init_settings_system()
            
            print("✅ تم تهيئة نظام التخزين الدائم")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة التخزين الدائم: {e}")
    
    def init_persistent_database(self):
        """تهيئة قاعدة البيانات الدائمة"""
        try:
            # مسار قاعدة البيانات الدائمة
            self.db_path = os.path.join(self.storage_config['db_dir'], 'protech_main.db')
            
            # إنشاء الاتصال
            self.db_conn = sqlite3.connect(self.db_path)
            self.db_cursor = self.db_conn.cursor()
            
            # إنشاء الجداول المحسنة
            self.create_enhanced_tables()
            
            # إنشاء المشاهدات (Views) المفيدة
            self.create_database_views()
            
            # إنشاء المؤشرات (Indexes) لتحسين الأداء
            self.create_database_indexes()
            
            print("✅ تم تهيئة قاعدة البيانات الدائمة")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            self.db_conn = None
            self.db_cursor = None
    
    def create_enhanced_tables(self):
        """إنشاء الجداول المحسنة"""
        try:
            # جدول المنتجات المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    name_ar TEXT,
                    category TEXT DEFAULT 'غير مصنف',
                    subcategory TEXT,
                    barcode TEXT UNIQUE,
                    sku TEXT UNIQUE,
                    quantity REAL DEFAULT 0,
                    unit TEXT DEFAULT 'قطعة',
                    base_price REAL DEFAULT 0,
                    cost_price REAL DEFAULT 0,
                    shop_owner_price REAL DEFAULT 0,
                    wholesale_price REAL DEFAULT 0,
                    retail_price REAL DEFAULT 0,
                    distributor_price REAL DEFAULT 0,
                    min_stock REAL DEFAULT 10,
                    max_stock REAL DEFAULT 1000,
                    reorder_point REAL DEFAULT 20,
                    supplier_id INTEGER,
                    brand TEXT,
                    model TEXT,
                    description TEXT,
                    specifications TEXT,
                    weight REAL,
                    dimensions TEXT,
                    color TEXT,
                    size TEXT,
                    warranty_period INTEGER,
                    expiry_date DATE,
                    location TEXT,
                    notes TEXT,
                    image_path TEXT,
                    tax_rate REAL DEFAULT 0,
                    discount_rate REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    is_featured BOOLEAN DEFAULT 0,
                    is_trackable BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    updated_by TEXT,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            """)
            
            # جدول العملاء المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_ar TEXT,
                    company_name TEXT,
                    customer_code TEXT UNIQUE,
                    customer_type TEXT DEFAULT 'تجزئة',
                    phone TEXT,
                    phone2 TEXT,
                    email TEXT,
                    website TEXT,
                    address TEXT,
                    city TEXT,
                    country TEXT DEFAULT 'لبنان',
                    postal_code TEXT,
                    tax_number TEXT,
                    commercial_register TEXT,
                    contact_person TEXT,
                    contact_title TEXT,
                    balance REAL DEFAULT 0,
                    credit_limit REAL DEFAULT 0,
                    payment_terms INTEGER DEFAULT 30,
                    discount_rate REAL DEFAULT 0,
                    price_level TEXT DEFAULT 'تجزئة',
                    currency TEXT DEFAULT 'LBP',
                    language TEXT DEFAULT 'ar',
                    notes TEXT,
                    tags TEXT,
                    birth_date DATE,
                    anniversary_date DATE,
                    last_purchase_date TIMESTAMP,
                    total_purchases REAL DEFAULT 0,
                    purchase_count INTEGER DEFAULT 0,
                    loyalty_points INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    is_vip BOOLEAN DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    updated_by TEXT
                )
            """)
            
            # جدول الموردين المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    name_ar TEXT,
                    company_name TEXT,
                    supplier_code TEXT UNIQUE,
                    phone TEXT,
                    phone2 TEXT,
                    email TEXT,
                    website TEXT,
                    address TEXT,
                    city TEXT,
                    country TEXT,
                    postal_code TEXT,
                    tax_number TEXT,
                    commercial_register TEXT,
                    contact_person TEXT,
                    contact_title TEXT,
                    payment_terms INTEGER DEFAULT 30,
                    currency TEXT DEFAULT 'LBP',
                    bank_account TEXT,
                    bank_name TEXT,
                    notes TEXT,
                    rating INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    updated_by TEXT
                )
            """)
            
            # جدول المبيعات المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    invoice_type TEXT DEFAULT 'sale',
                    customer_id INTEGER,
                    customer_name TEXT,
                    customer_type TEXT,
                    salesperson TEXT,
                    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    due_date DATE,
                    subtotal REAL DEFAULT 0,
                    discount_amount REAL DEFAULT 0,
                    discount_percentage REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    tax_percentage REAL DEFAULT 0,
                    shipping_amount REAL DEFAULT 0,
                    total_amount REAL NOT NULL,
                    paid_amount REAL DEFAULT 0,
                    balance_due REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'مكتمل',
                    payment_method TEXT,
                    currency TEXT DEFAULT 'LBP',
                    exchange_rate REAL DEFAULT 1,
                    reference_number TEXT,
                    po_number TEXT,
                    delivery_date DATE,
                    delivery_address TEXT,
                    delivery_status TEXT DEFAULT 'pending',
                    notes TEXT,
                    internal_notes TEXT,
                    terms_conditions TEXT,
                    status TEXT DEFAULT 'active',
                    is_printed BOOLEAN DEFAULT 0,
                    is_emailed BOOLEAN DEFAULT 0,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    updated_by TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            """)
            
            # جدول تفاصيل المبيعات المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS sale_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sale_id INTEGER NOT NULL,
                    product_id INTEGER,
                    product_name TEXT NOT NULL,
                    product_code TEXT,
                    description TEXT,
                    quantity REAL NOT NULL,
                    unit TEXT DEFAULT 'قطعة',
                    unit_price REAL NOT NULL,
                    cost_price REAL DEFAULT 0,
                    discount_amount REAL DEFAULT 0,
                    discount_percentage REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    tax_percentage REAL DEFAULT 0,
                    total_price REAL NOT NULL,
                    line_notes TEXT,
                    serial_numbers TEXT,
                    batch_numbers TEXT,
                    expiry_date DATE,
                    warranty_period INTEGER,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (sale_id) REFERENCES sales (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # جدول حركات المخزون المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    unit_cost REAL DEFAULT 0,
                    total_cost REAL DEFAULT 0,
                    reference_type TEXT,
                    reference_id INTEGER,
                    reference_number TEXT,
                    location_from TEXT,
                    location_to TEXT,
                    reason TEXT,
                    notes TEXT,
                    batch_number TEXT,
                    serial_number TEXT,
                    expiry_date DATE,
                    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # جدول المدفوعات
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    payment_number TEXT UNIQUE NOT NULL,
                    payment_type TEXT NOT NULL,
                    customer_id INTEGER,
                    supplier_id INTEGER,
                    invoice_id INTEGER,
                    amount REAL NOT NULL,
                    currency TEXT DEFAULT 'LBP',
                    exchange_rate REAL DEFAULT 1,
                    payment_method TEXT,
                    payment_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reference_number TEXT,
                    bank_account TEXT,
                    check_number TEXT,
                    check_date DATE,
                    notes TEXT,
                    status TEXT DEFAULT 'completed',
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    created_by TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id),
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id),
                    FOREIGN KEY (invoice_id) REFERENCES sales (id)
                )
            """)
            
            # جدول سجل الأنشطة المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_name TEXT,
                    action_type TEXT NOT NULL,
                    table_name TEXT,
                    record_id INTEGER,
                    old_values TEXT,
                    new_values TEXT,
                    ip_address TEXT,
                    user_agent TEXT,
                    session_id TEXT,
                    action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT
                )
            """)
            
            # جدول الإعدادات المحسن
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    category TEXT NOT NULL,
                    setting_key TEXT NOT NULL,
                    setting_value TEXT,
                    setting_type TEXT DEFAULT 'string',
                    description TEXT,
                    is_system BOOLEAN DEFAULT 0,
                    is_encrypted BOOLEAN DEFAULT 0,
                    validation_rule TEXT,
                    default_value TEXT,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_by TEXT,
                    UNIQUE(category, setting_key)
                )
            """)
            
            # جدول النسخ الاحتياطية
            self.db_cursor.execute("""
                CREATE TABLE IF NOT EXISTS backups (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    backup_name TEXT NOT NULL,
                    backup_type TEXT DEFAULT 'auto',
                    file_path TEXT NOT NULL,
                    file_size INTEGER,
                    backup_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    description TEXT,
                    status TEXT DEFAULT 'completed',
                    created_by TEXT
                )
            """)
            
            self.db_conn.commit()
            print("✅ تم إنشاء الجداول المحسنة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")
    
    def create_database_views(self):
        """إنشاء المشاهدات المفيدة"""
        try:
            # مشاهدة ملخص المنتجات
            self.db_cursor.execute("""
                CREATE VIEW IF NOT EXISTS products_summary AS
                SELECT 
                    p.id,
                    p.name,
                    p.category,
                    p.quantity,
                    p.unit,
                    p.base_price,
                    p.quantity * p.base_price as total_value,
                    CASE 
                        WHEN p.quantity <= p.min_stock THEN 'منخفض'
                        WHEN p.quantity >= p.max_stock THEN 'مرتفع'
                        ELSE 'طبيعي'
                    END as stock_status,
                    s.name as supplier_name
                FROM products p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE p.is_active = 1
            """)
            
            # مشاهدة ملخص المبيعات
            self.db_cursor.execute("""
                CREATE VIEW IF NOT EXISTS sales_summary AS
                SELECT 
                    s.id,
                    s.invoice_number,
                    s.customer_name,
                    s.sale_date,
                    s.total_amount,
                    s.paid_amount,
                    s.balance_due,
                    s.payment_status,
                    COUNT(si.id) as items_count,
                    SUM(si.quantity) as total_quantity
                FROM sales s
                LEFT JOIN sale_items si ON s.id = si.sale_id
                GROUP BY s.id
            """)
            
            # مشاهدة أرصدة العملاء
            self.db_cursor.execute("""
                CREATE VIEW IF NOT EXISTS customer_balances AS
                SELECT 
                    c.id,
                    c.name,
                    c.customer_type,
                    c.balance,
                    c.credit_limit,
                    COALESCE(SUM(s.balance_due), 0) as outstanding_invoices,
                    c.balance + COALESCE(SUM(s.balance_due), 0) as total_balance,
                    c.last_purchase_date,
                    c.total_purchases
                FROM customers c
                LEFT JOIN sales s ON c.id = s.customer_id AND s.balance_due > 0
                WHERE c.is_active = 1
                GROUP BY c.id
            """)
            
            self.db_conn.commit()
            print("✅ تم إنشاء المشاهدات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المشاهدات: {e}")
    
    def create_database_indexes(self):
        """إنشاء المؤشرات لتحسين الأداء"""
        try:
            indexes = [
                # فهارس المنتجات
                "CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)",
                "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
                "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
                "CREATE INDEX IF NOT EXISTS idx_products_supplier ON products(supplier_id)",
                "CREATE INDEX IF NOT EXISTS idx_products_active ON products(is_active)",
                
                # فهارس العملاء
                "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)",
                "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",
                "CREATE INDEX IF NOT EXISTS idx_customers_email ON customers(email)",
                "CREATE INDEX IF NOT EXISTS idx_customers_type ON customers(customer_type)",
                "CREATE INDEX IF NOT EXISTS idx_customers_active ON customers(is_active)",
                
                # فهارس المبيعات
                "CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)",
                "CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)",
                "CREATE INDEX IF NOT EXISTS idx_sales_invoice ON sales(invoice_number)",
                "CREATE INDEX IF NOT EXISTS idx_sales_status ON sales(payment_status)",
                
                # فهارس تفاصيل المبيعات
                "CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id)",
                "CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id)",
                
                # فهارس حركات المخزون
                "CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory_movements(product_id)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_date ON inventory_movements(movement_date)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_type ON inventory_movements(movement_type)",
                
                # فهارس المدفوعات
                "CREATE INDEX IF NOT EXISTS idx_payments_date ON payments(payment_date)",
                "CREATE INDEX IF NOT EXISTS idx_payments_customer ON payments(customer_id)",
                "CREATE INDEX IF NOT EXISTS idx_payments_invoice ON payments(invoice_id)",
                
                # فهارس سجل الأنشطة
                "CREATE INDEX IF NOT EXISTS idx_activity_date ON activity_log(action_date)",
                "CREATE INDEX IF NOT EXISTS idx_activity_user ON activity_log(user_name)",
                "CREATE INDEX IF NOT EXISTS idx_activity_table ON activity_log(table_name)"
            ]
            
            for index_sql in indexes:
                self.db_cursor.execute(index_sql)
            
            self.db_conn.commit()
            print("✅ تم إنشاء المؤشرات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء المؤشرات: {e}")
    
    def init_backup_system(self):
        """تهيئة نظام النسخ الاحتياطي"""
        try:
            self.backup_config = {
                'auto_backup_enabled': True,
                'backup_interval_hours': 1,
                'max_backups_count': 50,
                'backup_on_exit': True,
                'backup_on_data_change': True,
                'compress_backups': True
            }
            
            # إنشاء نسخة احتياطية أولية
            self.create_initial_backup()
            
            print("✅ تم تهيئة نظام النسخ الاحتياطي")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة النسخ الاحتياطي: {e}")
    
    def init_settings_system(self):
        """تهيئة نظام الإعدادات"""
        try:
            # الإعدادات الافتراضية
            default_settings = [
                ('general', 'company_name', 'ProTech', 'string', 'اسم الشركة'),
                ('general', 'company_address', '', 'string', 'عنوان الشركة'),
                ('general', 'company_phone', '', 'string', 'هاتف الشركة'),
                ('general', 'company_email', '', 'string', 'بريد الشركة الإلكتروني'),
                ('general', 'tax_number', '', 'string', 'الرقم الضريبي'),
                ('general', 'currency', 'LBP', 'string', 'العملة الافتراضية'),
                ('general', 'language', 'ar', 'string', 'اللغة الافتراضية'),
                
                ('sales', 'default_payment_terms', '30', 'int', 'شروط الدفع الافتراضية (أيام)'),
                ('sales', 'auto_generate_invoice_number', 'true', 'bool', 'إنشاء رقم فاتورة تلقائي'),
                ('sales', 'invoice_prefix', 'INV-', 'string', 'بادئة رقم الفاتورة'),
                ('sales', 'default_tax_rate', '11', 'float', 'معدل الضريبة الافتراضي (%)'),
                
                ('inventory', 'auto_update_stock', 'true', 'bool', 'تحديث المخزون تلقائياً'),
                ('inventory', 'low_stock_warning', 'true', 'bool', 'تحذير المخزون المنخفض'),
                ('inventory', 'default_unit', 'قطعة', 'string', 'الوحدة الافتراضية'),
                
                ('backup', 'auto_backup', 'true', 'bool', 'النسخ الاحتياطي التلقائي'),
                ('backup', 'backup_interval', '1', 'int', 'فترة النسخ الاحتياطي (ساعات)'),
                ('backup', 'max_backups', '50', 'int', 'الحد الأقصى للنسخ الاحتياطية'),
                
                ('ui', 'theme', 'default', 'string', 'المظهر'),
                ('ui', 'font_size', '10', 'int', 'حجم الخط'),
                ('ui', 'auto_refresh', 'true', 'bool', 'التحديث التلقائي'),
                ('ui', 'show_notifications', 'true', 'bool', 'عرض الإشعارات')
            ]
            
            # إدراج الإعدادات الافتراضية
            for category, key, value, type_val, description in default_settings:
                self.db_cursor.execute("""
                    INSERT OR IGNORE INTO settings 
                    (category, setting_key, setting_value, setting_type, description, is_system, default_value)
                    VALUES (?, ?, ?, ?, ?, 1, ?)
                """, (category, key, value, type_val, description, value))
            
            self.db_conn.commit()
            print("✅ تم تهيئة نظام الإعدادات")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة الإعدادات: {e}")
    
    def create_initial_backup(self):
        """إنشاء نسخة احتياطية أولية"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"protech_initial_backup_{timestamp}.db"
            backup_path = os.path.join(self.storage_config['backup_dir'], backup_name)
            
            # نسخ قاعدة البيانات
            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, backup_path)
                
                # تسجيل النسخة الاحتياطية
                file_size = os.path.getsize(backup_path)
                self.db_cursor.execute("""
                    INSERT INTO backups (backup_name, backup_type, file_path, file_size, description)
                    VALUES (?, ?, ?, ?, ?)
                """, (backup_name, 'initial', backup_path, file_size, 'النسخة الاحتياطية الأولية'))
                
                self.db_conn.commit()
                print(f"✅ تم إنشاء النسخة الاحتياطية الأولية: {backup_name}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية الأولية: {e}")
'''
        
        # Add storage methods before the last method
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + storage_methods + content[last_method:]
        else:
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + storage_methods + content[last_method:]
        
        # Add initialization call
        init_method = content.find("def __init__(self")
        if init_method != -1:
            init_end = content.find("\n    def ", init_method + 1)
            if init_end == -1:
                init_end = content.find("\nclass ", init_method + 1)
            if init_end == -1:
                init_end = len(content)
            
            # Add storage initialization
            storage_init = "\n        # تهيئة التخزين الدائم\n        self.init_persistent_storage()\n"
            content = content[:init_end] + storage_init + content[init_end:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة نظام التخزين الدائم")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تحديث التخزين الدائم: {e}")
        return False

def main():
    """Main function"""
    print("💾 تحديث نظام التخزين الدائم لـ ProTech")
    print("💾 Updating Persistent Storage System for ProTech")
    print("="*70)

    if update_persistent_storage():
        print("\n🎉 تم تحديث نظام التخزين الدائم بنجاح!")

        print("\n💾 الميزات الجديدة:")
        print("• 🗄️ قاعدة بيانات SQLite محسنة")
        print("• 📁 مجلدات منظمة للتخزين")
        print("• 💾 نسخ احتياطية تلقائية")
        print("• ⚙️ نظام إعدادات متقدم")
        print("• 📊 مشاهدات قاعدة البيانات")
        print("• 🔍 فهارس لتحسين الأداء")
        print("• 📝 سجل الأنشطة")

        print("\n📁 هيكل التخزين:")
        print("• protech_data/ - المجلد الرئيسي")
        print("• protech_data/database/ - قاعدة البيانات")
        print("• protech_data/backups/ - النسخ الاحتياطية")
        print("• protech_data/exports/ - الملفات المُصدرة")
        print("• protech_data/imports/ - الملفات المستوردة")
        print("• protech_data/logs/ - ملفات السجلات")
        print("• protech_data/reports/ - التقارير المحفوظة")
        print("• protech_data/settings/ - ملفات الإعدادات")

        print("\n🔧 الجداول المحسنة:")
        print("• products - منتجات محسنة مع تفاصيل شاملة")
        print("• customers - عملاء مع معلومات كاملة")
        print("• suppliers - موردين مع بيانات الاتصال")
        print("• sales - مبيعات مع تفاصيل الدفع")
        print("• sale_items - عناصر المبيعات")
        print("• inventory_movements - حركات المخزون")
        print("• payments - المدفوعات")
        print("• activity_log - سجل الأنشطة")
        print("• settings - الإعدادات")
        print("• backups - سجل النسخ الاحتياطية")

        print("\n📊 المشاهدات المفيدة:")
        print("• products_summary - ملخص المنتجات")
        print("• sales_summary - ملخص المبيعات")
        print("• customer_balances - أرصدة العملاء")

        print("\n💡 الفوائد:")
        print("• تخزين آمن ودائم للبيانات")
        print("• أداء محسن مع الفهارس")
        print("• نسخ احتياطية تلقائية")
        print("• إعدادات قابلة للتخصيص")
        print("• سجل شامل للأنشطة")
        print("• حماية من فقدان البيانات")
        print("• سهولة الصيانة والإدارة")

        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح ProTech")
        print("2. ستتم تهيئة النظام تلقائياً")
        print("3. ستُحفظ البيانات في قاعدة البيانات")
        print("4. ستُنشأ النسخ الاحتياطية تلقائياً")
        print("5. استمتع بالأمان والاستقرار!")

    else:
        print("\n❌ فشل في تحديث نظام التخزين الدائم")

    print("\n🔧 تم الانتهاء من تحديث نظام التخزين الدائم")

if __name__ == "__main__":
    main()
