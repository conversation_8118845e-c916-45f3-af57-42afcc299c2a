#!/usr/bin/env python3
"""
ProTech Accounting System - Standalone Desktop Application
نظام ProTech للمحاسبة - تطبيق سطح مكتب مستقل

A complete accounting system that runs entirely on desktop without browser
نظام محاسبة كامل يعمل بالكامل على سطح المكتب بدون متصفح
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
from tkinter import font as tkFont
import json
import os
from datetime import datetime, date
import sqlite3

class ProTechStandaloneApp:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام ProTech للمحاسبة - ProTech Accounting System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f8ff')
        self.root.state('zoomed')  # Maximize window

        # Initialize database
        self.init_database()

        # Create main interface
        self.create_main_interface()

        # Load data
        self.load_sample_data()

    def init_database(self):
        """Initialize SQLite database"""
        self.conn = sqlite3.connect('protech_accounting.db')
        self.cursor = self.conn.cursor()

        # Create tables
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                name TEXT,
                name_ar TEXT,
                category TEXT,
                price REAL,
                cost_price REAL,
                stock INTEGER,
                min_stock INTEGER,
                created_at TEXT
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                name TEXT,
                name_ar TEXT,
                email TEXT,
                phone TEXT,
                address TEXT,
                balance REAL,
                category TEXT,
                created_at TEXT
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                code TEXT UNIQUE,
                name TEXT,
                name_ar TEXT,
                email TEXT,
                phone TEXT,
                contact_person TEXT,
                created_at TEXT
            )
        ''')

        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                type TEXT,
                product_id INTEGER,
                customer_id INTEGER,
                quantity INTEGER,
                unit_price REAL,
                total_amount REAL,
                date TEXT,
                notes TEXT,
                FOREIGN KEY (product_id) REFERENCES products (id),
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        ''')

        self.conn.commit()

    def create_main_interface(self):
        """Create the main interface"""

        # Create main frame
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True)

        # Header
        self.create_header(main_frame)

        # Navigation and content
        content_frame = tk.Frame(main_frame, bg='#f0f8ff')
        content_frame.pack(fill='both', expand=True, padx=10, pady=5)

        # Left navigation panel
        nav_frame = tk.Frame(content_frame, bg='#2563eb', width=250)
        nav_frame.pack(side='left', fill='y', padx=(0, 10))
        nav_frame.pack_propagate(False)

        self.create_navigation(nav_frame)

        # Main content area
        self.content_frame = tk.Frame(content_frame, bg='white', relief='ridge', bd=2)
        self.content_frame.pack(side='right', fill='both', expand=True)

        # Show dashboard by default
        self.show_dashboard()

        # Status bar
        self.create_status_bar(main_frame)

    def create_header(self, parent):
        """Create header section"""
        header_frame = tk.Frame(parent, bg='#1e40af', height=80)
        header_frame.pack(fill='x', padx=10, pady=5)
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(
            header_frame,
            text="🎉 نظام ProTech للمحاسبة",
            font=('Arial', 24, 'bold'),
            fg='white',
            bg='#1e40af'
        )
        title_label.pack(side='left', padx=20, pady=15)

        # Subtitle
        subtitle_label = tk.Label(
            header_frame,
            text="ProTech Accounting System - Standalone Desktop Application",
            font=('Arial', 12),
            fg='#bfdbfe',
            bg='#1e40af'
        )
        subtitle_label.pack(side='left', padx=(0, 20), pady=(35, 15))

        # Date and time
        datetime_frame = tk.Frame(header_frame, bg='#1e40af')
        datetime_frame.pack(side='right', padx=20, pady=15)

        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        time_label = tk.Label(
            datetime_frame,
            text=f"🕒 {current_time}",
            font=('Arial', 11),
            fg='white',
            bg='#1e40af'
        )
        time_label.pack()

        status_label = tk.Label(
            datetime_frame,
            text="✅ نشط / Active",
            font=('Arial', 10),
            fg='#86efac',
            bg='#1e40af'
        )
        status_label.pack()

    def create_navigation(self, parent):
        """Create navigation menu"""
        nav_title = tk.Label(
            parent,
            text="📋 القوائم الرئيسية\nMain Menus",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='#2563eb'
        )
        nav_title.pack(pady=20)

        # Navigation buttons
        nav_buttons = [
            ("🏠 لوحة التحكم\nDashboard", self.show_dashboard),
            ("📦 إدارة المخزون\nInventory", self.show_inventory),
            ("👥 إدارة العملاء\nCustomers", self.show_customers),
            ("🏢 إدارة الموردين\nSuppliers", self.show_suppliers),
            ("💰 المبيعات\nSales", self.show_sales),
            ("📊 التقارير\nReports", self.show_reports),
            ("⚙️ الإعدادات\nSettings", self.show_settings),
            ("❓ المساعدة\nHelp", self.show_help)
        ]

        for text, command in nav_buttons:
            btn = tk.Button(
                parent,
                text=text,
                font=('Arial', 11, 'bold'),
                fg='white',
                bg='#3b82f6',
                activebackground='#1d4ed8',
                activeforeground='white',
                relief='flat',
                width=20,
                height=3,
                command=command,
                cursor='hand2'
            )
            btn.pack(pady=5, padx=10, fill='x')

    def create_status_bar(self, parent):
        """Create status bar"""
        status_frame = tk.Frame(parent, bg='#374151', height=30)
        status_frame.pack(fill='x', side='bottom', padx=10, pady=5)
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="جاهز / Ready",
            font=('Arial', 10),
            fg='white',
            bg='#374151'
        )
        self.status_label.pack(side='left', padx=10, pady=5)

        # Version info
        version_label = tk.Label(
            status_frame,
            text="ProTech v1.0 | © 2024",
            font=('Arial', 9),
            fg='#9ca3af',
            bg='#374151'
        )
        version_label.pack(side='right', padx=10, pady=5)

    def clear_content(self):
        """Clear content frame"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def update_status(self, message):
        """Update status bar message"""
        self.status_label.config(text=message)
        self.root.update()

    def load_sample_data(self):
        """Load sample data if database is empty"""
        # Check if data exists
        self.cursor.execute("SELECT COUNT(*) FROM products")
        if self.cursor.fetchone()[0] == 0:
            # Insert sample products
            sample_products = [
                ('LAPTOP001', 'Business Laptop', 'لابتوب الأعمال', 'Electronics', 1000.0, 800.0, 50, 10),
                ('MOUSE001', 'Wireless Mouse', 'فأرة لاسلكية', 'Electronics', 25.0, 15.0, 200, 50),
                ('NOTE001', 'Professional Notebook', 'دفتر مهني', 'Office Supplies', 5.0, 3.0, 5, 200),
                ('PHONE001', 'Business Smartphone', 'هاتف ذكي للأعمال', 'Electronics', 550.0, 400.0, 25, 5),
                ('DESK001', 'Office Desk', 'مكتب مكتبي', 'Furniture', 300.0, 200.0, 0, 3)
            ]

            for product in sample_products:
                self.cursor.execute('''
                    INSERT INTO products (code, name, name_ar, category, price, cost_price, stock, min_stock, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', product + (datetime.now().isoformat(),))

            # Insert sample customers
            sample_customers = [
                ('CUST001', 'John Smith', 'جون سميث', '<EMAIL>', '******-1234', '789 Customer Lane', 1250.0, 'RETAIL'),
                ('CUST002', 'ABC Corporation', 'شركة ABC', '<EMAIL>', '******-5678', '321 Corporate Blvd', 8750.0, 'WHOLESALE'),
                ('CUST003', 'Ahmed Al-Rashid', 'أحمد الراشد', '<EMAIL>', '+966-50-123-4567', 'King Fahd Road, Riyadh', 2500.0, 'RETAIL')
            ]

            for customer in sample_customers:
                self.cursor.execute('''
                    INSERT INTO customers (code, name, name_ar, email, phone, address, balance, category, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', customer + (datetime.now().isoformat(),))

            # Insert sample suppliers
            sample_suppliers = [
                ('TECH001', 'Tech Solutions Inc.', 'شركة الحلول التقنية', '<EMAIL>', '******-0123', 'Mike Johnson'),
                ('OFF001', 'Office World Ltd.', 'شركة عالم المكاتب', '<EMAIL>', '******-0456', 'Sarah Wilson'),
                ('FURN001', 'Furniture Plus', 'أثاث بلس', '<EMAIL>', '******-0789', 'David Brown')
            ]

            for supplier in sample_suppliers:
                self.cursor.execute('''
                    INSERT INTO suppliers (code, name, name_ar, email, phone, contact_person, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', supplier + (datetime.now().isoformat(),))

            self.conn.commit()
            self.update_status("تم تحميل البيانات النموذجية / Sample data loaded")

    def show_dashboard(self):
        """Show dashboard"""
        self.clear_content()
        self.update_status("عرض لوحة التحكم / Showing dashboard")

        # Dashboard title
        title_frame = tk.Frame(self.content_frame, bg='white')
        title_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            title_frame,
            text="📊 لوحة التحكم / Dashboard",
            font=('Arial', 20, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Statistics cards
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)

        # Get statistics
        self.cursor.execute("SELECT COUNT(*) FROM products")
        total_products = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT COUNT(*) FROM products WHERE stock <= min_stock")
        low_stock = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT COUNT(*) FROM customers")
        total_customers = self.cursor.fetchone()[0]

        self.cursor.execute("SELECT SUM(stock * price) FROM products")
        result = self.cursor.fetchone()[0]
        inventory_value = result if result else 0

        # Create stat cards
        stats = [
            ("📦", "إجمالي المنتجات\nTotal Products", total_products, "#3b82f6"),
            ("⚠️", "مخزون منخفض\nLow Stock", low_stock, "#ef4444"),
            ("👥", "العملاء\nCustomers", total_customers, "#10b981"),
            ("💰", "قيمة المخزون\nInventory Value", f"${inventory_value:,.0f}", "#8b5cf6")
        ]

        for i, (icon, title, value, color) in enumerate(stats):
            card = tk.Frame(stats_frame, bg=color, relief='raised', bd=2)
            card.pack(side='left', fill='both', expand=True, padx=5)

            tk.Label(
                card,
                text=icon,
                font=('Arial', 24),
                fg='white',
                bg=color
            ).pack(pady=5)

            tk.Label(
                card,
                text=title,
                font=('Arial', 10, 'bold'),
                fg='white',
                bg=color
            ).pack()

            tk.Label(
                card,
                text=str(value),
                font=('Arial', 16, 'bold'),
                fg='white',
                bg=color
            ).pack(pady=5)

        # Recent activity
        activity_frame = tk.LabelFrame(
            self.content_frame,
            text="النشاط الحديث / Recent Activity",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        activity_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Activity list
        activity_text = tk.Text(
            activity_frame,
            height=10,
            font=('Arial', 10),
            bg='#f9fafb',
            wrap='word'
        )
        activity_text.pack(fill='both', expand=True, padx=10, pady=10)

        # Add sample activity
        activities = [
            "🔄 تم تحديث المخزون / Inventory updated",
            "👤 تم إضافة عميل جديد / New customer added",
            "💰 تم تسجيل عملية بيع / Sale recorded",
            "📦 تم استلام بضاعة / Goods received",
            "📊 تم إنشاء تقرير / Report generated"
        ]

        for activity in activities:
            activity_text.insert('end', f"• {activity}\n")

        activity_text.config(state='disabled')

    def show_inventory(self):
        """Show inventory management"""
        self.clear_content()
        self.update_status("عرض إدارة المخزون / Showing inventory management")

        # Title and controls
        header_frame = tk.Frame(self.content_frame, bg='white')
        header_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(
            header_frame,
            text="📦 إدارة المخزون / Inventory Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(side='left')

        # Control buttons
        btn_frame = tk.Frame(header_frame, bg='white')
        btn_frame.pack(side='right')

        tk.Button(
            btn_frame,
            text="➕ إضافة منتج\nAdd Product",
            font=('Arial', 10, 'bold'),
            bg='#10b981',
            fg='white',
            command=self.add_product,
            cursor='hand2'
        ).pack(side='left', padx=5)

        tk.Button(
            btn_frame,
            text="✏️ تعديل\nEdit",
            font=('Arial', 10, 'bold'),
            bg='#f59e0b',
            fg='white',
            command=self.edit_product,
            cursor='hand2'
        ).pack(side='left', padx=5)

        tk.Button(
            btn_frame,
            text="🗑️ حذف\nDelete",
            font=('Arial', 10, 'bold'),
            bg='#ef4444',
            fg='white',
            command=self.delete_product,
            cursor='hand2'
        ).pack(side='left', padx=5)

        # Products table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Create treeview
        columns = ('Code', 'Name', 'Name_AR', 'Category', 'Price', 'Stock', 'Status')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        # Define headings
        headings = [
            ('Code', 'الكود'),
            ('Name', 'الاسم'),
            ('Name_AR', 'الاسم بالعربية'),
            ('Category', 'الفئة'),
            ('Price', 'السعر'),
            ('Stock', 'المخزون'),
            ('Status', 'الحالة')
        ]

        for col, (eng, ar) in zip(columns, headings):
            self.products_tree.heading(col, text=f"{ar}\n{eng}")
            self.products_tree.column(col, width=120, anchor='center')

        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        self.products_tree.configure(yscrollcommand=scrollbar.set)

        self.products_tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')

        # Load products data
        self.load_products_data()

    def load_products_data(self):
        """Load products data into treeview"""
        # Clear existing data
        for item in self.products_tree.get_children():
            self.products_tree.delete(item)

        # Fetch products from database
        self.cursor.execute('''
            SELECT code, name, name_ar, category, price, stock, min_stock
            FROM products ORDER BY name
        ''')

        products = self.cursor.fetchall()

        for product in products:
            code, name, name_ar, category, price, stock, min_stock = product

            # Determine status
            if stock == 0:
                status = "نفد / Out of Stock"
                tags = ('out_of_stock',)
            elif stock <= min_stock:
                status = "منخفض / Low"
                tags = ('low_stock',)
            else:
                status = "جيد / Good"
                tags = ('good_stock',)

            self.products_tree.insert('', 'end', values=(
                code, name, name_ar, category, f"${price:.2f}", stock, status
            ), tags=tags)

        # Configure tags
        self.products_tree.tag_configure('out_of_stock', background='#fee2e2', foreground='#dc2626')
        self.products_tree.tag_configure('low_stock', background='#fef3c7', foreground='#d97706')
        self.products_tree.tag_configure('good_stock', background='#dcfce7', foreground='#16a34a')

    def add_product(self):
        """Add new product"""
        dialog = ProductDialog(self.root, "إضافة منتج جديد / Add New Product")
        if dialog.result:
            try:
                self.cursor.execute('''
                    INSERT INTO products (code, name, name_ar, category, price, cost_price, stock, min_stock, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', dialog.result + (datetime.now().isoformat(),))
                self.conn.commit()
                self.load_products_data()
                self.update_status("تم إضافة المنتج بنجاح / Product added successfully")
                messagebox.showinfo("نجح / Success", "تم إضافة المنتج بنجاح\nProduct added successfully")
            except sqlite3.IntegrityError:
                messagebox.showerror("خطأ / Error", "كود المنتج موجود مسبقاً\nProduct code already exists")
            except Exception as e:
                messagebox.showerror("خطأ / Error", f"فشل في إضافة المنتج\nFailed to add product:\n{str(e)}")

    def edit_product(self):
        """Edit selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير / Warning", "يرجى اختيار منتج للتعديل\nPlease select a product to edit")
            return

        # Get selected product data
        item = self.products_tree.item(selection[0])
        code = item['values'][0]

        # Fetch full product data
        self.cursor.execute('SELECT * FROM products WHERE code = ?', (code,))
        product_data = self.cursor.fetchone()

        if product_data:
            dialog = ProductDialog(self.root, "تعديل المنتج / Edit Product", product_data)
            if dialog.result:
                try:
                    self.cursor.execute('''
                        UPDATE products SET name=?, name_ar=?, category=?, price=?, cost_price=?, stock=?, min_stock=?
                        WHERE code=?
                    ''', dialog.result[1:] + (dialog.result[0],))
                    self.conn.commit()
                    self.load_products_data()
                    self.update_status("تم تعديل المنتج بنجاح / Product updated successfully")
                    messagebox.showinfo("نجح / Success", "تم تعديل المنتج بنجاح\nProduct updated successfully")
                except Exception as e:
                    messagebox.showerror("خطأ / Error", f"فشل في تعديل المنتج\nFailed to update product:\n{str(e)}")

    def delete_product(self):
        """Delete selected product"""
        selection = self.products_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير / Warning", "يرجى اختيار منتج للحذف\nPlease select a product to delete")
            return

        item = self.products_tree.item(selection[0])
        code = item['values'][0]
        name = item['values'][1]

        result = messagebox.askyesno(
            "تأكيد الحذف / Confirm Delete",
            f"هل أنت متأكد من حذف المنتج؟\n{name} ({code})\n\nAre you sure you want to delete this product?"
        )

        if result:
            try:
                self.cursor.execute('DELETE FROM products WHERE code = ?', (code,))
                self.conn.commit()
                self.load_products_data()
                self.update_status("تم حذف المنتج بنجاح / Product deleted successfully")
                messagebox.showinfo("نجح / Success", "تم حذف المنتج بنجاح\nProduct deleted successfully")
            except Exception as e:
                messagebox.showerror("خطأ / Error", f"فشل في حذف المنتج\nFailed to delete product:\n{str(e)}")

    def show_customers(self):
        """Show customers management"""
        self.clear_content()
        self.update_status("عرض إدارة العملاء / Showing customers management")

        tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء / Customer Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير / Under Development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=10)

    def show_suppliers(self):
        """Show suppliers management"""
        self.clear_content()
        self.update_status("عرض إدارة الموردين / Showing suppliers management")

        tk.Label(
            self.content_frame,
            text="🏢 إدارة الموردين / Supplier Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير / Under Development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=10)

    def show_sales(self):
        """Show sales management"""
        self.clear_content()
        self.update_status("عرض إدارة المبيعات / Showing sales management")

        tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات / Sales Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير / Under Development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=10)

    def show_reports(self):
        """Show reports"""
        self.clear_content()
        self.update_status("عرض التقارير / Showing reports")

        tk.Label(
            self.content_frame,
            text="📊 التقارير / Reports",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير / Under Development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=10)

    def show_settings(self):
        """Show settings"""
        self.clear_content()
        self.update_status("عرض الإعدادات / Showing settings")

        tk.Label(
            self.content_frame,
            text="⚙️ الإعدادات / Settings",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير / Under Development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=10)

    def show_help(self):
        """Show help"""
        self.clear_content()
        self.update_status("عرض المساعدة / Showing help")

        # Help content
        help_frame = tk.Frame(self.content_frame, bg='white')
        help_frame.pack(fill='both', expand=True, padx=20, pady=20)

        tk.Label(
            help_frame,
            text="❓ المساعدة / Help",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=10)

        help_text = tk.Text(
            help_frame,
            font=('Arial', 11),
            bg='#f9fafb',
            wrap='word',
            height=20
        )
        help_text.pack(fill='both', expand=True)

        help_content = """
🎉 مرحباً بك في نظام ProTech للمحاسبة
Welcome to ProTech Accounting System

📋 كيفية الاستخدام / How to Use:

1. 🏠 لوحة التحكم / Dashboard
   - عرض الإحصائيات العامة
   - مراقبة النشاط الحديث
   - نظرة سريعة على النظام

2. 📦 إدارة المخزون / Inventory Management
   - إضافة منتجات جديدة
   - تعديل بيانات المنتجات
   - حذف المنتجات
   - مراقبة المخزون المنخفض

3. 👥 إدارة العملاء / Customer Management
   - إضافة عملاء جدد
   - تعديل بيانات العملاء
   - متابعة الأرصدة

4. 🏢 إدارة الموردين / Supplier Management
   - إدارة بيانات الموردين
   - متابعة جهات الاتصال

5. 💰 المبيعات / Sales
   - تسجيل عمليات البيع
   - إدارة الفواتير
   - متابعة المدفوعات

6. 📊 التقارير / Reports
   - تقارير المبيعات
   - تقارير المخزون
   - التحليلات المالية

🔧 نصائح / Tips:
- استخدم الأزرار في القائمة الجانبية للتنقل
- تأكد من حفظ البيانات قبل الإغلاق
- راجع التقارير بانتظام لمتابعة الأداء

📞 الدعم / Support:
📧 <EMAIL>
🌐 www.protech.com
📱 +966-11-123-4567
        """

        help_text.insert('1.0', help_content)
        help_text.config(state='disabled')

    def run(self):
        """Run the application"""
        self.root.mainloop()
        self.conn.close()

class ProductDialog:
    """Dialog for adding/editing products"""

    def __init__(self, parent, title, product_data=None):
        self.result = None

        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("500x600")
        self.dialog.configure(bg='#f0f8ff')
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # Center dialog
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (500 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (600 // 2)
        self.dialog.geometry(f"500x600+{x}+{y}")

        # Create form
        self.create_form(product_data)

        # Wait for dialog to close
        self.dialog.wait_window()

    def create_form(self, product_data):
        """Create product form"""

        # Title
        title_frame = tk.Frame(self.dialog, bg='#2563eb', height=60)
        title_frame.pack(fill='x')
        title_frame.pack_propagate(False)

        tk.Label(
            title_frame,
            text="📦 بيانات المنتج / Product Information",
            font=('Arial', 16, 'bold'),
            fg='white',
            bg='#2563eb'
        ).pack(pady=15)

        # Form frame
        form_frame = tk.Frame(self.dialog, bg='#f0f8ff')
        form_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Form fields
        self.entries = {}

        fields = [
            ('code', 'كود المنتج / Product Code', 'PROD001'),
            ('name', 'اسم المنتج (إنجليزي) / Product Name (English)', 'Product Name'),
            ('name_ar', 'اسم المنتج (عربي) / Product Name (Arabic)', 'اسم المنتج'),
            ('category', 'الفئة / Category', 'Electronics'),
            ('price', 'سعر البيع / Selling Price', '100.00'),
            ('cost_price', 'سعر التكلفة / Cost Price', '80.00'),
            ('stock', 'الكمية الحالية / Current Stock', '50'),
            ('min_stock', 'الحد الأدنى / Minimum Stock', '10')
        ]

        for i, (field, label, placeholder) in enumerate(fields):
            # Label
            tk.Label(
                form_frame,
                text=label,
                font=('Arial', 11, 'bold'),
                fg='#374151',
                bg='#f0f8ff'
            ).grid(row=i, column=0, sticky='w', pady=5)

            # Entry
            entry = tk.Entry(
                form_frame,
                font=('Arial', 11),
                width=30,
                relief='solid',
                bd=1
            )
            entry.grid(row=i, column=1, sticky='ew', pady=5, padx=(10, 0))
            entry.insert(0, placeholder)

            # If editing, populate with existing data
            if product_data and len(product_data) > i + 1:
                entry.delete(0, 'end')
                entry.insert(0, str(product_data[i + 1]))  # Skip ID field

            self.entries[field] = entry

        # Configure grid
        form_frame.columnconfigure(1, weight=1)

        # Buttons frame
        btn_frame = tk.Frame(self.dialog, bg='#f0f8ff')
        btn_frame.pack(fill='x', padx=20, pady=10)

        # Save button
        tk.Button(
            btn_frame,
            text="💾 حفظ / Save",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            width=15,
            command=self.save_product,
            cursor='hand2'
        ).pack(side='left', padx=5)

        # Cancel button
        tk.Button(
            btn_frame,
            text="❌ إلغاء / Cancel",
            font=('Arial', 12, 'bold'),
            bg='#6b7280',
            fg='white',
            width=15,
            command=self.dialog.destroy,
            cursor='hand2'
        ).pack(side='right', padx=5)

    def save_product(self):
        """Save product data"""
        try:
            # Validate and collect data
            data = []

            for field in ['code', 'name', 'name_ar', 'category', 'price', 'cost_price', 'stock', 'min_stock']:
                value = self.entries[field].get().strip()

                if not value:
                    messagebox.showerror("خطأ / Error", f"يرجى ملء جميع الحقول\nPlease fill all fields")
                    return

                # Convert numeric fields
                if field in ['price', 'cost_price']:
                    try:
                        value = float(value)
                    except ValueError:
                        messagebox.showerror("خطأ / Error", f"قيمة غير صحيحة في حقل {field}\nInvalid value in {field} field")
                        return
                elif field in ['stock', 'min_stock']:
                    try:
                        value = int(value)
                    except ValueError:
                        messagebox.showerror("خطأ / Error", f"قيمة غير صحيحة في حقل {field}\nInvalid value in {field} field")
                        return

                data.append(value)

            self.result = tuple(data)
            self.dialog.destroy()

        except Exception as e:
            messagebox.showerror("خطأ / Error", f"خطأ في حفظ البيانات\nError saving data:\n{str(e)}")

def main():
    """Main function"""
    print("🖥️ Starting ProTech Standalone Desktop Application...")
    print("🖥️ تشغيل تطبيق ProTech المستقل لسطح المكتب...")

    try:
        app = ProTechStandaloneApp()
        app.run()
    except Exception as e:
        print(f"❌ Error starting application: {e}")
        messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق\nFailed to start application:\n{str(e)}")

if __name__ == '__main__':
    main()