import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    // Get basic statistics
    const [
      totalProducts,
      totalCustomers,
      totalSuppliers,
      totalInvoices,
      lowStockProducts,
      recentInvoices,
      totalSales,
      totalPurchases,
    ] = await Promise.all([
      // Total products
      db.product.count({
        where: { isActive: true },
      }),
      
      // Total customers
      db.customer.count({
        where: { isActive: true },
      }),
      
      // Total suppliers
      db.supplier.count({
        where: { isActive: true },
      }),
      
      // Total invoices
      db.invoice.count(),
      
      // Low stock products
      db.product.count({
        where: {
          AND: [
            { trackInventory: true },
            { currentStock: { lte: 10 } },
            { isActive: true },
          ],
        },
      }),
      
      // Recent invoices (last 30 days)
      db.invoice.count({
        where: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      }),
      
      // Total sales amount (last 30 days)
      db.invoice.aggregate({
        where: {
          AND: [
            { status: 'PAID' },
            {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              },
            },
          ],
        },
        _sum: {
          totalAmount: true,
        },
      }),
      
      // Total purchases amount (last 30 days)
      db.purchase.aggregate({
        where: {
          AND: [
            { status: 'RECEIVED' },
            {
              createdAt: {
                gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
              },
            },
          ],
        },
        _sum: {
          totalAmount: true,
        },
      }),
    ]);

    // Get top selling products (last 30 days)
    const topProducts = await db.invoiceItem.groupBy({
      by: ['productId'],
      where: {
        invoice: {
          createdAt: {
            gte: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          },
        },
      },
      _sum: {
        quantity: true,
        lineTotal: true,
      },
      orderBy: {
        _sum: {
          quantity: 'desc',
        },
      },
      take: 5,
    });

    // Get product details for top products
    const topProductsWithDetails = await Promise.all(
      topProducts.map(async (item) => {
        const product = await db.product.findUnique({
          where: { id: item.productId },
          select: { id: true, name: true, code: true },
        });
        return {
          ...product,
          quantity: item._sum.quantity || 0,
          revenue: item._sum.lineTotal || 0,
        };
      })
    );

    // Get recent sales trend (last 7 days)
    const salesTrend = await Promise.all(
      Array.from({ length: 7 }, (_, i) => {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const startOfDay = new Date(date.setHours(0, 0, 0, 0));
        const endOfDay = new Date(date.setHours(23, 59, 59, 999));
        
        return db.invoice.aggregate({
          where: {
            AND: [
              { status: 'PAID' },
              { createdAt: { gte: startOfDay } },
              { createdAt: { lte: endOfDay } },
            ],
          },
          _sum: {
            totalAmount: true,
          },
          _count: true,
        }).then(result => ({
          date: startOfDay.toISOString().split('T')[0],
          sales: result._sum.totalAmount || 0,
          orders: result._count || 0,
        }));
      })
    );

    const stats = {
      overview: {
        totalProducts,
        totalCustomers,
        totalSuppliers,
        totalInvoices,
        lowStockProducts,
        recentInvoices,
      },
      financial: {
        totalSales: totalSales._sum.totalAmount || 0,
        totalPurchases: totalPurchases._sum.totalAmount || 0,
        profit: (totalSales._sum.totalAmount || 0) - (totalPurchases._sum.totalAmount || 0),
      },
      topProducts: topProductsWithDetails,
      salesTrend: salesTrend.reverse(), // Show oldest to newest
    };

    return NextResponse.json({
      success: true,
      data: stats,
    });
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard statistics',
      },
      { status: 500 }
    );
  }
}
