#!/usr/bin/env python3
"""
Upgrade ProTech to Persistent Storage
ترقية ProTech إلى التخزين الدائم

Convert ProTech from temporary JSON storage to permanent SQLite storage
تحويل ProTech من تخزين JSON المؤقت إلى تخزين SQLite الدائم
"""

import os
import shutil
import re
from datetime import datetime

def backup_current_file():
    """Create backup of current file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.pre_persistent_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def add_persistent_imports():
    """Add necessary imports for persistent storage"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add SQLite and other imports
        import_additions = """import sqlite3
import shutil
import gzip
import hashlib"""
        
        # Find the import section and add new imports
        if 'import sqlite3' not in content:
            # Add after existing imports
            import_pattern = r'(import os\n)'
            replacement = f'\\1{import_additions}\n'
            content = re.sub(import_pattern, replacement, content)
            
            with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إضافة المكتبات المطلوبة للتخزين الدائم")
            return True
        else:
            print("✅ المكتبات موجودة مسبقاً")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إضافة المكتبات: {e}")
        return False

def add_persistent_storage_class():
    """Add persistent storage manager class"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Persistent storage class
        storage_class = '''
class PersistentStorageManager:
    """Advanced persistent storage manager for ProTech"""
    
    def __init__(self, base_path=".", app_name="ProTech"):
        self.base_path = base_path
        self.app_name = app_name
        
        # Storage paths
        self.data_dir = os.path.join(base_path, "data")
        self.backup_dir = os.path.join(base_path, "backups")
        self.logs_dir = os.path.join(base_path, "logs")
        
        # Storage files
        self.json_file = os.path.join(self.data_dir, "protech_data.json")
        self.sqlite_file = os.path.join(self.data_dir, "protech_data.db")
        
        # Settings
        self.auto_backup_interval = 300  # 5 minutes
        self.max_backups = 50
        
        # Thread safety
        self.storage_lock = threading.RLock()
        
        # Initialize
        self.create_directories()
        self.init_sqlite()
        self.migrate_existing_data()
        self.start_background_tasks()
        
        print("✅ تم تهيئة نظام التخزين الدائم")

    def create_directories(self):
        """Create necessary directories"""
        for directory in [self.data_dir, self.backup_dir, self.logs_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)

    def init_sqlite(self):
        """Initialize SQLite database"""
        try:
            with self.storage_lock:
                conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
                
                # SQLite optimizations
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA cache_size=10000")
                
                cursor = conn.cursor()
                
                # Create main data table
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS app_data (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        data_type TEXT NOT NULL,
                        data_key TEXT NOT NULL,
                        data_value TEXT NOT NULL,
                        metadata TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        checksum TEXT,
                        UNIQUE(data_type, data_key)
                    )
                """)
                
                # Create indexes
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_app_data_type ON app_data(data_type)")
                cursor.execute("CREATE INDEX IF NOT EXISTS idx_app_data_key ON app_data(data_key)")
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            print(f"❌ خطأ في تهيئة SQLite: {e}")

    def save_data(self, data_type, data, key="default"):
        """Save data to persistent storage"""
        try:
            with self.storage_lock:
                # Serialize data
                if isinstance(data, (dict, list)):
                    serialized_data = json.dumps(data, ensure_ascii=False, default=str)
                else:
                    serialized_data = str(data)
                
                # Calculate checksum
                checksum = hashlib.md5(serialized_data.encode()).hexdigest()
                
                # Prepare metadata
                metadata = json.dumps({
                    'size': len(serialized_data),
                    'type': type(data).__name__,
                    'save_time': datetime.now().isoformat()
                })
                
                # Save to SQLite
                conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO app_data 
                    (data_type, data_key, data_value, metadata, checksum, updated_at)
                    VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (data_type, key, serialized_data, metadata, checksum))
                
                conn.commit()
                conn.close()
                
                # Also save to JSON for compatibility
                self.save_to_json_backup(data_type, data, key)
                
                return True
                
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            return False

    def load_data(self, data_type, key="default", default_value=None):
        """Load data from persistent storage"""
        try:
            with self.storage_lock:
                # Try SQLite first
                conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT data_value, checksum FROM app_data 
                    WHERE data_type = ? AND data_key = ?
                """, (data_type, key))
                
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    data_value, stored_checksum = result
                    
                    # Verify checksum
                    calculated_checksum = hashlib.md5(data_value.encode()).hexdigest()
                    if calculated_checksum == stored_checksum:
                        # Deserialize data
                        try:
                            return json.loads(data_value)
                        except json.JSONDecodeError:
                            return data_value
                
                # Fallback to JSON
                return self.load_from_json_backup(data_type, key, default_value)
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            return default_value

    def save_to_json_backup(self, data_type, data, key):
        """Save to JSON backup for compatibility"""
        try:
            json_data = {}
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
            
            if data_type not in json_data:
                json_data[data_type] = {}
            
            json_data[data_type][key] = data
            json_data['last_updated'] = datetime.now().isoformat()
            
            # Atomic write
            temp_file = self.json_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
            
            if os.path.exists(temp_file):
                shutil.move(temp_file, self.json_file)
                
        except Exception as e:
            print(f"❌ خطأ في النسخ الاحتياطي JSON: {e}")

    def load_from_json_backup(self, data_type, key, default_value):
        """Load from JSON backup"""
        try:
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                if data_type in json_data and key in json_data[data_type]:
                    return json_data[data_type][key]
            
            return default_value
            
        except Exception as e:
            print(f"❌ خطأ في تحميل JSON: {e}")
            return default_value

    def migrate_existing_data(self):
        """Migrate existing JSON data to SQLite"""
        try:
            old_file = "protech_simple_data.json"
            if os.path.exists(old_file):
                print("🔄 ترحيل البيانات الموجودة...")
                
                with open(old_file, 'r', encoding='utf-8') as f:
                    old_data = json.load(f)
                
                migrated_count = 0
                for data_type, data in old_data.items():
                    if data_type != 'last_updated' and data:
                        if self.save_data(data_type, data):
                            migrated_count += 1
                
                print(f"✅ تم ترحيل {migrated_count} نوع من البيانات")
                
                # Create backup of old file
                backup_name = f"{old_file}.migrated_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(old_file, backup_name)
                
        except Exception as e:
            print(f"❌ خطأ في ترحيل البيانات: {e}")

    def create_backup(self, backup_name=None):
        """Create compressed backup"""
        try:
            if backup_name is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_name = f"protech_backup_{timestamp}"
            
            backup_path = os.path.join(self.backup_dir, f"{backup_name}.db.gz")
            
            # Create compressed backup
            with open(self.sqlite_file, 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            return backup_path
            
        except Exception as e:
            print(f"❌ خطأ في النسخة الاحتياطية: {e}")
            return None

    def start_background_tasks(self):
        """Start background maintenance tasks"""
        try:
            self.backup_thread = threading.Thread(target=self.auto_backup_worker, daemon=True)
            self.backup_thread.start()
        except Exception as e:
            print(f"❌ خطأ في المهام التلقائية: {e}")

    def auto_backup_worker(self):
        """Background worker for automatic backups"""
        while True:
            try:
                time.sleep(self.auto_backup_interval)
                self.create_backup()
                self.cleanup_old_backups()
            except Exception as e:
                print(f"❌ خطأ في النسخ التلقائي: {e}")

    def cleanup_old_backups(self):
        """Clean up old backup files"""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith('protech_backup_'):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getctime(file_path)))
            
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            if len(backup_files) > self.max_backups:
                for file_path, _ in backup_files[self.max_backups:]:
                    os.remove(file_path)
                    
        except Exception as e:
            print(f"❌ خطأ في تنظيف النسخ: {e}")

    def close(self):
        """Close storage manager"""
        try:
            self.create_backup("final_backup")
            print("✅ تم إغلاق نظام التخزين الدائم")
        except Exception as e:
            print(f"❌ خطأ في الإغلاق: {e}")

'''
        
        # Add the class before the main ProTech class
        if 'class PersistentStorageManager:' not in content:
            # Find the main class and add storage class before it
            main_class_pattern = r'(class ProTechApp:)'
            content = re.sub(main_class_pattern, storage_class + '\\n\\1', content)
            
            with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إضافة فئة التخزين الدائم")
            return True
        else:
            print("✅ فئة التخزين موجودة مسبقاً")
            return True
            
    except Exception as e:
        print(f"❌ خطأ في إضافة فئة التخزين: {e}")
        return False

def integrate_persistent_storage():
    """Integrate persistent storage with ProTech"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add storage manager initialization in __init__
        init_pattern = r'(self\.start_performance_monitoring\(\))'
        storage_init = '''\\1
        
        # Initialize persistent storage
        self.storage_manager = PersistentStorageManager()
        print("✅ تم تهيئة نظام التخزين الدائم")'''
        
        content = re.sub(init_pattern, storage_init, content)
        
        # Replace save_data method
        save_data_pattern = r'def save_data\(self\):.*?(?=def|\Z)'
        new_save_data = '''def save_data(self):
        """Save data using persistent storage"""
        try:
            # Save all data types to persistent storage
            data_to_save = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': getattr(self, 'sales', []),
                'invoices': getattr(self, 'invoices', []),
                'settings': getattr(self, 'settings', {})
            }
            
            success_count = 0
            for data_type, data in data_to_save.items():
                if data and self.storage_manager.save_data(data_type, data):
                    success_count += 1
            
            print(f"✅ تم حفظ {success_count} نوع من البيانات في التخزين الدائم")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            return False

    '''
        
        content = re.sub(save_data_pattern, new_save_data, content, flags=re.DOTALL)
        
        # Replace load_data method calls
        content = re.sub(
            r'with open\(\'protech_simple_data\.json\', \'r\', encoding=\'utf-8\'\) as f:\s*data = json\.load\(f\)',
            'data = self.load_persistent_data()',
            content
        )
        
        # Add load_persistent_data method
        load_method = '''
    def load_persistent_data(self):
        """Load data from persistent storage"""
        try:
            data = {}
            data_types = ['suppliers', 'products', 'customers', 'sales', 'invoices', 'settings']
            
            for data_type in data_types:
                loaded_data = self.storage_manager.load_data(data_type, default_value=[])
                if loaded_data:
                    data[data_type] = loaded_data
            
            return data
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            return {}
'''
        
        # Add the method before the last method
        content = content.replace('if __name__ == "__main__":', load_method + '\\nif __name__ == "__main__":')
        
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم دمج نظام التخزين الدائم مع ProTech")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في دمج التخزين الدائم: {e}")
        return False

def main():
    """Main upgrade function"""
    print("🔄 ترقية ProTech إلى التخزين الدائم")
    print("🔄 Upgrading ProTech to Persistent Storage")
    print()
    
    try:
        # Step 1: Backup current file
        backup_file = backup_current_file()
        if not backup_file:
            print("❌ فشل في إنشاء النسخة الاحتياطية")
            return False
        
        # Step 2: Add imports
        if not add_persistent_imports():
            print("❌ فشل في إضافة المكتبات")
            return False
        
        # Step 3: Add storage class
        if not add_persistent_storage_class():
            print("❌ فشل في إضافة فئة التخزين")
            return False
        
        # Step 4: Integrate storage
        if not integrate_persistent_storage():
            print("❌ فشل في دمج التخزين")
            return False
        
        print("\n" + "="*60)
        print("✅ تم ترقية ProTech إلى التخزين الدائم بنجاح!")
        print("✅ ProTech successfully upgraded to persistent storage!")
        print("="*60)
        
        print("\n🎯 المميزات الجديدة:")
        print("• تخزين دائم باستخدام SQLite")
        print("• نسخ احتياطية تلقائية مضغوطة")
        print("• حماية من فقدان البيانات")
        print("• أداء محسن للبيانات الكبيرة")
        print("• تنظيم هيكلي للملفات")
        
        print("\n📁 الملفات الجديدة:")
        print("• data/protech_data.db - قاعدة البيانات الرئيسية")
        print("• data/protech_data.json - نسخة احتياطية JSON")
        print("• backups/ - النسخ الاحتياطية المضغوطة")
        print("• logs/ - ملفات السجلات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الترقية: {e}")
        return False

if __name__ == "__main__":
    main()
