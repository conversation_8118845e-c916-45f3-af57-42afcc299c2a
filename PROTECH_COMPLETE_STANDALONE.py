#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏢 ProTech Complete Standalone - All-in-One Accounting System
نظام ProTech الشامل المستقل - نظام محاسبة شامل في ملف واحد

Complete standalone accounting system with all tools included
نظام محاسبة شامل مستقل مع جميع الأدوات المدمجة

Developed by Augment Agent
تم تطويره بواسطة Augment Agent
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import os
import time
import threading
from datetime import datetime
import gc
import sys
import traceback
import shutil

class ProTechCompleteStandalone:
    """ProTech Complete Standalone Accounting System"""

    def __init__(self):
        print("🏢 تشغيل نظام ProTech الشامل المستقل...")
        print("🏢 Starting ProTech Complete Standalone System...")

        # Initialize data structures FIRST to prevent AttributeError
        self.products = []
        self.customers = []
        self.suppliers = []
        self.sales = []
        
        # Setup data file path
        self.setup_data_file_path()
        
        print("✅ تم تهيئة جميع المتغيرات بنجاح")
        print("✅ All variables initialized successfully")

        # Performance flags
        self.loading = True
        self.cache_enabled = True
        self.auto_save_enabled = True
        self.last_save_time = time.time()

        # Load data
        self.load_data()

        # Initialize main window
        self.create_main_window()
        
        self.loading = False
        print("🎉 تم تحميل نظام ProTech الشامل بنجاح!")
        print("🎉 ProTech Complete System loaded successfully!")

    def setup_data_file_path(self):
        """Setup data file path with multiple fallbacks"""
        possible_paths = [
            # Same directory as script
            os.path.join(os.path.dirname(os.path.abspath(__file__)), "protech_complete_data.json"),
            
            # Desktop accounting program folder
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_complete_data.json",
            
            # Documents folder as fallback
            os.path.join(os.path.expanduser("~"), "Documents", "protech_complete_data.json"),
            
            # Current working directory
            "protech_complete_data.json"
        ]
        
        # Find existing data file or use first location
        self.data_file = None
        for path in possible_paths:
            if os.path.exists(path):
                self.data_file = path
                print(f"✅ تم العثور على ملف البيانات: {path}")
                break
        
        # If no existing file found, use first writable location
        if not self.data_file:
            for path in possible_paths:
                try:
                    test_dir = os.path.dirname(path)
                    if os.path.exists(test_dir) or test_dir == "":
                        self.data_file = path
                        print(f"📁 سيتم إنشاء ملف البيانات في: {path}")
                        break
                except:
                    continue
        
        # Ultimate fallback
        if not self.data_file:
            self.data_file = "protech_complete_data.json"
            print(f"⚠️ استخدام المسار الافتراضي: {self.data_file}")

    def load_data(self):
        """Load data from file or create sample data"""
        try:
            if os.path.exists(self.data_file):
                print("📂 تحميل البيانات من الملف...")
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                print(f"✅ تم تحميل {len(self.products)} منتج، {len(self.customers)} عميل، {len(self.suppliers)} مورد")
                
            else:
                print("📝 إنشاء بيانات نموذجية شاملة...")
                self.create_comprehensive_sample_data()
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            print("🔄 إنشاء بيانات نموذجية...")
            self.create_comprehensive_sample_data()

    def create_comprehensive_sample_data(self):
        """Create comprehensive sample data"""
        print("🏗️ إنشاء البيانات النموذجية الشاملة...")
        
        # Suppliers
        self.suppliers = [
            {
                "id": 1,
                "name": "Tech Solutions Inc.",
                "name_ar": "شركة الحلول التقنية",
                "phone": "+966-11-123-4567",
                "email": "<EMAIL>",
                "address": "الرياض، المملكة العربية السعودية",
                "contact_person": "أحمد محمد",
                "payment_terms": "30 يوم",
                "notes": "مورد موثوق للمعدات التقنية"
            },
            {
                "id": 2,
                "name": "Office World Ltd.",
                "name_ar": "شركة عالم المكاتب",
                "phone": "+966-11-234-5678",
                "email": "<EMAIL>",
                "address": "جدة، المملكة العربية السعودية",
                "contact_person": "فاطمة أحمد",
                "payment_terms": "15 يوم",
                "notes": "متخصص في مستلزمات المكاتب"
            },
            {
                "id": 3,
                "name": "Electronics Hub",
                "name_ar": "مركز الإلكترونيات",
                "phone": "+966-11-345-6789",
                "email": "<EMAIL>",
                "address": "الدمام، المملكة العربية السعودية",
                "contact_person": "محمد علي",
                "payment_terms": "45 يوم",
                "notes": "أحدث الأجهزة الإلكترونية"
            }
        ]
        
        # Products with comprehensive details
        self.products = [
            {
                "id": 1,
                "barcode": "1234567890123",
                "name": "Business Laptop",
                "name_ar": "لابتوب الأعمال",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 1,
                "cost_price": 800.00,
                "base_price": 1000.00,
                "retail_price": 1300.00,
                "wholesale_price": 1200.00,
                "distributor_price": 1150.00,
                "shop_owner_price": 1050.00,
                "stock": 45,
                "min_stock": 10,
                "max_stock": 100,
                "unit": "piece",
                "unit_ar": "قطعة",
                "location": "A1-01",
                "notes": "لابتوب عالي الأداء للأعمال مع ضمان سنتين",
                "warranty_period": "24 months",
                "last_updated": datetime.now().isoformat()
            },
            {
                "id": 2,
                "barcode": "1234567890124",
                "name": "Wireless Mouse",
                "name_ar": "فأرة لاسلكية",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 2,
                "cost_price": 20.00,
                "base_price": 25.00,
                "retail_price": 32.50,
                "wholesale_price": 30.00,
                "distributor_price": 28.75,
                "shop_owner_price": 26.25,
                "stock": 150,
                "min_stock": 30,
                "max_stock": 200,
                "unit": "piece",
                "unit_ar": "قطعة",
                "location": "B2-15",
                "notes": "فأرة لاسلكية عالية الدقة مع بطارية طويلة المدى",
                "warranty_period": "12 months",
                "last_updated": datetime.now().isoformat()
            },
            {
                "id": 3,
                "barcode": "1234567890125",
                "name": "Mechanical Keyboard",
                "name_ar": "لوحة مفاتيح ميكانيكية",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 3,
                "cost_price": 60.00,
                "base_price": 80.00,
                "retail_price": 104.00,
                "wholesale_price": 96.00,
                "distributor_price": 92.00,
                "shop_owner_price": 84.00,
                "stock": 60,
                "min_stock": 15,
                "max_stock": 80,
                "unit": "piece",
                "unit_ar": "قطعة",
                "location": "C1-08",
                "notes": "لوحة مفاتيح ميكانيكية للألعاب مع إضاءة RGB",
                "warranty_period": "18 months",
                "last_updated": datetime.now().isoformat()
            },
            {
                "id": 4,
                "barcode": "1234567890126",
                "name": "24 inch Monitor",
                "name_ar": "شاشة 24 بوصة",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 1,
                "cost_price": 150.00,
                "base_price": 200.00,
                "retail_price": 260.00,
                "wholesale_price": 240.00,
                "distributor_price": 230.00,
                "shop_owner_price": 210.00,
                "stock": 30,
                "min_stock": 8,
                "max_stock": 50,
                "unit": "piece",
                "unit_ar": "قطعة",
                "location": "D3-05",
                "notes": "شاشة عالية الدقة 24 بوصة مع تقنية IPS",
                "warranty_period": "36 months",
                "last_updated": datetime.now().isoformat()
            },
            {
                "id": 5,
                "barcode": "1234567890127",
                "name": "Smartphone",
                "name_ar": "هاتف ذكي",
                "category": "Electronics",
                "category_ar": "إلكترونيات",
                "supplier_id": 2,
                "cost_price": 300.00,
                "base_price": 400.00,
                "retail_price": 520.00,
                "wholesale_price": 480.00,
                "distributor_price": 460.00,
                "shop_owner_price": 420.00,
                "stock": 75,
                "min_stock": 20,
                "max_stock": 100,
                "unit": "piece",
                "unit_ar": "قطعة",
                "location": "E1-12",
                "notes": "هاتف ذكي متطور مع كاميرا عالية الدقة",
                "warranty_period": "24 months",
                "last_updated": datetime.now().isoformat()
            }
        ]
        
        # Customers with comprehensive details
        self.customers = [
            {
                "id": 1,
                "name": "John Smith",
                "name_ar": "جون سميث",
                "phone": "******-1234",
                "email": "<EMAIL>",
                "address": "123 Main St, New York, USA",
                "city": "New York",
                "country": "USA",
                "type": "RETAIL",
                "type_ar": "تجزئة",
                "balance": 1250.00,
                "credit_limit": 5000.00,
                "payment_terms": "فوري",
                "discount_rate": 0.0,
                "tax_number": "US123456789",
                "notes": "عميل مميز منذ 2020",
                "last_purchase": "2024-12-15",
                "total_purchases": 15750.00
            },
            {
                "id": 2,
                "name": "ABC Corporation",
                "name_ar": "شركة ABC",
                "phone": "******-5678",
                "email": "<EMAIL>",
                "address": "456 Business Ave, Los Angeles, USA",
                "city": "Los Angeles",
                "country": "USA",
                "type": "WHOLESALE",
                "type_ar": "جملة",
                "balance": 8750.00,
                "credit_limit": 50000.00,
                "payment_terms": "30 يوم",
                "discount_rate": 5.0,
                "tax_number": "US987654321",
                "notes": "شريك تجاري استراتيجي",
                "last_purchase": "2024-12-10",
                "total_purchases": 125000.00
            },
            {
                "id": 3,
                "name": "Ahmed Al-Rashid",
                "name_ar": "أحمد الراشد",
                "phone": "+966-50-123-4567",
                "email": "<EMAIL>",
                "address": "الرياض، المملكة العربية السعودية",
                "city": "الرياض",
                "country": "السعودية",
                "type": "SHOP_OWNER",
                "type_ar": "صاحب محل",
                "balance": 2500.00,
                "credit_limit": 15000.00,
                "payment_terms": "15 يوم",
                "discount_rate": 3.0,
                "tax_number": "SA123456789",
                "notes": "صاحب محل إلكترونيات في الرياض",
                "last_purchase": "2024-12-12",
                "total_purchases": 45000.00
            }
        ]
        
        # Sample sales
        self.sales = [
            {
                "id": 1,
                "invoice_number": "INV-2024-001",
                "date": "2024-12-15",
                "customer_id": 1,
                "customer_name": "John Smith",
                "items": [
                    {"product_id": 2, "product_name": "Wireless Mouse", "quantity": 2, "unit_price": 32.50, "total": 65.00},
                    {"product_id": 3, "product_name": "Mechanical Keyboard", "quantity": 1, "unit_price": 104.00, "total": 104.00}
                ],
                "subtotal": 169.00,
                "tax_rate": 15.0,
                "tax_amount": 25.35,
                "discount_amount": 0.00,
                "total": 194.35,
                "paid": 194.35,
                "balance": 0.00,
                "status": "مدفوع",
                "payment_method": "نقد",
                "notes": "بيع نقدي"
            }
        ]
        
        # Save sample data
        self.save_data()
        print("✅ تم إنشاء البيانات النموذجية الشاملة بنجاح")

    def save_data(self):
        """Save data to file with backup"""
        try:
            # Create backup if file exists
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.data_file, backup_file)
            
            # Prepare data
            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0',
                'created_by': 'ProTech Complete Standalone'
            }
            
            # Ensure directory exists
            data_dir = os.path.dirname(self.data_file)
            if data_dir and not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # Save data
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"💾 تم حفظ البيانات في: {self.data_file}")
            self.last_save_time = time.time()
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            # Try alternative save location
            try:
                alt_file = os.path.join(os.path.expanduser("~"), "Documents", "protech_complete_backup.json")
                with open(alt_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print(f"💾 تم حفظ البيانات في الموقع البديل: {alt_file}")
            except:
                print("❌ فشل في حفظ البيانات في جميع المواقع")

    def create_main_window(self):
        """Create main application window"""
        self.root = tk.Tk()
        self.root.title("🏢 ProTech Complete Standalone - نظام ProTech الشامل المستقل")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f8ff')
        
        # Try to maximize window
        try:
            self.root.state('zoomed')
        except:
            self.root.attributes('-zoomed', True)
        
        # Create interface
        self.create_interface()
        
        # Setup window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

    def create_interface(self):
        """Create user interface"""
        # Main container
        main_container = tk.Frame(self.root, bg='#f0f8ff')
        main_container.pack(fill='both', expand=True)

        # Create sidebar
        self.create_sidebar(main_container)
        
        # Content area
        self.content_frame = tk.Frame(main_container, bg='white', relief='sunken', bd=2)
        self.content_frame.pack(side='right', fill='both', expand=True, padx=5, pady=5)
        
        # Show dashboard by default
        self.show_dashboard()

    def create_sidebar(self, parent):
        """Create navigation sidebar"""
        self.sidebar = tk.Frame(parent, bg='#2c3e50', width=280, relief='raised', bd=2)
        self.sidebar.pack(side='left', fill='y', padx=5, pady=5)
        self.sidebar.pack_propagate(False)

        # Title
        title_frame = tk.Frame(self.sidebar, bg='#34495e', relief='raised', bd=2)
        title_frame.pack(fill='x', padx=5, pady=5)
        
        title_label = tk.Label(
            title_frame,
            text="🏢 ProTech Complete\nنظام ProTech الشامل",
            font=('Arial', 16, 'bold'),
            bg='#34495e',
            fg='white',
            justify='center'
        )
        title_label.pack(pady=15)

        # Navigation buttons
        nav_buttons = [
            ("🏠 الرئيسية\nDashboard", self.show_dashboard, '#3498db'),
            ("📦 المخزون\nInventory", self.show_inventory, '#2ecc71'),
            ("👥 العملاء\nCustomers", self.show_customers, '#e74c3c'),
            ("🏢 الموردين\nSuppliers", self.show_suppliers, '#f39c12'),
            ("💰 المبيعات\nSales", self.show_sales, '#9b59b6'),
            ("📊 التقارير\nReports", self.show_reports, '#1abc9c'),
            ("🛠️ الأدوات\nTools", self.show_tools, '#95a5a6'),
            ("⚙️ الإعدادات\nSettings", self.show_settings, '#34495e')
        ]

        for text, command, color in nav_buttons:
            btn = tk.Button(
                self.sidebar,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=12,
                cursor='hand2',
                anchor='center'
            )
            btn.pack(fill='x', padx=10, pady=5)

        # System info
        info_frame = tk.Frame(self.sidebar, bg='#2c3e50')
        info_frame.pack(side='bottom', fill='x', padx=5, pady=5)
        
        info_label = tk.Label(
            info_frame,
            text=f"📅 {datetime.now().strftime('%Y-%m-%d')}\n🕐 {datetime.now().strftime('%H:%M')}\n💾 البيانات محفوظة",
            font=('Arial', 10),
            bg='#2c3e50',
            fg='#bdc3c7',
            justify='center'
        )
        info_label.pack(pady=10)

    def clear_content(self):
        """Clear content area"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        """Show main dashboard"""
        self.clear_content()

        # Title
        title_frame = tk.Frame(self.content_frame, bg='white')
        title_frame.pack(fill='x', pady=20)

        title_label = tk.Label(
            title_frame,
            text="🏠 لوحة التحكم الرئيسية\nMain Dashboard",
            font=('Arial', 24, 'bold'),
            bg='white',
            fg='#2c3e50',
            justify='center'
        )
        title_label.pack()

        # Statistics frame
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(pady=30)

        # Statistics cards
        stats = [
            ("📦 المنتجات\nProducts", len(self.products), '#3498db'),
            ("👥 العملاء\nCustomers", len(self.customers), '#2ecc71'),
            ("🏢 الموردين\nSuppliers", len(self.suppliers), '#e74c3c'),
            ("💰 المبيعات\nSales", len(self.sales), '#f39c12')
        ]

        for i, (text, count, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
            stat_frame.grid(row=0, column=i, padx=20, pady=10, sticky='ew')

            # Count
            count_label = tk.Label(
                stat_frame,
                text=str(count),
                font=('Arial', 36, 'bold'),
                bg=color,
                fg='white'
            )
            count_label.pack(pady=15)

            # Text
            text_label = tk.Label(
                stat_frame,
                text=text,
                font=('Arial', 14, 'bold'),
                bg=color,
                fg='white',
                justify='center'
            )
            text_label.pack(pady=(0, 15))

        # Quick actions
        actions_frame = tk.Frame(self.content_frame, bg='#ecf0f1', relief='sunken', bd=2)
        actions_frame.pack(fill='x', padx=20, pady=20)

        actions_title = tk.Label(
            actions_frame,
            text="⚡ إجراءات سريعة / Quick Actions",
            font=('Arial', 16, 'bold'),
            bg='#ecf0f1',
            fg='#2c3e50'
        )
        actions_title.pack(pady=10)

        buttons_frame = tk.Frame(actions_frame, bg='#ecf0f1')
        buttons_frame.pack(pady=10)

        quick_actions = [
            ("➕ إضافة منتج", self.quick_add_product, '#2ecc71'),
            ("👤 إضافة عميل", self.quick_add_customer, '#3498db'),
            ("💰 فاتورة جديدة", self.quick_new_sale, '#9b59b6'),
            ("📊 تقرير سريع", self.quick_report, '#1abc9c')
        ]

        for i, (text, command, color) in enumerate(quick_actions):
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=2,
                padx=15,
                pady=8,
                cursor='hand2'
            )
            btn.grid(row=0, column=i, padx=10, pady=5)

    def show_inventory(self):
        """Show inventory management"""
        self.clear_content()

        # Title
        title_frame = tk.Frame(self.content_frame, bg='white')
        title_frame.pack(fill='x', pady=10)

        title_label = tk.Label(
            title_frame,
            text="📦 إدارة المخزون\nInventory Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50',
            justify='center'
        )
        title_label.pack()

        # Control buttons
        control_frame = tk.Frame(self.content_frame, bg='white')
        control_frame.pack(fill='x', pady=10)

        # Force reload button (RED - IMPORTANT)
        reload_btn = tk.Button(
            control_frame,
            text="🔄 إعادة تحميل البيانات\nForce Reload Data",
            command=self.force_reload_data,
            font=('Arial', 14, 'bold'),
            bg='#dc2626',
            fg='white',
            relief='raised',
            bd=3,
            padx=20,
            pady=10,
            cursor='hand2'
        )
        reload_btn.pack(side='left', padx=10)

        # Add product button
        add_btn = tk.Button(
            control_frame,
            text="➕ إضافة منتج\nAdd Product",
            command=self.add_product_dialog,
            font=('Arial', 12, 'bold'),
            bg='#059669',
            fg='white',
            relief='raised',
            bd=3,
            padx=15,
            pady=8,
            cursor='hand2'
        )
        add_btn.pack(side='left', padx=10)

        # Products table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        # Table columns
        columns = ('ID', 'Barcode', 'Name', 'Category', 'Cost', 'Retail Price', 'Stock', 'Min Stock', 'Location')

        # Create Treeview
        self.inventory_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # Set column headings and widths
        for col in columns:
            self.inventory_tree.heading(col, text=col, anchor='center')
            self.inventory_tree.column(col, width=100, anchor='center')

        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.inventory_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.inventory_tree.xview)

        self.inventory_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack elements
        self.inventory_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Refresh table
        self.refresh_inventory_table()

    def refresh_inventory_table(self):
        """Refresh inventory table"""
        # Clear existing items
        for item in self.inventory_tree.get_children():
            self.inventory_tree.delete(item)

        # Add products
        for product in self.products:
            values = (
                product.get('id', ''),
                product.get('barcode', ''),
                f"{product.get('name', '')} | {product.get('name_ar', '')}",
                f"{product.get('category', '')} | {product.get('category_ar', '')}",
                f"${product.get('cost_price', 0):.2f}",
                f"${product.get('retail_price', 0):.2f}",
                product.get('stock', 0),
                product.get('min_stock', 0),
                product.get('location', '')
            )

            # Color coding for low stock
            item_id = self.inventory_tree.insert('', 'end', values=values)
            if product.get('stock', 0) <= product.get('min_stock', 0):
                self.inventory_tree.set(item_id, 'Stock', f"⚠️ {product.get('stock', 0)}")

    def force_reload_data(self):
        """Force reload data from file"""
        try:
            print("🔄 إعادة تحميل البيانات بالقوة...")

            # Clear current data
            self.products = []
            self.customers = []
            self.suppliers = []
            self.sales = []

            # Reload from file
            self.load_data()

            # Refresh current view
            if hasattr(self, 'inventory_tree'):
                self.refresh_inventory_table()

            # Success message
            messagebox.showinfo(
                "إعادة تحميل البيانات\nData Reload",
                f"✅ تم إعادة تحميل البيانات بنجاح!\n✅ Data reloaded successfully!\n\n"
                f"📊 المنتجات: {len(self.products)}\n📊 Products: {len(self.products)}\n"
                f"👥 العملاء: {len(self.customers)}\n👥 Customers: {len(self.customers)}\n"
                f"🏢 الموردين: {len(self.suppliers)}\n🏢 Suppliers: {len(self.suppliers)}\n"
                f"💰 المبيعات: {len(self.sales)}\n💰 Sales: {len(self.sales)}"
            )

            print("✅ تم إعادة تحميل البيانات بنجاح")

        except Exception as e:
            print(f"❌ خطأ في إعادة تحميل البيانات: {e}")
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في إعادة تحميل البيانات\nFailed to reload data:\n{str(e)}"
            )

    def show_customers(self):
        """Show customer management"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء\nCustomer Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # Customers table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('ID', 'Name', 'Phone', 'Email', 'Type', 'Balance', 'Credit Limit')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col, anchor='center')
            tree.column(col, width=150, anchor='center')

        # Add customers
        for customer in self.customers:
            tree.insert('', 'end', values=(
                customer.get('id', ''),
                f"{customer.get('name', '')} | {customer.get('name_ar', '')}",
                customer.get('phone', ''),
                customer.get('email', ''),
                f"{customer.get('type', '')} | {customer.get('type_ar', '')}",
                f"${customer.get('balance', 0):.2f}",
                f"${customer.get('credit_limit', 0):.2f}"
            ))

        tree.pack(fill='both', expand=True)

    def show_suppliers(self):
        """Show supplier management"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="🏢 إدارة الموردين\nSupplier Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # Suppliers table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('ID', 'Name', 'Phone', 'Email', 'Contact Person', 'Payment Terms')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col, anchor='center')
            tree.column(col, width=180, anchor='center')

        # Add suppliers
        for supplier in self.suppliers:
            tree.insert('', 'end', values=(
                supplier.get('id', ''),
                f"{supplier.get('name', '')} | {supplier.get('name_ar', '')}",
                supplier.get('phone', ''),
                supplier.get('email', ''),
                supplier.get('contact_person', ''),
                supplier.get('payment_terms', '')
            ))

        tree.pack(fill='both', expand=True)

    def show_sales(self):
        """Show sales management"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات\nSales Management",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # Sales table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('Invoice #', 'Date', 'Customer', 'Total', 'Paid', 'Balance', 'Status')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col, anchor='center')
            tree.column(col, width=120, anchor='center')

        # Add sales
        for sale in self.sales:
            tree.insert('', 'end', values=(
                sale.get('invoice_number', ''),
                sale.get('date', ''),
                sale.get('customer_name', ''),
                f"${sale.get('total', 0):.2f}",
                f"${sale.get('paid', 0):.2f}",
                f"${sale.get('balance', 0):.2f}",
                sale.get('status', '')
            ))

        tree.pack(fill='both', expand=True)

    def show_reports(self):
        """Show reports"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="📊 التقارير والإحصائيات\nReports & Statistics",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # Reports frame
        reports_frame = tk.Frame(self.content_frame, bg='#ecf0f1', relief='sunken', bd=2)
        reports_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Calculate statistics
        total_products = len(self.products)
        total_customers = len(self.customers)
        total_suppliers = len(self.suppliers)
        total_sales = len(self.sales)

        low_stock_items = len([p for p in self.products if p.get('stock', 0) <= p.get('min_stock', 0)])
        total_inventory_value = sum(p.get('stock', 0) * p.get('cost_price', 0) for p in self.products)
        total_sales_value = sum(s.get('total', 0) for s in self.sales)

        # Statistics text
        stats_text = f"""
📊 إحصائيات النظام الشاملة / Comprehensive System Statistics

📦 المخزون / Inventory:
   • إجمالي المنتجات: {total_products} منتج
   • المنتجات منخفضة المخزون: {low_stock_items} منتج
   • قيمة المخزون الإجمالية: ${total_inventory_value:,.2f}

👥 العملاء / Customers:
   • إجمالي العملاء: {total_customers} عميل
   • عملاء التجزئة: {len([c for c in self.customers if c.get('type') == 'RETAIL'])} عميل
   • عملاء الجملة: {len([c for c in self.customers if c.get('type') == 'WHOLESALE'])} عميل

🏢 الموردين / Suppliers:
   • إجمالي الموردين: {total_suppliers} مورد

💰 المبيعات / Sales:
   • إجمالي الفواتير: {total_sales} فاتورة
   • إجمالي قيمة المبيعات: ${total_sales_value:,.2f}

📅 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """

        stats_label = tk.Label(
            reports_frame,
            text=stats_text,
            font=('Arial', 12),
            bg='#ecf0f1',
            fg='#2c3e50',
            justify='right',
            anchor='e'
        )
        stats_label.pack(pady=20, padx=20, fill='both', expand=True)

    def show_tools(self):
        """Show tools and utilities"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="🛠️ الأدوات والمرافق\nTools & Utilities",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # Tools frame
        tools_frame = tk.Frame(self.content_frame, bg='white')
        tools_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # Tool buttons
        tools = [
            ("💾 إنشاء نسخة احتياطية\nCreate Backup", self.create_backup, '#3498db'),
            ("📂 استيراد البيانات\nImport Data", self.import_data, '#2ecc71'),
            ("📤 تصدير البيانات\nExport Data", self.export_data, '#f39c12'),
            ("🔍 فحص البيانات\nCheck Data", self.check_data_integrity, '#9b59b6'),
            ("🧹 تنظيف البيانات\nClean Data", self.clean_data, '#e74c3c'),
            ("📊 تحليل البيانات\nAnalyze Data", self.analyze_data, '#1abc9c')
        ]

        for i, (text, command, color) in enumerate(tools):
            row = i // 3
            col = i % 3

            btn = tk.Button(
                tools_frame,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=15,
                cursor='hand2',
                width=20,
                height=3
            )
            btn.grid(row=row, column=col, padx=10, pady=10, sticky='ew')

        # Configure grid weights
        for i in range(3):
            tools_frame.columnconfigure(i, weight=1)

    def show_settings(self):
        """Show system settings"""
        self.clear_content()

        title_label = tk.Label(
            self.content_frame,
            text="⚙️ إعدادات النظام\nSystem Settings",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title_label.pack(pady=20)

        # Settings frame
        settings_frame = tk.Frame(self.content_frame, bg='#ecf0f1', relief='sunken', bd=2)
        settings_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # System information
        system_info = f"""
📋 معلومات النظام / System Information:

🏢 اسم النظام: ProTech Complete Standalone
📋 الإصدار: 1.0
👨‍💻 المطور: Augment Agent
📅 تاريخ الإنشاء: 2025-06-20

📁 مسار ملف البيانات:
{self.data_file}

📊 إحصائيات البيانات الحالية:
• المنتجات: {len(self.products)}
• العملاء: {len(self.customers)}
• الموردين: {len(self.suppliers)}
• المبيعات: {len(self.sales)}

💾 آخر حفظ: {datetime.fromtimestamp(self.last_save_time).strftime('%Y-%m-%d %H:%M:%S')}
🔄 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🎯 الميزات المتوفرة:
✅ إدارة شاملة للمخزون مع تفاصيل كاملة
✅ إدارة العملاء مع أنواع مختلفة وحدود ائتمان
✅ إدارة الموردين مع معلومات الاتصال
✅ نظام مبيعات متكامل مع الفواتير
✅ تقارير وإحصائيات مفصلة
✅ أدوات النسخ الاحتياطي والاستيراد/التصدير
✅ فحص وتحليل البيانات
✅ واجهة ثنائية اللغة (عربي/إنجليزي)
✅ حفظ تلقائي ونسخ احتياطية
        """

        info_label = tk.Label(
            settings_frame,
            text=system_info,
            font=('Arial', 11),
            bg='#ecf0f1',
            fg='#2c3e50',
            justify='right',
            anchor='ne'
        )
        info_label.pack(pady=20, padx=20, fill='both', expand=True)

        # Action buttons
        buttons_frame = tk.Frame(settings_frame, bg='#ecf0f1')
        buttons_frame.pack(pady=10)

        action_buttons = [
            ("💾 حفظ البيانات\nSave Data", self.save_data, '#2ecc71'),
            ("🔄 إعادة تحميل\nReload", self.force_reload_data, '#3498db'),
            ("📋 نسخ المسار\nCopy Path", self.copy_data_path, '#f39c12')
        ]

        for text, command, color in action_buttons:
            btn = tk.Button(
                buttons_frame,
                text=text,
                command=command,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white',
                relief='raised',
                bd=3,
                padx=20,
                pady=10,
                cursor='hand2'
            )
            btn.pack(side='left', padx=10)

    # Quick action methods
    def quick_add_product(self):
        """Quick add product"""
        messagebox.showinfo(
            "إضافة منتج سريع\nQuick Add Product",
            "ميزة إضافة المنتجات ستكون متوفرة قريباً\nQuick add product feature coming soon"
        )

    def quick_add_customer(self):
        """Quick add customer"""
        messagebox.showinfo(
            "إضافة عميل سريع\nQuick Add Customer",
            "ميزة إضافة العملاء ستكون متوفرة قريباً\nQuick add customer feature coming soon"
        )

    def quick_new_sale(self):
        """Quick new sale"""
        messagebox.showinfo(
            "فاتورة جديدة\nNew Sale",
            "ميزة الفواتير الجديدة ستكون متوفرة قريباً\nNew sale feature coming soon"
        )

    def quick_report(self):
        """Quick report"""
        self.show_reports()

    def add_product_dialog(self):
        """Add product dialog"""
        messagebox.showinfo(
            "إضافة منتج\nAdd Product",
            "نافذة إضافة المنتجات ستكون متوفرة قريباً\nAdd product dialog coming soon"
        )

    # Tool methods
    def create_backup(self):
        """Create data backup"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"protech_complete_backup_{timestamp}.json"

            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'backup_created': datetime.now().isoformat(),
                'version': '1.0'
            }

            with open(backup_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)

            messagebox.showinfo(
                "نسخة احتياطية\nBackup",
                f"✅ تم إنشاء النسخة الاحتياطية بنجاح!\n✅ Backup created successfully!\n\n"
                f"📁 الملف: {backup_file}"
            )

        except Exception as e:
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في إنشاء النسخة الاحتياطية\nFailed to create backup:\n{str(e)}"
            )

    def import_data(self):
        """Import data from file"""
        try:
            file_path = filedialog.askopenfilename(
                title="استيراد البيانات / Import Data",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # Validate data structure
                required_keys = ['suppliers', 'products', 'customers', 'sales']
                if all(key in data for key in required_keys):
                    self.suppliers = data['suppliers']
                    self.products = data['products']
                    self.customers = data['customers']
                    self.sales = data['sales']

                    # Save imported data
                    self.save_data()

                    # Refresh current view
                    if hasattr(self, 'inventory_tree'):
                        self.refresh_inventory_table()

                    messagebox.showinfo(
                        "استيراد البيانات\nImport Data",
                        f"✅ تم استيراد البيانات بنجاح!\n✅ Data imported successfully!\n\n"
                        f"📊 المنتجات: {len(self.products)}\n"
                        f"👥 العملاء: {len(self.customers)}\n"
                        f"🏢 الموردين: {len(self.suppliers)}\n"
                        f"💰 المبيعات: {len(self.sales)}"
                    )
                else:
                    messagebox.showerror(
                        "خطأ\nError",
                        "تنسيق الملف غير صحيح\nInvalid file format"
                    )

        except Exception as e:
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في استيراد البيانات\nFailed to import data:\n{str(e)}"
            )

    def export_data(self):
        """Export data to file"""
        try:
            file_path = filedialog.asksaveasfilename(
                title="تصدير البيانات / Export Data",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )

            if file_path:
                data = {
                    'suppliers': self.suppliers,
                    'products': self.products,
                    'customers': self.customers,
                    'sales': self.sales,
                    'exported_at': datetime.now().isoformat(),
                    'version': '1.0',
                    'exported_by': 'ProTech Complete Standalone'
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)

                messagebox.showinfo(
                    "تصدير البيانات\nExport Data",
                    f"✅ تم تصدير البيانات بنجاح!\n✅ Data exported successfully!\n\n"
                    f"📁 الملف: {file_path}"
                )

        except Exception as e:
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في تصدير البيانات\nFailed to export data:\n{str(e)}"
            )

    def check_data_integrity(self):
        """Check data integrity"""
        issues = []

        # Check products
        product_ids = [p.get('id') for p in self.products]
        if len(product_ids) != len(set(product_ids)):
            issues.append("تكرار في معرفات المنتجات / Duplicate product IDs")

        # Check customers
        customer_ids = [c.get('id') for c in self.customers]
        if len(customer_ids) != len(set(customer_ids)):
            issues.append("تكرار في معرفات العملاء / Duplicate customer IDs")

        # Check suppliers
        supplier_ids = [s.get('id') for s in self.suppliers]
        if len(supplier_ids) != len(set(supplier_ids)):
            issues.append("تكرار في معرفات الموردين / Duplicate supplier IDs")

        # Check low stock
        low_stock = [p for p in self.products if p.get('stock', 0) <= p.get('min_stock', 0)]
        if low_stock:
            issues.append(f"منتجات منخفضة المخزون: {len(low_stock)} / Low stock items: {len(low_stock)}")

        if issues:
            messagebox.showwarning(
                "فحص البيانات\nData Check",
                f"تم العثور على مشاكل:\nIssues found:\n\n" + "\n".join(issues)
            )
        else:
            messagebox.showinfo(
                "فحص البيانات\nData Check",
                "✅ جميع البيانات سليمة!\n✅ All data is healthy!"
            )

    def clean_data(self):
        """Clean data"""
        messagebox.showinfo(
            "تنظيف البيانات\nClean Data",
            "ميزة تنظيف البيانات ستكون متوفرة قريباً\nData cleaning feature coming soon"
        )

    def analyze_data(self):
        """Analyze data"""
        self.show_reports()

    def copy_data_path(self):
        """Copy data file path to clipboard"""
        try:
            self.root.clipboard_clear()
            self.root.clipboard_append(self.data_file)
            messagebox.showinfo(
                "نسخ المسار\nCopy Path",
                f"تم نسخ المسار إلى الحافظة\nPath copied to clipboard:\n\n{self.data_file}"
            )
        except Exception as e:
            messagebox.showerror(
                "خطأ\nError",
                f"فشل في نسخ المسار\nFailed to copy path:\n{str(e)}"
            )

    def on_closing(self):
        """Handle application closing"""
        try:
            print("💾 حفظ البيانات قبل الإغلاق...")
            self.save_data()
            print("✅ تم حفظ البيانات بنجاح")
            print("👋 إغلاق ProTech Complete Standalone...")
        except Exception as e:
            print(f"⚠️ خطأ في حفظ البيانات: {e}")
        finally:
            self.root.destroy()

    def run(self):
        """Run the application"""
        try:
            print("🚀 تشغيل واجهة ProTech Complete Standalone...")
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            traceback.print_exc()

def main():
    """Main function"""
    try:
        print("🏢 بدء تشغيل ProTech Complete Standalone...")
        print("🏢 Starting ProTech Complete Standalone...")
        print("=" * 60)

        app = ProTechCompleteStandalone()
        app.run()

    except Exception as e:
        print(f"❌ خطأ في بدء التشغيل: {e}")
        print(f"❌ Startup error: {e}")
        traceback.print_exc()
        input("اضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
