#!/usr/bin/env python3

print("🚀 تشغيل نظام ProTech على المنفذ 8000 / Running ProTech on port 8000")

try:
    from flask import Flask
    
    app = Flask(__name__)
    
    @app.route('/')
    def home():
        return '''
        <html>
        <head>
            <title>ProTech Accounting System</title>
            <meta charset="utf-8">
            <style>
                body { font-family: Arial; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }
                .container { max-width: 800px; margin: 0 auto; background: rgba(255,255,255,0.1); padding: 30px; border-radius: 15px; }
                .success { background: rgba(16, 185, 129, 0.2); padding: 15px; border-radius: 8px; margin: 20px 0; }
                .info { background: rgba(59, 130, 246, 0.2); padding: 15px; border-radius: 8px; margin: 20px 0; }
                a { color: #fbbf24; text-decoration: none; }
                a:hover { text-decoration: underline; }
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🎉 نظام ProTech للمحاسبة</h1>
                <h2>🎉 ProTech Accounting System</h2>
                
                <div class="success">
                    <h3>✅ نجح التشغيل! / Successfully Running!</h3>
                    <p>🐍 Python Flask Server: نشط / Active</p>
                    <p>🌐 المنفذ / Port: 8000</p>
                    <p>📱 الحالة / Status: جاهز للاستخدام / Ready for use</p>
                </div>
                
                <div class="info">
                    <h3>🔗 روابط سريعة / Quick Links</h3>
                    <p><a href="http://localhost:8000">🏠 الصفحة الرئيسية / Home</a></p>
                    <p><a href="http://localhost:8000/dashboard">📊 لوحة التحكم / Dashboard</a></p>
                    <p><a href="http://localhost:8000/test">🧪 صفحة الاختبار / Test Page</a></p>
                </div>
                
                <div class="info">
                    <h3>📋 المميزات / Features</h3>
                    <ul>
                        <li>📦 إدارة المخزون / Inventory Management</li>
                        <li>👥 إدارة العملاء / Customer Management</li>
                        <li>💰 إدارة المبيعات / Sales Management</li>
                        <li>🏢 إدارة الموردين / Supplier Management</li>
                        <li>📈 التقارير والتحليلات / Reports & Analytics</li>
                        <li>🌍 دعم ثنائي اللغة / Bilingual Support</li>
                    </ul>
                </div>
                
                <p style="text-align: center; margin-top: 30px;">
                    <strong>🚀 النظام جاهز للاستخدام! / System ready for use!</strong>
                </p>
            </div>
        </body>
        </html>
        '''
    
    @app.route('/dashboard')
    def dashboard():
        return '<h1>📊 لوحة التحكم / Dashboard</h1><p>قريباً... / Coming soon...</p>'
    
    @app.route('/test')
    def test():
        return '<h1>🧪 صفحة الاختبار / Test Page</h1><p>الاختبار نجح! / Test successful!</p>'
    
    print("✅ تم إعداد التطبيق / Application configured")
    print("🌐 بدء الخادم على المنفذ 8000 / Starting server on port 8000")
    print("🔗 الرابط / URL: http://localhost:8000")
    
    app.run(host='0.0.0.0', port=8000, debug=True, use_reloader=False)
    
except Exception as e:
    print(f"❌ خطأ / Error: {e}")
    import traceback
    traceback.print_exc()
