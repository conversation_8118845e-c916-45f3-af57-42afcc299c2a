#!/usr/bin/env python3
"""
ProTech Accounting System - Working Version
نظام ProTech للمحاسبة - النسخة العاملة
"""

from flask import Flask, jsonify
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'protech-working-key'

# Sample data
products = [
    {'id': 1, 'name': 'Business Laptop', 'name_ar': 'لابتوب الأعمال', 'stock': 50, 'price': 1000, 'min_stock': 10},
    {'id': 2, 'name': 'Wireless Mouse', 'name_ar': 'فأرة لاسلكية', 'stock': 200, 'price': 25, 'min_stock': 50},
    {'id': 3, 'name': 'Professional Notebook', 'name_ar': 'دفتر مهني', 'stock': 5, 'price': 5, 'min_stock': 200},
    {'id': 4, 'name': 'Business Smartphone', 'name_ar': 'هاتف ذكي للأعمال', 'stock': 25, 'price': 550, 'min_stock': 5},
    {'id': 5, 'name': 'Office Desk', 'name_ar': 'مكتب مكتبي', 'stock': 0, 'price': 300, 'min_stock': 3}
]

customers = [
    {'id': 1, 'name': 'John Smith', 'name_ar': 'جون سميث', 'balance': 1250, 'category': 'RETAIL'},
    {'id': 2, 'name': 'ABC Corporation', 'name_ar': 'شركة ABC', 'balance': 8750, 'category': 'WHOLESALE'},
    {'id': 3, 'name': 'Ahmed Al-Rashid', 'name_ar': 'أحمد الراشد', 'balance': 2500, 'category': 'RETAIL'}
]

# HTML Template
def get_html_template(title, content):
    return f'''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title} - نظام ProTech للمحاسبة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .ltr {{ direction: ltr; text-align: left; }}
        .rtl {{ direction: rtl; text-align: right; }}
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <!-- Header -->
    <header class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white shadow-lg">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold">🎉 نظام ProTech للمحاسبة</h1>
                    <p class="text-blue-100 mt-1">ProTech Accounting System</p>
                </div>
                <div class="text-left ltr">
                    <div class="text-sm text-blue-100">🕒 {datetime.now().strftime('%Y-%m-%d %H:%M')}</div>
                    <div class="text-xs text-blue-200">🐍 Python Flask Server</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-blue-500 shadow-md">
        <div class="container mx-auto px-4">
            <div class="flex space-x-1 space-x-reverse py-3">
                <a href="/" class="px-4 py-2 rounded-lg hover:bg-blue-400 transition-colors duration-200 text-white">🏠 الرئيسية</a>
                <a href="/inventory" class="px-4 py-2 rounded-lg hover:bg-blue-400 transition-colors duration-200 text-white">📦 المخزون</a>
                <a href="/customers" class="px-4 py-2 rounded-lg hover:bg-blue-400 transition-colors duration-200 text-white">👥 العملاء</a>
                <a href="/sales" class="px-4 py-2 rounded-lg hover:bg-blue-400 transition-colors duration-200 text-white">💰 المبيعات</a>
                <a href="/reports" class="px-4 py-2 rounded-lg hover:bg-blue-400 transition-colors duration-200 text-white">📈 التقارير</a>
                <a href="/api/products" class="px-4 py-2 rounded-lg hover:bg-blue-400 transition-colors duration-200 text-white">🔌 API</a>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <main class="container mx-auto px-4 py-8">
        {content}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="container mx-auto px-4 py-6">
            <div class="text-center">
                <p class="text-lg font-semibold">© 2024 نظام ProTech للمحاسبة</p>
                <p class="text-gray-300 mt-1">ProTech Accounting System</p>
                <div class="mt-3 text-sm text-gray-400">
                    <span>🐍 Python Flask</span> • 
                    <span>🌍 Arabic & English</span> • 
                    <span>📱 Responsive Design</span> • 
                    <span>⚡ Fast Performance</span>
                </div>
            </div>
        </div>
    </footer>
</body>
</html>
'''

@app.route('/')
def dashboard():
    total_products = len(products)
    low_stock = len([p for p in products if p['stock'] <= p['min_stock']])
    total_customers = len(customers)
    total_value = sum(p['stock'] * p['price'] for p in products)
    
    content = f'''
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-blue-500">
            <div class="flex items-center">
                <div class="text-3xl">📦</div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">إجمالي المنتجات</h3>
                    <p class="text-3xl font-bold text-blue-600">{total_products}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-red-500">
            <div class="flex items-center">
                <div class="text-3xl">⚠️</div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">مخزون منخفض</h3>
                    <p class="text-3xl font-bold text-red-600">{low_stock}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-green-500">
            <div class="flex items-center">
                <div class="text-3xl">👥</div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">العملاء</h3>
                    <p class="text-3xl font-bold text-green-600">{total_customers}</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6 border-l-4 border-purple-500">
            <div class="flex items-center">
                <div class="text-3xl">💰</div>
                <div class="mr-4">
                    <h3 class="text-lg font-semibold text-gray-700">قيمة المخزون</h3>
                    <p class="text-2xl font-bold text-purple-600">${total_value:,.0f}</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="bg-gradient-to-r from-green-400 to-blue-500 rounded-xl shadow-lg p-8 text-white mb-8">
        <div class="text-center">
            <h2 class="text-3xl font-bold mb-4">🎉 مرحباً بك في نظام ProTech للمحاسبة</h2>
            <p class="text-xl mb-4">Welcome to ProTech Accounting System</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
                <div class="bg-white bg-opacity-20 rounded-lg p-4">
                    <h3 class="font-bold">✅ النظام نشط</h3>
                    <p>System Active</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-4">
                    <h3 class="font-bold">🌍 ثنائي اللغة</h3>
                    <p>Bilingual Support</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-4">
                    <h3 class="font-bold">⚡ أداء سريع</h3>
                    <p>Fast Performance</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">📊 نظرة سريعة على المخزون</h3>
            <div class="space-y-3">
    '''
    
    for product in products[:3]:
        status_color = "text-red-600" if product['stock'] <= product['min_stock'] else "text-green-600"
        status_text = "منخفض" if product['stock'] <= product['min_stock'] else "جيد"
        content += f'''
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                        <div class="font-semibold">{product['name']}</div>
                        <div class="text-sm text-gray-600">{product['name_ar']}</div>
                    </div>
                    <div class="text-left ltr">
                        <div class="font-bold">{product['stock']} units</div>
                        <div class="text-sm {status_color}">{status_text}</div>
                    </div>
                </div>
        '''
    
    content += '''
            </div>
        </div>
        
        <div class="bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4">👥 العملاء الرئيسيون</h3>
            <div class="space-y-3">
    '''
    
    for customer in customers:
        content += f'''
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div>
                        <div class="font-semibold">{customer['name']}</div>
                        <div class="text-sm text-gray-600">{customer['name_ar']}</div>
                    </div>
                    <div class="text-left ltr">
                        <div class="font-bold">${customer['balance']}</div>
                        <div class="text-sm text-blue-600">{customer['category']}</div>
                    </div>
                </div>
        '''
    
    content += '''
            </div>
        </div>
    </div>
    '''
    
    return get_html_template("لوحة التحكم", content)

@app.route('/inventory')
def inventory():
    content = '''
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white p-6">
            <h2 class="text-2xl font-bold">📦 إدارة المخزون</h2>
            <p class="text-blue-100">Inventory Management</p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase">المنتج</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase">الكمية</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase">السعر</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase">القيمة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase">الحالة</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
    '''
    
    for product in products:
        total_value = product['stock'] * product['price']
        if product['stock'] == 0:
            status = '<span class="px-2 py-1 text-xs font-semibold bg-red-100 text-red-800 rounded-full">نفد المخزون</span>'
        elif product['stock'] <= product['min_stock']:
            status = '<span class="px-2 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded-full">منخفض</span>'
        else:
            status = '<span class="px-2 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full">جيد</span>'
        
        content += f'''
                    <tr class="hover:bg-gray-50">
                        <td class="px-6 py-4">
                            <div class="font-medium text-gray-900">{product['name']}</div>
                            <div class="text-sm text-gray-500">{product['name_ar']}</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-900">{product['stock']}</td>
                        <td class="px-6 py-4 text-sm text-gray-900">${product['price']}</td>
                        <td class="px-6 py-4 text-sm text-gray-900">${total_value:,.0f}</td>
                        <td class="px-6 py-4">{status}</td>
                    </tr>
        '''
    
    content += '''
                </tbody>
            </table>
        </div>
    </div>
    '''
    
    return get_html_template("إدارة المخزون", content)

@app.route('/customers')
def customers_page():
    content = '''
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="bg-gradient-to-r from-green-600 to-teal-600 text-white p-6">
            <h2 class="text-2xl font-bold">👥 إدارة العملاء</h2>
            <p class="text-green-100">Customer Management</p>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    '''
    
    for customer in customers:
        content += f'''
                <div class="border border-gray-200 rounded-lg p-6 hover:shadow-lg transition-shadow duration-200">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold text-lg">
                            {customer['name'][0]}
                        </div>
                        <div class="mr-4">
                            <h3 class="font-bold text-lg text-gray-900">{customer['name']}</h3>
                            <p class="text-gray-600">{customer['name_ar']}</p>
                        </div>
                    </div>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between">
                            <span class="text-gray-600">الرصيد:</span>
                            <span class="font-semibold">${customer['balance']}</span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600">الفئة:</span>
                            <span class="font-semibold">{customer['category']}</span>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            نشط
                        </span>
                    </div>
                </div>
        '''
    
    content += '''
            </div>
        </div>
    </div>
    '''
    
    return get_html_template("إدارة العملاء", content)

@app.route('/sales')
def sales():
    content = '''
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">💰 إدارة المبيعات</h2>
            <p class="text-gray-600 mb-8">Sales Management</p>
            
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-8">
                <div class="text-6xl mb-4">🚧</div>
                <h3 class="text-xl font-bold text-blue-800 mb-2">قيد التطوير</h3>
                <p class="text-blue-600">هذا القسم قيد التطوير وسيكون متاحاً قريباً</p>
                <p class="text-blue-500 text-sm mt-2">This section is under development and will be available soon</p>
            </div>
        </div>
    </div>
    '''
    
    return get_html_template("إدارة المبيعات", content)

@app.route('/reports')
def reports():
    content = '''
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">📈 التقارير والتحليلات</h2>
            <p class="text-gray-600 mb-8">Reports & Analytics</p>
            
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-8">
                <div class="text-6xl mb-4">📊</div>
                <h3 class="text-xl font-bold text-purple-800 mb-2">قريباً</h3>
                <p class="text-purple-600">تقارير شاملة وتحليلات متقدمة قريباً</p>
                <p class="text-purple-500 text-sm mt-2">Comprehensive reports and advanced analytics coming soon</p>
            </div>
        </div>
    </div>
    '''
    
    return get_html_template("التقارير والتحليلات", content)

# API Routes
@app.route('/api/products')
def api_products():
    return jsonify({
        'success': True,
        'data': products,
        'count': len(products),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/customers')
def api_customers():
    return jsonify({
        'success': True,
        'data': customers,
        'count': len(customers),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/stats')
def api_stats():
    return jsonify({
        'success': True,
        'data': {
            'total_products': len(products),
            'total_customers': len(customers),
            'low_stock_items': len([p for p in products if p['stock'] <= p['min_stock']]),
            'out_of_stock_items': len([p for p in products if p['stock'] == 0]),
            'total_inventory_value': sum(p['stock'] * p['price'] for p in products)
        },
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 Starting ProTech Accounting System...")
    print("🌐 URL: http://localhost:5000")
    print("✅ Ready for use!")
    
    app.run(host='0.0.0.0', port=5000, debug=False, use_reloader=False)
