import jwt from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { db } from './db';
import { User } from '@/types';

const JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';

export interface JWTPayload {
  userId: string;
  email: string;
  role: string;
}

export class AuthService {
  static async hashPassword(password: string): Promise<string> {
    return bcrypt.hash(password, 10);
  }

  static async comparePassword(password: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(password, hashedPassword);
  }

  static generateToken(payload: JWTPayload): string {
    return jwt.sign(payload, JWT_SECRET, { expiresIn: '24h' });
  }

  static verifyToken(token: string): JWTPayload | null {
    try {
      return jwt.verify(token, JWT_SECRET) as JWTPayload;
    } catch (error) {
      return null;
    }
  }

  static async authenticate(email: string, password: string): Promise<{ user: User; token: string } | null> {
    try {
      const user = await db.user.findUnique({
        where: { email },
      });

      if (!user || !user.isActive) {
        return null;
      }

      const isPasswordValid = await this.comparePassword(password, user.password);
      if (!isPasswordValid) {
        return null;
      }

      const token = this.generateToken({
        userId: user.id,
        email: user.email,
        role: user.role,
      });

      // Remove password from user object
      const { password: _, ...userWithoutPassword } = user;

      return {
        user: userWithoutPassword as User,
        token,
      };
    } catch (error) {
      console.error('Authentication error:', error);
      return null;
    }
  }

  static async getUserFromToken(token: string): Promise<User | null> {
    try {
      const payload = this.verifyToken(token);
      if (!payload) {
        return null;
      }

      const user = await db.user.findUnique({
        where: { id: payload.userId },
      });

      if (!user || !user.isActive) {
        return null;
      }

      // Remove password from user object
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword as User;
    } catch (error) {
      console.error('Get user from token error:', error);
      return null;
    }
  }

  static async createUser(userData: {
    email: string;
    username: string;
    password: string;
    firstName: string;
    lastName: string;
    role?: 'ADMIN' | 'MANAGER' | 'USER';
  }): Promise<User> {
    const hashedPassword = await this.hashPassword(userData.password);

    const user = await db.user.create({
      data: {
        ...userData,
        password: hashedPassword,
        role: userData.role || 'USER',
      },
    });

    // Remove password from user object
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }

  static async updateUser(userId: string, userData: Partial<{
    email: string;
    username: string;
    firstName: string;
    lastName: string;
    role: 'ADMIN' | 'MANAGER' | 'USER';
    isActive: boolean;
  }>): Promise<User> {
    const user = await db.user.update({
      where: { id: userId },
      data: userData,
    });

    // Remove password from user object
    const { password: _, ...userWithoutPassword } = user;
    return userWithoutPassword as User;
  }

  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<boolean> {
    try {
      const user = await db.user.findUnique({
        where: { id: userId },
      });

      if (!user) {
        return false;
      }

      const isCurrentPasswordValid = await this.comparePassword(currentPassword, user.password);
      if (!isCurrentPasswordValid) {
        return false;
      }

      const hashedNewPassword = await this.hashPassword(newPassword);
      await db.user.update({
        where: { id: userId },
        data: { password: hashedNewPassword },
      });

      return true;
    } catch (error) {
      console.error('Change password error:', error);
      return false;
    }
  }

  static async resetPassword(email: string, newPassword: string): Promise<boolean> {
    try {
      const hashedPassword = await this.hashPassword(newPassword);
      await db.user.update({
        where: { email },
        data: { password: hashedPassword },
      });
      return true;
    } catch (error) {
      console.error('Reset password error:', error);
      return false;
    }
  }

  static hasPermission(userRole: string, requiredRole: string): boolean {
    const roleHierarchy = {
      'USER': 1,
      'MANAGER': 2,
      'ADMIN': 3,
    };

    const userLevel = roleHierarchy[userRole as keyof typeof roleHierarchy] || 0;
    const requiredLevel = roleHierarchy[requiredRole as keyof typeof roleHierarchy] || 0;

    return userLevel >= requiredLevel;
  }
}

// Middleware function for API routes
export function withAuth(handler: Function, requiredRole: string = 'USER') {
  return async (request: Request, ...args: any[]) => {
    try {
      const authHeader = request.headers.get('authorization');
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return new Response(
          JSON.stringify({ success: false, error: 'Unauthorized' }),
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        );
      }

      const token = authHeader.substring(7);
      const user = await AuthService.getUserFromToken(token);

      if (!user) {
        return new Response(
          JSON.stringify({ success: false, error: 'Invalid token' }),
          { status: 401, headers: { 'Content-Type': 'application/json' } }
        );
      }

      if (!AuthService.hasPermission(user.role, requiredRole)) {
        return new Response(
          JSON.stringify({ success: false, error: 'Insufficient permissions' }),
          { status: 403, headers: { 'Content-Type': 'application/json' } }
        );
      }

      // Add user to request context
      (request as any).user = user;

      return handler(request, ...args);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return new Response(
        JSON.stringify({ success: false, error: 'Authentication failed' }),
        { status: 500, headers: { 'Content-Type': 'application/json' } }
      );
    }
  };
}
