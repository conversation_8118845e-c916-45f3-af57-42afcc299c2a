#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Auto-Save Monitor
مراقب الحفظ التلقائي لـ ProTech
"""

import json
import os
import time
import shutil
from datetime import datetime

def monitor_and_save():
    """Monitor data file and create backups"""
    data_file = "protech_simple_data.json"
    last_backup_time = 0
    backup_interval = 300  # 5 minutes
    
    while True:
        try:
            if os.path.exists(data_file):
                current_time = time.time()
                
                if current_time - last_backup_time > backup_interval:
                    # Create backup
                    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                    backup_name = f"protech_auto_backup_{timestamp}.json"
                    
                    try:
                        shutil.copy2(data_file, backup_name)
                        print(f"✅ نسخة احتياطية تلقائية: {backup_name}")
                        last_backup_time = current_time
                    except Exception as e:
                        print(f"⚠️ فشل في النسخ الاحتياطي: {e}")
            
            time.sleep(60)  # Check every minute
            
        except KeyboardInterrupt:
            print("\n🛑 تم إيقاف مراقب الحفظ التلقائي")
            break
        except Exception as e:
            print(f"❌ خطأ في المراقب: {e}")
            time.sleep(60)

if __name__ == "__main__":
    print("🔄 بدء مراقب الحفظ التلقائي...")
    monitor_and_save()
