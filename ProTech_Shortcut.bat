@echo off
title ProTech Accounting System - نظام ProTech للمحاسبة
color 0A

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    ProTech Accounting System                 ║
echo ║                    نظام ProTech للمحاسبة                    ║
echo ╠══════════════════════════════════════════════════════════════╣
echo ║  🚀 Starting Advanced Accounting System...                  ║
echo ║  🚀 جاري تشغيل نظام المحاسبة المتقدم...                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

cd /d "%~dp0"

if exist "protech_simple_working.py" (
    echo ✅ Program file found / تم العثور على ملف البرنامج
    echo ⏳ Loading system / جاري تحميل النظام...
    echo.
    python protech_simple_working.py
) else (
    echo ❌ Error: Program file not found!
    echo ❌ خطأ: لم يتم العثور على ملف البرنامج!
    echo.
    echo Please make sure all files are in the same folder:
    echo يرجى التأكد من وجود جميع الملفات في نفس المجلد:
    echo - protech_simple_working.py
    echo - protech_data.json
    echo.
    pause
)

if %errorlevel% neq 0 (
    echo.
    echo ❌ Error occurred / حدث خطأ
    echo.
    echo Possible solutions / الحلول المحتملة:
    echo 1. Install Python / قم بتثبيت Python
    echo 2. Check file permissions / تحقق من صلاحيات الملفات
    echo 3. Run as administrator / شغل كمدير
    echo.
    pause
)
