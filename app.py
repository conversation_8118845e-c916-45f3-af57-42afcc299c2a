#!/usr/bin/env python3
"""
ProTech Accounting System - Python Flask Application
A comprehensive accounting software with inventory management, barcode scanning, and multi-language support
"""

from flask import Flask, render_template, jsonify, request, redirect, url_for, flash, session
from datetime import datetime, timedelta
import json
import os
import random
import logging
from functools import wraps
import time

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('protech.log'),
        logging.StreamHandler()
    ]
)

app = Flask(__name__)
app.secret_key = os.environ.get('SECRET_KEY', 'protech-accounting-secret-key-2024')
app.config['JSON_AS_ASCII'] = False  # Support for Arabic text
app.config['JSONIFY_PRETTYPRINT_REGULAR'] = True

# Performance monitoring decorator
def monitor_performance(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        start_time = time.time()
        result = f(*args, **kwargs)
        end_time = time.time()
        app.logger.info(f"Route {request.endpoint} took {end_time - start_time:.3f} seconds")
        return result
    return decorated_function

# Enhanced sample data with better performance and more realistic data
sample_data = {
    'products': [
        {
            'id': 1,
            'code': 'LAPTOP001',
            'name': 'Business Laptop Pro',
            'name_ar': 'لابتوب الأعمال المتقدم',
            'description': 'High-performance laptop for business use',
            'description_ar': 'لابتوب عالي الأداء للاستخدام التجاري',
            'barcode': '***********23',
            'category': 'Electronics',
            'category_ar': 'إلكترونيات',
            'supplier': 'Tech Solutions Inc.',
            'unit': 'PCS',
            'unit_ar': 'قطعة',
            'cost_price': 800.00,
            'base_price': 1000.00,
            'price_level_1': 1000.00,  # Base price
            'price_level_2': 950.00,   # 5% discount
            'price_level_3': 900.00,   # 10% discount
            'price_level_4': 850.00,   # 15% discount
            'current_stock': 50,
            'min_stock': 10,
            'max_stock': 100,
            'location': 'A1-01',
            'is_active': True,
            'created_at': '2024-01-15',
            'updated_at': '2024-12-15'
        },
        {
            'id': 2,
            'code': 'MOUSE001',
            'name': 'Wireless Mouse',
            'name_ar': 'فأرة لاسلكية',
            'description': 'Ergonomic wireless mouse',
            'description_ar': 'فأرة لاسلكية مريحة',
            'barcode': '***********24',
            'category': 'Electronics',
            'category_ar': 'إلكترونيات',
            'supplier': 'Tech Solutions Inc.',
            'unit': 'PCS',
            'unit_ar': 'قطعة',
            'cost_price': 15.00,
            'base_price': 25.00,
            'price_level_1': 25.00,
            'price_level_2': 23.75,
            'price_level_3': 22.50,
            'price_level_4': 21.25,
            'current_stock': 200,
            'min_stock': 50,
            'max_stock': 500,
            'location': 'B2-15',
            'is_active': True,
            'created_at': '2024-02-01',
            'updated_at': '2024-12-10'
        },
        {
            'id': 3,
            'code': 'NOTE001',
            'name': 'Professional Notebook',
            'name_ar': 'دفتر مهني',
            'description': 'A4 lined notebook for professional use',
            'description_ar': 'دفتر A4 مسطر للاستخدام المهني',
            'barcode': '***********25',
            'category': 'Office Supplies',
            'category_ar': 'مستلزمات مكتبية',
            'supplier': 'Office World Ltd.',
            'unit': 'PCS',
            'unit_ar': 'قطعة',
            'cost_price': 3.00,
            'base_price': 5.00,
            'price_level_1': 5.00,
            'price_level_2': 4.75,
            'price_level_3': 4.50,
            'price_level_4': 4.25,
            'current_stock': 5,  # Low stock for demo
            'min_stock': 200,
            'max_stock': 2000,
            'location': 'C3-20',
            'is_active': True,
            'created_at': '2024-03-01',
            'updated_at': '2024-12-14'
        },
        {
            'id': 4,
            'code': 'PHONE001',
            'name': 'Business Smartphone',
            'name_ar': 'هاتف ذكي للأعمال',
            'description': 'Professional smartphone for business communication',
            'description_ar': 'هاتف ذكي مهني للتواصل التجاري',
            'barcode': '***********26',
            'category': 'Electronics',
            'category_ar': 'إلكترونيات',
            'supplier': 'Tech Solutions Inc.',
            'unit': 'PCS',
            'unit_ar': 'قطعة',
            'cost_price': 400.00,
            'base_price': 550.00,
            'price_level_1': 550.00,
            'price_level_2': 522.50,
            'price_level_3': 495.00,
            'price_level_4': 467.50,
            'current_stock': 25,
            'min_stock': 5,
            'max_stock': 50,
            'location': 'A1-02',
            'is_active': True,
            'created_at': '2024-04-01',
            'updated_at': '2024-12-12'
        },
        {
            'id': 5,
            'code': 'DESK001',
            'name': 'Office Desk',
            'name_ar': 'مكتب مكتبي',
            'description': 'Ergonomic office desk with storage',
            'description_ar': 'مكتب مكتبي مريح مع تخزين',
            'barcode': '***********27',
            'category': 'Furniture',
            'category_ar': 'أثاث',
            'supplier': 'Furniture Plus',
            'unit': 'PCS',
            'unit_ar': 'قطعة',
            'cost_price': 200.00,
            'base_price': 300.00,
            'price_level_1': 300.00,
            'price_level_2': 285.00,
            'price_level_3': 270.00,
            'price_level_4': 255.00,
            'current_stock': 0,  # Out of stock for demo
            'min_stock': 3,
            'max_stock': 20,
            'location': 'D1-01',
            'is_active': True,
            'created_at': '2024-05-01',
            'updated_at': '2024-12-13'
        }
    ],
    'customers': [
        {
            'id': 1,
            'code': 'CUST001',
            'name': 'John Smith',
            'name_ar': 'جون سميث',
            'email': '<EMAIL>',
            'phone': '******-1234',
            'address': '789 Customer Lane',
            'address_ar': '789 شارع العملاء',
            'city': 'Customer City',
            'city_ar': 'مدينة العملاء',
            'country': 'USA',
            'country_ar': 'الولايات المتحدة',
            'credit_limit': 5000.00,
            'current_balance': 1250.00,
            'category': 'RETAIL',
            'category_ar': 'تجزئة',
            'price_level': 1,
            'tax_number': 'TAX001',
            'payment_terms': 30,
            'is_active': True,
            'created_at': '2024-01-10',
            'last_purchase': '2024-12-10'
        },
        {
            'id': 2,
            'code': 'CUST002',
            'name': 'ABC Corporation',
            'name_ar': 'شركة ABC',
            'email': '<EMAIL>',
            'phone': '******-5678',
            'address': '321 Corporate Blvd',
            'address_ar': '321 شارع الشركات',
            'city': 'Business District',
            'city_ar': 'المنطقة التجارية',
            'country': 'USA',
            'country_ar': 'الولايات المتحدة',
            'credit_limit': 25000.00,
            'current_balance': 8750.00,
            'category': 'WHOLESALE',
            'category_ar': 'جملة',
            'price_level': 2,
            'tax_number': 'TAX002',
            'payment_terms': 15,
            'is_active': True,
            'created_at': '2024-02-15',
            'last_purchase': '2024-12-14'
        },
        {
            'id': 3,
            'code': 'CUST003',
            'name': 'Ahmed Al-Rashid',
            'name_ar': 'أحمد الراشد',
            'email': '<EMAIL>',
            'phone': '+966-50-123-4567',
            'address': 'King Fahd Road, Riyadh',
            'address_ar': 'طريق الملك فهد، الرياض',
            'city': 'Riyadh',
            'city_ar': 'الرياض',
            'country': 'Saudi Arabia',
            'country_ar': 'المملكة العربية السعودية',
            'credit_limit': 10000.00,
            'current_balance': 2500.00,
            'category': 'RETAIL',
            'category_ar': 'تجزئة',
            'price_level': 1,
            'tax_number': 'VAT003',
            'payment_terms': 30,
            'is_active': True,
            'created_at': '2024-03-20',
            'last_purchase': '2024-12-12'
        }
    ],
    'suppliers': [
        {
            'id': 1,
            'code': 'TECH001',
            'name': 'Tech Solutions Inc.',
            'name_ar': 'شركة الحلول التقنية',
            'email': '<EMAIL>',
            'phone': '******-0123',
            'address': '123 Tech Street',
            'address_ar': '123 شارع التقنية',
            'city': 'Tech City',
            'city_ar': 'مدينة التقنية',
            'country': 'USA',
            'country_ar': 'الولايات المتحدة',
            'contact_person': 'Mike Johnson',
            'contact_person_ar': 'مايك جونسون',
            'payment_terms': 30,
            'is_active': True,
            'created_at': '2024-01-05'
        },
        {
            'id': 2,
            'code': 'OFF001',
            'name': 'Office World Ltd.',
            'name_ar': 'شركة عالم المكاتب',
            'email': '<EMAIL>',
            'phone': '******-0456',
            'address': '456 Office Ave',
            'address_ar': '456 شارع المكاتب',
            'city': 'Business City',
            'city_ar': 'مدينة الأعمال',
            'country': 'USA',
            'country_ar': 'الولايات المتحدة',
            'contact_person': 'Sarah Wilson',
            'contact_person_ar': 'سارة ويلسون',
            'payment_terms': 45,
            'is_active': True,
            'created_at': '2024-02-10'
        },
        {
            'id': 3,
            'code': 'FURN001',
            'name': 'Furniture Plus',
            'name_ar': 'أثاث بلس',
            'email': '<EMAIL>',
            'phone': '******-0789',
            'address': '789 Furniture Blvd',
            'address_ar': '789 شارع الأثاث',
            'city': 'Design City',
            'city_ar': 'مدينة التصميم',
            'country': 'USA',
            'country_ar': 'الولايات المتحدة',
            'contact_person': 'David Brown',
            'contact_person_ar': 'ديفيد براون',
            'payment_terms': 60,
            'is_active': True,
            'created_at': '2024-03-15'
        }
    ],
    'invoices': [
        {
            'id': 1,
            'number': 'INV-202412-0001',
            'customer_id': 1,
            'customer_name': 'John Smith',
            'customer_name_ar': 'جون سميث',
            'date': '2024-12-15',
            'due_date': '2025-01-14',
            'subtotal': 1000.00,
            'tax_amount': 150.00,
            'discount_amount': 0.00,
            'total_amount': 1150.00,
            'paid_amount': 1150.00,
            'status': 'PAID',
            'status_ar': 'مدفوع',
            'payment_method': 'CASH',
            'notes': 'Payment received in full',
            'created_by': 'admin',
            'created_at': '2024-12-15 10:30:00'
        },
        {
            'id': 2,
            'number': 'INV-202412-0002',
            'customer_id': 2,
            'customer_name': 'ABC Corporation',
            'customer_name_ar': 'شركة ABC',
            'date': '2024-12-14',
            'due_date': '2024-12-29',
            'subtotal': 15000.00,
            'tax_amount': 2250.00,
            'discount_amount': 500.00,
            'total_amount': 16750.00,
            'paid_amount': 0.00,
            'status': 'PENDING',
            'status_ar': 'معلق',
            'payment_method': 'CREDIT',
            'notes': 'Wholesale order - 15 day terms',
            'created_by': 'admin',
            'created_at': '2024-12-14 14:15:00'
        },
        {
            'id': 3,
            'number': 'INV-202412-0003',
            'customer_id': 3,
            'customer_name': 'Ahmed Al-Rashid',
            'customer_name_ar': 'أحمد الراشد',
            'date': '2024-12-12',
            'due_date': '2025-01-11',
            'subtotal': 2500.00,
            'tax_amount': 375.00,
            'discount_amount': 125.00,
            'total_amount': 2750.00,
            'paid_amount': 1000.00,
            'status': 'PARTIAL',
            'status_ar': 'جزئي',
            'payment_method': 'BANK_TRANSFER',
            'notes': 'Partial payment received',
            'created_by': 'admin',
            'created_at': '2024-12-12 09:45:00'
        }
    ],
    'inventory_movements': [
        {
            'id': 1,
            'product_id': 1,
            'product_code': 'LAPTOP001',
            'product_name': 'Business Laptop Pro',
            'product_name_ar': 'لابتوب الأعمال المتقدم',
            'type': 'IN',
            'type_ar': 'دخول',
            'quantity': 50,
            'unit_cost': 800.00,
            'total_cost': 40000.00,
            'reason': 'Initial stock',
            'reason_ar': 'مخزون أولي',
            'reference_type': 'PURCHASE',
            'reference_id': 'PO-001',
            'date': '2024-12-01 08:00:00',
            'user': 'Admin User',
            'user_ar': 'مستخدم إداري',
            'notes': 'Initial inventory setup'
        },
        {
            'id': 2,
            'product_id': 2,
            'product_code': 'MOUSE001',
            'product_name': 'Wireless Mouse',
            'product_name_ar': 'فأرة لاسلكية',
            'type': 'OUT',
            'type_ar': 'خروج',
            'quantity': -5,
            'unit_cost': 15.00,
            'total_cost': -75.00,
            'reason': 'Sale to customer',
            'reason_ar': 'بيع للعميل',
            'reference_type': 'SALE',
            'reference_id': 'INV-202412-0001',
            'date': '2024-12-15 10:30:00',
            'user': 'Sales User',
            'user_ar': 'مستخدم مبيعات',
            'notes': 'Sold to John Smith'
        },
        {
            'id': 3,
            'product_id': 3,
            'product_code': 'NOTE001',
            'product_name': 'Professional Notebook',
            'product_name_ar': 'دفتر مهني',
            'type': 'ADJUSTMENT',
            'type_ar': 'تعديل',
            'quantity': -195,
            'unit_cost': 3.00,
            'total_cost': -585.00,
            'reason': 'Physical Count',
            'reason_ar': 'جرد فعلي',
            'reference_type': 'ADJUSTMENT',
            'reference_id': 'ADJ-001',
            'date': '2024-12-14 16:00:00',
            'user': 'Inventory Manager',
            'user_ar': 'مدير المخزون',
            'notes': 'Stock adjustment after physical count'
        },
        {
            'id': 4,
            'product_id': 4,
            'product_code': 'PHONE001',
            'product_name': 'Business Smartphone',
            'product_name_ar': 'هاتف ذكي للأعمال',
            'type': 'IN',
            'type_ar': 'دخول',
            'quantity': 25,
            'unit_cost': 400.00,
            'total_cost': 10000.00,
            'reason': 'Purchase order received',
            'reason_ar': 'استلام أمر شراء',
            'reference_type': 'PURCHASE',
            'reference_id': 'PO-002',
            'date': '2024-12-10 14:20:00',
            'user': 'Receiving Clerk',
            'user_ar': 'موظف الاستلام',
            'notes': 'New smartphone model received'
        }
    ]
}

def get_dashboard_stats():
    """Calculate enhanced dashboard statistics with caching"""
    try:
        # Basic counts
        total_products = len([p for p in sample_data['products'] if p['is_active']])
        total_customers = len([c for c in sample_data['customers'] if c['is_active']])
        total_suppliers = len([s for s in sample_data['suppliers'] if s['is_active']])

        # Calculate inventory value (using cost price for valuation)
        total_inventory_value = sum(
            p['current_stock'] * p['cost_price']
            for p in sample_data['products']
            if p['is_active']
        )

        # Calculate retail value
        total_retail_value = sum(
            p['current_stock'] * p['base_price']
            for p in sample_data['products']
            if p['is_active']
        )

        # Stock analysis
        low_stock_items = len([
            p for p in sample_data['products']
            if p['is_active'] and p['current_stock'] <= p['min_stock']
        ])

        out_of_stock_items = len([
            p for p in sample_data['products']
            if p['is_active'] and p['current_stock'] == 0
        ])

        # Sales analysis
        total_sales = sum(
            inv['total_amount'] for inv in sample_data['invoices']
            if inv['status'] == 'PAID'
        )

        pending_invoices = len([
            inv for inv in sample_data['invoices']
            if inv['status'] in ['PENDING', 'PARTIAL']
        ])

        pending_amount = sum(
            inv['total_amount'] - inv['paid_amount']
            for inv in sample_data['invoices']
            if inv['status'] in ['PENDING', 'PARTIAL']
        )

        # Customer analysis
        total_receivables = sum(c['current_balance'] for c in sample_data['customers'])

        # Recent activity
        recent_movements = len([
            m for m in sample_data['inventory_movements']
            if m['date'] >= '2024-12-10'
        ])

        return {
            'total_products': total_products,
            'total_customers': total_customers,
            'total_suppliers': total_suppliers,
            'total_inventory_value': total_inventory_value,
            'total_retail_value': total_retail_value,
            'low_stock_items': low_stock_items,
            'out_of_stock': out_of_stock_items,
            'total_sales': total_sales,
            'pending_invoices': pending_invoices,
            'pending_amount': pending_amount,
            'total_receivables': total_receivables,
            'recent_movements': recent_movements,
            'profit_margin': ((total_retail_value - total_inventory_value) / total_retail_value * 100) if total_retail_value > 0 else 0
        }
    except Exception as e:
        app.logger.error(f"Error calculating dashboard stats: {e}")
        return {
            'total_products': 0,
            'total_customers': 0,
            'total_suppliers': 0,
            'total_inventory_value': 0.0,
            'low_stock_items': 0,
            'total_sales': 0.0,
            'pending_invoices': 0,
            'out_of_stock': 0
        }

@app.route('/')
@monitor_performance
def dashboard():
    """Main dashboard page with enhanced error handling"""
    try:
        stats = get_dashboard_stats()
        app.logger.info("Dashboard accessed successfully")
        return render_template('dashboard.html', stats=stats)
    except Exception as e:
        app.logger.error(f"Error loading dashboard: {e}")
        flash('حدث خطأ في تحميل لوحة التحكم', 'error')
        return render_template('error.html', error="Dashboard loading error"), 500

@app.route('/inventory')
@monitor_performance
def inventory():
    """Enhanced inventory management page"""
    try:
        # Filter active products only
        products = [p for p in sample_data['products'] if p['is_active']]
        stats = get_dashboard_stats()

        # Add search functionality
        search_query = request.args.get('search', '').lower()
        if search_query:
            products = [
                p for p in products
                if search_query in p['name'].lower()
                or search_query in p['code'].lower()
                or search_query in p.get('name_ar', '').lower()
            ]

        app.logger.info(f"Inventory page accessed, showing {len(products)} products")
        return render_template('inventory.html', products=products, stats=stats, search=search_query)
    except Exception as e:
        app.logger.error(f"Error loading inventory: {e}")
        flash('حدث خطأ في تحميل صفحة المخزون', 'error')
        return render_template('error.html', error="Inventory loading error"), 500

@app.route('/customers')
@monitor_performance
def customers():
    """Enhanced customer management page"""
    try:
        # Filter active customers only
        customers_list = [c for c in sample_data['customers'] if c['is_active']]

        # Add search and filter functionality
        search_query = request.args.get('search', '').lower()
        category_filter = request.args.get('category', '')

        if search_query:
            customers_list = [
                c for c in customers_list
                if search_query in c['name'].lower()
                or search_query in c['code'].lower()
                or search_query in c.get('name_ar', '').lower()
                or search_query in c.get('email', '').lower()
            ]

        if category_filter:
            customers_list = [c for c in customers_list if c['category'] == category_filter]

        # Calculate enhanced statistics
        stats = {
            'total_customers': len(sample_data['customers']),
            'retail_customers': len([c for c in sample_data['customers'] if c['category'] == 'RETAIL']),
            'wholesale_customers': len([c for c in sample_data['customers'] if c['category'] == 'WHOLESALE']),
            'distributor_customers': len([c for c in sample_data['customers'] if c['category'] == 'DISTRIBUTOR']),
            'vip_customers': len([c for c in sample_data['customers'] if c['category'] == 'VIP']),
            'total_balance': sum(c['current_balance'] for c in sample_data['customers']),
            'total_credit_limit': sum(c['credit_limit'] for c in sample_data['customers']),
            'credit_utilization': sum(c['current_balance'] for c in sample_data['customers']) / sum(c['credit_limit'] for c in sample_data['customers']) * 100 if sum(c['credit_limit'] for c in sample_data['customers']) > 0 else 0
        }

        app.logger.info(f"Customers page accessed, showing {len(customers_list)} customers")
        return render_template('customers.html', customers=customers_list, stats=stats, search=search_query, category_filter=category_filter)
    except Exception as e:
        app.logger.error(f"Error loading customers: {e}")
        flash('حدث خطأ في تحميل صفحة العملاء', 'error')
        return render_template('error.html', error="Customers loading error"), 500

@app.route('/sales')
@monitor_performance
def sales():
    """Enhanced sales management page"""
    try:
        invoices = sample_data['invoices']

        # Add search and filter functionality
        search_query = request.args.get('search', '').lower()
        status_filter = request.args.get('status', '')

        if search_query:
            invoices = [
                inv for inv in invoices
                if search_query in inv['number'].lower()
                or search_query in inv['customer_name'].lower()
                or search_query in inv.get('customer_name_ar', '').lower()
            ]

        if status_filter:
            invoices = [inv for inv in invoices if inv['status'] == status_filter]

        # Calculate enhanced sales statistics
        today = datetime.now().strftime('%Y-%m-%d')
        current_month = datetime.now().strftime('%Y-%m')

        stats = {
            'today_sales': sum(
                inv['paid_amount'] for inv in sample_data['invoices']
                if inv['date'] == today
            ),
            'month_sales': sum(
                inv['paid_amount'] for inv in sample_data['invoices']
                if inv['date'].startswith(current_month)
            ),
            'total_sales': sum(inv['paid_amount'] for inv in sample_data['invoices']),
            'pending_invoices': len([
                inv for inv in sample_data['invoices']
                if inv['status'] in ['PENDING', 'PARTIAL']
            ]),
            'pending_amount': sum(
                inv['total_amount'] - inv['paid_amount']
                for inv in sample_data['invoices']
                if inv['status'] in ['PENDING', 'PARTIAL']
            ),
            'overdue_invoices': len([
                inv for inv in sample_data['invoices']
                if inv['status'] in ['PENDING', 'PARTIAL']
                and inv['due_date'] < today
            ]),
            'paid_invoices': len([
                inv for inv in sample_data['invoices']
                if inv['status'] == 'PAID'
            ]),
            'average_invoice': sum(inv['total_amount'] for inv in sample_data['invoices']) / len(sample_data['invoices']) if sample_data['invoices'] else 0
        }

        app.logger.info(f"Sales page accessed, showing {len(invoices)} invoices")
        return render_template('sales.html', invoices=invoices, stats=stats, search=search_query, status_filter=status_filter)
    except Exception as e:
        app.logger.error(f"Error loading sales: {e}")
        flash('حدث خطأ في تحميل صفحة المبيعات', 'error')
        return render_template('error.html', error="Sales loading error"), 500

@app.route('/suppliers')
@monitor_performance
def suppliers():
    """Enhanced supplier management page"""
    try:
        suppliers_list = [s for s in sample_data['suppliers'] if s['is_active']]

        # Add search functionality
        search_query = request.args.get('search', '').lower()
        if search_query:
            suppliers_list = [
                s for s in suppliers_list
                if search_query in s['name'].lower()
                or search_query in s['code'].lower()
                or search_query in s.get('name_ar', '').lower()
                or search_query in s.get('email', '').lower()
            ]

        # Calculate supplier statistics
        stats = {
            'total_suppliers': len(sample_data['suppliers']),
            'active_suppliers': len([s for s in sample_data['suppliers'] if s['is_active']]),
            'average_payment_terms': sum(s['payment_terms'] for s in sample_data['suppliers']) / len(sample_data['suppliers']) if sample_data['suppliers'] else 0
        }

        app.logger.info(f"Suppliers page accessed, showing {len(suppliers_list)} suppliers")
        return render_template('suppliers.html', suppliers=suppliers_list, stats=stats, search=search_query)
    except Exception as e:
        app.logger.error(f"Error loading suppliers: {e}")
        flash('حدث خطأ في تحميل صفحة الموردين', 'error')
        return render_template('error.html', error="Suppliers loading error"), 500

@app.route('/reports')
@monitor_performance
def reports():
    """Enhanced reports and analytics page"""
    try:
        stats = get_dashboard_stats()

        # Add additional analytics
        analytics = {
            'top_selling_products': get_top_selling_products(),
            'customer_analysis': get_customer_analysis(),
            'inventory_turnover': calculate_inventory_turnover(),
            'monthly_trends': get_monthly_trends()
        }

        app.logger.info("Reports page accessed successfully")
        return render_template('reports.html', stats=stats, analytics=analytics)
    except Exception as e:
        app.logger.error(f"Error loading reports: {e}")
        flash('حدث خطأ في تحميل صفحة التقارير', 'error')
        return render_template('error.html', error="Reports loading error"), 500

@app.route('/test')
@monitor_performance
def test_page():
    """System testing page"""
    try:
        app.logger.info("Test page accessed")
        return render_template('test.html')
    except Exception as e:
        app.logger.error(f"Error loading test page: {e}")
        return render_template('error.html', error="Test page loading error"), 500

# Helper functions for analytics
def get_top_selling_products(limit=5):
    """Get top selling products based on invoice data"""
    try:
        # This is a simplified version - in real app would query actual sales data
        product_sales = {}
        for product in sample_data['products']:
            # Simulate sales data based on stock movement
            sold_quantity = max(0, product['max_stock'] - product['current_stock'])
            product_sales[product['id']] = {
                'product': product,
                'quantity_sold': sold_quantity,
                'revenue': sold_quantity * product['base_price']
            }

        return sorted(product_sales.values(), key=lambda x: x['revenue'], reverse=True)[:limit]
    except Exception as e:
        app.logger.error(f"Error calculating top selling products: {e}")
        return []

def get_customer_analysis():
    """Analyze customer data"""
    try:
        total_customers = len(sample_data['customers'])
        if total_customers == 0:
            return {}

        return {
            'by_category': {
                'RETAIL': len([c for c in sample_data['customers'] if c['category'] == 'RETAIL']),
                'WHOLESALE': len([c for c in sample_data['customers'] if c['category'] == 'WHOLESALE']),
                'DISTRIBUTOR': len([c for c in sample_data['customers'] if c['category'] == 'DISTRIBUTOR']),
                'VIP': len([c for c in sample_data['customers'] if c['category'] == 'VIP'])
            },
            'average_balance': sum(c['current_balance'] for c in sample_data['customers']) / total_customers,
            'average_credit_limit': sum(c['credit_limit'] for c in sample_data['customers']) / total_customers
        }
    except Exception as e:
        app.logger.error(f"Error in customer analysis: {e}")
        return {}

def calculate_inventory_turnover():
    """Calculate inventory turnover ratio"""
    try:
        total_cost = sum(p['current_stock'] * p['cost_price'] for p in sample_data['products'])
        # Simplified calculation - in real app would use COGS
        estimated_cogs = sum(inv['total_amount'] * 0.7 for inv in sample_data['invoices'] if inv['status'] == 'PAID')

        return estimated_cogs / total_cost if total_cost > 0 else 0
    except Exception as e:
        app.logger.error(f"Error calculating inventory turnover: {e}")
        return 0

def get_monthly_trends():
    """Get monthly sales trends"""
    try:
        monthly_data = {}
        for invoice in sample_data['invoices']:
            month = invoice['date'][:7]  # YYYY-MM format
            if month not in monthly_data:
                monthly_data[month] = {'sales': 0, 'invoices': 0}
            monthly_data[month]['sales'] += invoice['paid_amount']
            monthly_data[month]['invoices'] += 1

        return monthly_data
    except Exception as e:
        app.logger.error(f"Error calculating monthly trends: {e}")
        return {}

# Enhanced API endpoints
@app.route('/api/products')
@monitor_performance
def api_products():
    """Enhanced API endpoint for products with filtering"""
    try:
        products = sample_data['products']

        # Apply filters
        category = request.args.get('category')
        active_only = request.args.get('active', 'true').lower() == 'true'
        search = request.args.get('search', '').lower()

        if active_only:
            products = [p for p in products if p['is_active']]

        if category:
            products = [p for p in products if p['category'] == category]

        if search:
            products = [
                p for p in products
                if search in p['name'].lower()
                or search in p['code'].lower()
                or search in p.get('name_ar', '').lower()
            ]

        return jsonify({
            'success': True,
            'data': products,
            'count': len(products),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        app.logger.error(f"Error in products API: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch products',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/low-stock')
@monitor_performance
def api_low_stock():
    """Enhanced API endpoint for low stock products"""
    try:
        low_stock = [
            p for p in sample_data['products']
            if p['is_active'] and p['current_stock'] <= p['min_stock']
        ]

        # Add additional information
        for product in low_stock:
            product['stock_status'] = 'OUT_OF_STOCK' if product['current_stock'] == 0 else 'LOW_STOCK'
            product['reorder_quantity'] = product['max_stock'] - product['current_stock']
            product['days_of_stock'] = max(0, product['current_stock'] / 1) if product['current_stock'] > 0 else 0  # Simplified calculation

        return jsonify({
            'success': True,
            'data': low_stock,
            'count': len(low_stock),
            'critical_items': len([p for p in low_stock if p['current_stock'] == 0]),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        app.logger.error(f"Error in low-stock API: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch low stock items',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/inventory-movements')
@monitor_performance
def api_inventory_movements():
    """Enhanced API endpoint for inventory movements"""
    try:
        movements = sample_data['inventory_movements']

        # Apply filters
        product_id = request.args.get('product_id', type=int)
        movement_type = request.args.get('type')
        date_from = request.args.get('date_from')
        date_to = request.args.get('date_to')
        limit = request.args.get('limit', 50, type=int)

        if product_id:
            movements = [m for m in movements if m['product_id'] == product_id]

        if movement_type:
            movements = [m for m in movements if m['type'] == movement_type]

        if date_from:
            movements = [m for m in movements if m['date'] >= date_from]

        if date_to:
            movements = [m for m in movements if m['date'] <= date_to]

        # Sort by date (newest first) and limit
        movements = sorted(movements, key=lambda x: x['date'], reverse=True)[:limit]

        return jsonify({
            'success': True,
            'data': movements,
            'count': len(movements),
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        app.logger.error(f"Error in inventory movements API: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch inventory movements',
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/adjust-stock', methods=['POST'])
@monitor_performance
def api_adjust_stock():
    """Enhanced API endpoint for stock adjustment with validation"""
    try:
        # Validate request data
        if not request.is_json:
            return jsonify({
                'success': False,
                'error': 'Content-Type must be application/json'
            }), 400

        data = request.get_json()
        product_id = data.get('product_id')
        new_quantity = data.get('new_quantity')
        reason = data.get('reason')
        notes = data.get('notes', '')
        user = data.get('user', 'System User')

        # Validate required fields
        if not all([product_id, new_quantity is not None, reason]):
            return jsonify({
                'success': False,
                'error': 'Missing required fields: product_id, new_quantity, reason'
            }), 400

        # Validate quantity
        if not isinstance(new_quantity, (int, float)) or new_quantity < 0:
            return jsonify({
                'success': False,
                'error': 'New quantity must be a non-negative number'
            }), 400

        # Find and update product
        product_found = False
        for product in sample_data['products']:
            if product['id'] == product_id and product['is_active']:
                old_quantity = product['current_stock']
                quantity_change = new_quantity - old_quantity

                # Update product stock
                product['current_stock'] = new_quantity
                product['updated_at'] = datetime.now().strftime('%Y-%m-%d')

                # Create detailed movement record
                movement = {
                    'id': len(sample_data['inventory_movements']) + 1,
                    'product_id': product_id,
                    'product_code': product['code'],
                    'product_name': product['name'],
                    'product_name_ar': product.get('name_ar', ''),
                    'type': 'ADJUSTMENT',
                    'type_ar': 'تعديل',
                    'quantity': quantity_change,
                    'unit_cost': product['cost_price'],
                    'total_cost': quantity_change * product['cost_price'],
                    'reason': reason,
                    'reason_ar': get_reason_arabic(reason),
                    'reference_type': 'ADJUSTMENT',
                    'reference_id': f"ADJ-{datetime.now().strftime('%Y%m%d')}-{len(sample_data['inventory_movements']) + 1:03d}",
                    'date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'user': user,
                    'user_ar': user,
                    'notes': notes
                }

                sample_data['inventory_movements'].append(movement)
                product_found = True

                app.logger.info(f"Stock adjusted for product {product['code']}: {old_quantity} -> {new_quantity} (change: {quantity_change})")

                return jsonify({
                    'success': True,
                    'message': 'Stock adjusted successfully',
                    'message_ar': 'تم تعديل المخزون بنجاح',
                    'data': {
                        'product_id': product_id,
                        'old_quantity': old_quantity,
                        'new_quantity': new_quantity,
                        'quantity_change': quantity_change,
                        'movement_id': movement['id']
                    },
                    'timestamp': datetime.now().isoformat()
                })

        if not product_found:
            return jsonify({
                'success': False,
                'error': 'Product not found or inactive',
                'error_ar': 'المنتج غير موجود أو غير نشط'
            }), 404

    except Exception as e:
        app.logger.error(f"Error adjusting stock: {e}")
        return jsonify({
            'success': False,
            'error': 'Internal server error during stock adjustment',
            'error_ar': 'خطأ داخلي في الخادم أثناء تعديل المخزون'
        }), 500

def get_reason_arabic(reason):
    """Convert English reason to Arabic"""
    reason_map = {
        'Physical Count': 'جرد فعلي',
        'Damaged Goods': 'بضائع تالفة',
        'Expired Items': 'عناصر منتهية الصلاحية',
        'Theft/Loss': 'سرقة/فقدان',
        'Found Items': 'عناصر موجودة',
        'System Correction': 'تصحيح النظام',
        'Other': 'أخرى'
    }
    return reason_map.get(reason, reason)

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    """Handle 404 errors"""
    app.logger.warning(f"404 error: {request.url}")
    return render_template('error.html',
                         error="الصفحة غير موجودة",
                         error_en="Page not found",
                         error_code=404), 404

@app.errorhandler(500)
def internal_error(error):
    """Handle 500 errors"""
    app.logger.error(f"500 error: {error}")
    return render_template('error.html',
                         error="خطأ داخلي في الخادم",
                         error_en="Internal server error",
                         error_code=500), 500

@app.errorhandler(403)
def forbidden_error(error):
    """Handle 403 errors"""
    app.logger.warning(f"403 error: {request.url}")
    return render_template('error.html',
                         error="غير مسموح بالوصول",
                         error_en="Access forbidden",
                         error_code=403), 403

# Additional API endpoints
@app.route('/api/dashboard-stats')
@monitor_performance
def api_dashboard_stats():
    """API endpoint for dashboard statistics"""
    try:
        stats = get_dashboard_stats()
        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        app.logger.error(f"Error fetching dashboard stats: {e}")
        return jsonify({
            'success': False,
            'error': 'Failed to fetch dashboard statistics'
        }), 500

@app.route('/api/search', methods=['GET'])
@monitor_performance
def api_search():
    """Global search API endpoint"""
    try:
        query = request.args.get('q', '').lower()
        if not query or len(query) < 2:
            return jsonify({
                'success': False,
                'error': 'Search query must be at least 2 characters'
            }), 400

        results = {
            'products': [],
            'customers': [],
            'suppliers': [],
            'invoices': []
        }

        # Search products
        for product in sample_data['products']:
            if (query in product['name'].lower() or
                query in product['code'].lower() or
                query in product.get('name_ar', '').lower()):
                results['products'].append(product)

        # Search customers
        for customer in sample_data['customers']:
            if (query in customer['name'].lower() or
                query in customer['code'].lower() or
                query in customer.get('name_ar', '').lower()):
                results['customers'].append(customer)

        # Search suppliers
        for supplier in sample_data['suppliers']:
            if (query in supplier['name'].lower() or
                query in supplier['code'].lower() or
                query in supplier.get('name_ar', '').lower()):
                results['suppliers'].append(supplier)

        # Search invoices
        for invoice in sample_data['invoices']:
            if (query in invoice['number'].lower() or
                query in invoice['customer_name'].lower() or
                query in invoice.get('customer_name_ar', '').lower()):
                results['invoices'].append(invoice)

        total_results = sum(len(results[key]) for key in results)

        return jsonify({
            'success': True,
            'query': query,
            'results': results,
            'total_results': total_results,
            'timestamp': datetime.now().isoformat()
        })

    except Exception as e:
        app.logger.error(f"Error in search API: {e}")
        return jsonify({
            'success': False,
            'error': 'Search failed'
        }), 500

# Performance monitoring
@app.before_request
def before_request():
    """Log request start time"""
    request.start_time = time.time()

@app.after_request
def after_request(response):
    """Log request completion and add headers"""
    if hasattr(request, 'start_time'):
        duration = time.time() - request.start_time
        app.logger.info(f"Request {request.method} {request.path} completed in {duration:.3f}s")

    # Add security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'

    return response

if __name__ == '__main__':
    print("=" * 60)
    print("   ProTech Accounting System - Enhanced Python Flask")
    print("=" * 60)
    print()
    print("🚀 Starting enhanced server...")
    print("📱 Access the application at: http://localhost:5000")
    print("🌐 Network access: http://0.0.0.0:5000")
    print("📊 API Documentation: http://localhost:5000/api/")
    print()
    print("✅ Enhanced Features Available:")
    print("   • 📊 Dashboard with real-time statistics")
    print("   • 📦 Advanced inventory management with stock tracking")
    print("   • 👥 Customer relationship management with analytics")
    print("   • 💰 Sales and invoice management with filtering")
    print("   • 🏢 Supplier management with search")
    print("   • 📈 Reports and analytics with trends")
    print("   • 🔍 Global search functionality")
    print("   • 🌐 RESTful API endpoints with validation")
    print("   • 🔒 Enhanced security and error handling")
    print("   • ⚡ Performance monitoring and logging")
    print("   • 🌍 Bilingual support (Arabic/English)")
    print()
    print("🔧 Technical Improvements:")
    print("   • Enhanced error handling and validation")
    print("   • Performance monitoring and logging")
    print("   • Improved data structure with Arabic support")
    print("   • Advanced filtering and search capabilities")
    print("   • Better API responses with timestamps")
    print("   • Security headers and request monitoring")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 60)

    # Configure Flask for production-like settings
    app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 1 year cache for static files

    try:
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        app.logger.error(f"Server startup error: {e}")
