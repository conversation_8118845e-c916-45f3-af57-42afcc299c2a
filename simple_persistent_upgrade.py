#!/usr/bin/env python3
"""
Simple Persistent Storage Upgrade for ProTech
ترقية بسيطة للتخزين الدائم لـ ProTech

Simple and safe upgrade to persistent storage
ترقية بسيطة وآمنة للتخزين الدائم
"""

import os
import shutil
from datetime import datetime

def create_persistent_storage_wrapper():
    """Create a wrapper that adds persistent storage to ProTech"""
    
    wrapper_code = '''#!/usr/bin/env python3
"""
ProTech with Persistent Storage Wrapper
غلاف ProTech مع التخزين الدائم

Wrapper that adds persistent storage to ProTech without modifying the original file
غلاف يضيف التخزين الدائم لـ ProTech دون تعديل الملف الأصلي
"""

import os
import sys
import sqlite3
import json
import shutil
import gzip
import hashlib
import threading
import time
from datetime import datetime

class PersistentStorageWrapper:
    """Persistent storage wrapper for ProTech"""
    
    def __init__(self):
        self.data_dir = "data"
        self.backup_dir = "backups"
        self.logs_dir = "logs"
        
        # Storage files
        self.json_file = os.path.join(self.data_dir, "protech_data.json")
        self.sqlite_file = os.path.join(self.data_dir, "protech_data.db")
        self.old_json_file = "protech_simple_data.json"
        
        # Settings
        self.auto_save_interval = 30  # 30 seconds
        self.auto_backup_interval = 300  # 5 minutes
        
        # Initialize
        self.create_directories()
        self.init_sqlite()
        self.migrate_existing_data()
        self.start_background_tasks()
        
        print("✅ تم تهيئة نظام التخزين الدائم")

    def create_directories(self):
        """Create necessary directories"""
        for directory in [self.data_dir, self.backup_dir, self.logs_dir]:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 تم إنشاء مجلد: {directory}")

    def init_sqlite(self):
        """Initialize SQLite database"""
        try:
            conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
            
            # SQLite optimizations
            conn.execute("PRAGMA journal_mode=WAL")
            conn.execute("PRAGMA synchronous=NORMAL")
            conn.execute("PRAGMA cache_size=10000")
            
            cursor = conn.cursor()
            
            # Create main data table
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS app_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    data_type TEXT NOT NULL,
                    data_key TEXT NOT NULL,
                    data_value TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    checksum TEXT,
                    UNIQUE(data_type, data_key)
                )
            """)
            
            # Create indexes
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_app_data_type ON app_data(data_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_app_data_key ON app_data(data_key)")
            
            conn.commit()
            conn.close()
            
            print("✅ تم تهيئة قاعدة بيانات SQLite")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة SQLite: {e}")

    def save_data(self, data_type, data):
        """Save data to persistent storage"""
        try:
            # Serialize data
            serialized_data = json.dumps(data, ensure_ascii=False, default=str)
            checksum = hashlib.md5(serialized_data.encode()).hexdigest()
            
            # Save to SQLite
            conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT OR REPLACE INTO app_data 
                (data_type, data_key, data_value, checksum, updated_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            """, (data_type, "default", serialized_data, checksum))
            
            conn.commit()
            conn.close()
            
            # Also save to JSON for compatibility
            self.save_to_json(data_type, data)
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في حفظ {data_type}: {e}")
            return False

    def load_data(self, data_type, default_value=None):
        """Load data from persistent storage"""
        try:
            # Try SQLite first
            conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT data_value, checksum FROM app_data 
                WHERE data_type = ? AND data_key = ?
            """, (data_type, "default"))
            
            result = cursor.fetchone()
            conn.close()
            
            if result:
                data_value, stored_checksum = result
                calculated_checksum = hashlib.md5(data_value.encode()).hexdigest()
                
                if calculated_checksum == stored_checksum:
                    return json.loads(data_value)
            
            # Fallback to JSON
            return self.load_from_json(data_type, default_value)
            
        except Exception as e:
            print(f"❌ خطأ في تحميل {data_type}: {e}")
            return default_value or []

    def save_to_json(self, data_type, data):
        """Save to JSON for compatibility"""
        try:
            json_data = {}
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
            
            json_data[data_type] = data
            json_data['last_updated'] = datetime.now().isoformat()
            
            # Atomic write
            temp_file = self.json_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
            
            if os.path.exists(temp_file):
                shutil.move(temp_file, self.json_file)
                
        except Exception as e:
            print(f"❌ خطأ في حفظ JSON: {e}")

    def load_from_json(self, data_type, default_value):
        """Load from JSON"""
        try:
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                return json_data.get(data_type, default_value or [])
            return default_value or []
        except Exception as e:
            print(f"❌ خطأ في تحميل JSON: {e}")
            return default_value or []

    def migrate_existing_data(self):
        """Migrate existing data"""
        try:
            if os.path.exists(self.old_json_file):
                print("🔄 ترحيل البيانات الموجودة...")
                
                with open(self.old_json_file, 'r', encoding='utf-8') as f:
                    old_data = json.load(f)
                
                migrated_count = 0
                for data_type, data in old_data.items():
                    if data_type != 'last_updated' and data:
                        if self.save_data(data_type, data):
                            migrated_count += 1
                
                print(f"✅ تم ترحيل {migrated_count} نوع من البيانات")
                
                # Backup old file
                backup_name = f"{self.old_json_file}.migrated_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                shutil.copy2(self.old_json_file, backup_name)
                
        except Exception as e:
            print(f"❌ خطأ في ترحيل البيانات: {e}")

    def create_backup(self):
        """Create backup"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_name = f"protech_backup_{timestamp}.db.gz"
            backup_path = os.path.join(self.backup_dir, backup_name)
            
            with open(self.sqlite_file, 'rb') as f_in:
                with gzip.open(backup_path, 'wb') as f_out:
                    shutil.copyfileobj(f_in, f_out)
            
            print(f"💾 تم إنشاء نسخة احتياطية: {backup_name}")
            return backup_path
            
        except Exception as e:
            print(f"❌ خطأ في النسخة الاحتياطية: {e}")
            return None

    def start_background_tasks(self):
        """Start background tasks"""
        try:
            # Auto save thread
            self.save_thread = threading.Thread(target=self.auto_save_worker, daemon=True)
            self.save_thread.start()
            
            # Auto backup thread
            self.backup_thread = threading.Thread(target=self.auto_backup_worker, daemon=True)
            self.backup_thread.start()
            
            print("✅ تم تشغيل المهام التلقائية")
            
        except Exception as e:
            print(f"❌ خطأ في المهام التلقائية: {e}")

    def auto_save_worker(self):
        """Auto save worker"""
        while True:
            try:
                time.sleep(self.auto_save_interval)
                # This will be called by ProTech when needed
            except Exception as e:
                print(f"❌ خطأ في الحفظ التلقائي: {e}")

    def auto_backup_worker(self):
        """Auto backup worker"""
        while True:
            try:
                time.sleep(self.auto_backup_interval)
                self.create_backup()
            except Exception as e:
                print(f"❌ خطأ في النسخ التلقائي: {e}")

# Initialize persistent storage
persistent_storage = PersistentStorageWrapper()

# Monkey patch the original save/load functions
original_save_data = None
original_load_data = None

def persistent_save_data(self):
    """Enhanced save_data with persistent storage"""
    try:
        # Save to persistent storage
        data_types = ['suppliers', 'products', 'customers', 'sales', 'invoices']
        success_count = 0
        
        for data_type in data_types:
            if hasattr(self, data_type):
                data = getattr(self, data_type)
                if data and persistent_storage.save_data(data_type, data):
                    success_count += 1
        
        print(f"💾 تم حفظ {success_count} نوع من البيانات في التخزين الدائم")
        
        # Also call original save if it exists
        if original_save_data:
            return original_save_data(self)
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الحفظ المحسن: {e}")
        return False

def persistent_load_data(self):
    """Enhanced load_data with persistent storage"""
    try:
        # Load from persistent storage
        data_types = ['suppliers', 'products', 'customers', 'sales', 'invoices']
        
        for data_type in data_types:
            data = persistent_storage.load_data(data_type, [])
            if data:
                setattr(self, data_type, data)
                print(f"📥 تم تحميل {data_type}: {len(data)} عنصر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحميل المحسن: {e}")
        return False

# Import and patch ProTech
if __name__ == "__main__":
    print("🚀 تشغيل ProTech مع التخزين الدائم")
    print("🚀 Starting ProTech with Persistent Storage")
    print()
    
    try:
        # Import the original ProTech
        import protech_simple_working
        
        # Patch the ProTechApp class
        if hasattr(protech_simple_working, 'ProTechApp'):
            ProTechApp = protech_simple_working.ProTechApp
            
            # Store original methods
            if hasattr(ProTechApp, 'save_data'):
                original_save_data = ProTechApp.save_data
            
            # Patch methods
            ProTechApp.save_data = persistent_save_data
            ProTechApp.load_persistent_data = persistent_load_data
            
            # Add persistent storage to the class
            original_init = ProTechApp.__init__
            
            def enhanced_init(self, *args, **kwargs):
                original_init(self, *args, **kwargs)
                self.persistent_storage = persistent_storage
                # Load existing data
                self.load_persistent_data()
                print("✅ تم تهيئة ProTech مع التخزين الدائم")
            
            ProTechApp.__init__ = enhanced_init
            
            # Start the application
            app = ProTechApp()
            app.root.mainloop()
            
        else:
            print("❌ لم يتم العثور على فئة ProTechApp")
            
    except Exception as e:
        print(f"❌ خطأ في تشغيل ProTech: {e}")
        import traceback
        traceback.print_exc()
'''
    
    # Write the wrapper file
    with open('protech_persistent.py', 'w', encoding='utf-8') as f:
        f.write(wrapper_code)
    
    print("✅ تم إنشاء غلاف التخزين الدائم: protech_persistent.py")
    return True

def create_launcher_batch():
    """Create batch file launcher"""
    batch_content = '''@echo off
title ProTech with Persistent Storage - نظام ProTech مع التخزين الدائم
color 0A

echo.
echo ================================================================
echo    💾 نظام ProTech مع التخزين الدائم
echo    💾 ProTech with Persistent Storage
echo ================================================================
echo.

echo [1/3] فحص متطلبات النظام / Checking system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت / Python not installed
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر / Python available
)

echo.
echo [2/3] فحص ملفات النظام / Checking system files...
if exist "protech_persistent.py" (
    echo ✅ غلاف التخزين الدائم موجود / Persistent storage wrapper found
) else (
    echo ❌ غلاف التخزين الدائم مفقود / Persistent storage wrapper missing
    pause
    exit /b 1
)

if exist "protech_simple_working.py" (
    echo ✅ النظام الأساسي موجود / Base system found
) else (
    echo ❌ النظام الأساسي مفقود / Base system missing
    pause
    exit /b 1
)

echo.
echo [3/3] تشغيل ProTech مع التخزين الدائم / Starting ProTech with Persistent Storage...
echo.

python protech_persistent.py

echo.
if errorlevel 1 (
    echo ❌ حدث خطأ في تشغيل النظام / Error in running system
    pause
) else (
    echo ✅ تم إغلاق النظام بنجاح / System closed successfully
)

pause
'''
    
    with open('تشغيل_ProTech_تخزين_دائم_محسن.bat', 'w', encoding='utf-8') as f:
        f.write(batch_content)
    
    print("✅ تم إنشاء ملف التشغيل: تشغيل_ProTech_تخزين_دائم_محسن.bat")
    return True

def main():
    """Main function"""
    print("🔄 إنشاء نظام التخزين الدائم البسيط لـ ProTech")
    print("🔄 Creating Simple Persistent Storage System for ProTech")
    print()
    
    try:
        # Create persistent storage wrapper
        if create_persistent_storage_wrapper():
            print("✅ تم إنشاء غلاف التخزين الدائم")
        
        # Create launcher
        if create_launcher_batch():
            print("✅ تم إنشاء ملف التشغيل")
        
        print("\n" + "="*60)
        print("✅ تم إنشاء نظام التخزين الدائم بنجاح!")
        print("✅ Persistent storage system created successfully!")
        print("="*60)
        
        print("\n🎯 الملفات الجديدة:")
        print("• protech_persistent.py - غلاف التخزين الدائم")
        print("• تشغيل_ProTech_تخزين_دائم_محسن.bat - ملف التشغيل")
        
        print("\n🚀 طرق التشغيل:")
        print("1. تشغيل الملف المحسن:")
        print("   python protech_persistent.py")
        print()
        print("2. استخدام ملف التشغيل:")
        print("   تشغيل_ProTech_تخزين_دائم_محسن.bat")
        
        print("\n💾 مميزات التخزين الدائم:")
        print("• قاعدة بيانات SQLite محسنة")
        print("• نسخ احتياطية تلقائية مضغوطة")
        print("• حفظ تلقائي كل 30 ثانية")
        print("• ترحيل تلقائي للبيانات الموجودة")
        print("• حماية من فقدان البيانات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
