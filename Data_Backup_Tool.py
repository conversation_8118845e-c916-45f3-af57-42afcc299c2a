#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
💾 Data Backup Tool - Nassar Program
أداة النسخ الاحتياطي - برنامج ناصر

أداة لإنشاء نسخ احتياطية من بيانات البرنامج
Tool for creating backups of program data
"""

import os
import json
import shutil
from datetime import datetime
import tkinter as tk
from tkinter import messagebox, filedialog

class DataBackupTool:
    def __init__(self):
        self.base_path = self.find_base_path()
        self.data_file = os.path.join(self.base_path, "02_Data_Files", "protech_simple_data.json")
        self.backup_dir = os.path.join(self.base_path, "05_Backups")
        
    def find_base_path(self):
        """Find the base path of nassar program final"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # If we're in Tools folder, go up one level
        if os.path.basename(current_dir) == "04_Tools_Utilities":
            return os.path.dirname(current_dir)
        
        # If we're in the base folder
        return current_dir
    
    def create_backup(self):
        """Create a backup of the data file"""
        try:
            if not os.path.exists(self.data_file):
                messagebox.showerror("خطأ / Error", "ملف البيانات غير موجود\nData file not found")
                return False
            
            # Create backup directory if it doesn't exist
            os.makedirs(self.backup_dir, exist_ok=True)
            
            # Generate backup filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"nassar_data_backup_{timestamp}.json"
            backup_path = os.path.join(self.backup_dir, backup_filename)
            
            # Copy the data file
            shutil.copy2(self.data_file, backup_path)
            
            # Verify backup
            if os.path.exists(backup_path):
                messagebox.showinfo(
                    "نجح النسخ الاحتياطي / Backup Success",
                    f"تم إنشاء النسخة الاحتياطية بنجاح!\nBackup created successfully!\n\n"
                    f"📁 المسار / Path:\n{backup_path}"
                )
                return True
            else:
                messagebox.showerror("خطأ / Error", "فشل في إنشاء النسخة الاحتياطية\nFailed to create backup")
                return False
                
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"خطأ في النسخ الاحتياطي:\nBackup error:\n{str(e)}")
            return False
    
    def restore_backup(self):
        """Restore from a backup file"""
        try:
            # Select backup file
            backup_file = filedialog.askopenfilename(
                title="اختر ملف النسخة الاحتياطية / Select Backup File",
                initialdir=self.backup_dir,
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if not backup_file:
                return False
            
            # Confirm restore
            result = messagebox.askyesno(
                "تأكيد الاستعادة / Confirm Restore",
                "هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟\n"
                "سيتم استبدال البيانات الحالية!\n\n"
                "Are you sure you want to restore this backup?\n"
                "Current data will be replaced!"
            )
            
            if not result:
                return False
            
            # Create backup of current data first
            if os.path.exists(self.data_file):
                current_backup = os.path.join(
                    self.backup_dir, 
                    f"before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                )
                shutil.copy2(self.data_file, current_backup)
            
            # Restore the backup
            shutil.copy2(backup_file, self.data_file)
            
            messagebox.showinfo(
                "نجحت الاستعادة / Restore Success",
                "تم استعادة النسخة الاحتياطية بنجاح!\nBackup restored successfully!"
            )
            return True
            
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"خطأ في الاستعادة:\nRestore error:\n{str(e)}")
            return False
    
    def show_backup_info(self):
        """Show information about existing backups"""
        try:
            if not os.path.exists(self.backup_dir):
                messagebox.showinfo(
                    "معلومات النسخ الاحتياطية / Backup Info",
                    "لا توجد نسخ احتياطية\nNo backups found"
                )
                return
            
            backup_files = [f for f in os.listdir(self.backup_dir) if f.endswith('.json')]
            
            if not backup_files:
                messagebox.showinfo(
                    "معلومات النسخ الاحتياطية / Backup Info",
                    "لا توجد نسخ احتياطية\nNo backups found"
                )
                return
            
            backup_info = "📋 النسخ الاحتياطية المتوفرة / Available Backups:\n\n"
            
            for backup_file in sorted(backup_files, reverse=True):
                backup_path = os.path.join(self.backup_dir, backup_file)
                file_size = os.path.getsize(backup_path)
                mod_time = datetime.fromtimestamp(os.path.getmtime(backup_path))
                
                backup_info += f"📁 {backup_file}\n"
                backup_info += f"   📅 {mod_time.strftime('%Y-%m-%d %H:%M:%S')}\n"
                backup_info += f"   📊 {file_size:,} bytes\n\n"
            
            messagebox.showinfo("معلومات النسخ الاحتياطية / Backup Info", backup_info)
            
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"خطأ في عرض المعلومات:\nError showing info:\n{str(e)}")
    
    def run(self):
        """Run the backup tool GUI"""
        root = tk.Tk()
        root.title("أداة النسخ الاحتياطي - برنامج ناصر / Data Backup Tool - Nassar Program")
        root.geometry("500x400")
        root.configure(bg='#f0f0f0')
        
        # Title
        title_label = tk.Label(
            root, 
            text="💾 أداة النسخ الاحتياطي\nData Backup Tool",
            font=('Arial', 16, 'bold'),
            bg='#f0f0f0',
            fg='#2563eb'
        )
        title_label.pack(pady=20)
        
        # Buttons frame
        buttons_frame = tk.Frame(root, bg='#f0f0f0')
        buttons_frame.pack(pady=20)
        
        # Create backup button
        backup_btn = tk.Button(
            buttons_frame,
            text="📥 إنشاء نسخة احتياطية\nCreate Backup",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            command=self.create_backup,
            width=20,
            height=3
        )
        backup_btn.pack(pady=10)
        
        # Restore backup button
        restore_btn = tk.Button(
            buttons_frame,
            text="📤 استعادة نسخة احتياطية\nRestore Backup",
            font=('Arial', 12, 'bold'),
            bg='#f59e0b',
            fg='white',
            command=self.restore_backup,
            width=20,
            height=3
        )
        restore_btn.pack(pady=10)
        
        # Show info button
        info_btn = tk.Button(
            buttons_frame,
            text="📋 معلومات النسخ الاحتياطية\nBackup Information",
            font=('Arial', 12, 'bold'),
            bg='#3b82f6',
            fg='white',
            command=self.show_backup_info,
            width=20,
            height=3
        )
        info_btn.pack(pady=10)
        
        # Exit button
        exit_btn = tk.Button(
            buttons_frame,
            text="❌ إغلاق\nClose",
            font=('Arial', 12, 'bold'),
            bg='#ef4444',
            fg='white',
            command=root.destroy,
            width=20,
            height=2
        )
        exit_btn.pack(pady=10)
        
        # Info text
        info_text = tk.Label(
            root,
            text="💡 نصيحة: قم بإنشاء نسخة احتياطية قبل إجراء أي تغييرات مهمة\n"
                 "💡 Tip: Create a backup before making any important changes",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#6b7280',
            wraplength=450
        )
        info_text.pack(side='bottom', pady=20)
        
        root.mainloop()

def main():
    """Main function"""
    tool = DataBackupTool()
    tool.run()

if __name__ == "__main__":
    main()
