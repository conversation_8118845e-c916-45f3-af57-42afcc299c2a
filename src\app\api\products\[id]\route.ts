import { NextRequest, NextResponse } from 'next/server';
import { productOperations } from '@/lib/database';
import { productSchema } from '@/lib/validations';
import { calculatePrice } from '@/lib/utils';

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const product = await productOperations.getById(params.id);

    if (!product) {
      return NextResponse.json(
        {
          success: false,
          error: 'Product not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: product,
    });
  } catch (error) {
    console.error('Error fetching product:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch product',
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = productSchema.partial().parse(body);

    // Recalculate price levels if base price changed
    if (validatedData.basePrice !== undefined) {
      validatedData.priceLevel1 = calculatePrice(validatedData.basePrice, 1);
      validatedData.priceLevel2 = calculatePrice(validatedData.basePrice, 2);
      validatedData.priceLevel3 = calculatePrice(validatedData.basePrice, 3);
      validatedData.priceLevel4 = calculatePrice(validatedData.basePrice, 4);
    }

    const product = await productOperations.update(params.id, validatedData);

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product updated successfully',
    });
  } catch (error) {
    console.error('Error updating product:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to update product',
      },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await productOperations.delete(params.id);

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting product:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to delete product',
      },
      { status: 500 }
    );
  }
}
