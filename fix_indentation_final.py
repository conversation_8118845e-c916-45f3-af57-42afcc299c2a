#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Indentation Issues - Final Solution
إصلاح مشاكل المسافات البادئة - الحل النهائي
"""

import os
import re
import shutil
from datetime import datetime

def create_backup():
    """Create backup"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.indent_fix_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def fix_indentation_issues():
    """Fix all indentation issues comprehensively"""
    try:
        # Read file with proper encoding
        with open('protech_simple_working.py', 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
        
        print(f"📄 معالجة {len(lines)} سطر...")
        
        new_lines = []
        fixed_count = 0
        current_class_indent = 0
        current_method_indent = 4
        
        for i, line in enumerate(lines):
            original_line = line
            
            # Skip empty lines and comments
            if not line.strip() or line.strip().startswith('#'):
                new_lines.append(line)
                continue
            
            # Detect class definitions
            if line.strip().startswith('class '):
                current_class_indent = len(line) - len(line.lstrip())
                current_method_indent = current_class_indent + 4
                new_lines.append(line)
                continue
            
            # Detect method definitions
            if line.strip().startswith('def '):
                # Check if it's inside a class
                if current_class_indent >= 0:
                    expected_indent = current_class_indent + 4
                    actual_indent = len(line) - len(line.lstrip())
                    
                    if actual_indent != expected_indent:
                        # Fix method indentation
                        fixed_line = ' ' * expected_indent + line.lstrip()
                        new_lines.append(fixed_line)
                        fixed_count += 1
                        print(f"🔧 إصلاح دالة في السطر {i+1}: {line.strip()}")
                    else:
                        new_lines.append(line)
                else:
                    new_lines.append(line)
                continue
            
            # Fix content inside methods
            if current_class_indent >= 0:
                # We're inside a class
                stripped = line.lstrip()
                
                # Skip lines that are already properly indented
                if line.startswith('    ') or line.startswith('\t'):
                    new_lines.append(line)
                    continue
                
                # Fix lines that should be indented
                if stripped and not stripped.startswith('class ') and not stripped.startswith('def '):
                    # This should be indented as method content
                    expected_indent = current_class_indent + 8  # Method content
                    fixed_line = ' ' * expected_indent + stripped
                    new_lines.append(fixed_line)
                    fixed_count += 1
                    print(f"🔧 إصلاح محتوى في السطر {i+1}")
                else:
                    new_lines.append(line)
            else:
                new_lines.append(line)
        
        # Write the fixed file
        with open('protech_simple_working.py', 'w', encoding='utf-8-sig') as f:
            f.writelines(new_lines)
        
        print(f"✅ تم إصلاح {fixed_count} سطر")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح المسافات البادئة: {e}")
        return False

def fix_specific_line_1064():
    """Fix specific issue at line 1064"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8-sig') as f:
            lines = f.readlines()
        
        # Fix the specific area around line 1064
        if len(lines) > 1063:
            for i in range(1060, min(1070, len(lines))):
                line = lines[i]
                
                # Check if this is the problematic line
                if 'def clear_content(self):' in line:
                    # Ensure proper indentation for method definition
                    lines[i] = '    def clear_content(self):\n'
                    print(f"🔧 إصلاح السطر {i+1}: def clear_content")
                    
                    # Fix the following lines too
                    for j in range(i+1, min(i+10, len(lines))):
                        if lines[j].strip() and not lines[j].startswith('    '):
                            if not lines[j].strip().startswith('def ') and not lines[j].strip().startswith('class '):
                                lines[j] = '        ' + lines[j].lstrip()
                                print(f"🔧 إصلاح محتوى السطر {j+1}")
                    break
        
        # Write back
        with open('protech_simple_working.py', 'w', encoding='utf-8-sig') as f:
            f.writelines(lines)
        
        print("✅ تم إصلاح السطر 1064 والمنطقة المحيطة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح السطر 1064: {e}")
        return False

def test_syntax():
    """Test file syntax"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8-sig') as f:
            content = f.read()
        
        compile(content, 'protech_simple_working.py', 'exec')
        print("✅ اختبار التركيب نجح")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ في التركيب: {e}")
        print(f"السطر {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_simple_test_launcher():
    """Create simple test launcher"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Simple Test Launcher
مشغل اختبار ProTech البسيط
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def test_protech():
    """Test ProTech startup"""
    try:
        print("🧪 اختبار تشغيل ProTech...")
        
        # Check if file exists
        if not os.path.exists('protech_simple_working.py'):
            messagebox.showerror("خطأ", "ملف ProTech غير موجود!")
            return False
        
        # Test syntax first
        try:
            with open('protech_simple_working.py', 'r', encoding='utf-8-sig') as f:
                content = f.read()
            compile(content, 'protech_simple_working.py', 'exec')
            print("✅ اختبار التركيب نجح")
        except Exception as e:
            messagebox.showerror("خطأ في التركيب", f"خطأ في تركيب الملف:\\n{e}")
            return False
        
        # Launch ProTech
        print("🚀 تشغيل ProTech...")
        process = subprocess.Popen([
            sys.executable, 'protech_simple_working.py'
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        print("✅ تم تشغيل ProTech بنجاح")
        return True
        
    except Exception as e:
        messagebox.showerror("خطأ في التشغيل", f"فشل في تشغيل ProTech:\\n{e}")
        return False

def main():
    """Main function"""
    # Create a simple GUI for testing
    root = tk.Tk()
    root.title("ProTech Test Launcher")
    root.geometry("400x200")
    root.configure(bg='#f0f8ff')
    
    # Title
    title_label = tk.Label(root, text="🧪 ProTech Test Launcher", 
                          font=('Arial', 16, 'bold'), bg='#f0f8ff')
    title_label.pack(pady=20)
    
    # Test button
    test_btn = tk.Button(root, text="🚀 اختبار وتشغيل ProTech", 
                        command=test_protech, bg='#4CAF50', fg='white',
                        font=('Arial', 12, 'bold'), width=20)
    test_btn.pack(pady=10)
    
    # Info label
    info_label = tk.Label(root, text="سيتم اختبار الملف أولاً ثم تشغيله", 
                         font=('Arial', 10), bg='#f0f8ff', fg='#666')
    info_label.pack(pady=5)
    
    # Exit button
    exit_btn = tk.Button(root, text="خروج", command=root.quit, 
                        bg='#f44336', fg='white', font=('Arial', 10))
    exit_btn.pack(pady=10)
    
    root.mainloop()

if __name__ == "__main__":
    main()
'''
    
    with open('test_protech.py', 'w', encoding='utf-8-sig') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل الاختبار: test_protech.py")

def main():
    """Main function"""
    print("🔧 إصلاح مشاكل المسافات البادئة - الحل النهائي")
    print("🔧 Fix Indentation Issues - Final Solution")
    print("=" * 60)
    
    try:
        # Step 1: Backup
        create_backup()
        
        # Step 2: Fix specific line 1064
        print("\n🔧 إصلاح السطر 1064...")
        if fix_specific_line_1064():
            print("✅ تم إصلاح السطر 1064")
        
        # Step 3: Fix general indentation issues
        print("\n🔧 إصلاح جميع مشاكل المسافات البادئة...")
        if fix_indentation_issues():
            print("✅ تم إصلاح المسافات البادئة")
        
        # Step 4: Test syntax
        print("\n🧪 اختبار التركيب...")
        if test_syntax():
            print("✅ التركيب صحيح")
        else:
            print("❌ لا يزال هناك خطأ في التركيب")
            return False
        
        # Step 5: Create test launcher
        print("\n🚀 إنشاء مشغل الاختبار...")
        create_simple_test_launcher()
        
        print("\n" + "=" * 60)
        print("✅ تم إصلاح جميع مشاكل المسافات البادئة!")
        print("✅ All indentation issues fixed!")
        print("=" * 60)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إصلاح السطر 1064 المشكل")
        print("• إصلاح جميع مشاكل المسافات البادئة")
        print("• تصحيح تعريفات الدوال والفئات")
        print("• إنشاء مشغل اختبار")
        
        print("\n🚀 الآن يمكنك:")
        print("1. تشغيل test_protech.py للاختبار")
        print("2. النقر المزدوج على protech_simple_working.py")
        print("3. استخدام launch_protech_final.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
