#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive Traceback Error Fixer for ProTech
مصلح أخطاء Traceback الشامل لـ ProTech

Fix all Traceback errors and prevent them from happening again
إصلاح جميع أخطاء Traceback ومنع حدوثها مرة أخرى
"""

import os
import ast
import re
import shutil
import subprocess
import sys
from datetime import datetime

class ComprehensiveTracebackFixer:
    """Comprehensive Traceback error fixer"""
    
    def __init__(self):
        self.data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        self.main_file = "protech_simple_working.py"
        self.errors_found = []
        self.fixes_applied = []
    
    def diagnose_traceback_errors(self):
        """Diagnose all Traceback errors"""
        try:
            print("🔍 تشخيص أخطاء Traceback...")
            
            code_path = os.path.join(self.data_dir, self.main_file)
            
            if not os.path.exists(code_path):
                print("❌ ملف البرنامج غير موجود!")
                return False
            
            # Test 1: Syntax check
            result = subprocess.run([sys.executable, '-m', 'py_compile', code_path], 
                                  capture_output=True, text=True)
            
            if result.returncode != 0:
                print("❌ خطأ في التركيب:")
                print(result.stderr)
                self.errors_found.append({
                    'type': 'syntax_error',
                    'message': result.stderr,
                    'severity': 'critical'
                })
            else:
                print("✅ لا توجد أخطاء تركيب")
            
            # Test 2: AST parsing
            try:
                with open(code_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                ast.parse(content)
                print("✅ تحليل AST نجح")
            except SyntaxError as e:
                print(f"❌ خطأ AST في السطر {e.lineno}: {e.msg}")
                self.errors_found.append({
                    'type': 'ast_error',
                    'line': e.lineno,
                    'message': e.msg,
                    'text': e.text,
                    'severity': 'critical'
                })
            
            # Test 3: Import check
            self.check_import_errors(content)
            
            # Test 4: Indentation check
            self.check_indentation_errors(content)
            
            # Test 5: Common Python errors
            self.check_common_errors(content)
            
            print(f"🔍 تم العثور على {len(self.errors_found)} خطأ")
            return len(self.errors_found) == 0
            
        except Exception as e:
            print(f"❌ خطأ في التشخيص: {e}")
            return False
    
    def check_import_errors(self, content):
        """Check for import errors"""
        try:
            print("📦 فحص أخطاء الاستيراد...")
            
            # Find all import statements
            import_lines = re.findall(r'^(import .*|from .* import .*)$', content, re.MULTILINE)
            
            for import_line in import_lines:
                # Check for common problematic imports
                if 'tkinter' in import_line.lower():
                    # Check if tkinter is available
                    try:
                        import tkinter
                    except ImportError:
                        self.errors_found.append({
                            'type': 'import_error',
                            'message': f'tkinter not available: {import_line}',
                            'severity': 'high'
                        })
                
                # Check for relative imports issues
                if import_line.startswith('from .'):
                    self.errors_found.append({
                        'type': 'relative_import_error',
                        'message': f'Relative import in main script: {import_line}',
                        'severity': 'medium'
                    })
            
            print(f"📦 فحص {len(import_lines)} استيراد")
            
        except Exception as e:
            print(f"❌ خطأ في فحص الاستيراد: {e}")
    
    def check_indentation_errors(self, content):
        """Check for indentation errors"""
        try:
            print("📏 فحص أخطاء المسافات البادئة...")
            
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                if line.strip():  # Skip empty lines
                    # Check for mixed tabs and spaces
                    if '\t' in line and ' ' in line[:len(line) - len(line.lstrip())]:
                        self.errors_found.append({
                            'type': 'mixed_indentation',
                            'line': i,
                            'message': 'Mixed tabs and spaces',
                            'severity': 'high'
                        })
                    
                    # Check for inconsistent indentation
                    indent = len(line) - len(line.lstrip())
                    if indent % 4 != 0 and line.lstrip():  # Should be multiple of 4
                        if not line.lstrip().startswith('#'):  # Skip comments
                            self.errors_found.append({
                                'type': 'inconsistent_indentation',
                                'line': i,
                                'message': f'Inconsistent indentation: {indent} spaces',
                                'severity': 'medium'
                            })
            
            print("📏 فحص المسافات البادئة مكتمل")
            
        except Exception as e:
            print(f"❌ خطأ في فحص المسافات البادئة: {e}")
    
    def check_common_errors(self, content):
        """Check for common Python errors"""
        try:
            print("🐛 فحص الأخطاء الشائعة...")
            
            # Check for unmatched brackets
            brackets = {'(': ')', '[': ']', '{': '}'}
            stack = []
            
            for i, char in enumerate(content):
                if char in brackets:
                    stack.append((char, i))
                elif char in brackets.values():
                    if not stack:
                        self.errors_found.append({
                            'type': 'unmatched_bracket',
                            'position': i,
                            'message': f'Unmatched closing bracket: {char}',
                            'severity': 'high'
                        })
                    else:
                        open_bracket, _ = stack.pop()
                        if brackets[open_bracket] != char:
                            self.errors_found.append({
                                'type': 'mismatched_bracket',
                                'position': i,
                                'message': f'Mismatched bracket: expected {brackets[open_bracket]}, got {char}',
                                'severity': 'high'
                            })
            
            # Check for remaining unclosed brackets
            for bracket, pos in stack:
                self.errors_found.append({
                    'type': 'unclosed_bracket',
                    'position': pos,
                    'message': f'Unclosed bracket: {bracket}',
                    'severity': 'high'
                })
            
            # Check for common syntax patterns
            problematic_patterns = [
                (r'except\s*:', 'Bare except clause'),
                (r'except\s+Exception\s*:\s*pass', 'Empty exception handler'),
                (r'if\s+.*:\s*$', 'Empty if statement'),
                (r'def\s+\w+\([^)]*\):\s*$', 'Empty function definition'),
                (r'class\s+\w+.*:\s*$', 'Empty class definition'),
            ]
            
            for pattern, message in problematic_patterns:
                matches = re.finditer(pattern, content, re.MULTILINE)
                for match in matches:
                    line_num = content[:match.start()].count('\n') + 1
                    self.errors_found.append({
                        'type': 'syntax_pattern',
                        'line': line_num,
                        'message': message,
                        'severity': 'medium'
                    })
            
            print("🐛 فحص الأخطاء الشائعة مكتمل")
            
        except Exception as e:
            print(f"❌ خطأ في فحص الأخطاء الشائعة: {e}")
    
    def fix_syntax_errors(self):
        """Fix syntax errors"""
        try:
            print("🔧 إصلاح أخطاء التركيب...")
            
            code_path = os.path.join(self.data_dir, self.main_file)
            
            # Create backup
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"{code_path}.traceback_fix_backup_{timestamp}"
            shutil.copy2(code_path, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}")
            
            # Read content
            with open(code_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixes_count = 0
            
            # Fix 1: Remove mixed indentation
            lines = content.split('\n')
            fixed_lines = []
            
            for line in lines:
                if line.strip():
                    # Convert tabs to spaces
                    fixed_line = line.expandtabs(4)
                    
                    # Fix inconsistent indentation
                    indent = len(fixed_line) - len(fixed_line.lstrip())
                    if indent % 4 != 0 and fixed_line.lstrip():
                        # Round to nearest multiple of 4
                        new_indent = (indent // 4) * 4
                        fixed_line = ' ' * new_indent + fixed_line.lstrip()
                        fixes_count += 1
                    
                    fixed_lines.append(fixed_line)
                else:
                    fixed_lines.append(line)
            
            content = '\n'.join(fixed_lines)
            
            # Fix 2: Add missing colons
            content = re.sub(r'(if\s+.*[^:])(\s*\n)', r'\1:\2', content)
            content = re.sub(r'(elif\s+.*[^:])(\s*\n)', r'\1:\2', content)
            content = re.sub(r'(else\s*[^:])(\s*\n)', r'else:\2', content)
            content = re.sub(r'(try\s*[^:])(\s*\n)', r'try:\2', content)
            content = re.sub(r'(except\s+.*[^:])(\s*\n)', r'\1:\2', content)
            content = re.sub(r'(finally\s*[^:])(\s*\n)', r'finally:\2', content)
            
            # Fix 3: Add missing pass statements
            content = re.sub(r'(:\s*\n)(\s*)(def|class|if|elif|else|try|except|finally|for|while)', 
                           r'\1\2pass\n\2\3', content)
            
            # Fix 4: Fix bare except clauses
            content = re.sub(r'except\s*:', 'except Exception:', content)
            
            if content != original_content:
                fixes_count += 1
            
            # Write fixed content
            with open(code_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.fixes_applied.append(f"إصلاح {fixes_count} خطأ تركيب")
            print(f"✅ تم إصلاح {fixes_count} خطأ تركيب")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح التركيب: {e}")
            return False
    
    def fix_import_errors(self):
        """Fix import errors"""
        try:
            print("📦 إصلاح أخطاء الاستيراد...")
            
            code_path = os.path.join(self.data_dir, self.main_file)
            
            with open(code_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            fixes_count = 0
            
            # Fix 1: Add try-except around imports
            import_pattern = r'^(import .*|from .* import .*)$'
            
            def wrap_import(match):
                import_line = match.group(1)
                return f"""try:
    {import_line}
except ImportError as e:
    print(f"تحذير: فشل في استيراد {{e}}")
    pass"""
            
            # Only wrap risky imports
            risky_imports = ['tkinter', 'PIL', 'numpy', 'pandas', 'matplotlib']
            
            for risky in risky_imports:
                pattern = f'^((?:import {risky}|from {risky} import .*))'
                if re.search(pattern, content, re.MULTILINE):
                    content = re.sub(pattern, wrap_import, content, flags=re.MULTILINE)
                    fixes_count += 1
            
            # Fix 2: Remove relative imports in main script
            content = re.sub(r'^from \. import', 'import', content, flags=re.MULTILINE)
            
            if content != original_content:
                with open(code_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append(f"إصلاح {fixes_count} خطأ استيراد")
                print(f"✅ تم إصلاح {fixes_count} خطأ استيراد")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح الاستيراد: {e}")
            return False
    
    def add_comprehensive_error_handling(self):
        """Add comprehensive error handling"""
        try:
            print("🛡️ إضافة معالجة شاملة للأخطاء...")
            
            code_path = os.path.join(self.data_dir, self.main_file)
            
            with open(code_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Add global exception handler
            error_handler_code = '''
import sys
import traceback
import logging
from datetime import datetime

# Setup error logging
logging.basicConfig(
    filename='protech_errors.log',
    level=logging.ERROR,
    format='%(asctime)s - %(levelname)s - %(message)s',
    encoding='utf-8'
)

def global_exception_handler(exc_type, exc_value, exc_traceback):
    """Global exception handler to catch all unhandled exceptions"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    error_msg = f"Unhandled exception: {exc_type.__name__}: {exc_value}"
    
    # Log the error
    logging.error(error_msg, exc_info=(exc_type, exc_value, exc_traceback))
    
    # Print error for debugging
    print(f"❌ خطأ غير معالج: {error_msg}")
    traceback.print_exception(exc_type, exc_value, exc_traceback)
    
    # Try to save data before crash
    try:
        # Emergency save logic here
        print("🚨 محاولة حفظ طوارئ...")
    except:
        pass

# Set global exception handler
sys.excepthook = global_exception_handler

'''
            
            # Insert error handler at the beginning (after imports)
            if 'global_exception_handler' not in content:
                # Find the end of imports
                lines = content.split('\n')
                insert_pos = 0
                
                for i, line in enumerate(lines):
                    if (line.strip().startswith('import ') or 
                        line.strip().startswith('from ') or
                        line.strip().startswith('#') or
                        line.strip() == ''):
                        continue
                    else:
                        insert_pos = i
                        break
                
                lines.insert(insert_pos, error_handler_code)
                content = '\n'.join(lines)
                
                with open(code_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixes_applied.append("إضافة معالج أخطاء شامل")
                print("✅ تم إضافة معالج أخطاء شامل")
            
            return True

        except Exception as e:
            print(f"❌ خطأ في إضافة معالجة الأخطاء: {e}")
            return False

    def create_error_prevention_system(self):
        """Create system to prevent future errors"""
        try:
            print("🛡️ إنشاء نظام منع الأخطاء المستقبلية...")

            prevention_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Error Prevention System
نظام منع أخطاء ProTech
"""

import os
import ast
import subprocess
import sys
from datetime import datetime

class ErrorPreventionSystem:
    """System to prevent future errors"""

    def __init__(self):
        self.main_file = "protech_simple_working.py"

    def validate_code_before_run(self):
        """Validate code before running"""
        try:
            print("🔍 فحص الكود قبل التشغيل...")

            # Syntax check
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.main_file],
                                  capture_output=True, text=True)

            if result.returncode != 0:
                print("❌ خطأ في التركيب:")
                print(result.stderr)
                return False

            # AST check
            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()

            try:
                ast.parse(content)
                print("✅ الكود سليم")
                return True
            except SyntaxError as e:
                print(f"❌ خطأ AST: {e}")
                return False

        except Exception as e:
            print(f"❌ خطأ في الفحص: {e}")
            return False

    def auto_fix_common_issues(self):
        """Auto-fix common issues"""
        try:
            print("🔧 إصلاح تلقائي للمشاكل الشائعة...")

            with open(self.main_file, 'r', encoding='utf-8') as f:
                content = f.read()

            original_content = content

            # Fix common issues
            fixes = [
                # Fix missing colons
                (r'(if\\s+.*[^:])\\s*\\n', r'\\1:\\n'),
                (r'(else\\s*[^:])\\s*\\n', r'else:\\n'),
                (r'(try\\s*[^:])\\s*\\n', r'try:\\n'),
                (r'(except\\s+.*[^:])\\s*\\n', r'\\1:\\n'),

                # Fix bare except
                (r'except\\s*:', 'except Exception:'),

                # Fix empty blocks
                (r'(:\\s*\\n)(\\s*)(def|class|if|elif|else|try|except|finally)',
                 r'\\1\\2pass\\n\\2\\3'),
            ]

            for pattern, replacement in fixes:
                content = re.sub(pattern, replacement, content)

            if content != original_content:
                # Create backup
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_file = f"{self.main_file}.auto_fix_backup_{timestamp}"

                with open(backup_file, 'w', encoding='utf-8') as f:
                    f.write(original_content)

                # Write fixed content
                with open(self.main_file, 'w', encoding='utf-8') as f:
                    f.write(content)

                print(f"✅ تم الإصلاح التلقائي - نسخة احتياطية: {backup_file}")
                return True

            print("✅ لا توجد مشاكل للإصلاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في الإصلاح التلقائي: {e}")
            return False

    def run_prevention_check(self):
        """Run complete prevention check"""
        print("🛡️ تشغيل فحص منع الأخطاء...")

        if not self.validate_code_before_run():
            print("🔧 محاولة إصلاح تلقائي...")
            if self.auto_fix_common_issues():
                # Re-validate after fix
                if self.validate_code_before_run():
                    print("✅ تم الإصلاح بنجاح")
                    return True
                else:
                    print("❌ فشل الإصلاح التلقائي")
                    return False
            else:
                return False

        return True

def main():
    """Main prevention function"""
    prevention = ErrorPreventionSystem()
    return prevention.run_prevention_check()

if __name__ == "__main__":
    main()
'''

            prevention_path = os.path.join(self.data_dir, "error_prevention_system.py")
            with open(prevention_path, 'w', encoding='utf-8') as f:
                f.write(prevention_code)

            self.fixes_applied.append("إنشاء نظام منع الأخطاء")
            print(f"✅ تم إنشاء نظام منع الأخطاء: error_prevention_system.py")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء نظام منع الأخطاء: {e}")
            return False

    def create_safe_launcher(self):
        """Create safe launcher with error handling"""
        try:
            print("🚀 إنشاء مشغل آمن...")

            launcher_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Safe Launcher with Error Prevention
مشغل ProTech الآمن مع منع الأخطاء
"""

import os
import sys
import subprocess
import time
from datetime import datetime

def safe_launch_protech():
    """Safely launch ProTech with error prevention"""
    try:
        print("🚀 تشغيل ProTech الآمن...")
        print("🚀 Safe ProTech Launch...")
        print("="*50)

        # Step 1: Check if files exist
        required_files = ["protech_simple_working.py", "protech_simple_data.json"]

        for file in required_files:
            if not os.path.exists(file):
                print(f"❌ ملف مطلوب مفقود: {file}")
                input("اضغط Enter للخروج...")
                return False

        print("✅ جميع الملفات المطلوبة موجودة")

        # Step 2: Run error prevention check
        if os.path.exists("error_prevention_system.py"):
            print("🛡️ تشغيل فحص منع الأخطاء...")

            result = subprocess.run([sys.executable, "error_prevention_system.py"],
                                  capture_output=True, text=True)

            if result.returncode != 0:
                print("⚠️ فحص منع الأخطاء فشل:")
                print(result.stderr)
                print("🔧 محاولة المتابعة...")
            else:
                print("✅ فحص منع الأخطاء نجح")

        # Step 3: Test compilation
        print("🧪 اختبار تجميع الكود...")

        test_result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'],
                                   capture_output=True, text=True)

        if test_result.returncode != 0:
            print("❌ خطأ في التجميع:")
            print(test_result.stderr)

            # Try to auto-fix
            print("🔧 محاولة إصلاح تلقائي...")
            if os.path.exists("comprehensive_traceback_fixer.py"):
                fix_result = subprocess.run([sys.executable, "comprehensive_traceback_fixer.py"],
                                          capture_output=True, text=True)

                if fix_result.returncode == 0:
                    print("✅ تم الإصلاح التلقائي")
                    # Re-test
                    retest_result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'],
                                                 capture_output=True, text=True)

                    if retest_result.returncode != 0:
                        print("❌ فشل الإصلاح التلقائي")
                        input("اضغط Enter للخروج...")
                        return False
                else:
                    print("❌ فشل الإصلاح التلقائي")
                    input("اضغط Enter للخروج...")
                    return False
            else:
                input("اضغط Enter للخروج...")
                return False

        print("✅ تجميع الكود نجح")

        # Step 4: Launch ProTech
        print("🚀 تشغيل ProTech...")

        # Set environment for better compatibility
        env = os.environ.copy()
        env['PYTHONIOENCODING'] = 'utf-8'
        env['PYTHONUTF8'] = '1'

        # Launch in new process
        process = subprocess.Popen([sys.executable, 'protech_simple_working.py'],
                                 env=env,
                                 stdout=subprocess.PIPE,
                                 stderr=subprocess.PIPE)

        # Wait a bit to check if it starts successfully
        time.sleep(3)

        if process.poll() is None:
            print("✅ تم تشغيل ProTech بنجاح!")
            print("✅ ProTech launched successfully!")
            print("🖥️ تحقق من النافذة المفتوحة")
            print("🖥️ Check the opened window")

            # Keep launcher running to monitor
            try:
                process.wait()
            except KeyboardInterrupt:
                print("\\n⏹️ تم إيقاف ProTech بواسطة المستخدم")
                process.terminate()

            return True
        else:
            # Process ended, check for errors
            stdout, stderr = process.communicate()

            print("❌ فشل في تشغيل ProTech:")
            if stderr:
                print("الأخطاء:")
                print(stderr.decode('utf-8', errors='ignore'))
            if stdout:
                print("المخرجات:")
                print(stdout.decode('utf-8', errors='ignore'))

            input("اضغط Enter للخروج...")
            return False

    except Exception as e:
        print(f"❌ خطأ في المشغل الآمن: {e}")
        input("اضغط Enter للخروج...")
        return False

def main():
    """Main launcher function"""
    try:
        success = safe_launch_protech()

        if success:
            print("\\n🎉 تم التشغيل بنجاح!")
        else:
            print("\\n❌ فشل في التشغيل")

    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
'''

            launcher_path = os.path.join(self.data_dir, "safe_launch_protech.py")
            with open(launcher_path, 'w', encoding='utf-8') as f:
                f.write(launcher_code)

            self.fixes_applied.append("إنشاء مشغل آمن")
            print(f"✅ تم إنشاء المشغل الآمن: safe_launch_protech.py")
            return True

        except Exception as e:
            print(f"❌ خطأ في إنشاء المشغل الآمن: {e}")
            return False

    def run_comprehensive_fix(self):
        """Run comprehensive Traceback fix"""
        try:
            print("🔧 بدء الإصلاح الشامل لأخطاء Traceback")
            print("🔧 Starting Comprehensive Traceback Fix")
            print("="*60)

            # Step 1: Diagnose errors
            print("\\n🔍 الخطوة 1: تشخيص الأخطاء...")
            diagnosis_success = self.diagnose_traceback_errors()

            if not diagnosis_success:
                print("⚠️ تم العثور على أخطاء، بدء الإصلاح...")

            # Step 2: Fix syntax errors
            print("\\n🔧 الخطوة 2: إصلاح أخطاء التركيب...")
            self.fix_syntax_errors()

            # Step 3: Fix import errors
            print("\\n📦 الخطوة 3: إصلاح أخطاء الاستيراد...")
            self.fix_import_errors()

            # Step 4: Add error handling
            print("\\n🛡️ الخطوة 4: إضافة معالجة الأخطاء...")
            self.add_comprehensive_error_handling()

            # Step 5: Create prevention system
            print("\\n🛡️ الخطوة 5: إنشاء نظام منع الأخطاء...")
            self.create_error_prevention_system()

            # Step 6: Create safe launcher
            print("\\n🚀 الخطوة 6: إنشاء مشغل آمن...")
            self.create_safe_launcher()

            # Step 7: Final validation
            print("\\n✅ الخطوة 7: التحقق النهائي...")
            final_check = self.final_validation()

            # Generate report
            self.generate_fix_report()

            print("\\n" + "="*60)
            print(f"📊 ملخص الإصلاح:")
            print(f"🔍 أخطاء مكتشفة: {len(self.errors_found)}")
            print(f"🔧 إصلاحات مطبقة: {len(self.fixes_applied)}")

            if final_check:
                print("\\n🎉 تم الإصلاح الشامل بنجاح!")
                print("🎉 Comprehensive fix completed successfully!")
                print("\\n📋 الخطوات التالية:")
                print("1. استخدم safe_launch_protech.py للتشغيل الآمن")
                print("2. استخدم error_prevention_system.py للفحص الدوري")
                print("3. راجع protech_errors.log للأخطاء المستقبلية")
            else:
                print("\\n⚠️ الإصلاح مكتمل مع تحذيرات")
                print("⚠️ Fix completed with warnings")

            print("="*60)

            return final_check

        except Exception as e:
            print(f"❌ خطأ في الإصلاح الشامل: {e}")
            return False

    def final_validation(self):
        """Final validation after all fixes"""
        try:
            print("✅ التحقق النهائي من الإصلاحات...")

            code_path = os.path.join(self.data_dir, self.main_file)

            # Test compilation
            result = subprocess.run([sys.executable, '-m', 'py_compile', code_path],
                                  capture_output=True, text=True)

            if result.returncode == 0:
                print("✅ تجميع الكود: نجح")
                return True
            else:
                print("❌ تجميع الكود: فشل")
                print(result.stderr)
                return False

        except Exception as e:
            print(f"❌ خطأ في التحقق النهائي: {e}")
            return False

    def generate_fix_report(self):
        """Generate comprehensive fix report"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"traceback_fix_report_{timestamp}.json"

            import json

            report = {
                'timestamp': datetime.now().isoformat(),
                'errors_found': self.errors_found,
                'fixes_applied': self.fixes_applied,
                'summary': {
                    'total_errors': len(self.errors_found),
                    'total_fixes': len(self.fixes_applied),
                    'critical_errors': len([e for e in self.errors_found if e.get('severity') == 'critical']),
                    'high_errors': len([e for e in self.errors_found if e.get('severity') == 'high']),
                    'medium_errors': len([e for e in self.errors_found if e.get('severity') == 'medium'])
                },
                'prevention_tools_created': [
                    'error_prevention_system.py',
                    'safe_launch_protech.py',
                    'Global exception handler',
                    'Enhanced error logging'
                ]
            }

            report_path = os.path.join(self.data_dir, report_file)
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)

            print(f"📄 تم حفظ تقرير الإصلاح: {report_file}")

        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير: {e}")

def main():
    """Main function"""
    fixer = ComprehensiveTracebackFixer()
    success = fixer.run_comprehensive_fix()

    if success:
        print("\\n🎉 إصلاح أخطاء Traceback مكتمل بنجاح!")
        print("🎉 Traceback error fix completed successfully!")
    else:
        print("\\n⚠️ إصلاح أخطاء Traceback مكتمل مع مشاكل")
        print("⚠️ Traceback error fix completed with issues")

    return success

if __name__ == "__main__":
    main()
