{% extends "base.html" %}

{% block title %}اختبار النظام - ProTech Accounting{% endblock %}
{% block page_title %}اختبار النظام / System Test{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Test Header -->
    <div class="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg shadow-lg p-6 text-white">
        <h2 class="text-2xl font-bold mb-2">🧪 اختبار شامل للنظام / Comprehensive System Test</h2>
        <p class="text-green-100">فحص جميع الوظائف والأزرار والـ APIs</p>
    </div>

    <!-- Navigation Test -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">🧭 اختبار التنقل / Navigation Test</h3>
        <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
            <a href="{{ url_for('dashboard') }}" class="test-link bg-blue-50 hover:bg-blue-100 p-4 rounded-lg text-center transition-colors">
                <div class="text-blue-600 font-medium">لوحة التحكم</div>
                <div class="text-sm text-gray-500">Dashboard</div>
            </a>
            <a href="{{ url_for('inventory') }}" class="test-link bg-green-50 hover:bg-green-100 p-4 rounded-lg text-center transition-colors">
                <div class="text-green-600 font-medium">المخزون</div>
                <div class="text-sm text-gray-500">Inventory</div>
            </a>
            <a href="{{ url_for('customers') }}" class="test-link bg-purple-50 hover:bg-purple-100 p-4 rounded-lg text-center transition-colors">
                <div class="text-purple-600 font-medium">العملاء</div>
                <div class="text-sm text-gray-500">Customers</div>
            </a>
            <a href="{{ url_for('sales') }}" class="test-link bg-orange-50 hover:bg-orange-100 p-4 rounded-lg text-center transition-colors">
                <div class="text-orange-600 font-medium">المبيعات</div>
                <div class="text-sm text-gray-500">Sales</div>
            </a>
            <a href="{{ url_for('suppliers') }}" class="test-link bg-indigo-50 hover:bg-indigo-100 p-4 rounded-lg text-center transition-colors">
                <div class="text-indigo-600 font-medium">الموردين</div>
                <div class="text-sm text-gray-500">Suppliers</div>
            </a>
            <a href="{{ url_for('reports') }}" class="test-link bg-pink-50 hover:bg-pink-100 p-4 rounded-lg text-center transition-colors">
                <div class="text-pink-600 font-medium">التقارير</div>
                <div class="text-sm text-gray-500">Reports</div>
            </a>
        </div>
    </div>

    <!-- API Test -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">🔌 اختبار APIs / API Test</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <button onclick="testAPI('/api/products', 'products-result')" class="api-test-btn bg-blue-600 hover:bg-blue-700 text-white p-3 rounded-lg">
                اختبار API المنتجات / Test Products API
            </button>
            <button onclick="testAPI('/api/low-stock', 'lowstock-result')" class="api-test-btn bg-orange-600 hover:bg-orange-700 text-white p-3 rounded-lg">
                اختبار API المخزون المنخفض / Test Low Stock API
            </button>
            <button onclick="testAPI('/api/inventory-movements', 'movements-result')" class="api-test-btn bg-green-600 hover:bg-green-700 text-white p-3 rounded-lg">
                اختبار API حركات المخزون / Test Inventory Movements API
            </button>
            <button onclick="testAPI('/api/dashboard-stats', 'stats-result')" class="api-test-btn bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-lg">
                اختبار API الإحصائيات / Test Dashboard Stats API
            </button>
        </div>
        
        <!-- API Results -->
        <div class="mt-6 space-y-4">
            <div id="products-result" class="api-result hidden"></div>
            <div id="lowstock-result" class="api-result hidden"></div>
            <div id="movements-result" class="api-result hidden"></div>
            <div id="stats-result" class="api-result hidden"></div>
        </div>
    </div>

    <!-- Interactive Features Test -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">⚡ اختبار الوظائف التفاعلية / Interactive Features Test</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Stock Adjustment Test -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 mb-3">تعديل المخزون / Stock Adjustment</h4>
                <button onclick="testStockAdjustment()" class="w-full bg-green-600 hover:bg-green-700 text-white p-2 rounded-lg">
                    اختبار تعديل المخزون / Test Stock Adjustment
                </button>
                <div id="stock-test-result" class="mt-2 text-sm"></div>
            </div>

            <!-- Search Test -->
            <div class="border border-gray-200 rounded-lg p-4">
                <h4 class="font-medium text-gray-900 mb-3">البحث العام / Global Search</h4>
                <input type="text" id="search-input" placeholder="ابحث عن أي شيء / Search anything..." class="w-full p-2 border border-gray-300 rounded-lg mb-2">
                <button onclick="testGlobalSearch()" class="w-full bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg">
                    اختبار البحث / Test Search
                </button>
                <div id="search-test-result" class="mt-2 text-sm"></div>
            </div>
        </div>
    </div>

    <!-- Performance Test -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">🚀 اختبار الأداء / Performance Test</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-blue-600" id="page-load-time">--</div>
                <div class="text-sm text-gray-500">وقت تحميل الصفحة (ms)</div>
                <div class="text-xs text-gray-400">Page Load Time</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-green-600" id="api-response-time">--</div>
                <div class="text-sm text-gray-500">وقت استجابة API (ms)</div>
                <div class="text-xs text-gray-400">API Response Time</div>
            </div>
            <div class="text-center p-4 bg-gray-50 rounded-lg">
                <div class="text-2xl font-bold text-purple-600" id="memory-usage">--</div>
                <div class="text-sm text-gray-500">استخدام الذاكرة (MB)</div>
                <div class="text-xs text-gray-400">Memory Usage</div>
            </div>
        </div>
        <button onclick="runPerformanceTest()" class="mt-4 w-full bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-lg">
            تشغيل اختبار الأداء / Run Performance Test
        </button>
    </div>

    <!-- Test Results Summary -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">📊 ملخص نتائج الاختبار / Test Results Summary</h3>
        <div id="test-summary" class="space-y-2">
            <div class="text-gray-500">لم يتم تشغيل أي اختبارات بعد / No tests run yet</div>
        </div>
        <button onclick="runAllTests()" class="mt-4 w-full bg-red-600 hover:bg-red-700 text-white p-3 rounded-lg font-medium">
            🚀 تشغيل جميع الاختبارات / Run All Tests
        </button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let testResults = {
    navigation: 0,
    apis: 0,
    interactive: 0,
    performance: 0
};

// Page load performance
document.addEventListener('DOMContentLoaded', function() {
    const loadTime = Math.round(performance.now());
    document.getElementById('page-load-time').textContent = loadTime;
    
    // Memory usage (approximate)
    if (performance.memory) {
        const memoryMB = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
        document.getElementById('memory-usage').textContent = memoryMB;
    }
});

// Test API endpoints
async function testAPI(endpoint, resultId) {
    const resultDiv = document.getElementById(resultId);
    resultDiv.classList.remove('hidden');
    resultDiv.innerHTML = '<div class="text-blue-600">جاري الاختبار... / Testing...</div>';
    
    const startTime = performance.now();
    
    try {
        const response = await fetch(endpoint);
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);
        
        if (response.ok) {
            const data = await response.json();
            resultDiv.innerHTML = `
                <div class="bg-green-50 border border-green-200 rounded-lg p-3">
                    <div class="text-green-800 font-medium">✅ نجح الاختبار / Test Passed</div>
                    <div class="text-sm text-green-600">
                        الاستجابة: ${responseTime}ms | البيانات: ${data.count || 'N/A'} عنصر
                    </div>
                    <div class="text-xs text-green-500">Response: ${responseTime}ms | Data: ${data.count || 'N/A'} items</div>
                </div>
            `;
            testResults.apis++;
        } else {
            throw new Error(`HTTP ${response.status}`);
        }
    } catch (error) {
        const endTime = performance.now();
        const responseTime = Math.round(endTime - startTime);
        resultDiv.innerHTML = `
            <div class="bg-red-50 border border-red-200 rounded-lg p-3">
                <div class="text-red-800 font-medium">❌ فشل الاختبار / Test Failed</div>
                <div class="text-sm text-red-600">خطأ: ${error.message}</div>
                <div class="text-xs text-red-500">Error: ${error.message}</div>
            </div>
        `;
    }
    
    updateTestSummary();
}

// Test stock adjustment
async function testStockAdjustment() {
    const resultDiv = document.getElementById('stock-test-result');
    resultDiv.innerHTML = '<div class="text-blue-600">جاري اختبار تعديل المخزون...</div>';
    
    try {
        const response = await fetch('/api/adjust-stock', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_id: 1,
                new_quantity: 55,
                reason: 'System Test',
                notes: 'Automated test adjustment'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = '<div class="text-green-600">✅ نجح اختبار تعديل المخزون</div>';
            testResults.interactive++;
        } else {
            resultDiv.innerHTML = '<div class="text-red-600">❌ فشل اختبار تعديل المخزون</div>';
        }
    } catch (error) {
        resultDiv.innerHTML = '<div class="text-red-600">❌ خطأ في اختبار تعديل المخزون</div>';
    }
    
    updateTestSummary();
}

// Test global search
async function testGlobalSearch() {
    const query = document.getElementById('search-input').value || 'laptop';
    const resultDiv = document.getElementById('search-test-result');
    resultDiv.innerHTML = '<div class="text-blue-600">جاري اختبار البحث...</div>';
    
    try {
        const response = await fetch(`/api/search?q=${encodeURIComponent(query)}`);
        const data = await response.json();
        
        if (data.success) {
            resultDiv.innerHTML = `<div class="text-green-600">✅ نجح اختبار البحث - ${data.total_results} نتيجة</div>`;
            testResults.interactive++;
        } else {
            resultDiv.innerHTML = '<div class="text-red-600">❌ فشل اختبار البحث</div>';
        }
    } catch (error) {
        resultDiv.innerHTML = '<div class="text-red-600">❌ خطأ في اختبار البحث</div>';
    }
    
    updateTestSummary();
}

// Run performance test
async function runPerformanceTest() {
    const startTime = performance.now();
    
    // Test multiple API calls
    const promises = [
        fetch('/api/products'),
        fetch('/api/dashboard-stats'),
        fetch('/api/low-stock')
    ];
    
    try {
        await Promise.all(promises);
        const endTime = performance.now();
        const totalTime = Math.round(endTime - startTime);
        
        document.getElementById('api-response-time').textContent = totalTime;
        testResults.performance++;
        
        updateTestSummary();
    } catch (error) {
        console.error('Performance test failed:', error);
    }
}

// Run all tests
async function runAllTests() {
    testResults = { navigation: 0, apis: 0, interactive: 0, performance: 0 };
    
    // Test all APIs
    await testAPI('/api/products', 'products-result');
    await new Promise(resolve => setTimeout(resolve, 500));
    await testAPI('/api/low-stock', 'lowstock-result');
    await new Promise(resolve => setTimeout(resolve, 500));
    await testAPI('/api/inventory-movements', 'movements-result');
    await new Promise(resolve => setTimeout(resolve, 500));
    await testAPI('/api/dashboard-stats', 'stats-result');
    
    // Test interactive features
    await new Promise(resolve => setTimeout(resolve, 1000));
    await testStockAdjustment();
    await new Promise(resolve => setTimeout(resolve, 500));
    await testGlobalSearch();
    
    // Test performance
    await new Promise(resolve => setTimeout(resolve, 1000));
    await runPerformanceTest();
    
    updateTestSummary();
}

// Update test summary
function updateTestSummary() {
    const summaryDiv = document.getElementById('test-summary');
    const total = testResults.apis + testResults.interactive + testResults.performance;
    
    summaryDiv.innerHTML = `
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div class="text-center p-3 bg-blue-50 rounded-lg">
                <div class="text-xl font-bold text-blue-600">${testResults.apis}</div>
                <div class="text-sm text-blue-600">APIs نجحت</div>
            </div>
            <div class="text-center p-3 bg-green-50 rounded-lg">
                <div class="text-xl font-bold text-green-600">${testResults.interactive}</div>
                <div class="text-sm text-green-600">وظائف تفاعلية</div>
            </div>
            <div class="text-center p-3 bg-purple-50 rounded-lg">
                <div class="text-xl font-bold text-purple-600">${testResults.performance}</div>
                <div class="text-sm text-purple-600">اختبارات أداء</div>
            </div>
            <div class="text-center p-3 bg-gray-50 rounded-lg">
                <div class="text-xl font-bold text-gray-600">${total}</div>
                <div class="text-sm text-gray-600">إجمالي النجح</div>
            </div>
        </div>
    `;
}

// Track navigation clicks
document.addEventListener('click', function(e) {
    if (e.target.closest('.test-link')) {
        testResults.navigation++;
        updateTestSummary();
    }
});
</script>
{% endblock %}
