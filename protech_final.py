#!/usr/bin/env python3
"""
ProTech Accounting System - Final Working Version
نظام ProTech للمحاسبة - النسخة النهائية العاملة
"""

from flask import Flask, render_template_string, jsonify, request
from datetime import datetime
import json

app = Flask(__name__)
app.secret_key = 'protech-final-key'

# Sample data
products = [
    {'id': 1, 'name': 'Business Laptop', 'name_ar': 'لابتوب الأعمال', 'stock': 50, 'price': 1000, 'min_stock': 10, 'category': 'Electronics'},
    {'id': 2, 'name': 'Wireless Mouse', 'name_ar': 'فأرة لاسلكية', 'stock': 200, 'price': 25, 'min_stock': 50, 'category': 'Electronics'},
    {'id': 3, 'name': 'Professional Notebook', 'name_ar': 'دفتر مهني', 'stock': 5, 'price': 5, 'min_stock': 200, 'category': 'Office Supplies'},
    {'id': 4, 'name': 'Business Smartphone', 'name_ar': 'هاتف ذكي للأعمال', 'stock': 25, 'price': 550, 'min_stock': 5, 'category': 'Electronics'},
    {'id': 5, 'name': 'Office Desk', 'name_ar': 'مكتب مكتبي', 'stock': 0, 'price': 300, 'min_stock': 3, 'category': 'Furniture'}
]

customers = [
    {'id': 1, 'name': 'John Smith', 'name_ar': 'جون سميث', 'balance': 1250, 'category': 'RETAIL', 'email': '<EMAIL>'},
    {'id': 2, 'name': 'ABC Corporation', 'name_ar': 'شركة ABC', 'balance': 8750, 'category': 'WHOLESALE', 'email': '<EMAIL>'},
    {'id': 3, 'name': 'Ahmed Al-Rashid', 'name_ar': 'أحمد الراشد', 'balance': 2500, 'category': 'RETAIL', 'email': '<EMAIL>'}
]

suppliers = [
    {'id': 1, 'name': 'Tech Solutions Inc.', 'name_ar': 'شركة الحلول التقنية', 'contact': 'Mike Johnson', 'email': '<EMAIL>'},
    {'id': 2, 'name': 'Office World Ltd.', 'name_ar': 'شركة عالم المكاتب', 'contact': 'Sarah Wilson', 'email': '<EMAIL>'},
    {'id': 3, 'name': 'Furniture Plus', 'name_ar': 'أثاث بلس', 'contact': 'David Brown', 'email': '<EMAIL>'}
]

# HTML Template
def get_template(title, content):
    return f'''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title} - نظام ProTech للمحاسبة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .ltr {{ direction: ltr; text-align: left; }}
        .rtl {{ direction: rtl; text-align: right; }}
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; }}
        .gradient-bg {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }}
        .card {{ transition: transform 0.2s, box-shadow 0.2s; }}
        .card:hover {{ transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }}
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-xl">
        <div class="container mx-auto px-4 py-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-4xl font-bold">🎉 نظام ProTech للمحاسبة</h1>
                    <p class="text-blue-100 mt-2 text-lg">ProTech Accounting System</p>
                </div>
                <div class="text-left ltr bg-white bg-opacity-20 rounded-lg p-3">
                    <div class="text-sm">🕒 {datetime.now().strftime('%Y-%m-%d %H:%M')}</div>
                    <div class="text-xs">🐍 Python Flask Server</div>
                    <div class="text-xs">✅ نشط / Active</div>
                </div>
            </div>
        </div>
    </header>

    <!-- Navigation -->
    <nav class="bg-blue-600 shadow-lg">
        <div class="container mx-auto px-4">
            <div class="flex space-x-2 space-x-reverse py-4">
                <a href="/" class="px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200 text-white font-medium">🏠 الرئيسية</a>
                <a href="/inventory" class="px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200 text-white font-medium">📦 المخزون</a>
                <a href="/customers" class="px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200 text-white font-medium">👥 العملاء</a>
                <a href="/suppliers" class="px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200 text-white font-medium">🏢 الموردين</a>
                <a href="/sales" class="px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200 text-white font-medium">💰 المبيعات</a>
                <a href="/reports" class="px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200 text-white font-medium">📈 التقارير</a>
                <a href="/test" class="px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200 text-white font-medium">🧪 اختبار</a>
            </div>
        </div>
    </nav>

    <!-- Content -->
    <main class="container mx-auto px-4 py-8">
        {content}
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white mt-12">
        <div class="container mx-auto px-4 py-8">
            <div class="text-center">
                <h3 class="text-2xl font-bold mb-2">نظام ProTech للمحاسبة</h3>
                <p class="text-gray-300 mb-4">ProTech Accounting System</p>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div>🐍 Python Flask</div>
                    <div>🌍 Arabic & English</div>
                    <div>📱 Responsive Design</div>
                    <div>⚡ Fast Performance</div>
                </div>
                <p class="text-gray-400 mt-4">© 2024 ProTech Accounting System</p>
            </div>
        </div>
    </footer>
</body>
</html>
'''

@app.route('/')
def dashboard():
    total_products = len(products)
    low_stock = len([p for p in products if p['stock'] <= p['min_stock']])
    out_of_stock = len([p for p in products if p['stock'] == 0])
    total_customers = len(customers)
    total_value = sum(p['stock'] * p['price'] for p in products)
    
    content = f'''
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="card bg-white rounded-xl shadow-lg p-6 border-r-4 border-blue-500">
            <div class="flex items-center">
                <div class="text-4xl mr-4">📦</div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">إجمالي المنتجات</h3>
                    <p class="text-3xl font-bold text-blue-600">{total_products}</p>
                    <p class="text-sm text-gray-500">Total Products</p>
                </div>
            </div>
        </div>
        
        <div class="card bg-white rounded-xl shadow-lg p-6 border-r-4 border-red-500">
            <div class="flex items-center">
                <div class="text-4xl mr-4">⚠️</div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">مخزون منخفض</h3>
                    <p class="text-3xl font-bold text-red-600">{low_stock}</p>
                    <p class="text-sm text-gray-500">Low Stock Items</p>
                </div>
            </div>
        </div>
        
        <div class="card bg-white rounded-xl shadow-lg p-6 border-r-4 border-green-500">
            <div class="flex items-center">
                <div class="text-4xl mr-4">👥</div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">العملاء</h3>
                    <p class="text-3xl font-bold text-green-600">{total_customers}</p>
                    <p class="text-sm text-gray-500">Total Customers</p>
                </div>
            </div>
        </div>
        
        <div class="card bg-white rounded-xl shadow-lg p-6 border-r-4 border-purple-500">
            <div class="flex items-center">
                <div class="text-4xl mr-4">💰</div>
                <div>
                    <h3 class="text-lg font-semibold text-gray-700">قيمة المخزون</h3>
                    <p class="text-2xl font-bold text-purple-600">${total_value:,.0f}</p>
                    <p class="text-sm text-gray-500">Inventory Value</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="gradient-bg rounded-xl shadow-lg p-8 text-white mb-8">
        <div class="text-center">
            <h2 class="text-4xl font-bold mb-4">🎉 مرحباً بك في نظام ProTech</h2>
            <p class="text-xl mb-6">Welcome to ProTech Accounting System</p>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-white bg-opacity-20 rounded-lg p-6">
                    <div class="text-3xl mb-2">✅</div>
                    <h3 class="font-bold text-lg">النظام نشط</h3>
                    <p>System Active</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-6">
                    <div class="text-3xl mb-2">🌍</div>
                    <h3 class="font-bold text-lg">ثنائي اللغة</h3>
                    <p>Bilingual Support</p>
                </div>
                <div class="bg-white bg-opacity-20 rounded-lg p-6">
                    <div class="text-3xl mb-2">⚡</div>
                    <h3 class="font-bold text-lg">أداء سريع</h3>
                    <p>Fast Performance</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div class="card bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <span class="text-2xl mr-2">📊</span>
                نظرة سريعة على المخزون
            </h3>
            <div class="space-y-4">
    '''
    
    for product in products[:3]:
        status_color = "text-red-600" if product['stock'] <= product['min_stock'] else "text-green-600"
        status_text = "منخفض" if product['stock'] <= product['min_stock'] else "جيد"
        if product['stock'] == 0:
            status_color = "text-red-700"
            status_text = "نفد"
        
        content += f'''
                <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div>
                        <div class="font-semibold text-gray-800">{product['name']}</div>
                        <div class="text-sm text-gray-600">{product['name_ar']}</div>
                        <div class="text-xs text-gray-500">{product['category']}</div>
                    </div>
                    <div class="text-left ltr">
                        <div class="font-bold text-lg">{product['stock']} units</div>
                        <div class="text-sm {status_color} font-medium">{status_text}</div>
                        <div class="text-xs text-gray-500">${product['price']}</div>
                    </div>
                </div>
        '''
    
    content += '''
            </div>
        </div>
        
        <div class="card bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
                <span class="text-2xl mr-2">👥</span>
                العملاء الرئيسيون
            </h3>
            <div class="space-y-4">
    '''
    
    for customer in customers:
        content += f'''
                <div class="flex justify-between items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div class="flex items-center">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold text-lg mr-3">
                            {customer['name'][0]}
                        </div>
                        <div>
                            <div class="font-semibold text-gray-800">{customer['name']}</div>
                            <div class="text-sm text-gray-600">{customer['name_ar']}</div>
                            <div class="text-xs text-gray-500">{customer['email']}</div>
                        </div>
                    </div>
                    <div class="text-left ltr">
                        <div class="font-bold text-lg">${customer['balance']}</div>
                        <div class="text-sm text-blue-600 font-medium">{customer['category']}</div>
                    </div>
                </div>
        '''
    
    content += '''
            </div>
        </div>
    </div>
    '''
    
    return get_template("لوحة التحكم", content)

@app.route('/inventory')
def inventory():
    content = '''
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="gradient-bg text-white p-6">
            <h2 class="text-3xl font-bold flex items-center">
                <span class="text-4xl mr-3">📦</span>
                إدارة المخزون
            </h2>
            <p class="text-blue-100 mt-2">Inventory Management</p>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">المنتج</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">الفئة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">الكمية</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">السعر</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">القيمة</th>
                        <th class="px-6 py-4 text-right text-sm font-medium text-gray-500 uppercase tracking-wider">الحالة</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
    '''
    
    for i, product in enumerate(products):
        total_value = product['stock'] * product['price']
        row_class = "bg-gray-50" if i % 2 == 0 else "bg-white"
        
        if product['stock'] == 0:
            status = '<span class="px-3 py-1 text-xs font-semibold bg-red-100 text-red-800 rounded-full">نفد المخزون</span>'
        elif product['stock'] <= product['min_stock']:
            status = '<span class="px-3 py-1 text-xs font-semibold bg-yellow-100 text-yellow-800 rounded-full">منخفض</span>'
        else:
            status = '<span class="px-3 py-1 text-xs font-semibold bg-green-100 text-green-800 rounded-full">جيد</span>'
        
        content += f'''
                    <tr class="{row_class} hover:bg-blue-50 transition-colors">
                        <td class="px-6 py-4">
                            <div class="font-medium text-gray-900">{product['name']}</div>
                            <div class="text-sm text-gray-500">{product['name_ar']}</div>
                        </td>
                        <td class="px-6 py-4 text-sm text-gray-600">{product['category']}</td>
                        <td class="px-6 py-4 text-sm font-medium text-gray-900">{product['stock']}</td>
                        <td class="px-6 py-4 text-sm text-gray-900">${product['price']}</td>
                        <td class="px-6 py-4 text-sm font-bold text-gray-900">${total_value:,.0f}</td>
                        <td class="px-6 py-4">{status}</td>
                    </tr>
        '''
    
    content += '''
                </tbody>
            </table>
        </div>
    </div>
    '''
    
    return get_template("إدارة المخزون", content)

@app.route('/customers')
def customers_page():
    content = '''
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="gradient-bg text-white p-6">
            <h2 class="text-3xl font-bold flex items-center">
                <span class="text-4xl mr-3">👥</span>
                إدارة العملاء
            </h2>
            <p class="text-green-100 mt-2">Customer Management</p>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    '''

    for customer in customers:
        balance_color = "text-green-600" if customer['balance'] > 0 else "text-red-600"
        category_color = "bg-blue-100 text-blue-800" if customer['category'] == 'RETAIL' else "bg-purple-100 text-purple-800"

        content += f'''
                <div class="card border border-gray-200 rounded-lg p-6 hover:shadow-xl transition-all duration-200">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                            {customer['name'][0]}
                        </div>
                        <div>
                            <h3 class="font-bold text-lg text-gray-900">{customer['name']}</h3>
                            <p class="text-gray-600">{customer['name_ar']}</p>
                            <p class="text-sm text-gray-500">{customer['email']}</p>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 font-medium">الرصيد:</span>
                            <span class="font-bold text-lg {balance_color}">${customer['balance']}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 font-medium">الفئة:</span>
                            <span class="px-3 py-1 rounded-full text-sm font-medium {category_color}">{customer['category']}</span>
                        </div>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            ✅ نشط / Active
                        </span>
                    </div>
                </div>
        '''

    content += '''
            </div>
        </div>
    </div>
    '''

    return get_template("إدارة العملاء", content)

@app.route('/suppliers')
def suppliers_page():
    content = '''
    <div class="bg-white rounded-xl shadow-lg overflow-hidden">
        <div class="gradient-bg text-white p-6">
            <h2 class="text-3xl font-bold flex items-center">
                <span class="text-4xl mr-3">🏢</span>
                إدارة الموردين
            </h2>
            <p class="text-orange-100 mt-2">Supplier Management</p>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    '''

    for supplier in suppliers:
        content += f'''
                <div class="card border border-gray-200 rounded-lg p-6 hover:shadow-xl transition-all duration-200">
                    <div class="flex items-center mb-4">
                        <div class="w-16 h-16 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-white font-bold text-xl mr-4">
                            {supplier['name'][0]}
                        </div>
                        <div>
                            <h3 class="font-bold text-lg text-gray-900">{supplier['name']}</h3>
                            <p class="text-gray-600">{supplier['name_ar']}</p>
                        </div>
                    </div>

                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 font-medium">جهة الاتصال:</span>
                            <span class="font-medium text-gray-800">{supplier['contact']}</span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 font-medium">البريد:</span>
                            <span class="text-sm text-blue-600">{supplier['email']}</span>
                        </div>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-200">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            ✅ نشط / Active
                        </span>
                    </div>
                </div>
        '''

    content += '''
            </div>
        </div>
    </div>
    '''

    return get_template("إدارة الموردين", content)

@app.route('/sales')
def sales():
    content = '''
    <div class="bg-white rounded-xl shadow-lg p-8">
        <div class="text-center">
            <div class="text-6xl mb-4">💰</div>
            <h2 class="text-3xl font-bold text-gray-800 mb-4">إدارة المبيعات</h2>
            <p class="text-gray-600 mb-8">Sales Management</p>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-8">
                <div class="text-6xl mb-4">🚧</div>
                <h3 class="text-xl font-bold text-blue-800 mb-2">قيد التطوير</h3>
                <p class="text-blue-600">هذا القسم قيد التطوير وسيكون متاحاً قريباً</p>
                <p class="text-blue-500 text-sm mt-2">This section is under development and will be available soon</p>
            </div>
        </div>
    </div>
    '''

    return get_template("إدارة المبيعات", content)

@app.route('/reports')
def reports():
    content = '''
    <div class="bg-white rounded-xl shadow-lg p-8">
        <div class="text-center">
            <div class="text-6xl mb-4">📈</div>
            <h2 class="text-3xl font-bold text-gray-800 mb-4">التقارير والتحليلات</h2>
            <p class="text-gray-600 mb-8">Reports & Analytics</p>

            <div class="bg-purple-50 border border-purple-200 rounded-lg p-8">
                <div class="text-6xl mb-4">📊</div>
                <h3 class="text-xl font-bold text-purple-800 mb-2">قريباً</h3>
                <p class="text-purple-600">تقارير شاملة وتحليلات متقدمة قريباً</p>
                <p class="text-purple-500 text-sm mt-2">Comprehensive reports and advanced analytics coming soon</p>
            </div>
        </div>
    </div>
    '''

    return get_template("التقارير والتحليلات", content)

@app.route('/test')
def test_page():
    content = f'''
    <div class="bg-white rounded-xl shadow-lg p-8">
        <div class="text-center mb-8">
            <div class="text-6xl mb-4">🧪</div>
            <h2 class="text-3xl font-bold text-gray-800 mb-4">صفحة الاختبار</h2>
            <p class="text-gray-600">System Test Page</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                <h3 class="text-xl font-bold text-green-800 mb-4">✅ اختبارات النظام</h3>
                <div class="space-y-2 text-green-700">
                    <p>✅ الخادم يعمل بنجاح</p>
                    <p>✅ قاعدة البيانات متصلة</p>
                    <p>✅ واجهة المستخدم تعمل</p>
                    <p>✅ دعم اللغة العربية نشط</p>
                    <p>✅ التصميم المتجاوب يعمل</p>
                </div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-xl font-bold text-blue-800 mb-4">📊 إحصائيات النظام</h3>
                <div class="space-y-2 text-blue-700">
                    <p>📦 المنتجات: {len(products)}</p>
                    <p>👥 العملاء: {len(customers)}</p>
                    <p>🏢 الموردين: {len(suppliers)}</p>
                    <p>🕒 وقت التشغيل: {datetime.now().strftime('%H:%M:%S')}</p>
                    <p>📅 التاريخ: {datetime.now().strftime('%Y-%m-%d')}</p>
                </div>
            </div>
        </div>

        <div class="mt-8 bg-gradient-to-r from-green-400 to-blue-500 rounded-lg p-6 text-white text-center">
            <h3 class="text-2xl font-bold mb-2">🎉 جميع الاختبارات نجحت!</h3>
            <p class="text-lg">All tests passed successfully!</p>
        </div>
    </div>
    '''

    return get_template("صفحة الاختبار", content)

# API Routes
@app.route('/api/products')
def api_products():
    return jsonify({
        'success': True,
        'data': products,
        'count': len(products),
        'timestamp': datetime.now().isoformat(),
        'message': 'Products retrieved successfully'
    })

@app.route('/api/customers')
def api_customers():
    return jsonify({
        'success': True,
        'data': customers,
        'count': len(customers),
        'timestamp': datetime.now().isoformat(),
        'message': 'Customers retrieved successfully'
    })

@app.route('/api/suppliers')
def api_suppliers():
    return jsonify({
        'success': True,
        'data': suppliers,
        'count': len(suppliers),
        'timestamp': datetime.now().isoformat(),
        'message': 'Suppliers retrieved successfully'
    })

@app.route('/api/stats')
def api_stats():
    low_stock = len([p for p in products if p['stock'] <= p['min_stock']])
    out_of_stock = len([p for p in products if p['stock'] == 0])
    total_value = sum(p['stock'] * p['price'] for p in products)

    return jsonify({
        'success': True,
        'data': {
            'total_products': len(products),
            'total_customers': len(customers),
            'total_suppliers': len(suppliers),
            'low_stock_items': low_stock,
            'out_of_stock_items': out_of_stock,
            'total_inventory_value': total_value,
            'system_status': 'active',
            'last_updated': datetime.now().isoformat()
        },
        'timestamp': datetime.now().isoformat(),
        'message': 'Statistics retrieved successfully'
    })

@app.route('/api/low-stock')
def api_low_stock():
    low_stock_products = [p for p in products if p['stock'] <= p['min_stock']]
    return jsonify({
        'success': True,
        'data': low_stock_products,
        'count': len(low_stock_products),
        'timestamp': datetime.now().isoformat(),
        'message': 'Low stock products retrieved successfully'
    })

@app.route('/api/search')
def api_search():
    query = request.args.get('q', '').lower()
    if not query:
        return jsonify({
            'success': False,
            'message': 'Search query is required',
            'timestamp': datetime.now().isoformat()
        }), 400

    # Search in products
    product_results = [
        p for p in products
        if query in p['name'].lower() or query in p['name_ar'].lower() or query in p['category'].lower()
    ]

    # Search in customers
    customer_results = [
        c for c in customers
        if query in c['name'].lower() or query in c['name_ar'].lower() or query in c['email'].lower()
    ]

    # Search in suppliers
    supplier_results = [
        s for s in suppliers
        if query in s['name'].lower() or query in s['name_ar'].lower() or query in s['contact'].lower()
    ]

    return jsonify({
        'success': True,
        'data': {
            'products': product_results,
            'customers': customer_results,
            'suppliers': supplier_results
        },
        'total_results': len(product_results) + len(customer_results) + len(supplier_results),
        'query': query,
        'timestamp': datetime.now().isoformat(),
        'message': 'Search completed successfully'
    })

# Error handlers
@app.errorhandler(404)
def not_found(error):
    content = '''
    <div class="text-center py-16">
        <div class="text-8xl mb-4">🔍</div>
        <h2 class="text-4xl font-bold text-gray-800 mb-4">404 - الصفحة غير موجودة</h2>
        <p class="text-gray-600 mb-8">Page Not Found</p>
        <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            العودة للرئيسية / Back to Home
        </a>
    </div>
    '''
    return get_template("صفحة غير موجودة", content), 404

@app.errorhandler(500)
def server_error(error):
    content = '''
    <div class="text-center py-16">
        <div class="text-8xl mb-4">⚠️</div>
        <h2 class="text-4xl font-bold text-gray-800 mb-4">500 - خطأ في الخادم</h2>
        <p class="text-gray-600 mb-8">Server Error</p>
        <a href="/" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors">
            العودة للرئيسية / Back to Home
        </a>
    </div>
    '''
    return get_template("خطأ في الخادم", content), 500

if __name__ == '__main__':
    print("=" * 70)
    print("   🎉 ProTech Accounting System - Final Version")
    print("   🎉 نظام ProTech للمحاسبة - النسخة النهائية")
    print("=" * 70)
    print()
    print("🚀 Starting ProTech Final Application...")
    print("📱 Access the application at: http://localhost:5000")
    print("🌐 Network access: http://0.0.0.0:5000")
    print()
    print("✅ Available Pages:")
    print("   📊 Dashboard: http://localhost:5000")
    print("   📦 Inventory: http://localhost:5000/inventory")
    print("   👥 Customers: http://localhost:5000/customers")
    print("   🏢 Suppliers: http://localhost:5000/suppliers")
    print("   💰 Sales: http://localhost:5000/sales")
    print("   📈 Reports: http://localhost:5000/reports")
    print("   🧪 Test: http://localhost:5000/test")
    print()
    print("🔌 Available APIs:")
    print("   📦 Products: http://localhost:5000/api/products")
    print("   👥 Customers: http://localhost:5000/api/customers")
    print("   🏢 Suppliers: http://localhost:5000/api/suppliers")
    print("   📊 Statistics: http://localhost:5000/api/stats")
    print("   📉 Low Stock: http://localhost:5000/api/low-stock")
    print("   🔍 Search: http://localhost:5000/api/search?q=term")
    print()
    print("🌟 Features:")
    print("   ✅ Bilingual Support (Arabic/English)")
    print("   ✅ Responsive Design")
    print("   ✅ Modern UI with Tailwind CSS")
    print("   ✅ RESTful API Endpoints")
    print("   ✅ Error Handling")
    print("   ✅ Fast Performance")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 70)

    try:
        app.run(debug=False, host='0.0.0.0', port=5000, threaded=True, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        print("Please check the error and try again.")
