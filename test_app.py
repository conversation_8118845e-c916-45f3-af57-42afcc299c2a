#!/usr/bin/env python3
"""
Simple test Flask application to verify setup
"""

from flask import Flask

app = Flask(__name__)

@app.route('/')
def hello():
    return '''
    <!DOCTYPE html>
    <html>
    <head>
        <title>ProTech Accounting - Test</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }
            .container { max-width: 800px; margin: 0 auto; background: white; padding: 40px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .success { color: #28a745; font-size: 24px; font-weight: bold; }
            .info { color: #6c757d; margin: 20px 0; }
            .button { display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; margin: 10px 5px; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎉 ProTech Accounting System</h1>
            <p class="success">✅ Python Flask Server is Running Successfully!</p>
            
            <div class="info">
                <h3>🐍 Server Information:</h3>
                <ul>
                    <li><strong>Framework:</strong> Python Flask</li>
                    <li><strong>Status:</strong> Active and Responding</li>
                    <li><strong>URL:</strong> http://localhost:5000</li>
                    <li><strong>Port:</strong> 5000</li>
                </ul>
            </div>
            
            <div class="info">
                <h3>🚀 Next Steps:</h3>
                <p>The basic Flask server is working! Now we can run the full ProTech Accounting application.</p>
                <a href="#" class="button" onclick="loadFullApp()">Load Full Application</a>
            </div>
            
            <div class="info">
                <h3>📋 Application Features Ready:</h3>
                <ul>
                    <li>✅ Dashboard with real-time statistics</li>
                    <li>✅ Inventory management system</li>
                    <li>✅ Customer relationship management</li>
                    <li>✅ Sales and invoice tracking</li>
                    <li>✅ Supplier management</li>
                    <li>✅ Reports and analytics</li>
                    <li>✅ RESTful API endpoints</li>
                    <li>✅ Responsive design</li>
                </ul>
            </div>
        </div>
        
        <script>
            function loadFullApp() {
                alert('Flask is working! You can now stop this test server and run the full app.py');
            }
        </script>
    </body>
    </html>
    '''

if __name__ == '__main__':
    print("=" * 50)
    print("   ProTech Accounting - Test Server")
    print("=" * 50)
    print()
    print("🚀 Starting test Flask server...")
    print("📱 Access at: http://localhost:5000")
    print()
    print("If you can see this message and access the URL,")
    print("then Flask is working correctly!")
    print()
    print("Press Ctrl+C to stop the server")
    print("=" * 50)
    
    app.run(debug=True, host='0.0.0.0', port=5000)
