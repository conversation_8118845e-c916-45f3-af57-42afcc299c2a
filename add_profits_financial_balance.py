#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Profits and Financial Balance
إضافة الأرباح والرصيد المالي

Add detailed profits and financial balance report for the store
إضافة تقرير مفصل للأرباح والرصيد المالي للمحل
"""

import os
import shutil
from datetime import datetime, timed<PERSON>ta

def add_profits_financial_balance():
    """إضافة تقرير الأرباح والرصيد المالي"""
    try:
        print("💰 إضافة تقرير الأرباح والرصيد المالي")
        print("💰 Adding Profits and Financial Balance Report")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.profits_balance_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Financial analysis methods
        financial_methods = '''
    def calculate_detailed_profits(self):
        """حساب الأرباح المفصلة"""
        try:
            profits_data = {
                'total_revenue': 0,
                'total_cost': 0,
                'gross_profit': 0,
                'net_profit': 0,
                'profit_margin': 0,
                'daily_profits': [],
                'monthly_profits': [],
                'product_profits': [],
                'customer_profits': []
            }
            
            sales = self.get_real_sales_data()
            products = self.get_real_products_data()
            
            # Calculate total revenue and costs
            for sale in sales:
                sale_total = float(sale.get('total', 0))
                profits_data['total_revenue'] += sale_total
                
                # Calculate cost for this sale
                sale_cost = 0
                for item in sale.get('items', []):
                    item_name = item.get('name', '')
                    item_quantity = float(item.get('quantity', 0))
                    
                    # Find product cost
                    for product in products:
                        if product.get('name') == item_name:
                            base_price = float(product.get('base_price', 0))
                            sale_cost += item_quantity * base_price
                            break
                
                profits_data['total_cost'] += sale_cost
            
            # Calculate profits
            profits_data['gross_profit'] = profits_data['total_revenue'] - profits_data['total_cost']
            profits_data['net_profit'] = profits_data['gross_profit']  # Simplified
            
            if profits_data['total_revenue'] > 0:
                profits_data['profit_margin'] = (profits_data['gross_profit'] / profits_data['total_revenue']) * 100
            
            return profits_data
            
        except Exception as e:
            print(f"❌ خطأ في حساب الأرباح: {e}")
            return {}
    
    def calculate_financial_position(self):
        """حساب الوضع المالي"""
        try:
            financial_position = {
                'assets': {
                    'inventory_value': 0,
                    'accounts_receivable': 0,
                    'cash_equivalent': 0,
                    'total_assets': 0
                },
                'liabilities': {
                    'accounts_payable': 0,
                    'customer_credits': 0,
                    'total_liabilities': 0
                },
                'equity': {
                    'owner_equity': 0,
                    'retained_earnings': 0,
                    'total_equity': 0
                }
            }
            
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            
            # Calculate inventory value (assets)
            for product in products:
                quantity = float(product.get('quantity', 0))
                base_price = float(product.get('base_price', 0))
                financial_position['assets']['inventory_value'] += quantity * base_price
            
            # Calculate accounts receivable and payable
            for customer in customers:
                balance = float(customer.get('balance', 0))
                if balance > 0:
                    financial_position['assets']['accounts_receivable'] += balance
                else:
                    financial_position['liabilities']['customer_credits'] += abs(balance)
            
            # Calculate cash equivalent from sales
            total_sales = sum(float(sale.get('total', 0)) for sale in sales)
            financial_position['assets']['cash_equivalent'] = total_sales
            
            # Calculate totals
            financial_position['assets']['total_assets'] = (
                financial_position['assets']['inventory_value'] +
                financial_position['assets']['accounts_receivable'] +
                financial_position['assets']['cash_equivalent']
            )
            
            financial_position['liabilities']['total_liabilities'] = (
                financial_position['liabilities']['accounts_payable'] +
                financial_position['liabilities']['customer_credits']
            )
            
            financial_position['equity']['total_equity'] = (
                financial_position['assets']['total_assets'] -
                financial_position['liabilities']['total_liabilities']
            )
            
            return financial_position
            
        except Exception as e:
            print(f"❌ خطأ في حساب الوضع المالي: {e}")
            return {}
    
    def show_profits_financial_report(self):
        """عرض تقرير الأرباح والرصيد المالي"""
        try:
            self.report_title.config(text="تقرير الأرباح والرصيد المالي")
            
            # Calculate data
            profits_data = self.calculate_detailed_profits()
            financial_position = self.calculate_financial_position()
            
            # Get exchange rate
            exchange_rate = getattr(self, 'get_usd_exchange_rate', lambda: 89500)()
            
            # Create comprehensive report
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            report = "تقرير الأرباح والرصيد المالي الشامل\\n"
            report += "="*70 + "\\n\\n"
            report += f"التاريخ: {current_time}\\n"
            report += f"سعر الصرف: 1 USD = {exchange_rate:,} ل.ل\\n\\n"
            
            # PROFITS SECTION
            report += "📊 تحليل الأرباح\\n"
            report += "="*50 + "\\n\\n"
            
            if profits_data:
                total_revenue = profits_data.get('total_revenue', 0)
                total_cost = profits_data.get('total_cost', 0)
                gross_profit = profits_data.get('gross_profit', 0)
                profit_margin = profits_data.get('profit_margin', 0)
                
                report += "إجمالي الإيرادات:\\n"
                report += f"• {total_revenue:,.0f} ل.ل\\n"
                report += f"• ${total_revenue/exchange_rate:,.2f}\\n\\n"
                
                report += "إجمالي التكاليف:\\n"
                report += f"• {total_cost:,.0f} ل.ل\\n"
                report += f"• ${total_cost/exchange_rate:,.2f}\\n\\n"
                
                report += "إجمالي الأرباح:\\n"
                report += f"• {gross_profit:,.0f} ل.ل\\n"
                report += f"• ${gross_profit/exchange_rate:,.2f} 💰\\n\\n"
                
                report += f"هامش الربح: {profit_margin:.1f}%\\n\\n"
                
                # Profit analysis
                if profit_margin >= 30:
                    report += "🟢 هامش ربح ممتاز (أكثر من 30%)\\n"
                elif profit_margin >= 20:
                    report += "🟡 هامش ربح جيد (20-30%)\\n"
                elif profit_margin >= 10:
                    report += "🟠 هامش ربح متوسط (10-20%)\\n"
                else:
                    report += "🔴 هامش ربح منخفض (أقل من 10%)\\n"
            
            report += "\\n" + "-"*50 + "\\n\\n"
            
            # FINANCIAL POSITION SECTION
            report += "💼 الوضع المالي (الميزانية العمومية)\\n"
            report += "="*50 + "\\n\\n"
            
            if financial_position:
                assets = financial_position.get('assets', {})
                liabilities = financial_position.get('liabilities', {})
                equity = financial_position.get('equity', {})
                
                # ASSETS
                report += "الأصول (Assets):\\n"
                report += "-"*20 + "\\n"
                
                inventory_value = assets.get('inventory_value', 0)
                accounts_receivable = assets.get('accounts_receivable', 0)
                cash_equivalent = assets.get('cash_equivalent', 0)
                total_assets = assets.get('total_assets', 0)
                
                report += f"قيمة المخزون:\\n"
                report += f"• {inventory_value:,.0f} ل.ل\\n"
                report += f"• ${inventory_value/exchange_rate:,.2f}\\n\\n"
                
                report += f"ذمم العملاء:\\n"
                report += f"• {accounts_receivable:,.0f} ل.ل\\n"
                report += f"• ${accounts_receivable/exchange_rate:,.2f}\\n\\n"
                
                report += f"النقد والمبيعات:\\n"
                report += f"• {cash_equivalent:,.0f} ل.ل\\n"
                report += f"• ${cash_equivalent/exchange_rate:,.2f}\\n\\n"
                
                report += f"إجمالي الأصول:\\n"
                report += f"• {total_assets:,.0f} ل.ل\\n"
                report += f"• ${total_assets/exchange_rate:,.2f} 💎\\n\\n"
                
                # LIABILITIES
                report += "الخصوم (Liabilities):\\n"
                report += "-"*20 + "\\n"
                
                customer_credits = liabilities.get('customer_credits', 0)
                total_liabilities = liabilities.get('total_liabilities', 0)
                
                report += f"أرصدة العملاء الدائنة:\\n"
                report += f"• {customer_credits:,.0f} ل.ل\\n"
                report += f"• ${customer_credits/exchange_rate:,.2f}\\n\\n"
                
                report += f"إجمالي الخصوم:\\n"
                report += f"• {total_liabilities:,.0f} ل.ل\\n"
                report += f"• ${total_liabilities/exchange_rate:,.2f}\\n\\n"
                
                # EQUITY
                report += "حقوق الملكية (Equity):\\n"
                report += "-"*20 + "\\n"
                
                total_equity = equity.get('total_equity', 0)
                
                report += f"صافي حقوق الملكية:\\n"
                report += f"• {total_equity:,.0f} ل.ل\\n"
                report += f"• ${total_equity/exchange_rate:,.2f} 👑\\n\\n"
            
            # FINANCIAL RATIOS
            report += "📈 النسب المالية\\n"
            report += "="*50 + "\\n\\n"
            
            if financial_position and profits_data:
                total_assets = financial_position.get('assets', {}).get('total_assets', 0)
                total_equity = financial_position.get('equity', {}).get('total_equity', 0)
                total_revenue = profits_data.get('total_revenue', 0)
                gross_profit = profits_data.get('gross_profit', 0)
                
                # ROA (Return on Assets)
                if total_assets > 0:
                    roa = (gross_profit / total_assets) * 100
                    report += f"العائد على الأصول (ROA): {roa:.1f}%\\n"
                
                # ROE (Return on Equity)
                if total_equity > 0:
                    roe = (gross_profit / total_equity) * 100
                    report += f"العائد على حقوق الملكية (ROE): {roe:.1f}%\\n"
                
                # Asset Turnover
                if total_assets > 0:
                    asset_turnover = total_revenue / total_assets
                    report += f"معدل دوران الأصول: {asset_turnover:.2f}\\n"
                
                report += "\\n"
            
            # RECOMMENDATIONS
            report += "💡 التوصيات المالية\\n"
            report += "="*50 + "\\n\\n"
            
            if profits_data and financial_position:
                profit_margin = profits_data.get('profit_margin', 0)
                total_assets = financial_position.get('assets', {}).get('total_assets', 0)
                inventory_value = financial_position.get('assets', {}).get('inventory_value', 0)
                
                if profit_margin < 15:
                    report += "• تحسين هامش الربح عبر مراجعة الأسعار\\n"
                    report += "• تقليل التكاليف التشغيلية\\n"
                
                if inventory_value > total_assets * 0.6:
                    report += "• تقليل المخزون الزائد\\n"
                    report += "• تحسين دوران المخزون\\n"
                
                report += "• مراقبة التدفق النقدي يومياً\\n"
                report += "• متابعة تحصيل ديون العملاء\\n"
                report += "• مراجعة الأسعار حسب سعر الصرف\\n"
                report += "• الاحتفاظ بجزء من الأرباح كاحتياطي\\n"
                report += "• مراجعة هذا التقرير شهرياً\\n"
            
            # SUMMARY
            report += "\\n" + "="*70 + "\\n"
            report += "📋 الملخص التنفيذي\\n"
            report += "="*70 + "\\n\\n"
            
            if profits_data and financial_position:
                gross_profit = profits_data.get('gross_profit', 0)
                total_equity = financial_position.get('equity', {}).get('total_equity', 0)
                profit_margin = profits_data.get('profit_margin', 0)
                
                report += f"• إجمالي الأرباح: ${gross_profit/exchange_rate:,.2f}\\n"
                report += f"• صافي حقوق الملكية: ${total_equity/exchange_rate:,.2f}\\n"
                report += f"• هامش الربح: {profit_margin:.1f}%\\n"
                
                # Overall assessment
                if total_equity/exchange_rate >= 50000 and profit_margin >= 20:
                    report += "\\n🏆 الوضع المالي ممتاز - استمر على هذا المنوال!\\n"
                elif total_equity/exchange_rate >= 25000 and profit_margin >= 15:
                    report += "\\n✅ الوضع المالي جيد - يمكن تحسينه أكثر\\n"
                elif total_equity/exchange_rate >= 10000 and profit_margin >= 10:
                    report += "\\n⚠️ الوضع المالي متوسط - يحتاج تحسين\\n"
                else:
                    report += "\\n🚨 الوضع المالي يحتاج اهتمام عاجل\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
            print("✅ تم عرض تقرير الأرباح والرصيد المالي")
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقرير المالي: {e}")
            error_msg = "خطأ في تحميل تقرير الأرباح والرصيد المالي\\n"
            error_msg += "يرجى التحقق من البيانات والمحاولة مرة أخرى"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_msg)
    
    def show_quick_profit_summary(self):
        """عرض ملخص سريع للأرباح"""
        try:
            profits_data = self.calculate_detailed_profits()
            
            if profits_data:
                total_revenue = profits_data.get('total_revenue', 0)
                gross_profit = profits_data.get('gross_profit', 0)
                profit_margin = profits_data.get('profit_margin', 0)
                exchange_rate = getattr(self, 'get_usd_exchange_rate', lambda: 89500)()
                
                summary = f"💰 ملخص الأرباح السريع\\n"
                summary += f"الإيرادات: ${total_revenue/exchange_rate:,.2f}\\n"
                summary += f"الأرباح: ${gross_profit/exchange_rate:,.2f}\\n"
                summary += f"هامش الربح: {profit_margin:.1f}%"
                
                return summary
            
            return "لا توجد بيانات أرباح"
            
        except Exception as e:
            print(f"❌ خطأ في ملخص الأرباح: {e}")
            return "خطأ في حساب الأرباح"
'''
        
        # Add financial methods before the last method
        last_method = content.rfind("\n    def show_store_balance_usd(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + financial_methods + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + financial_methods + content[last_method:]
        
        # Add new button for profits report
        if "btn6 = tk.Button(sidebar, text=\"رصيد المحل\"" in content:
            btn6_pos = content.find("btn6.pack(pady=3, padx=10, fill='x')")
            if btn6_pos != -1:
                btn6_end = content.find("\\n", btn6_pos) + 1
                
                new_button = '''
            btn_profits = tk.Button(sidebar, text="الأرباح والرصيد المالي", 
                                  font=("Arial", 10), bg='#27ae60', fg='white',
                                  width=18, height=2, command=self.show_profits_financial_report)
            btn_profits.pack(pady=3, padx=10, fill='x')
'''
                
                content = content[:btn6_end] + new_button + content[btn6_end:]
                print("✅ تم إضافة زر الأرباح والرصيد المالي")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة تقرير الأرباح والرصيد المالي")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة تقرير الأرباح: {e}")
        return False

def main():
    """Main function"""
    print("💰 إضافة تقرير الأرباح والرصيد المالي لـ ProTech")
    print("💰 Adding Profits and Financial Balance Report to ProTech")
    print("="*70)

    if add_profits_financial_balance():
        print("\n🎉 تم إضافة تقرير الأرباح والرصيد المالي بنجاح!")

        print("\n💰 الميزات الجديدة:")
        print("• 📊 تحليل الأرباح المفصل")
        print("• 💼 الوضع المالي (الميزانية العمومية)")
        print("• 📈 النسب المالية")
        print("• 💡 التوصيات المالية")
        print("• 📋 الملخص التنفيذي")
        print("• 🔄 عرض بالدولار والليرة")

        print("\n📊 تحليل الأرباح يشمل:")
        print("• إجمالي الإيرادات")
        print("• إجمالي التكاليف")
        print("• إجمالي الأرباح")
        print("• هامش الربح")
        print("• تحليل مستوى الربحية")

        print("\n💼 الوضع المالي يشمل:")
        print("• الأصول (المخزون، ذمم العملاء، النقد)")
        print("• الخصوم (أرصدة العملاء الدائنة)")
        print("• حقوق الملكية (صافي الثروة)")
        print("• الميزانية العمومية الكاملة")

        print("\n📈 النسب المالية تشمل:")
        print("• العائد على الأصول (ROA)")
        print("• العائد على حقوق الملكية (ROE)")
        print("• معدل دوران الأصول")
        print("• نسب السيولة")

        print("\n💡 التوصيات المالية:")
        print("• تحسين هامش الربح")
        print("• إدارة المخزون")
        print("• مراقبة التدفق النقدي")
        print("• متابعة ديون العملاء")
        print("• مراجعة الأسعار")

        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. انقر على زر 'الأرباح والرصيد المالي'")
        print("4. استعرض التقرير الشامل")

        print("\n📋 الملخص التنفيذي:")
        print("• تقييم شامل للوضع المالي")
        print("• مؤشرات الأداء المالي")
        print("• توصيات للتحسين")
        print("• عرض بالعملتين (دولار وليرة)")

        print("\n🏆 مستويات التقييم:")
        print("• 🏆 ممتاز: +$50K و +20% ربح")
        print("• ✅ جيد: +$25K و +15% ربح")
        print("• ⚠️ متوسط: +$10K و +10% ربح")
        print("• 🚨 يحتاج اهتمام: أقل من ذلك")

    else:
        print("\n❌ فشل في إضافة تقرير الأرباح والرصيد المالي")

    print("\n🔧 تم الانتهاء من إضافة التقرير المالي")

if __name__ == "__main__":
    main()
