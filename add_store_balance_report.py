#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Store Balance Report
إضافة تقرير رصيد المحل

Add comprehensive store balance report showing inventory and cash
إضافة تقرير شامل لرصيد المحل يعرض البضاعة والأموال
"""

import os
import shutil
from datetime import datetime

def add_store_balance_report():
    """إضافة تقرير رصيد المحل"""
    try:
        print("💰 إضافة تقرير رصيد المحل")
        print("💰 Adding Store Balance Report")
        print("="*50)

        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")

        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False

        # Create backup
        backup_name = f"{protech_file}.balance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")

        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Add store balance report button to existing buttons
        if "btn5 = tk.Button(sidebar, text=\"تقرير شامل\"" in content:
            # Find the position after btn5
            btn5_pos = content.find("btn5.pack(pady=3, padx=10, fill='x')")
            if btn5_pos != -1:
                btn5_end = content.find("\n", btn5_pos) + 1

                # Add new button
                new_button = '''
            btn6 = tk.Button(sidebar, text="رصيد المحل",
                           font=("Arial", 10), bg='#1abc9c', fg='white',
                           width=18, height=2, command=self.show_store_balance)
            btn6.pack(pady=3, padx=10, fill='x')
'''

                content = content[:btn5_end] + new_button + content[btn5_end:]
                print("✅ تم إضافة زر رصيد المحل")

        # Add the store balance method
        store_balance_method = '''
    def show_store_balance(self):
        """عرض تقرير رصيد المحل"""
        try:
            self.report_title.config(text="رصيد المحل الشامل")

            # حساب رصيد البضاعة
            products = getattr(self, 'products', [])
            sales = getattr(self, 'sales', [])
            customers = getattr(self, 'customers', [])

            # حساب قيمة البضاعة الإجمالية
            total_inventory_value = 0
            total_inventory_cost = 0
            categories_value = {}

            for product in products:
                stock = product.get('quantity', 0)
                base_price = product.get('base_price', 0)
                selling_price = product.get('shop_owner_price', base_price * 1.05)  # سعر البيع
                category = product.get('category', 'غير مصنف')

                # قيمة البضاعة بسعر التكلفة
                cost_value = stock * base_price
                total_inventory_cost += cost_value

                # قيمة البضاعة بسعر البيع
                selling_value = stock * selling_price
                total_inventory_value += selling_value

                # تصنيف حسب الفئات
                if category not in categories_value:
                    categories_value[category] = {'cost': 0, 'selling': 0, 'quantity': 0}
                categories_value[category]['cost'] += cost_value
                categories_value[category]['selling'] += selling_value
                categories_value[category]['quantity'] += stock

            # حساب الأموال
            total_sales_revenue = sum(sale.get('total', 0) for sale in sales)
            total_sales_cost = 0

            # حساب تكلفة المبيعات
            for sale in sales:
                for item in sale.get('items', []):
                    # البحث عن المنتج لمعرفة سعر التكلفة
                    for product in products:
                        if product.get('name') == item.get('name'):
                            cost_price = product.get('base_price', 0)
                            quantity_sold = item.get('quantity', 0)
                            total_sales_cost += cost_price * quantity_sold
                            break

            # صافي الربح من المبيعات
            net_profit = total_sales_revenue - total_sales_cost

            # حساب أرصدة العملاء
            customer_balances = {}
            total_customer_debt = 0
            total_customer_credit = 0

            for customer in customers:
                balance = customer.get('balance', 0)
                customer_name = customer.get('name', 'غير محدد')
                customer_balances[customer_name] = balance

                if balance > 0:
                    total_customer_credit += balance  # العميل له رصيد
                else:
                    total_customer_debt += abs(balance)  # العميل عليه دين

            # إنشاء التقرير
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            report = "رصيد المحل الشامل\\n"
            report += "="*60 + "\\n\\n"
            report += "التاريخ: " + current_time + "\\n\\n"

            # قسم البضاعة
            report += "📦 رصيد البضاعة:\\n"
            report += "-"*40 + "\\n"
            report += "إجمالي المنتجات: " + str(len(products)) + " صنف\\n"
            report += "قيمة البضاعة (سعر التكلفة): " + str(round(total_inventory_cost, 2)) + " ريال\\n"
            report += "قيمة البضاعة (سعر البيع): " + str(round(total_inventory_value, 2)) + " ريال\\n"
            report += "الربح المتوقع من البضاعة: " + str(round(total_inventory_value - total_inventory_cost, 2)) + " ريال\\n\\n"

            # تفصيل حسب الفئات
            if categories_value:
                report += "تفصيل البضاعة حسب الفئات:\\n"
                for category, values in categories_value.items():
                    report += "• " + category + ":\\n"
                    report += "  الكمية: " + str(values['quantity']) + "\\n"
                    report += "  قيمة التكلفة: " + str(round(values['cost'], 2)) + " ريال\\n"
                    report += "  قيمة البيع: " + str(round(values['selling'], 2)) + " ريال\\n"
                    report += "  الربح المتوقع: " + str(round(values['selling'] - values['cost'], 2)) + " ريال\\n\\n"

            # قسم الأموال
            report += "💰 رصيد الأموال:\\n"
            report += "-"*40 + "\\n"
            report += "إجمالي إيرادات المبيعات: " + str(round(total_sales_revenue, 2)) + " ريال\\n"
            report += "إجمالي تكلفة المبيعات: " + str(round(total_sales_cost, 2)) + " ريال\\n"
            report += "صافي الربح المحقق: " + str(round(net_profit, 2)) + " ريال\\n\\n"

            # أرصدة العملاء
            report += "👥 أرصدة العملاء:\\n"
            report += "-"*40 + "\\n"
            report += "إجمالي ديون العملاء: " + str(round(total_customer_debt, 2)) + " ريال\\n"
            report += "إجمالي أرصدة العملاء: " + str(round(total_customer_credit, 2)) + " ريال\\n"
            report += "صافي أرصدة العملاء: " + str(round(total_customer_credit - total_customer_debt, 2)) + " ريال\\n\\n"

            # تفصيل أرصدة العملاء
            if customer_balances:
                report += "تفصيل أرصدة العملاء:\\n"
                for customer_name, balance in customer_balances.items():
                    if balance != 0:
                        status = "له رصيد" if balance > 0 else "عليه دين"
                        report += "• " + customer_name + ": " + str(abs(balance)) + " ريال (" + status + ")\\n"

            # الرصيد الإجمالي للمحل
            report += "\\n" + "="*60 + "\\n"
            report += "📊 الرصيد الإجمالي للمحل:\\n"
            report += "="*60 + "\\n"

            total_assets = total_inventory_value + total_customer_credit
            total_liabilities = total_customer_debt
            net_worth = total_assets - total_liabilities + net_profit

            report += "إجمالي الأصول:\\n"
            report += "• قيمة البضاعة: " + str(round(total_inventory_value, 2)) + " ريال\\n"
            report += "• أرصدة العملاء: " + str(round(total_customer_credit, 2)) + " ريال\\n"
            report += "• الأرباح المحققة: " + str(round(net_profit, 2)) + " ريال\\n"
            report += "إجمالي الأصول: " + str(round(total_assets + net_profit, 2)) + " ريال\\n\\n"

            report += "إجمالي الخصوم:\\n"
            report += "• ديون العملاء: " + str(round(total_customer_debt, 2)) + " ريال\\n\\n"

            report += "صافي رصيد المحل: " + str(round(net_worth, 2)) + " ريال\\n\\n"

            # تحليل الأداء
            if total_inventory_cost > 0:
                inventory_margin = ((total_inventory_value - total_inventory_cost) / total_inventory_cost) * 100
                report += "📈 تحليل الأداء:\\n"
                report += "-"*40 + "\\n"
                report += "هامش ربح البضاعة: " + str(round(inventory_margin, 1)) + "%\\n"

                if total_sales_revenue > 0:
                    sales_margin = (net_profit / total_sales_revenue) * 100
                    report += "هامش ربح المبيعات: " + str(round(sales_margin, 1)) + "%\\n"

                turnover_ratio = total_sales_cost / total_inventory_cost if total_inventory_cost > 0 else 0
                report += "معدل دوران المخزون: " + str(round(turnover_ratio, 2)) + "\\n\\n"

            # توصيات
            report += "💡 التوصيات:\\n"
            report += "-"*40 + "\\n"

            if total_customer_debt > total_customer_credit:
                report += "• متابعة تحصيل ديون العملاء\\n"

            if total_inventory_cost > total_sales_revenue:
                report += "• زيادة المبيعات أو تقليل المخزون\\n"

            low_margin_categories = [cat for cat, vals in categories_value.items()
                                   if vals['cost'] > 0 and ((vals['selling'] - vals['cost']) / vals['cost']) < 0.1]
            if low_margin_categories:
                report += "• مراجعة أسعار فئات: " + ", ".join(low_margin_categories) + "\\n"

            report += "• حفظ البيانات بانتظام\\n"
            report += "• مراجعة التقرير شهرياً\\n"

            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)

        except Exception as e:
            print("خطأ في عرض تقرير رصيد المحل:", str(e))
            error_report = "خطأ في تحميل تقرير رصيد المحل\\n"
            error_report += "تأكد من وجود البيانات وأعد المحاولة"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_report)
'''

        # Add the method before the last method
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + store_balance_method + content[last_method:]
        else:
            # If fallback method not found, add before any method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + store_balance_method + content[last_method:]

        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم إضافة تقرير رصيد المحل")

        # Test compilation
        import subprocess
        import sys

        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file],
                              capture_output=True, text=True)

        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")

            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False

    except Exception as e:
        print(f"❌ خطأ في إضافة تقرير رصيد المحل: {e}")
        return False

def create_standalone_balance_report():
    """إنشاء تقرير رصيد مستقل"""
    try:
        print("\n📊 إنشاء تقرير رصيد المحل المستقل...")

        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        standalone_file = os.path.join(desktop_path, "تقرير_رصيد_المحل.py")

        standalone_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Store Balance Report - Standalone
تقرير رصيد المحل - مستقل

Standalone store balance report application
تطبيق مستقل لتقرير رصيد المحل
"""

import tkinter as tk
from tkinter import messagebox
import json
import os
from datetime import datetime

class StoreBalanceReport:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("تقرير رصيد المحل - ProTech")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f8f9fa')

        # Center window
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (1000 // 2)
        y = (self.root.winfo_screenheight() // 2) - (700 // 2)
        self.root.geometry(f"1000x700+{x}+{y}")

        # Load data
        self.load_data()

        # Create interface
        self.create_interface()

    def load_data(self):
        """تحميل البيانات"""
        try:
            data_file = "protech_simple_data.json"
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.suppliers = data.get('suppliers', [])
                self.sales = data.get('sales', [])

                print(f"✅ تم تحميل البيانات: {len(self.products)} منتج، {len(self.sales)} مبيعة")
            else:
                self.create_sample_data()

        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            self.create_sample_data()

    def create_sample_data(self):
        """إنشاء بيانات تجريبية"""
        self.products = [
            {"name": "منتج تجريبي 1", "quantity": 100, "base_price": 50.0, "category": "فئة أ"},
            {"name": "منتج تجريبي 2", "quantity": 75, "base_price": 30.0, "category": "فئة ب"},
            {"name": "منتج تجريبي 3", "quantity": 50, "base_price": 80.0, "category": "فئة أ"}
        ]

        self.customers = [
            {"name": "عميل تجريبي 1", "balance": 500.0},
            {"name": "عميل تجريبي 2", "balance": -200.0}
        ]

        self.suppliers = []

        self.sales = [
            {"total": 1500.0, "items": [{"name": "منتج تجريبي 1", "quantity": 10, "unit_price": 60.0}]},
            {"total": 900.0, "items": [{"name": "منتج تجريبي 2", "quantity": 15, "unit_price": 35.0}]}
        ]

        print("✅ تم إنشاء بيانات تجريبية")

    def create_interface(self):
        """إنشاء الواجهة"""
        # العنوان الرئيسي
        header_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        title_label = tk.Label(header_frame, text="💰 تقرير رصيد المحل الشامل",
                              font=("Arial", 18, "bold"), bg='#2c3e50', fg='white')
        title_label.pack(expand=True)

        # الإطار الرئيسي
        main_frame = tk.Frame(self.root, bg='#f8f9fa')
        main_frame.pack(fill='both', expand=True, padx=20, pady=20)

        # أزرار التحكم
        control_frame = tk.Frame(main_frame, bg='#f8f9fa')
        control_frame.pack(fill='x', pady=(0, 20))

        refresh_btn = tk.Button(control_frame, text="🔄 تحديث التقرير",
                              font=("Arial", 12), bg='#3498db', fg='white',
                              width=15, height=2, command=self.generate_report)
        refresh_btn.pack(side='left', padx=(0, 10))

        export_btn = tk.Button(control_frame, text="📄 تصدير التقرير",
                             font=("Arial", 12), bg='#27ae60', fg='white',
                             width=15, height=2, command=self.export_report)
        export_btn.pack(side='left', padx=(0, 10))

        # منطقة التقرير
        report_frame = tk.Frame(main_frame, bg='white')
        report_frame.pack(fill='both', expand=True)

        # شريط التمرير
        scrollbar = tk.Scrollbar(report_frame)
        scrollbar.pack(side='right', fill='y')

        # منطقة النص
        self.report_text = tk.Text(report_frame, font=("Arial", 11),
                                 bg='#fafbfc', fg='#2c3e50',
                                 yscrollcommand=scrollbar.set,
                                 wrap='word', padx=20, pady=20)
        self.report_text.pack(fill='both', expand=True)
        scrollbar.config(command=self.report_text.yview)

        # شريط الحالة
        status_frame = tk.Frame(self.root, bg='#27ae60', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(status_frame, text="✅ جاهز لعرض تقرير رصيد المحل",
                                   font=("Arial", 9, "bold"), bg='#27ae60', fg='white')
        self.status_label.pack(side='left', padx=15, pady=5)

        time_label = tk.Label(status_frame, text=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                            font=("Arial", 9), bg='#27ae60', fg='white')
        time_label.pack(side='right', padx=15, pady=5)

        # إنشاء التقرير تلقائياً
        self.generate_report()

    def generate_report(self):
        """إنشاء تقرير رصيد المحل"""
        try:
            self.status_label.config(text="🔄 جاري إنشاء التقرير...")
            self.root.update()

            # حساب رصيد البضاعة
            total_inventory_value = 0
            total_inventory_cost = 0
            categories_value = {}

            for product in self.products:
                stock = product.get('quantity', 0)
                base_price = product.get('base_price', 0)
                selling_price = base_price * 1.2  # افتراض هامش ربح 20%
                category = product.get('category', 'غير مصنف')

                cost_value = stock * base_price
                total_inventory_cost += cost_value

                selling_value = stock * selling_price
                total_inventory_value += selling_value

                if category not in categories_value:
                    categories_value[category] = {'cost': 0, 'selling': 0, 'quantity': 0}
                categories_value[category]['cost'] += cost_value
                categories_value[category]['selling'] += selling_value
                categories_value[category]['quantity'] += stock

            # حساب الأموال
            total_sales_revenue = sum(sale.get('total', 0) for sale in self.sales)
            total_sales_cost = 0

            for sale in self.sales:
                for item in sale.get('items', []):
                    for product in self.products:
                        if product.get('name') == item.get('name'):
                            cost_price = product.get('base_price', 0)
                            quantity_sold = item.get('quantity', 0)
                            total_sales_cost += cost_price * quantity_sold
                            break

            net_profit = total_sales_revenue - total_sales_cost

            # حساب أرصدة العملاء
            total_customer_debt = 0
            total_customer_credit = 0

            for customer in self.customers:
                balance = customer.get('balance', 0)
                if balance > 0:
                    total_customer_credit += balance
                else:
                    total_customer_debt += abs(balance)

            # إنشاء التقرير
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            report = f"""💰 تقرير رصيد المحل الشامل
{'='*70}

📅 تاريخ التقرير: {current_time}

📦 رصيد البضاعة:
{'-'*50}
إجمالي المنتجات: {len(self.products)} صنف
قيمة البضاعة (سعر التكلفة): {total_inventory_cost:,.2f} ريال
قيمة البضاعة (سعر البيع): {total_inventory_value:,.2f} ريال
الربح المتوقع من البضاعة: {total_inventory_value - total_inventory_cost:,.2f} ريال

تفصيل البضاعة حسب الفئات:
"""

            for category, values in categories_value.items():
                report += f"""• {category}:
  الكمية: {values['quantity']}
  قيمة التكلفة: {values['cost']:,.2f} ريال
  قيمة البيع: {values['selling']:,.2f} ريال
  الربح المتوقع: {values['selling'] - values['cost']:,.2f} ريال

"""

            report += f"""💰 رصيد الأموال:
{'-'*50}
إجمالي إيرادات المبيعات: {total_sales_revenue:,.2f} ريال
إجمالي تكلفة المبيعات: {total_sales_cost:,.2f} ريال
صافي الربح المحقق: {net_profit:,.2f} ريال

👥 أرصدة العملاء:
{'-'*50}
إجمالي ديون العملاء: {total_customer_debt:,.2f} ريال
إجمالي أرصدة العملاء: {total_customer_credit:,.2f} ريال
صافي أرصدة العملاء: {total_customer_credit - total_customer_debt:,.2f} ريال

تفصيل أرصدة العملاء:
"""

            for customer in self.customers:
                name = customer.get('name', 'غير محدد')
                balance = customer.get('balance', 0)
                if balance != 0:
                    status = "له رصيد" if balance > 0 else "عليه دين"
                    report += f"• {name}: {abs(balance):,.2f} ريال ({status})\\n"

            # الرصيد الإجمالي
            total_assets = total_inventory_value + total_customer_credit
            net_worth = total_assets - total_customer_debt + net_profit

            report += f"""
{'='*70}
📊 الرصيد الإجمالي للمحل:
{'='*70}

إجمالي الأصول:
• قيمة البضاعة: {total_inventory_value:,.2f} ريال
• أرصدة العملاء: {total_customer_credit:,.2f} ريال
• الأرباح المحققة: {net_profit:,.2f} ريال
إجمالي الأصول: {total_assets + net_profit:,.2f} ريال

إجمالي الخصوم:
• ديون العملاء: {total_customer_debt:,.2f} ريال

صافي رصيد المحل: {net_worth:,.2f} ريال

📈 تحليل الأداء:
{'-'*50}
"""

            if total_inventory_cost > 0:
                inventory_margin = ((total_inventory_value - total_inventory_cost) / total_inventory_cost) * 100
                report += f"هامش ربح البضاعة: {inventory_margin:.1f}%\\n"

                if total_sales_revenue > 0:
                    sales_margin = (net_profit / total_sales_revenue) * 100
                    report += f"هامش ربح المبيعات: {sales_margin:.1f}%\\n"

                turnover_ratio = total_sales_cost / total_inventory_cost if total_inventory_cost > 0 else 0
                report += f"معدل دوران المخزون: {turnover_ratio:.2f}\\n"

            report += f"""
💡 التوصيات:
{'-'*50}
"""

            if total_customer_debt > total_customer_credit:
                report += "• متابعة تحصيل ديون العملاء\\n"

            if total_inventory_cost > total_sales_revenue:
                report += "• زيادة المبيعات أو تقليل المخزون\\n"

            report += "• حفظ البيانات بانتظام\\n"
            report += "• مراجعة التقرير شهرياً\\n"
            report += "• متابعة هوامش الربح\\n"

            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)

            self.status_label.config(text="✅ تم إنشاء تقرير رصيد المحل بنجاح")

        except Exception as e:
            error_msg = f"❌ خطأ في إنشاء التقرير: {e}"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_msg)
            self.status_label.config(text="❌ فشل في إنشاء التقرير")

    def export_report(self):
        """تصدير التقرير"""
        try:
            report_content = self.report_text.get(1.0, tk.END)
            filename = f"تقرير_رصيد_المحل_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"

            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)

            messagebox.showinfo("نجح التصدير", f"تم تصدير التقرير إلى:\\n{filename}")
            self.status_label.config(text=f"✅ تم تصدير التقرير: {filename}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير التقرير: {e}")
            self.status_label.config(text="❌ فشل في تصدير التقرير")

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

if __name__ == "__main__":
    app = StoreBalanceReport()
    app.run()
'''

        with open(standalone_file, 'w', encoding='utf-8') as f:
            f.write(standalone_code)

        print(f"✅ تم إنشاء تقرير رصيد المحل المستقل: {os.path.basename(standalone_file)}")
        return standalone_file

    except Exception as e:
        print(f"❌ فشل في إنشاء التقرير المستقل: {e}")
        return None

def main():
    """Main function"""
    print("💰 إضافة تقرير رصيد المحل إلى ProTech")
    print("💰 Adding Store Balance Report to ProTech")
    print("="*60)

    created_items = []

    # Add store balance report to ProTech
    if add_store_balance_report():
        created_items.append("تقرير رصيد المحل في ProTech")

    # Create standalone report
    standalone = create_standalone_balance_report()
    if standalone:
        created_items.append("تقرير رصيد المحل المستقل")

    # Summary
    print("\n" + "="*60)
    print("📊 ملخص الإضافات:")

    if created_items:
        print(f"✅ تم إنشاء {len(created_items)} عنصر:")
        for i, item in enumerate(created_items, 1):
            print(f"  {i}. {item}")
    else:
        print("❌ لم يتم إنشاء أي عناصر")

    print("\n💰 محتويات تقرير رصيد المحل:")
    print("• 📦 رصيد البضاعة (قيمة التكلفة والبيع)")
    print("• 💰 رصيد الأموال (الإيرادات والأرباح)")
    print("• 👥 أرصدة العملاء (الديون والأرصدة)")
    print("• 📊 الرصيد الإجمالي للمحل")
    print("• 📈 تحليل الأداء والهوامش")
    print("• 📂 تفصيل حسب الفئات")
    print("• 💡 التوصيات والنصائح")

    print("\n🎯 كيفية الاستخدام:")

    if "تقرير رصيد المحل في ProTech" in created_items:
        print("✅ في ProTech:")
        print("  1. افتح ProTech")
        print("  2. اذهب إلى صفحة التقارير")
        print("  3. انقر على زر 'رصيد المحل'")
        print("  4. استعرض التقرير الشامل")

    if "تقرير رصيد المحل المستقل" in created_items:
        print("✅ التطبيق المستقل:")
        print("  1. شغل 'تقرير_رصيد_المحل.py'")
        print("  2. سيتم إنشاء التقرير تلقائياً")
        print("  3. استخدم زر 'تحديث التقرير' للتحديث")
        print("  4. استخدم زر 'تصدير التقرير' للحفظ")

    print("\n📊 الفوائد:")
    print("• معرفة القيمة الحقيقية للمحل")
    print("• متابعة الأرباح والخسائر")
    print("• مراقبة أرصدة العملاء")
    print("• تحليل أداء الفئات المختلفة")
    print("• اتخاذ قرارات مالية مدروسة")

    print("\n🎉 تم إضافة تقرير رصيد المحل بنجاح!")

    if len(created_items) >= 1:
        print("✅ لديك الآن نظام شامل لمتابعة رصيد المحل")
    else:
        print("⚠️ قد تحتاج مراجعة إضافية")

if __name__ == "__main__":
    main()