{"errors": [{"timestamp": "2025-06-19T15:40:22.588726", "error": "name 'self' is not defined", "type": "NameError", "context": "ProTech execution", "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\Documents\\augment-projects\\protech\\smart_protech_wrapper.py\", line 121, in run_protech_safely\n    import protech_simple_working\n  File \"C:\\Users\\<USER>\\Documents\\augment-projects\\protech\\protech_simple_working.py\", line 32, in <module>\n    class ProTechSimpleWorking:\n    ...<24270 lines>...\n                print(\"💾 Data saved\")\n  File \"C:\\Users\\<USER>\\Documents\\augment-projects\\protech\\protech_simple_working.py\", line 9660, in ProTechSimpleWorking\n    class BatchOperations:\n    ...<15 lines>...\n                self.parent.root.update()\n  File \"C:\\Users\\<USER>\\Documents\\augment-projects\\protech\\protech_simple_working.py\", line 9666, in BatchOperations\n    self.add_save_button()\n    ^^^^\nNameError: name 'self' is not defined\n"}]}