#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Minimal Working Version
نسخة ProTech الأساسية العاملة
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime

class ProTechMinimal:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("ProTech - نظام المحاسبة البسيط")
        self.root.geometry("800x600")
        self.root.configure(bg='#f0f0f0')
        
        # Data
        self.data_file = "protech_simple_data.json"
        self.suppliers = []
        self.products = []
        self.customers = []
        self.sales = []
        
        # Load data
        self.load_data()
        
        # Create UI
        self.create_ui()
    
    def create_ui(self):
        """Create basic UI"""
        try:
            # Main frame
            main_frame = ttk.Frame(self.root)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # Title
            title_label = ttk.Label(main_frame, text="ProTech - نظام المحاسبة", 
                                  font=("Arial", 18, "bold"))
            title_label.pack(pady=15)
            
            # Status frame
            status_frame = ttk.LabelFrame(main_frame, text="حالة النظام", padding=15)
            status_frame.pack(fill=tk.X, pady=10)
            
            # Status info
            status_info = ttk.Frame(status_frame)
            status_info.pack(fill=tk.X)
            
            ttk.Label(status_info, text=f"📊 الموردين: {len(self.suppliers)}", 
                     font=("Arial", 12)).pack(side=tk.LEFT, padx=10)
            ttk.Label(status_info, text=f"📦 المنتجات: {len(self.products)}", 
                     font=("Arial", 12)).pack(side=tk.LEFT, padx=10)
            ttk.Label(status_info, text=f"👥 العملاء: {len(self.customers)}", 
                     font=("Arial", 12)).pack(side=tk.LEFT, padx=10)
            ttk.Label(status_info, text=f"💰 المبيعات: {len(self.sales)}", 
                     font=("Arial", 12)).pack(side=tk.LEFT, padx=10)
            
            # Success message
            success_frame = ttk.LabelFrame(main_frame, text="حالة البرنامج", padding=15)
            success_frame.pack(fill=tk.X, pady=10)
            
            success_label = ttk.Label(success_frame, 
                                    text="✅ البرنامج يعمل بنجاح!\n✅ جميع البيانات محملة بشكل صحيح\n✅ النظام جاهز للاستخدام",
                                    font=("Arial", 12),
                                    foreground="green")
            success_label.pack(pady=10)
            
            # Buttons frame
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(fill=tk.X, pady=20)
            
            # Test save button
            test_button = ttk.Button(buttons_frame, text="🧪 اختبار الحفظ", 
                                   command=self.test_save,
                                   style="Accent.TButton")
            test_button.pack(side=tk.LEFT, padx=5)
            
            # Refresh button
            refresh_button = ttk.Button(buttons_frame, text="🔄 تحديث البيانات", 
                                      command=self.refresh_data)
            refresh_button.pack(side=tk.LEFT, padx=5)
            
            # View data button
            view_button = ttk.Button(buttons_frame, text="👁️ عرض البيانات", 
                                   command=self.view_data)
            view_button.pack(side=tk.LEFT, padx=5)
            
            # Exit button
            exit_button = ttk.Button(buttons_frame, text="🚪 خروج", 
                                   command=self.safe_exit)
            exit_button.pack(side=tk.RIGHT, padx=5)
            
            # Data display frame
            self.data_frame = ttk.LabelFrame(main_frame, text="عرض البيانات", padding=10)
            self.data_frame.pack(fill=tk.BOTH, expand=True, pady=10)
            
            # Text widget for data display
            self.data_text = tk.Text(self.data_frame, height=15, width=80, 
                                   font=("Courier", 10), wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(self.data_frame, orient=tk.VERTICAL, command=self.data_text.yview)
            self.data_text.configure(yscrollcommand=scrollbar.set)
            
            self.data_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # Show initial data
            self.show_data_summary()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في إنشاء الواجهة: {e}")
    
    def load_data(self):
        """Load data from file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                print(f"✅ تم تحميل البيانات: {len(self.suppliers)} موردين، {len(self.products)} منتجات")
            else:
                print("⚠️ لا يوجد ملف بيانات، بدء بقوائم فارغة")
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            messagebox.showwarning("تحذير", f"خطأ في تحميل البيانات: {e}")
    
    def test_save(self):
        """Test save functionality"""
        try:
            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat(),
                'version': '1.0',
                'status': 'working'
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            messagebox.showinfo("نجح", "✅ تم حفظ البيانات بنجاح!")
            self.show_data_summary()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"❌ فشل في الحفظ: {e}")
    
    def refresh_data(self):
        """Refresh data from file"""
        try:
            self.load_data()
            self.show_data_summary()
            messagebox.showinfo("تحديث", "✅ تم تحديث البيانات بنجاح!")
        except Exception as e:
            messagebox.showerror("خطأ", f"❌ فشل في التحديث: {e}")
    
    def view_data(self):
        """View detailed data"""
        try:
            self.data_text.delete(1.0, tk.END)
            
            data_display = f"""
📊 تفاصيل البيانات المحملة:
{'='*50}

📊 الموردين ({len(self.suppliers)}):
{'-'*30}
"""
            
            for i, supplier in enumerate(self.suppliers, 1):
                data_display += f"{i}. {supplier.get('name', 'غير محدد')} - {supplier.get('phone', 'لا يوجد')}\n"
            
            data_display += f"""
📦 المنتجات ({len(self.products)}):
{'-'*30}
"""
            
            for i, product in enumerate(self.products, 1):
                data_display += f"{i}. {product.get('name', 'غير محدد')} - {product.get('price', 0)} ريال\n"
            
            data_display += f"""
👥 العملاء ({len(self.customers)}):
{'-'*30}
"""
            
            for i, customer in enumerate(self.customers, 1):
                data_display += f"{i}. {customer.get('name', 'غير محدد')} - {customer.get('type', 'غير محدد')}\n"
            
            data_display += f"""
💰 المبيعات ({len(self.sales)}):
{'-'*30}
"""
            
            for i, sale in enumerate(self.sales, 1):
                data_display += f"{i}. العميل: {sale.get('customer_name', 'غير محدد')} - المجموع: {sale.get('total', 0)} ريال\n"
            
            data_display += f"""
{'='*50}
📅 آخر تحديث: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
✅ النظام يعمل بشكل طبيعي
"""
            
            self.data_text.insert(1.0, data_display)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"❌ فشل في عرض البيانات: {e}")
    
    def show_data_summary(self):
        """Show data summary"""
        try:
            summary = f"""
🎉 مرحباً بك في ProTech!
{'='*40}

✅ البرنامج يعمل بنجاح
✅ جميع البيانات محملة بشكل صحيح
✅ النظام جاهز للاستخدام

📊 ملخص البيانات:
• الموردين: {len(self.suppliers)}
• المنتجات: {len(self.products)}
• العملاء: {len(self.customers)}
• المبيعات: {len(self.sales)}

💡 استخدم الأزرار أعلاه لـ:
• اختبار الحفظ
• تحديث البيانات
• عرض التفاصيل

📅 الوقت الحالي: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
            
            self.data_text.delete(1.0, tk.END)
            self.data_text.insert(1.0, summary)
            
        except Exception as e:
            print(f"خطأ في عرض الملخص: {e}")
    
    def safe_exit(self):
        """Safe exit with data save"""
        try:
            # Auto-save before exit
            self.test_save()
            
            # Confirm exit
            if messagebox.askyesno("تأكيد الخروج", "هل تريد إغلاق البرنامج؟"):
                self.root.quit()
                self.root.destroy()
        except Exception as e:
            print(f"خطأ في الخروج الآمن: {e}")
            self.root.quit()
    
    def run(self):
        """Run the application"""
        try:
            print("🚀 تشغيل ProTech الأساسي...")
            self.root.protocol("WM_DELETE_WINDOW", self.safe_exit)
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل البرنامج:\n{str(e)}")

def main():
    """Main function"""
    try:
        print("🎯 بدء تشغيل ProTech الأساسي")
        print("🎯 Starting ProTech Minimal")
        print("="*40)
        
        app = ProTechMinimal()
        app.run()
        
        print("👋 تم إغلاق ProTech")
        
    except Exception as e:
        print(f"❌ خطأ في البرنامج الرئيسي: {e}")
        try:
            messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق\nFailed to start application:\n{str(e)}")
        except:
            print("فشل في عرض رسالة الخطأ")

if __name__ == "__main__":
    main()
