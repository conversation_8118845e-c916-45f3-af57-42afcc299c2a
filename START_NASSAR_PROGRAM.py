#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🏢 Nassar Program Final - ProTech Accounting System
نظام ناصر النهائي - برنامج ProTech للمحاسبة

المطور: Augment Agent
التاريخ: 2025-06-20
الإصدار: النهائي المحسن

هذا هو المشغل الرئيسي لبرنامج ناصر للمحاسبة
This is the main launcher for Nassar Accounting Program
"""

import os
import sys
import json
import subprocess
import tkinter as tk
from tkinter import messagebox
from datetime import datetime

class NassarProgramLauncher:
    def __init__(self):
        self.base_path = os.path.dirname(os.path.abspath(__file__))
        self.main_program = os.path.join(self.base_path, "01_Main_Program", "protech_simple_working.py")
        self.data_file = os.path.join(self.base_path, "02_Data_Files", "protech_simple_data.json")
        
    def check_files(self):
        """Check if all required files exist"""
        print("🔍 فحص الملفات المطلوبة...")
        print("🔍 Checking required files...")
        
        if not os.path.exists(self.main_program):
            print(f"❌ ملف البرنامج الرئيسي غير موجود: {self.main_program}")
            print(f"❌ Main program file not found: {self.main_program}")
            return False
            
        if not os.path.exists(self.data_file):
            print(f"⚠️ ملف البيانات غير موجود: {self.data_file}")
            print(f"⚠️ Data file not found: {self.data_file}")
            print("   سيتم إنشاء ملف بيانات جديد")
            print("   A new data file will be created")
            
        print("✅ جميع الملفات جاهزة")
        print("✅ All files ready")
        return True
    
    def show_welcome_dialog(self):
        """Show welcome dialog"""
        root = tk.Tk()
        root.withdraw()  # Hide main window
        
        welcome_msg = """
🏢 مرحباً بك في برنامج ناصر للمحاسبة
Welcome to Nassar Accounting Program

📋 الميزات المتوفرة / Available Features:
• إدارة المخزون مع الباركود / Inventory Management with Barcode
• إدارة العملاء والموردين / Customer & Supplier Management  
• نظام المبيعات والفواتير / Sales & Invoice System
• التقارير والإحصائيات / Reports & Statistics
• واجهة ثنائية اللغة / Bilingual Interface

🚀 هل تريد تشغيل البرنامج الآن؟
Do you want to start the program now?
        """
        
        result = messagebox.askyesno(
            "برنامج ناصر للمحاسبة / Nassar Accounting Program",
            welcome_msg
        )
        
        root.destroy()
        return result
    
    def copy_data_file(self):
        """Copy data file to main program directory for compatibility"""
        try:
            main_program_dir = os.path.dirname(self.main_program)
            target_data_file = os.path.join(main_program_dir, "protech_simple_data.json")
            
            if os.path.exists(self.data_file):
                import shutil
                shutil.copy2(self.data_file, target_data_file)
                print(f"✅ تم نسخ ملف البيانات إلى: {target_data_file}")
                print(f"✅ Data file copied to: {target_data_file}")
            
        except Exception as e:
            print(f"⚠️ تحذير في نسخ ملف البيانات: {e}")
            print(f"⚠️ Warning copying data file: {e}")
    
    def launch_program(self):
        """Launch the main program"""
        try:
            print("🚀 تشغيل برنامج ناصر للمحاسبة...")
            print("🚀 Starting Nassar Accounting Program...")
            
            # Change to main program directory
            main_program_dir = os.path.dirname(self.main_program)
            os.chdir(main_program_dir)
            
            # Copy data file for compatibility
            self.copy_data_file()
            
            # Launch the program
            subprocess.run([sys.executable, "protech_simple_working.py"])
            
            print("✅ تم إغلاق البرنامج بنجاح")
            print("✅ Program closed successfully")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            print(f"❌ Error launching program: {e}")
            
            root = tk.Tk()
            root.withdraw()
            messagebox.showerror(
                "خطأ / Error",
                f"فشل في تشغيل البرنامج\nFailed to launch program:\n{str(e)}"
            )
            root.destroy()
    
    def run(self):
        """Main run method"""
        print("=" * 60)
        print("🏢 برنامج ناصر النهائي للمحاسبة")
        print("🏢 Nassar Final Accounting Program")
        print("=" * 60)
        print(f"📅 التاريخ / Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 المسار / Path: {self.base_path}")
        print("=" * 60)
        
        # Check files
        if not self.check_files():
            input("\nاضغط Enter للخروج / Press Enter to exit...")
            return
        
        # Show welcome dialog
        if self.show_welcome_dialog():
            self.launch_program()
        else:
            print("❌ تم إلغاء تشغيل البرنامج")
            print("❌ Program launch cancelled")
        
        input("\nاضغط Enter للخروج / Press Enter to exit...")

def main():
    """Main function"""
    launcher = NassarProgramLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
