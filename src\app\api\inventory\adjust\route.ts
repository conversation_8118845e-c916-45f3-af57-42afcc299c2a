import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { stockAdjustmentSchema } from '@/lib/validations';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = stockAdjustmentSchema.parse(body);

    // Get current user (in a real app, this would come from authentication)
    const userId = 'admin-user-id'; // This should come from the authenticated user

    // Start a transaction to ensure data consistency
    const result = await db.$transaction(async (prisma) => {
      // Get current product stock
      const product = await prisma.product.findUnique({
        where: { id: validatedData.productId },
      });

      if (!product) {
        throw new Error('Product not found');
      }

      const currentStock = product.currentStock;
      const newStock = validatedData.newQuantity;
      const difference = newStock - currentStock;

      // Create inventory movement record
      const movement = await prisma.inventoryMovement.create({
        data: {
          productId: validatedData.productId,
          userId,
          type: 'ADJUSTMENT',
          quantity: difference,
          reason: validatedData.reason,
          notes: validatedData.notes,
          referenceType: 'adjustment',
        },
        include: {
          product: {
            select: {
              id: true,
              code: true,
              name: true,
              unit: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Update product stock
      const updatedProduct = await prisma.product.update({
        where: { id: validatedData.productId },
        data: { currentStock: newStock },
        include: {
          category: true,
          supplier: true,
        },
      });

      return {
        movement,
        product: updatedProduct,
        previousStock: currentStock,
        newStock,
        difference,
      };
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Stock adjusted successfully',
    });
  } catch (error) {
    console.error('Error adjusting stock:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to adjust stock',
      },
      { status: 500 }
    );
  }
}
