#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚨 EMERGENCY FIX LAUNCHER - Nassar Accounting Program
مشغل الإصلاح الطارئ - برنامج ناصر للمحاسبة

يحل مشكلة AttributeError فوراً
Fixes AttributeError immediately
"""

import os
import sys
import subprocess
import tempfile

def create_emergency_fix():
    """Create emergency fix for the AttributeError"""
    
    print("🚨 إصلاح طارئ لمشكلة AttributeError...")
    print("🚨 Emergency fix for AttributeError...")
    
    # Emergency fix code
    emergency_fix = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import tkinter as tk
from tkinter import ttk, messagebox, filedialog, scrolledtext
import json
import os
import time
import threading
from datetime import datetime
import gc
import psutil
import sys
import traceback
import logging

class ProTechSimpleWorking:
    """Simple working ProTech application with emergency fixes"""

    def __init__(self):
        print("🚀 تشغيل نظام ProTech البسيط العامل...")
        print("🚀 Starting ProTech Simple Working System...")

        # EMERGENCY: Initialize ALL data structures FIRST
        self.products = []
        self.customers = []
        self.suppliers = []
        self.sales = []
        self.data_file = "protech_simple_data.json"
        self.backup_file = "protech_backup.json"
        
        print("✅ تم تهيئة هياكل البيانات الأساسية")
        print("✅ Basic data structures initialized")

        # Performance flags
        self.loading = True
        self.cache_enabled = True
        self.auto_save_enabled = True
        self.last_save_time = time.time()

        # Load data immediately
        self.init_data()

        # Initialize main window
        self.root = tk.Tk()
        self.root.title("نظام ProTech للمحاسبة - ProTech Accounting System")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f8ff')
        self.root.state('zoomed')

        # Create interface
        self.create_interface()
        
        self.loading = False
        print("✅ تم تحميل النظام بنجاح!")
        print("✅ System loaded successfully!")

    def init_data(self):
        """Initialize data with emergency fallback"""
        try:
            print("🔍 تحميل البيانات...")
            print("🔍 Loading data...")
            
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                print(f"✅ تم تحميل {len(self.products)} منتج، {len(self.customers)} عميل")
                print(f"✅ Loaded {len(self.products)} products, {len(self.customers)} customers")
            else:
                print("⚠️ ملف البيانات غير موجود، إنشاء بيانات نموذجية...")
                print("⚠️ Data file not found, creating sample data...")
                self.create_sample_data()
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            print("🔄 إنشاء بيانات نموذجية...")
            self.create_sample_data()

    def create_sample_data(self):
        """Create sample data"""
        self.suppliers = [
            {"id": 1, "name": "Tech Solutions", "name_ar": "شركة الحلول التقنية", "phone": "+966-11-123-4567"},
            {"id": 2, "name": "Office World", "name_ar": "شركة عالم المكاتب", "phone": "+966-11-234-5678"},
            {"id": 3, "name": "Electronics Hub", "name_ar": "مركز الإلكترونيات", "phone": "+966-11-345-6789"}
        ]
        
        self.products = [
            {"id": 1, "barcode": "1234567890123", "name": "Business Laptop", "name_ar": "لابتوب الأعمال", "category": "Electronics", "supplier_id": 1, "price": 1200, "stock": 45, "min_stock": 10},
            {"id": 2, "barcode": "1234567890124", "name": "Wireless Mouse", "name_ar": "فأرة لاسلكية", "category": "Electronics", "supplier_id": 2, "price": 35, "stock": 150, "min_stock": 30},
            {"id": 3, "barcode": "1234567890125", "name": "Mechanical Keyboard", "name_ar": "لوحة مفاتيح ميكانيكية", "category": "Electronics", "supplier_id": 3, "price": 120, "stock": 60, "min_stock": 15}
        ]
        
        self.customers = [
            {"id": 1, "name": "John Smith", "name_ar": "جون سميث", "phone": "******-1234", "balance": 1250, "type": "RETAIL"},
            {"id": 2, "name": "ABC Corporation", "name_ar": "شركة ABC", "phone": "******-5678", "balance": 8750, "type": "WHOLESALE"},
            {"id": 3, "name": "Ahmed Al-Rashid", "name_ar": "أحمد الراشد", "phone": "+966-50-123-4567", "balance": 2500, "type": "SHOP_OWNER"}
        ]
        
        self.sales = []
        
        # Save sample data
        self.save_data()
        print("✅ تم إنشاء البيانات النموذجية")
        print("✅ Sample data created")

    def save_data(self):
        """Save data to file"""
        try:
            data = {
                'suppliers': self.suppliers,
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            print("💾 تم حفظ البيانات")
            print("💾 Data saved")
            
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def create_interface(self):
        """Create main interface"""
        # Main container
        main_container = tk.Frame(self.root, bg='#f0f8ff')
        main_container.pack(fill='both', expand=True)

        # Sidebar
        self.sidebar = tk.Frame(main_container, bg='#2c3e50', width=250)
        self.sidebar.pack(side='left', fill='y')
        self.sidebar.pack_propagate(False)

        # Content area
        self.content_frame = tk.Frame(main_container, bg='white')
        self.content_frame.pack(side='right', fill='both', expand=True)

        # Create sidebar buttons
        self.create_sidebar()
        
        # Show dashboard by default
        self.show_dashboard()

    def create_sidebar(self):
        """Create sidebar with navigation buttons"""
        # Title
        title_label = tk.Label(
            self.sidebar,
            text="برنامج ناصر\\nNassar Program",
            font=('Arial', 16, 'bold'),
            bg='#2c3e50',
            fg='white'
        )
        title_label.pack(pady=20)

        # Navigation buttons
        buttons = [
            ("🏠 الرئيسية / Dashboard", self.show_dashboard),
            ("📦 المخزون / Inventory", self.show_inventory),
            ("👥 العملاء / Customers", self.show_customers),
            ("🏢 الموردين / Suppliers", self.show_suppliers),
            ("💰 المبيعات / Sales", self.show_sales),
            ("📊 التقارير / Reports", self.show_reports)
        ]

        for text, command in buttons:
            btn = tk.Button(
                self.sidebar,
                text=text,
                command=command,
                font=('Arial', 11, 'bold'),
                bg='#34495e',
                fg='white',
                relief='flat',
                padx=20,
                pady=10,
                anchor='w'
            )
            btn.pack(fill='x', padx=10, pady=5)

    def clear_content(self):
        """Clear content area"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        """Show dashboard"""
        self.clear_content()
        
        # Title
        title = tk.Label(
            self.content_frame,
            text="🏠 لوحة التحكم الرئيسية / Main Dashboard",
            font=('Arial', 20, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=20)

        # Stats frame
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(pady=20)

        # Statistics
        stats = [
            ("📦 المنتجات / Products", len(self.products), '#3498db'),
            ("👥 العملاء / Customers", len(self.customers), '#2ecc71'),
            ("🏢 الموردين / Suppliers", len(self.suppliers), '#e74c3c'),
            ("💰 المبيعات / Sales", len(self.sales), '#f39c12')
        ]

        for i, (text, count, color) in enumerate(stats):
            stat_frame = tk.Frame(stats_frame, bg=color, relief='raised', bd=2)
            stat_frame.grid(row=0, column=i, padx=20, pady=10, sticky='ew')

            tk.Label(
                stat_frame,
                text=str(count),
                font=('Arial', 24, 'bold'),
                bg=color,
                fg='white'
            ).pack(pady=10)

            tk.Label(
                stat_frame,
                text=text,
                font=('Arial', 12, 'bold'),
                bg=color,
                fg='white'
            ).pack(pady=(0, 10))

    def show_inventory(self):
        """Show inventory"""
        self.clear_content()
        
        # Title
        title = tk.Label(
            self.content_frame,
            text="📦 إدارة المخزون / Inventory Management",
            font=('Arial', 18, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=20)

        # Force reload button
        reload_btn = tk.Button(
            self.content_frame,
            text="🔄 إعادة تحميل البيانات / Force Reload Data",
            command=self.force_reload_data,
            font=('Arial', 12, 'bold'),
            bg='#dc2626',
            fg='white',
            relief='raised',
            bd=2
        )
        reload_btn.pack(pady=10)

        # Products table
        columns = ('ID', 'Barcode', 'Name', 'Category', 'Price', 'Stock')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=120, anchor='center')

        # Add products
        for product in self.products:
            tree.insert('', 'end', values=(
                product.get('id', ''),
                product.get('barcode', ''),
                f"{product.get('name', '')} | {product.get('name_ar', '')}",
                product.get('category', ''),
                f"${product.get('price', 0):.2f}",
                product.get('stock', 0)
            ))

        tree.pack(fill='both', expand=True, padx=20, pady=10)

    def show_customers(self):
        """Show customers"""
        self.clear_content()
        
        title = tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء / Customer Management",
            font=('Arial', 18, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=20)

        # Customers table
        columns = ('ID', 'Name', 'Phone', 'Type', 'Balance')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=150, anchor='center')

        # Add customers
        for customer in self.customers:
            tree.insert('', 'end', values=(
                customer.get('id', ''),
                f"{customer.get('name', '')} | {customer.get('name_ar', '')}",
                customer.get('phone', ''),
                customer.get('type', ''),
                f"${customer.get('balance', 0):.2f}"
            ))

        tree.pack(fill='both', expand=True, padx=20, pady=10)

    def show_suppliers(self):
        """Show suppliers"""
        self.clear_content()
        
        title = tk.Label(
            self.content_frame,
            text="🏢 إدارة الموردين / Supplier Management",
            font=('Arial', 18, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=20)

        # Suppliers table
        columns = ('ID', 'Name', 'Phone')
        tree = ttk.Treeview(self.content_frame, columns=columns, show='headings', height=15)

        for col in columns:
            tree.heading(col, text=col)
            tree.column(col, width=200, anchor='center')

        # Add suppliers
        for supplier in self.suppliers:
            tree.insert('', 'end', values=(
                supplier.get('id', ''),
                f"{supplier.get('name', '')} | {supplier.get('name_ar', '')}",
                supplier.get('phone', '')
            ))

        tree.pack(fill='both', expand=True, padx=20, pady=10)

    def show_sales(self):
        """Show sales"""
        self.clear_content()
        
        title = tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات / Sales Management",
            font=('Arial', 18, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=20)

        info_label = tk.Label(
            self.content_frame,
            text="قريباً: نظام المبيعات المتقدم\\nComing Soon: Advanced Sales System",
            font=('Arial', 14),
            bg='white',
            fg='#7f8c8d'
        )
        info_label.pack(pady=50)

    def show_reports(self):
        """Show reports"""
        self.clear_content()
        
        title = tk.Label(
            self.content_frame,
            text="📊 التقارير والإحصائيات / Reports & Statistics",
            font=('Arial', 18, 'bold'),
            bg='white',
            fg='#2c3e50'
        )
        title.pack(pady=20)

        info_label = tk.Label(
            self.content_frame,
            text="قريباً: تقارير مفصلة\\nComing Soon: Detailed Reports",
            font=('Arial', 14),
            bg='white',
            fg='#7f8c8d'
        )
        info_label.pack(pady=50)

    def force_reload_data(self):
        """Force reload data from file"""
        try:
            print("🔄 إعادة تحميل البيانات...")
            
            # Clear current data
            self.products = []
            self.customers = []
            self.suppliers = []
            self.sales = []
            
            # Reload from file
            self.init_data()
            
            # Show success message
            messagebox.showinfo(
                "إعادة تحميل البيانات / Data Reload",
                f"✅ تم إعادة تحميل البيانات بنجاح!\\n✅ Data reloaded successfully!\\n\\n"
                f"📊 المنتجات: {len(self.products)}\\n📊 Products: {len(self.products)}\\n"
                f"👥 العملاء: {len(self.customers)}\\n👥 Customers: {len(self.customers)}\\n"
                f"🏢 الموردين: {len(self.suppliers)}\\n🏢 Suppliers: {len(self.suppliers)}"
            )
            
            # Refresh current view
            self.show_inventory()
            
        except Exception as e:
            print(f"❌ Error in force_reload_data: {e}")
            messagebox.showerror("خطأ / Error", f"فشل في إعادة تحميل البيانات\\nFailed to reload data:\\n{str(e)}")

    def run(self):
        """Run the application"""
        try:
            self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
            self.root.mainloop()
        except Exception as e:
            print(f"❌ خطأ في تشغيل التطبيق: {e}")

    def on_closing(self):
        """Handle application closing"""
        try:
            print("💾 حفظ البيانات قبل الإغلاق...")
            self.save_data()
            print("✅ تم حفظ البيانات بنجاح")
        except Exception as e:
            print(f"⚠️ خطأ في حفظ البيانات: {e}")
        finally:
            self.root.destroy()

def main():
    """Main function"""
    try:
        print("🚀 بدء تشغيل برنامج ناصر للمحاسبة...")
        print("🚀 Starting Nassar Accounting Program...")
        
        app = ProTechSimpleWorking()
        app.run()
        
    except Exception as e:
        print(f"❌ خطأ في بدء التشغيل: {e}")
        print(f"❌ Startup error: {e}")
        import traceback
        traceback.print_exc()
        input("Press Enter to exit...")

if __name__ == "__main__":
    main()
'''
    
    # Write emergency fix to file
    emergency_file = r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_working_EMERGENCY.py"
    
    try:
        with open(emergency_file, 'w', encoding='utf-8') as f:
            f.write(emergency_fix)
        
        print(f"✅ تم إنشاء الإصلاح الطارئ: {emergency_file}")
        print(f"✅ Emergency fix created: {emergency_file}")
        return emergency_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الإصلاح الطارئ: {e}")
        return None

def main():
    """Main function"""
    print("🚨 مشغل الإصلاح الطارئ - برنامج ناصر")
    print("🚨 EMERGENCY FIX LAUNCHER - Nassar Program")
    print("=" * 60)
    
    # Create emergency fix
    emergency_file = create_emergency_fix()
    
    if not emergency_file:
        print("❌ فشل في إنشاء الإصلاح الطارئ")
        input("Press Enter to exit...")
        return
    
    print(f"\n🚀 تشغيل الإصلاح الطارئ...")
    print(f"🚀 Running emergency fix...")
    
    try:
        # Change to the directory
        work_dir = os.path.dirname(emergency_file)
        os.chdir(work_dir)
        
        # Run the emergency fix
        subprocess.run([sys.executable, "protech_simple_working_EMERGENCY.py"])
        
        print("✅ تم تشغيل الإصلاح الطارئ بنجاح")
        print("✅ Emergency fix ran successfully")
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل الإصلاح الطارئ: {e}")
        print(f"❌ Error running emergency fix: {e}")
    
    input("\nPress Enter to exit...")

if __name__ == "__main__":
    main()
