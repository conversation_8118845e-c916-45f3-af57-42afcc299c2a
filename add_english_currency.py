#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add English Currency Support
إضافة دعم العملات بالإنجليزية

English text currency support to avoid encoding issues
دعم العملات بنص إنجليزي لتجنب مشاكل الترميز
"""

import os
import shutil
from datetime import datetime

def add_english_currency():
    """إضافة دعم العملات بالإنجليزية"""
    try:
        print("💱 إضافة دعم العملات بالإنجليزية")
        print("💱 Adding English Currency Support")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.english_currency_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # English currency methods
        currency_methods = '''
    def get_usd_rate(self):
        """Get USD exchange rate"""
        return 89500  # 1 USD = 89,500 LBP
    
    def format_lbp(self, amount):
        """Format Lebanese Pound"""
        return f"{amount:,.0f} LBP"
    
    def format_usd(self, amount):
        """Format US Dollar"""
        return f"${amount:,.2f} USD"
    
    def lbp_to_usd(self, lbp_amount):
        """Convert LBP to USD"""
        return lbp_amount / self.get_usd_rate()
    
    def show_dual_currency_balance(self):
        """Show balance in LBP and USD"""
        try:
            self.report_title.config(text="Store Balance - LBP & USD")
            
            # Get data
            products = self.get_real_products_data()
            sales = self.get_real_sales_data()
            customers = self.get_real_customers_data()
            
            # Calculate inventory value in LBP
            inventory_cost_lbp = 0
            inventory_value_lbp = 0
            
            for product in products:
                stock = product.get('quantity', 0)
                cost_price = product.get('base_price', 0)
                sell_price = product.get('shop_owner_price', cost_price * 1.05)
                
                inventory_cost_lbp += stock * cost_price
                inventory_value_lbp += stock * sell_price
            
            # Convert to USD
            inventory_cost_usd = self.lbp_to_usd(inventory_cost_lbp)
            inventory_value_usd = self.lbp_to_usd(inventory_value_lbp)
            
            # Calculate sales
            sales_total_lbp = sum(sale.get('total', 0) for sale in sales)
            sales_total_usd = self.lbp_to_usd(sales_total_lbp)
            
            # Calculate customer balances
            customer_debt_lbp = 0
            customer_credit_lbp = 0
            
            for customer in customers:
                balance = customer.get('balance', 0)
                if balance > 0:
                    customer_credit_lbp += balance
                else:
                    customer_debt_lbp += abs(balance)
            
            customer_debt_usd = self.lbp_to_usd(customer_debt_lbp)
            customer_credit_usd = self.lbp_to_usd(customer_credit_lbp)
            
            # Calculate total worth
            total_assets_lbp = inventory_value_lbp + customer_credit_lbp + sales_total_lbp
            total_assets_usd = self.lbp_to_usd(total_assets_lbp)
            net_worth_lbp = total_assets_lbp - customer_debt_lbp
            net_worth_usd = self.lbp_to_usd(net_worth_lbp)
            
            # Create report
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            usd_rate = self.get_usd_rate()
            
            report = "Store Balance Report - Lebanese Pound & US Dollar\\n"
            report += "="*60 + "\\n\\n"
            report += "Date: " + current_time + "\\n"
            report += "Exchange Rate: 1 USD = " + f"{usd_rate:,}" + " LBP\\n\\n"
            
            # Inventory section
            report += "INVENTORY:\\n"
            report += "-"*30 + "\\n"
            report += "Total Products: " + str(len(products)) + "\\n\\n"
            
            report += "Cost Value:\\n"
            report += "• " + self.format_lbp(inventory_cost_lbp) + "\\n"
            report += "• " + self.format_usd(inventory_cost_usd) + "\\n\\n"
            
            report += "Selling Value:\\n"
            report += "• " + self.format_lbp(inventory_value_lbp) + "\\n"
            report += "• " + self.format_usd(inventory_value_usd) + "\\n\\n"
            
            profit_lbp = inventory_value_lbp - inventory_cost_lbp
            profit_usd = self.lbp_to_usd(profit_lbp)
            report += "Expected Profit:\\n"
            report += "• " + self.format_lbp(profit_lbp) + "\\n"
            report += "• " + self.format_usd(profit_usd) + "\\n\\n"
            
            # Sales section
            report += "SALES:\\n"
            report += "-"*30 + "\\n"
            report += "Total Invoices: " + str(len(sales)) + "\\n\\n"
            
            report += "Total Sales:\\n"
            report += "• " + self.format_lbp(sales_total_lbp) + "\\n"
            report += "• " + self.format_usd(sales_total_usd) + "\\n\\n"
            
            # Customers section
            report += "CUSTOMERS:\\n"
            report += "-"*30 + "\\n"
            report += "Total Customers: " + str(len(customers)) + "\\n\\n"
            
            report += "Customer Debts:\\n"
            report += "• " + self.format_lbp(customer_debt_lbp) + "\\n"
            report += "• " + self.format_usd(customer_debt_usd) + "\\n\\n"
            
            report += "Customer Credits:\\n"
            report += "• " + self.format_lbp(customer_credit_lbp) + "\\n"
            report += "• " + self.format_usd(customer_credit_usd) + "\\n\\n"
            
            # Total balance
            report += "="*60 + "\\n"
            report += "TOTAL STORE BALANCE:\\n"
            report += "="*60 + "\\n\\n"
            
            report += "Net Store Worth:\\n"
            report += "• " + self.format_lbp(net_worth_lbp) + "\\n"
            report += "• " + self.format_usd(net_worth_usd) + "\\n\\n"
            
            # Analysis
            report += "FINANCIAL ANALYSIS:\\n"
            report += "-"*30 + "\\n"
            
            if net_worth_usd >= 10000:
                report += "Status: Excellent (Over $10,000)\\n"
            elif net_worth_usd >= 5000:
                report += "Status: Good ($5,000 - $10,000)\\n"
            elif net_worth_usd >= 1000:
                report += "Status: Average ($1,000 - $5,000)\\n"
            else:
                report += "Status: Needs Improvement (Under $1,000)\\n"
            
            # Recommendations
            report += "\\nRECOMMENDations:\\n"
            report += "-"*30 + "\\n"
            
            if customer_debt_lbp > customer_credit_lbp:
                report += "• Follow up on customer debts\\n"
            
            if net_worth_usd < 5000:
                report += "• Work on increasing capital\\n"
            
            report += "• Monitor USD exchange rate daily\\n"
            report += "• Update prices based on exchange rate\\n"
            report += "• Keep some profits in USD\\n"
            report += "• Review this report monthly\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
        except Exception as e:
            print("Error showing currency report:", str(e))
            error_msg = "Error loading currency report\\n"
            error_msg += "Please check data and try again"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_msg)
'''
        
        # Add currency methods before the last method
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + currency_methods + content[last_method:]
        else:
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + currency_methods + content[last_method:]
        
        # Add currency button with English text
        if "btn6 = tk.Button(sidebar, text=\"رصيد المحل\"" in content:
            btn6_pos = content.find("btn6.pack(pady=3, padx=10, fill='x')")
            if btn6_pos != -1:
                btn6_end = content.find("\\n", btn6_pos) + 1
                
                new_button = '''
            btn_curr = tk.Button(sidebar, text="LBP & USD", 
                               font=("Arial", 10), bg='#e67e22', fg='white',
                               width=18, height=2, command=self.show_dual_currency_balance)
            btn_curr.pack(pady=3, padx=10, fill='x')
'''
                
                content = content[:btn6_end] + new_button + content[btn6_end:]
                print("✅ تم إضافة زر LBP & USD")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة دعم العملات بالإنجليزية")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة دعم العملات: {e}")
        return False

def main():
    """Main function"""
    print("💱 إضافة دعم الليرة اللبنانية والدولار")
    print("💱 Adding Lebanese Pound and USD Support")
    print("="*60)
    
    if add_english_currency():
        print("\n🎉 تم إضافة دعم العملات بنجاح!")
        
        print("\n💱 العملات المدعومة:")
        print("• 🇱🇧 الليرة اللبنانية (LBP)")
        print("• 🇺🇸 الدولار الأمريكي (USD)")
        
        print("\n📊 التقرير الجديد:")
        print("• LBP & USD")
        print("• عرض القيم بالعملتين")
        print("• تحويل تلقائي")
        print("• تحليل مالي بالإنجليزية")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. انقر على 'LBP & USD'")
        print("4. استعرض التقرير")
        
        print("\n💡 الميزات:")
        print("• سعر صرف: 1 USD = 89,500 LBP")
        print("• تحويل دقيق بين العملات")
        print("• تقرير بالإنجليزية")
        print("• تحليل مالي شامل")
        
    else:
        print("\n❌ فشل في إضافة دعم العملات")

if __name__ == "__main__":
    main()
