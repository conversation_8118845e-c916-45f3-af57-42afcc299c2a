#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Double Click Traceback Issue
إصلاح مشكلة Traceback عند النقر المزدوج

Fix the Traceback error that occurs when double-clicking ProTech
إصلاح خطأ Traceback الذي يحدث عند النقر المزدوج على ProTech
"""

import os
import sys
import shutil
from datetime import datetime

def create_self_contained_protech():
    """Create a self-contained version that works with double-click"""
    try:
        print("🔧 إنشاء نسخة مستقلة من ProTech للنقر المزدوج")
        print("🔧 Creating Self-Contained ProTech for Double-Click")
        print("="*60)
        
        # Find the original ProTech file
        possible_locations = [
            "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program\\protech_simple_working.py",
            "C:\\Users\\<USER>\\Documents\\augment-projects\\protech\\protech_simple_working.py",
            "protech_simple_working.py"
        ]
        
        original_file = None
        for location in possible_locations:
            if os.path.exists(location):
                original_file = location
                print(f"✅ وجد الملف الأصلي: {location}")
                break
        
        if not original_file:
            print("❌ لم يتم العثور على الملف الأصلي")
            return False
        
        # Read the original file
        with open(original_file, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # Create the self-contained version
        self_contained_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Self-Contained Version
نسخة ProTech المستقلة

Self-contained version that works with double-click
نسخة مستقلة تعمل مع النقر المزدوج
"""

import os
import sys
import json
from datetime import datetime

# Fix working directory issue
def fix_working_directory():
    """Fix working directory to script location"""
    try:
        # Get the directory where this script is located
        script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Change to script directory
        os.chdir(script_dir)
        
        print(f"📁 تم تعيين المجلد: {script_dir}")
        return script_dir
    except Exception as e:
        print(f"❌ خطأ في تعيين المجلد: {e}")
        return None

# Fix encoding issues
def setup_encoding():
    """Setup proper encoding for console output"""
    try:
        # Set UTF-8 encoding
        import locale
        import codecs
        
        # Set environment variables
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        
        # Try to set console encoding
        try:
            sys.stdout.reconfigure(encoding='utf-8')
            sys.stderr.reconfigure(encoding='utf-8')
        except:
            pass
        
        return True
    except Exception as e:
        print(f"تحذير: مشكلة في الترميز: {e}")
        return False

# Safe print function
def safe_print(*args, **kwargs):
    """Safe print function that handles encoding issues"""
    try:
        print(*args, **kwargs)
    except UnicodeEncodeError:
        try:
            # Convert to ASCII-safe strings
            safe_args = []
            for arg in args:
                try:
                    safe_args.append(str(arg).encode('ascii', 'replace').decode('ascii'))
                except:
                    safe_args.append(repr(arg))
            print(*safe_args, **kwargs)
        except:
            print("Message printed (encoding issue)")

# Create missing data files
def create_missing_files():
    """Create missing data files"""
    try:
        # Create data file if missing
        data_file = "protech_simple_data.json"
        if not os.path.exists(data_file):
            default_data = {
                "suppliers": [],
                "products": [],
                "customers": [],
                "sales": [],
                "settings": {
                    "auto_save": False,
                    "language": "ar"
                }
            }
            
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(default_data, f, ensure_ascii=False, indent=2)
            
            safe_print(f"✅ تم إنشاء ملف البيانات: {data_file}")
        
        # Create config file if missing
        config_file = "protech_config.json"
        if not os.path.exists(config_file):
            default_config = {
                "version": "1.0",
                "double_click_mode": True,
                "safe_mode": True
            }
            
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, ensure_ascii=False, indent=2)
            
            safe_print(f"✅ تم إنشاء ملف الإعدادات: {config_file}")
        
        return True
    except Exception as e:
        safe_print(f"❌ خطأ في إنشاء الملفات: {e}")
        return False

# Main initialization
def initialize_protech():
    """Initialize ProTech for double-click execution"""
    try:
        safe_print("🚀 بدء تشغيل ProTech...")
        safe_print("🚀 Starting ProTech...")
        
        # Setup encoding
        setup_encoding()
        
        # Fix working directory
        script_dir = fix_working_directory()
        if not script_dir:
            safe_print("❌ فشل في تعيين المجلد")
            return False
        
        # Create missing files
        if not create_missing_files():
            safe_print("⚠️ تحذير: مشكلة في إنشاء الملفات")
        
        safe_print("✅ تم تهيئة ProTech بنجاح")
        return True
        
    except Exception as e:
        safe_print(f"❌ خطأ في تهيئة ProTech: {e}")
        return False

# Initialize before importing the main code
if __name__ == "__main__":
    if not initialize_protech():
        safe_print("❌ فشل في تهيئة ProTech")
        input("اضغط Enter للخروج...")
        sys.exit(1)

# Now include the original ProTech code with modifications
''' + original_content.replace('if __name__ == "__main__":', '''
# Modified main execution
if __name__ == "__main__":
    try:
        # The initialization is already done above
        # Just run the main ProTech code
''')
        
        # Save the self-contained version
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        if not os.path.exists(desktop_path):
            os.makedirs(desktop_path)
        
        self_contained_file = os.path.join(desktop_path, "ProTech_DoubleClick.py")
        
        with open(self_contained_file, 'w', encoding='utf-8') as f:
            f.write(self_contained_code)
        
        print(f"✅ تم إنشاء النسخة المستقلة: {self_contained_file}")
        
        # Copy data files if they exist
        data_files = ["protech_simple_data.json", "protech_config.json"]
        
        for data_file in data_files:
            source_path = None
            for location in [os.path.dirname(original_file), os.getcwd()]:
                potential_source = os.path.join(location, data_file)
                if os.path.exists(potential_source):
                    source_path = potential_source
                    break
            
            if source_path:
                dest_path = os.path.join(desktop_path, data_file)
                try:
                    shutil.copy2(source_path, dest_path)
                    print(f"✅ تم نسخ: {data_file}")
                except Exception as e:
                    print(f"⚠️ فشل في نسخ {data_file}: {e}")
        
        return self_contained_file
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة المستقلة: {e}")
        return None

def create_simple_launcher():
    """Create a simple launcher that always works"""
    try:
        print("\n🚀 إنشاء مشغل بسيط...")
        
        launcher_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple ProTech Launcher
مشغل ProTech البسيط
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def main():
    try:
        # Get script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        # Look for ProTech files
        protech_files = [
            "ProTech_DoubleClick.py",
            "protech_simple_working.py"
        ]
        
        protech_file = None
        for file in protech_files:
            if os.path.exists(file):
                protech_file = file
                break
        
        if not protech_file:
            messagebox.showerror("خطأ", "لم يتم العثور على ملف ProTech")
            return
        
        # Launch ProTech
        subprocess.Popen([sys.executable, protech_file])
        
    except Exception as e:
        try:
            messagebox.showerror("خطأ", f"فشل في تشغيل ProTech:\\n{e}")
        except:
            print(f"Error: {e}")
            input("Press Enter to exit...")

if __name__ == "__main__":
    main()
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        launcher_file = os.path.join(desktop_path, "تشغيل_ProTech_بسيط.py")
        
        with open(launcher_file, 'w', encoding='utf-8') as f:
            f.write(launcher_code)
        
        print(f"✅ تم إنشاء المشغل البسيط: {launcher_file}")
        return launcher_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء المشغل البسيط: {e}")
        return None

def create_batch_launcher():
    """Create a batch launcher that handles all issues"""
    try:
        print("\n📄 إنشاء مشغل Batch محسن...")
        
        batch_content = '''@echo off
chcp 65001 > nul
title ProTech - نظام المحاسبة المتقدم

echo.
echo ========================================
echo        ProTech - نظام المحاسبة
echo ========================================
echo.

cd /d "%~dp0"

echo المجلد الحالي: %CD%
echo.

echo فحص الملفات...
if exist "ProTech_DoubleClick.py" (
    echo ✓ تم العثور على ProTech_DoubleClick.py
    set PROTECH_FILE=ProTech_DoubleClick.py
) else if exist "protech_simple_working.py" (
    echo ✓ تم العثور على protech_simple_working.py
    set PROTECH_FILE=protech_simple_working.py
) else (
    echo ✗ لم يتم العثور على ملف ProTech
    echo.
    echo تأكد من وجود أحد الملفات التالية:
    echo - ProTech_DoubleClick.py
    echo - protech_simple_working.py
    echo.
    pause
    exit /b 1
)

echo.
echo تشغيل %PROTECH_FILE%...
echo.

python "%PROTECH_FILE%"

if errorlevel 1 (
    echo.
    echo ========================================
    echo حدث خطأ في تشغيل ProTech
    echo ========================================
    echo.
    echo محاولة تشغيل بديل...
    
    python -u "%PROTECH_FILE%"
    
    if errorlevel 1 (
        echo.
        echo فشل التشغيل البديل أيضاً
        echo.
        echo فتح الملف للمراجعة...
        notepad "%PROTECH_FILE%"
    )
) else (
    echo.
    echo ========================================
    echo تم إغلاق ProTech بنجاح
    echo ========================================
)

echo.
echo اضغط أي مفتاح للخروج...
pause > nul
'''
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        batch_file = os.path.join(desktop_path, "تشغيل_ProTech_محسن.bat")
        
        with open(batch_file, 'w', encoding='utf-8') as f:
            f.write(batch_content)
        
        print(f"✅ تم إنشاء مشغل Batch محسن: {batch_file}")
        return batch_file
        
    except Exception as e:
        print(f"❌ فشل في إنشاء مشغل Batch: {e}")
        return None

def main():
    """Main function"""
    print("🔧 إصلاح مشكلة Traceback عند النقر المزدوج")
    print("🔧 Fixing Double-Click Traceback Issue")
    print("="*70)
    
    print("\n💡 المشكلة:")
    print("عندما تضغط مرتين على ملف Python، يتم تشغيله من مجلد مختلف")
    print("مما يسبب مشاكل في العثور على الملفات المطلوبة")
    
    created_files = []
    
    # Create self-contained version
    self_contained = create_self_contained_protech()
    if self_contained:
        created_files.append(("النسخة المستقلة", self_contained))
    
    # Create simple launcher
    simple_launcher = create_simple_launcher()
    if simple_launcher:
        created_files.append(("المشغل البسيط", simple_launcher))
    
    # Create batch launcher
    batch_launcher = create_batch_launcher()
    if batch_launcher:
        created_files.append(("مشغل Batch", batch_launcher))
    
    # Summary
    print("\n" + "="*70)
    print("📊 ملخص الحلول المنشأة:")
    
    if created_files:
        print(f"✅ تم إنشاء {len(created_files)} حل:")
        for i, (name, file_path) in enumerate(created_files, 1):
            print(f"  {i}. {name}: {os.path.basename(file_path)}")
    else:
        print("❌ لم يتم إنشاء أي حلول")
    
    print("\n🚀 طرق التشغيل الجديدة:")
    print("1. انقر مزدوج على 'ProTech_DoubleClick.py' - النسخة المستقلة")
    print("2. انقر مزدوج على 'تشغيل_ProTech_بسيط.py' - المشغل البسيط")
    print("3. انقر مزدوج على 'تشغيل_ProTech_محسن.bat' - مشغل Batch")
    
    print("\n💡 الفرق بين الحلول:")
    print("• النسخة المستقلة: تحل مشاكل المجلد والترميز تلقائياً")
    print("• المشغل البسيط: واجهة بسيطة لتشغيل ProTech")
    print("• مشغل Batch: يعرض رسائل مفصلة ويتعامل مع الأخطاء")
    
    print("\n🎯 التوصية:")
    print("استخدم 'ProTech_DoubleClick.py' للحصول على أفضل تجربة")
    
    print("\n🎉 تم حل مشكلة Traceback عند النقر المزدوج!")

if __name__ == "__main__":
    main()
