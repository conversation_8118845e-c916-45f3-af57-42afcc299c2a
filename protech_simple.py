#!/usr/bin/env python3
"""
ProTech Accounting System - Simplified Working Version
نظام ProTech للمحاسبة - نسخة مبسطة تعمل
"""

from flask import Flask, render_template_string, jsonify
from datetime import datetime

app = Flask(__name__)
app.secret_key = 'protech-simple-key'

# Simple data
products = [
    {'id': 1, 'name': 'Laptop', 'name_ar': 'لابتوب', 'stock': 50, 'price': 1000},
    {'id': 2, 'name': 'Mouse', 'name_ar': 'فأرة', 'stock': 200, 'price': 25},
    {'id': 3, 'name': 'Keyboard', 'name_ar': 'لوحة مفاتيح', 'stock': 5, 'price': 75}
]

customers = [
    {'id': 1, 'name': '<PERSON>', 'name_ar': 'جون سميث', 'balance': 1250},
    {'id': 2, 'name': 'ABC Corp', 'name_ar': 'شركة ABC', 'balance': 8750}
]

# HTML Template
MAIN_TEMPLATE = '''
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام ProTech للمحاسبة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .ltr { direction: ltr; text-align: left; }
        .rtl { direction: rtl; text-align: right; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="min-h-screen">
        <!-- Header -->
        <header class="bg-blue-600 text-white p-4">
            <div class="container mx-auto">
                <h1 class="text-2xl font-bold">🎉 نظام ProTech للمحاسبة</h1>
                <p class="text-blue-100">ProTech Accounting System</p>
            </div>
        </header>

        <!-- Navigation -->
        <nav class="bg-blue-500 text-white p-2">
            <div class="container mx-auto">
                <div class="flex space-x-4 space-x-reverse">
                    <a href="/" class="px-3 py-2 rounded hover:bg-blue-400">🏠 الرئيسية</a>
                    <a href="/inventory" class="px-3 py-2 rounded hover:bg-blue-400">📦 المخزون</a>
                    <a href="/customers" class="px-3 py-2 rounded hover:bg-blue-400">👥 العملاء</a>
                    <a href="/api/products" class="px-3 py-2 rounded hover:bg-blue-400">🔌 API</a>
                </div>
            </div>
        </nav>

        <!-- Content -->
        <main class="container mx-auto p-4">
            {{ content }}
        </main>

        <!-- Footer -->
        <footer class="bg-gray-800 text-white p-4 mt-8">
            <div class="container mx-auto text-center">
                <p>© 2024 نظام ProTech للمحاسبة | ProTech Accounting System</p>
                <p class="text-sm text-gray-400">🐍 Python Flask | 🌍 Arabic & English</p>
            </div>
        </footer>
    </div>
</body>
</html>
'''

@app.route('/')
def dashboard():
    content = '''
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-bold text-blue-600 mb-2">📊 الإحصائيات</h3>
            <p>المنتجات: ''' + str(len(products)) + '''</p>
            <p>العملاء: ''' + str(len(customers)) + '''</p>
            <p>الحالة: نشط ✅</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-bold text-green-600 mb-2">💰 المبيعات</h3>
            <p>اليوم: $2,500</p>
            <p>هذا الشهر: $45,000</p>
            <p>النمو: +15% 📈</p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="text-lg font-bold text-orange-600 mb-2">⚠️ تنبيهات</h3>
            <p>مخزون منخفض: 1 منتج</p>
            <p>فواتير معلقة: 3</p>
            <p>مراجعة مطلوبة: 2</p>
        </div>
    </div>
    
    <div class="mt-8 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
        <h3 class="font-bold">🎉 نجح التشغيل!</h3>
        <p>نظام ProTech للمحاسبة يعمل بنجاح مع Python Flask</p>
        <p class="ltr">ProTech Accounting System is running successfully with Python Flask</p>
    </div>
    '''
    return render_template_string(MAIN_TEMPLATE, content=content)

@app.route('/inventory')
def inventory():
    content = '''
    <h2 class="text-2xl font-bold mb-4">📦 إدارة المخزون</h2>
    <div class="bg-white rounded-lg shadow overflow-hidden">
        <table class="min-w-full">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">المنتج</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الكمية</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">السعر</th>
                    <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase">الحالة</th>
                </tr>
            </thead>
            <tbody>
    '''
    
    for product in products:
        status = "🔴 منخفض" if product['stock'] < 10 else "✅ جيد"
        content += f'''
                <tr class="border-t">
                    <td class="px-6 py-4">
                        <div class="font-medium">{product['name']}</div>
                        <div class="text-sm text-gray-500">{product['name_ar']}</div>
                    </td>
                    <td class="px-6 py-4">{product['stock']}</td>
                    <td class="px-6 py-4">${product['price']}</td>
                    <td class="px-6 py-4">{status}</td>
                </tr>
        '''
    
    content += '''
            </tbody>
        </table>
    </div>
    '''
    return render_template_string(MAIN_TEMPLATE, content=content)

@app.route('/customers')
def customers_page():
    content = '''
    <h2 class="text-2xl font-bold mb-4">👥 إدارة العملاء</h2>
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
    '''
    
    for customer in customers:
        content += f'''
        <div class="bg-white p-6 rounded-lg shadow">
            <h3 class="font-bold text-lg">{customer['name']}</h3>
            <p class="text-gray-600">{customer['name_ar']}</p>
            <p class="mt-2">الرصيد: ${customer['balance']}</p>
            <div class="mt-4">
                <span class="bg-green-100 text-green-800 px-2 py-1 rounded text-sm">نشط</span>
            </div>
        </div>
        '''
    
    content += '</div>'
    return render_template_string(MAIN_TEMPLATE, content=content)

# API Routes
@app.route('/api/products')
def api_products():
    return jsonify({
        'success': True,
        'data': products,
        'count': len(products),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/customers')
def api_customers():
    return jsonify({
        'success': True,
        'data': customers,
        'count': len(customers),
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/stats')
def api_stats():
    return jsonify({
        'success': True,
        'data': {
            'total_products': len(products),
            'total_customers': len(customers),
            'low_stock_items': len([p for p in products if p['stock'] < 10]),
            'total_inventory_value': sum(p['stock'] * p['price'] for p in products)
        },
        'timestamp': datetime.now().isoformat()
    })

if __name__ == '__main__':
    print("🚀 بدء نظام ProTech المبسط...")
    print("🌐 الرابط: http://localhost:5000")
    print("✅ جاهز للاستخدام!")
    
    try:
        app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
    except Exception as e:
        print(f"❌ خطأ: {e}")
