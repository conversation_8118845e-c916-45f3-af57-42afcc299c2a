#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick Syntax Fixer for ProTech
مصلح التركيب السريع لـ ProTech

Quick fix for immediate syntax errors
إصلاح سريع لأخطاء التركيب الفورية
"""

import os
import re
import shutil
from datetime import datetime

def quick_fix_syntax_errors():
    """Quick fix for syntax errors"""
    try:
        print("🔧 إصلاح سريع لأخطاء التركيب...")
        
        # Create backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = f"protech_simple_working.py.quick_fix_backup_{timestamp}"
        shutil.copy2("protech_simple_working.py", backup_file)
        print(f"✅ نسخة احتياطية: {backup_file}")
        
        # Read file
        with open("protech_simple_working.py", 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 قراءة {len(lines)} سطر")
        
        # Fix common issues
        fixes_count = 0
        
        for i in range(len(lines)):
            line = lines[i]
            original_line = line
            
            # Fix 1: Remove invalid 'as' statements
            if ' as tk' in line and 'import' not in line:
                line = line.replace(' as tk', '')
                fixes_count += 1
            
            # Fix 2: Fix empty if/else/try blocks
            if line.strip().endswith(':') and i + 1 < len(lines):
                next_line = lines[i + 1] if i + 1 < len(lines) else ""
                
                # Check if next line is properly indented
                if next_line.strip() == "" or not next_line.startswith('    '):
                    # Add pass statement
                    indent = len(line) - len(line.lstrip())
                    pass_line = ' ' * (indent + 4) + 'pass\n'
                    lines.insert(i + 1, pass_line)
                    fixes_count += 1
            
            # Fix 3: Fix list comprehension syntax
            if ']:' in line and '[' in line and 'for' in line:
                line = line.replace(']:', ']')
                fixes_count += 1
            
            # Fix 4: Fix indentation issues
            if line.strip() and not line.startswith('#'):
                # Ensure proper indentation (multiple of 4)
                indent = len(line) - len(line.lstrip())
                if indent % 4 != 0:
                    new_indent = (indent // 4) * 4
                    line = ' ' * new_indent + line.lstrip()
                    fixes_count += 1
            
            # Fix 5: Fix missing colons
            stripped = line.strip()
            if (stripped.startswith('if ') or stripped.startswith('elif ') or 
                stripped.startswith('else') or stripped.startswith('try') or
                stripped.startswith('except') or stripped.startswith('finally') or
                stripped.startswith('for ') or stripped.startswith('while ') or
                stripped.startswith('def ') or stripped.startswith('class ')):
                
                if not stripped.endswith(':') and not stripped.endswith(':\\'):
                    line = line.rstrip() + ':\n'
                    fixes_count += 1
            
            # Update line if changed
            if line != original_line:
                lines[i] = line
        
        # Write fixed file
        with open("protech_simple_working.py", 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print(f"✅ تم تطبيق {fixes_count} إصلاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح السريع: {e}")
        return False

def test_compilation():
    """Test compilation after fix"""
    try:
        print("🧪 اختبار التجميع...")
        
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', 'protech_simple_working.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ التجميع نجح!")
            return True
        else:
            print("❌ التجميع فشل:")
            print(result.stderr)
            return False
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التجميع: {e}")
        return False

def fix_specific_line_issues():
    """Fix specific line issues found"""
    try:
        print("🎯 إصلاح مشاكل أسطر محددة...")
        
        with open("protech_simple_working.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix specific patterns
        fixes = [
            # Fix pass statements
            (r'pass as \w+', 'pass'),
            
            # Fix list comprehensions
            (r'\[([^\]]+)\]:', r'[\1]'),
            
            # Fix empty blocks
            (r':\s*\n(\s*)(def|class|if|elif|else|try|except|finally|for|while)', 
             r':\n\1pass\n\1\2'),
            
            # Fix bare except
            (r'except\s*:', 'except Exception:'),
            
            # Fix missing pass in empty functions
            (r'(def\s+\w+\([^)]*\):\s*\n)(\s*)(def|class|\Z)', 
             r'\1\2pass\n\2\3'),
        ]
        
        original_content = content
        
        for pattern, replacement in fixes:
            content = re.sub(pattern, replacement, content, flags=re.MULTILINE)
        
        if content != original_content:
            with open("protech_simple_working.py", 'w', encoding='utf-8') as f:
                f.write(content)
            print("✅ تم إصلاح المشاكل المحددة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح المشاكل المحددة: {e}")
        return False

def main():
    """Main quick fix function"""
    print("🔧 مصلح التركيب السريع لـ ProTech")
    print("🔧 ProTech Quick Syntax Fixer")
    print("="*50)
    
    try:
        # Step 1: Quick syntax fix
        if quick_fix_syntax_errors():
            print("✅ الإصلاح السريع مكتمل")
            
            # Step 2: Fix specific issues
            if fix_specific_line_issues():
                print("✅ إصلاح المشاكل المحددة مكتمل")
                
                # Step 3: Test compilation
                if test_compilation():
                    print("\n🎉 جميع أخطاء التركيب تم إصلاحها!")
                    print("🎉 All syntax errors fixed!")
                    return True
                else:
                    print("\n⚠️ لا تزال هناك أخطاء تركيب")
                    return False
        
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
