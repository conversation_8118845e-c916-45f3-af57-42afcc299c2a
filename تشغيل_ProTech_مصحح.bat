@echo off
title ProTech Fixed System - نظام ProTech المصحح
color 0A

echo.
echo ================================================================
echo    🔧 نظام ProTech المصحح - ProTech Fixed System
echo    🔧 Fixed version with bug fixes and optimizations
echo ================================================================
echo.

echo [1/4] فحص متطلبات النظام / Checking system requirements...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python غير مثبت / Python not installed
    echo يرجى تثبيت Python 3.8+ من https://python.org
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
) else (
    echo ✅ Python متوفر / Python available
)

echo.
echo [2/4] فحص المكتبات المطلوبة / Checking required libraries...
python -c "import tkinter, json, os, datetime, threading, time, gc, logging" >nul 2>&1
if errorlevel 1 (
    echo ❌ مكتبات مطلوبة مفقودة / Required libraries missing
    pause
    exit /b 1
) else (
    echo ✅ جميع المكتبات متوفرة / All libraries available
)

echo.
echo [3/4] إنشاء مجلدات النظام / Creating system directories...
if not exist "logs" mkdir logs
if not exist "backups" mkdir backups
echo ✅ تم إنشاء مجلدات النظام / System directories created

echo.
echo [4/4] تشغيل نظام ProTech المصحح / Starting ProTech Fixed System...
echo.
echo 🎯 الإصلاحات المطبقة / Applied Fixes:
echo    • إصلاح مشاكل الحفظ / Fixed save issues
echo    • إصلاح أخطاء واجهة المستخدم / Fixed UI errors
echo    • إصلاح الوظائف المفقودة / Fixed missing functions
echo    • تحسين الاستقرار / Improved stability
echo    • معالجة أخطاء محسنة / Enhanced error handling
echo.

echo 🚀 بدء التشغيل / Starting application...
echo.

python protech_fixed.py

echo.
if errorlevel 1 (
    echo ❌ حدث خطأ في تشغيل البرنامج / Error occurred while running the application
    echo.
    echo 🔧 خطوات استكشاف الأخطاء / Troubleshooting steps:
    echo    1. تأكد من وجود ملف protech_fixed.py
    echo    2. تحقق من سجل الأخطاء في protech_errors.log
    echo    3. تأكد من صحة البيانات في ملف protech_simple_data.json
    echo.
) else (
    echo ✅ تم إغلاق البرنامج بنجاح / Application closed successfully
    echo.
    echo 📊 النظام المصحح يعمل بشكل مستقر
    echo 📊 Fixed system running stably
)

echo.
echo ================================================================
echo    شكراً لاستخدام نظام ProTech المصحح
echo    Thank you for using ProTech Fixed System
echo ================================================================
echo.

pause
