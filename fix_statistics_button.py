#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Statistics Button
إصلاح زر الإحصائيات

Fix the statistics button syntax error
إصلاح خطأ التركيب في زر الإحصائيات
"""

import os
import shutil
from datetime import datetime

def fix_statistics_button():
    """إصلاح زر الإحصائيات"""
    try:
        print("🔧 إصلاح زر الإحصائيات")
        print("🔧 Fixing Statistics Button")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.fix_stats_btn_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and fix the button with syntax error
        if 'width=18, height=2, command=self.show_store_balance_usd)' in content:
            # This suggests there's a missing opening parenthesis
            # Let's find the problematic button and fix it
            
            # Look for btn6 definition
            import re
            
            # Find all btn6 definitions
            btn6_pattern = r'btn6 = tk\.Button\([^)]+\)'
            matches = list(re.finditer(btn6_pattern, content, re.DOTALL))
            
            if matches:
                for match in matches:
                    button_def = match.group()
                    print(f"Found btn6: {button_def[:100]}...")
                    
                    # Check if this is the problematic one
                    if 'show_store_balance_usd' in button_def:
                        # Replace with correct statistics button
                        new_button = '''btn6 = tk.Button(sidebar, text="Advanced Stats", 
                               font=("Arial", 10), bg='#16a085', fg='white', 
                               width=18, height=2, command=self.create_advanced_statistics_page)'''
                        
                        content = content.replace(button_def, new_button)
                        print("✅ تم إصلاح زر الإحصائيات")
                        break
            else:
                print("❌ لم يتم العثور على btn6")
                return False
        else:
            # Look for any btn6 with "رصيد المحل" or similar
            if 'btn6 = tk.Button(sidebar, text="رصيد المحل"' in content:
                # Find the complete button definition
                start_pos = content.find('btn6 = tk.Button(sidebar, text="رصيد المحل"')
                if start_pos != -1:
                    # Find the end of this button definition
                    lines = content[start_pos:].split('\n')
                    button_lines = []
                    for i, line in enumerate(lines):
                        button_lines.append(line)
                        if ')' in line and 'btn6' in button_lines[0]:
                            break
                    
                    old_button = '\n'.join(button_lines)
                    new_button = '''btn6 = tk.Button(sidebar, text="Advanced Stats", 
                               font=("Arial", 10), bg='#16a085', fg='white', 
                               width=18, height=2, command=self.create_advanced_statistics_page)'''
                    
                    content = content.replace(old_button, new_button)
                    print("✅ تم تحديث زر الإحصائيات")
                else:
                    print("❌ لم يتم العثور على بداية تعريف الزر")
                    return False
            else:
                print("❌ لم يتم العثور على زر رصيد المحل")
                return False
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح زر الإحصائيات")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح زر الإحصائيات: {e}")
        return False

def verify_statistics_system():
    """التحقق من نظام الإحصائيات"""
    try:
        print("\n🔍 التحقق من نظام الإحصائيات")
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('زر Advanced Stats', 'Advanced Stats' in content),
            ('استدعاء create_advanced_statistics_page', 'command=self.create_advanced_statistics_page' in content),
            ('تعريف create_advanced_statistics_page', 'def create_advanced_statistics_page(self):' in content),
            ('تعريف create_stats_cards', 'def create_stats_cards(self):' in content),
            ('تعريف create_interactive_table_section', 'def create_interactive_table_section(self):' in content),
            ('تعريف update_interactive_table', 'def update_interactive_table(self):' in content),
            ('تعريف create_overview_table', 'def create_overview_table(self):' in content),
            ('تعريف create_analysis_section', 'def create_analysis_section(self):' in content)
        ]
        
        all_good = True
        for check_name, result in checks:
            if result:
                print(f"✅ {check_name}")
            else:
                print(f"❌ {check_name}")
                all_good = False
        
        if all_good:
            print("\n🎉 جميع التحققات نجحت! نظام الإحصائيات جاهز")
        else:
            print("\n⚠️ بعض التحققات فشلت")
        
        return all_good
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح زر الإحصائيات في ProTech")
    print("🔧 Fixing Statistics Button in ProTech")
    print("="*60)
    
    if fix_statistics_button():
        print("\n🎉 تم إصلاح زر الإحصائيات بنجاح!")
        
        # Verify the system
        if verify_statistics_system():
            print("\n📊 نظام الإحصائيات المتقدم جاهز الآن!")
            
            print("\n🎯 الميزات المتاحة:")
            print("• 📈 صفحة إحصائيات متقدمة وتفاعلية")
            print("• 🎯 8 بطاقات إحصائيات سريعة ملونة")
            print("• 📋 5 أنواع جداول تفاعلية مختلفة")
            print("• 📊 تحليل الأعمال والرؤى الذكية")
            print("• 📤 تصدير البيانات إلى CSV")
            print("• 🔄 تحديث فوري للبيانات")
            
            print("\n📋 أنواع الجداول:")
            print("• 🏢 Business Overview - نظرة عامة")
            print("• 🛍️ Top Products - أفضل المنتجات")
            print("• 👥 Customer Analysis - تحليل العملاء")
            print("• 📈 Sales Trends - اتجاهات المبيعات")
            print("• 💰 Financial Summary - الملخص المالي")
            
            print("\n🎯 كيفية الاستخدام:")
            print("1. افتح برنامج ProTech")
            print("2. انقر على زر 'Advanced Stats' في الشريط الجانبي")
            print("3. استعرض بطاقات الإحصائيات السريعة")
            print("4. اختر نوع الجدول من القائمة المنسدلة")
            print("5. انقر على 'Update Table' لتحديث البيانات")
            print("6. انقر على 'Export' لتصدير الجدول")
            print("7. اقرأ تحليل الأعمال والتوصيات")
            
            print("\n💡 الميزات المتقدمة:")
            print("• نقاط الأداء والتقييم التلقائي")
            print("• مؤشرات الصحة المالية للأعمال")
            print("• توصيات ذكية لتحسين الأداء")
            print("• عرض بالليرة والدولار")
            print("• جداول قابلة للتمرير")
            print("• تحليل اتجاهات المبيعات")
        
    else:
        print("\n❌ فشل في إصلاح زر الإحصائيات")
    
    print("\n🔧 تم الانتهاء من الإصلاح")

if __name__ == "__main__":
    main()
