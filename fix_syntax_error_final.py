#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Syntax Error Final - ProTech
إصلاح خطأ التركيب النهائي - ProTech
"""

import os
import shutil
from datetime import datetime

def create_backup():
    """Create backup"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.syntax_error_fix_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def fix_syntax_error():
    """Fix the syntax error in line 396"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📄 معالجة {len(lines)} سطر...")
        
        # البحث عن السطر 396 والمنطقة المحيطة
        for i in range(390, min(400, len(lines))):
            line = lines[i]
            
            # البحث عن المشكلة: except بدون try
            if 'except Exception as e:' in line:
                # فحص الأسطر السابقة للبحث عن try
                has_try = False
                for j in range(i-1, max(0, i-10), -1):
                    if 'try:' in lines[j]:
                        has_try = True
                        break
                    elif lines[j].strip().startswith('def ') or lines[j].strip().startswith('class '):
                        break
                
                if not has_try:
                    # إضافة try: قبل except
                    indent = '        '  # نفس مستوى المسافة البادئة
                    lines.insert(i, f'{indent}try:\n')
                    print(f"🔧 تم إضافة try: قبل السطر {i+1}")
                    break
        
        # كتابة الملف المصحح
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print("✅ تم إصلاح خطأ التركيب")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def test_syntax():
    """Test file syntax"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        compile(content, 'protech_simple_working.py', 'exec')
        print("✅ اختبار التركيب نجح")
        return True
        
    except SyntaxError as e:
        print(f"❌ خطأ في التركيب: {e}")
        print(f"السطر {e.lineno}: {e.text}")
        return False
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def test_program_execution():
    """Test program execution"""
    try:
        import subprocess
        import sys
        
        print("🧪 اختبار تشغيل البرنامج...")
        
        # اختبار التشغيل لمدة قصيرة
        result = subprocess.run([
            sys.executable, '-c', 
            'import protech_simple_working; print("✅ الاستيراد نجح")'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ اختبار التشغيل نجح")
            return True
        else:
            print(f"❌ خطأ في التشغيل: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار التشغيل: {e}")
        return False

def create_display_launcher():
    """Create launcher that forces display"""
    launcher_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Display Launcher
مشغل عرض ProTech
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def show_protech():
    """Show ProTech on screen"""
    try:
        print("🚀 تشغيل ProTech وإظهاره...")
        
        # Check if file exists
        if not os.path.exists('protech_simple_working.py'):
            messagebox.showerror("خطأ", "ملف ProTech غير موجود!")
            return False
        
        # Test syntax first
        try:
            with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
                content = f.read()
            compile(content, 'protech_simple_working.py', 'exec')
            print("✅ اختبار التركيب نجح")
        except Exception as e:
            messagebox.showerror("خطأ في التركيب", f"خطأ في تركيب الملف:\\n{e}")
            return False
        
        # Launch ProTech with focus
        print("📺 إظهار ProTech على الشاشة...")
        
        # Use different methods to ensure visibility
        if sys.platform == 'win32':
            # Windows specific launch
            subprocess.Popen([
                sys.executable, 'protech_simple_working.py'
            ], creationflags=subprocess.CREATE_NEW_CONSOLE)
        else:
            # Unix/Linux launch
            subprocess.Popen([sys.executable, 'protech_simple_working.py'])
        
        print("✅ تم تشغيل ProTech")
        return True
        
    except Exception as e:
        messagebox.showerror("خطأ في التشغيل", f"فشل في تشغيل ProTech:\\n{e}")
        return False

def main():
    """Main function"""
    # Create a simple launcher window
    root = tk.Tk()
    root.title("ProTech Display Launcher")
    root.geometry("500x300")
    root.configure(bg='#f0f8ff')
    
    # Center the window
    root.eval('tk::PlaceWindow . center')
    
    # Title
    title_label = tk.Label(root, text="🧮 ProTech Display Launcher", 
                          font=('Arial', 18, 'bold'), bg='#f0f8ff', fg='#1e3a8a')
    title_label.pack(pady=30)
    
    # Description
    desc_label = tk.Label(root, text="مشغل ProTech مع ضمان الظهور على الشاشة", 
                         font=('Arial', 12), bg='#f0f8ff', fg='#374151')
    desc_label.pack(pady=10)
    
    # Launch button
    launch_btn = tk.Button(root, text="🚀 تشغيل وإظهار ProTech", 
                          command=lambda: [show_protech(), root.destroy()], 
                          bg='#4CAF50', fg='white', font=('Arial', 14, 'bold'), 
                          width=20, height=2)
    launch_btn.pack(pady=20)
    
    # Test button
    test_btn = tk.Button(root, text="🧪 اختبار الملف فقط", 
                        command=test_file, bg='#2196F3', fg='white', 
                        font=('Arial', 12), width=20)
    test_btn.pack(pady=10)
    
    # Exit button
    exit_btn = tk.Button(root, text="خروج", command=root.quit, 
                        bg='#f44336', fg='white', font=('Arial', 10))
    exit_btn.pack(pady=10)
    
    # Status label
    status_label = tk.Label(root, text="جاهز للتشغيل", 
                           font=('Arial', 10), bg='#f0f8ff', fg='#666')
    status_label.pack(pady=10)
    
    root.mainloop()

def test_file():
    """Test file only"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        compile(content, 'protech_simple_working.py', 'exec')
        messagebox.showinfo("نجح", "الملف يعمل بدون أخطاء!")
    except Exception as e:
        messagebox.showerror("خطأ", f"خطأ في الملف:\\n{e}")

if __name__ == "__main__":
    main()
'''
    
    with open('show_protech.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل العرض: show_protech.py")

def main():
    """Main function"""
    print("🔧 إصلاح خطأ التركيب النهائي - ProTech")
    print("🔧 Fix Syntax Error Final - ProTech")
    print("=" * 60)
    
    try:
        # Step 1: Backup
        create_backup()
        
        # Step 2: Fix syntax error
        print("\n🔧 إصلاح خطأ التركيب...")
        if fix_syntax_error():
            print("✅ تم إصلاح خطأ التركيب")
        
        # Step 3: Test syntax
        print("\n🧪 اختبار التركيب...")
        if test_syntax():
            print("✅ التركيب صحيح")
        else:
            print("❌ لا يزال هناك خطأ في التركيب")
            return False
        
        # Step 4: Test execution
        print("\n🧪 اختبار التشغيل...")
        if test_program_execution():
            print("✅ اختبار التشغيل نجح")
        
        # Step 5: Create display launcher
        print("\n🚀 إنشاء مشغل العرض...")
        create_display_launcher()
        
        print("\n" + "=" * 60)
        print("✅ تم إصلاح جميع مشاكل التركيب!")
        print("✅ All syntax issues fixed!")
        print("=" * 60)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إصلاح خطأ except بدون try")
        print("• اختبار التركيب والتشغيل")
        print("• إنشاء مشغل عرض محسن")
        
        print("\n🚀 الآن يمكنك:")
        print("1. تشغيل show_protech.py لإظهار البرنامج")
        print("2. النقر المزدوج على protech_simple_working.py")
        print("3. استخدام launch_protech.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
