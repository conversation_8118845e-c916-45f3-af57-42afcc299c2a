import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting database seed...');

  // Create default admin user
  const hashedPassword = await bcrypt.hash('admin123', 10);
  
  const adminUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      username: 'admin',
      password: hashedPassword,
      firstName: 'System',
      lastName: 'Administrator',
      role: 'ADMIN',
      isActive: true,
    },
  });

  console.log('✅ Created admin user:', adminUser.email);

  // Create sample categories
  const electronicsCategory = await prisma.category.upsert({
    where: { id: 'electronics' },
    update: {},
    create: {
      id: 'electronics',
      name: 'Electronics',
      description: 'Electronic devices and components',
      isActive: true,
    },
  });

  const computersCategory = await prisma.category.upsert({
    where: { id: 'computers' },
    update: {},
    create: {
      id: 'computers',
      name: 'Computers',
      description: 'Computer hardware and accessories',
      parentId: electronicsCategory.id,
      isActive: true,
    },
  });

  const officeCategory = await prisma.category.upsert({
    where: { id: 'office' },
    update: {},
    create: {
      id: 'office',
      name: 'Office Supplies',
      description: 'Office equipment and supplies',
      isActive: true,
    },
  });

  console.log('✅ Created categories');

  // Create sample suppliers
  const techSupplier = await prisma.supplier.upsert({
    where: { code: 'TECH001' },
    update: {},
    create: {
      code: 'TECH001',
      name: 'Tech Solutions Inc.',
      email: '<EMAIL>',
      phone: '******-0123',
      address: '123 Tech Street',
      city: 'Tech City',
      country: 'USA',
      taxNumber: 'TAX123456',
      paymentTerms: 'Net 30',
      isActive: true,
    },
  });

  const officeSupplier = await prisma.supplier.upsert({
    where: { code: 'OFF001' },
    update: {},
    create: {
      code: 'OFF001',
      name: 'Office World Ltd.',
      email: '<EMAIL>',
      phone: '******-0456',
      address: '456 Office Ave',
      city: 'Business City',
      country: 'USA',
      taxNumber: 'TAX789012',
      paymentTerms: 'Net 15',
      isActive: true,
    },
  });

  console.log('✅ Created suppliers');

  // Create sample customers
  const retailCustomer = await prisma.customer.upsert({
    where: { code: 'CUST001' },
    update: {},
    create: {
      code: 'CUST001',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '******-1234',
      address: '789 Customer Lane',
      city: 'Customer City',
      country: 'USA',
      creditLimit: 5000,
      currentBalance: 0,
      category: 'RETAIL',
      priceLevel: 1,
      isActive: true,
    },
  });

  const wholesaleCustomer = await prisma.customer.upsert({
    where: { code: 'CUST002' },
    update: {},
    create: {
      code: 'CUST002',
      name: 'ABC Corporation',
      email: '<EMAIL>',
      phone: '******-5678',
      address: '321 Corporate Blvd',
      city: 'Business District',
      country: 'USA',
      taxNumber: 'TAX345678',
      creditLimit: 25000,
      currentBalance: 0,
      category: 'WHOLESALE',
      priceLevel: 2,
      isActive: true,
    },
  });

  console.log('✅ Created customers');

  // Create sample products
  const laptop = await prisma.product.upsert({
    where: { code: 'LAPTOP001' },
    update: {},
    create: {
      code: 'LAPTOP001',
      name: 'Business Laptop Pro',
      description: 'High-performance laptop for business use',
      barcode: '1234567890123',
      categoryId: computersCategory.id,
      supplierId: techSupplier.id,
      unit: 'PCS',
      costPrice: 800,
      basePrice: 1000,
      priceLevel1: 1050, // Base + 5%
      priceLevel2: 1150, // Base + 15%
      priceLevel3: 1200, // Base + 20%
      priceLevel4: 1300, // Base + 30%
      currentStock: 50,
      minStock: 10,
      maxStock: 100,
      location: 'A1-01',
      isActive: true,
      trackInventory: true,
      allowNegative: false,
      weight: 2.5,
      dimensions: '35x25x2 cm',
    },
  });

  const mouse = await prisma.product.upsert({
    where: { code: 'MOUSE001' },
    update: {},
    create: {
      code: 'MOUSE001',
      name: 'Wireless Mouse',
      description: 'Ergonomic wireless mouse',
      barcode: '1234567890124',
      categoryId: computersCategory.id,
      supplierId: techSupplier.id,
      unit: 'PCS',
      costPrice: 15,
      basePrice: 25,
      priceLevel1: 26.25, // Base + 5%
      priceLevel2: 28.75, // Base + 15%
      priceLevel3: 30, // Base + 20%
      priceLevel4: 32.50, // Base + 30%
      currentStock: 200,
      minStock: 50,
      maxStock: 500,
      location: 'B2-15',
      isActive: true,
      trackInventory: true,
      allowNegative: false,
      weight: 0.1,
      dimensions: '12x7x4 cm',
    },
  });

  const notebook = await prisma.product.upsert({
    where: { code: 'NOTE001' },
    update: {},
    create: {
      code: 'NOTE001',
      name: 'Professional Notebook',
      description: 'A4 lined notebook for professional use',
      barcode: '1234567890125',
      categoryId: officeCategory.id,
      supplierId: officeSupplier.id,
      unit: 'PCS',
      costPrice: 3,
      basePrice: 5,
      priceLevel1: 5.25, // Base + 5%
      priceLevel2: 5.75, // Base + 15%
      priceLevel3: 6, // Base + 20%
      priceLevel4: 6.50, // Base + 30%
      currentStock: 1000,
      minStock: 200,
      maxStock: 2000,
      location: 'C3-20',
      isActive: true,
      trackInventory: true,
      allowNegative: false,
      weight: 0.3,
      dimensions: '21x29.7x1 cm',
    },
  });

  console.log('✅ Created products');

  // Create system settings
  const settings = [
    { key: 'company_name', value: 'ProTech Accounting', category: 'company' },
    { key: 'company_address', value: '123 Business Street, City, Country', category: 'company' },
    { key: 'company_phone', value: '******-0000', category: 'company' },
    { key: 'company_email', value: '<EMAIL>', category: 'company' },
    { key: 'tax_number', value: 'TAX000000', category: 'company' },
    { key: 'default_currency', value: 'USD', category: 'general' },
    { key: 'default_tax_rate', value: '0.15', category: 'general' },
    { key: 'default_language', value: 'en', category: 'general' },
    { key: 'barcode_prefix', value: 'PT', category: 'barcode' },
    { key: 'invoice_prefix', value: 'INV', category: 'numbering' },
    { key: 'purchase_prefix', value: 'PO', category: 'numbering' },
    { key: 'auto_generate_barcodes', value: 'true', category: 'barcode' },
    { key: 'track_inventory', value: 'true', category: 'inventory' },
    { key: 'allow_negative_stock', value: 'false', category: 'inventory' },
    { key: 'low_stock_threshold', value: '10', category: 'inventory' },
  ];

  for (const setting of settings) {
    await prisma.setting.upsert({
      where: { key: setting.key },
      update: { value: setting.value },
      create: setting,
    });
  }

  console.log('✅ Created system settings');

  // Create initial inventory movements for the products
  await prisma.inventoryMovement.create({
    data: {
      productId: laptop.id,
      userId: adminUser.id,
      type: 'IN',
      quantity: 50,
      unitCost: 800,
      totalCost: 40000,
      reason: 'Initial stock',
      referenceType: 'adjustment',
    },
  });

  await prisma.inventoryMovement.create({
    data: {
      productId: mouse.id,
      userId: adminUser.id,
      type: 'IN',
      quantity: 200,
      unitCost: 15,
      totalCost: 3000,
      reason: 'Initial stock',
      referenceType: 'adjustment',
    },
  });

  await prisma.inventoryMovement.create({
    data: {
      productId: notebook.id,
      userId: adminUser.id,
      type: 'IN',
      quantity: 1000,
      unitCost: 3,
      totalCost: 3000,
      reason: 'Initial stock',
      referenceType: 'adjustment',
    },
  });

  console.log('✅ Created inventory movements');

  console.log('🎉 Database seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
