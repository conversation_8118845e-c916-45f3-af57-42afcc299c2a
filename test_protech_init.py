#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os

class TestProTechInit:
    def __init__(self):
        print("🚀 DEBUG: Starting ProTech test initialization...")
        
        # Initialize data
        self.data_file = "protech_simple_data.json"
        self.products = []
        self.customers = []
        self.suppliers = []
        self.sales = []
        
        print(f"🔍 DEBUG: Data file path set to: {self.data_file}")
        
        # Test data loading
        self.init_data()
        
        # Test show_inventory simulation
        self.test_show_inventory()
    
    def init_data(self):
        """Initialize data storage with enhanced validation and error recovery"""
        try:
            print(f"🔍 DEBUG: Starting data initialization...")
            print(f"🔍 DEBUG: Checking for data file: {self.data_file}")
            print(f"🔍 DEBUG: File exists: {os.path.exists(self.data_file)}")
            
            if os.path.exists(self.data_file):
                print("📁 DEBUG: Data file found, loading...")
                
                # Load data with validation
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    print(f"🔍 DEBUG: Raw data loaded, keys: {list(data.keys())}")
                    
                    self.suppliers = data.get('suppliers', [])
                    self.products = data.get('products', [])
                    self.customers = data.get('customers', [])
                    self.sales = data.get('sales', [])
                    
                    print(f"🔍 DEBUG: Data assigned to variables:")
                    print(f"  - Suppliers: {len(self.suppliers)}")
                    print(f"  - Products: {len(self.products)}")
                    print(f"  - Customers: {len(self.customers)}")
                    print(f"  - Sales: {len(self.sales)}")

                print(f"✅ DEBUG: Data loaded successfully: {len(self.products)} products, {len(self.customers)} customers, {len(self.sales)} sales")
                    
                # Debug: Print first product if exists
                if len(self.products) > 0:
                    print(f"🔍 DEBUG: First product: {self.products[0].get('name', 'NO_NAME')}")
                    print(f"🔍 DEBUG: First product details: {self.products[0]}")

            else:
                print("⚠️ DEBUG: Data file not found")
                
        except Exception as e:
            print(f"❌ DEBUG: Error loading data: {e}")
            import traceback
            traceback.print_exc()
    
    def test_show_inventory(self):
        """Test show_inventory simulation"""
        print(f"\n🔍 DEBUG: Testing show_inventory simulation...")
        print(f"🔍 DEBUG: self.products length: {len(getattr(self, 'products', []))}")
        print(f"🔍 DEBUG: self.suppliers length: {len(getattr(self, 'suppliers', []))}")
        print(f"🔍 DEBUG: self.customers length: {len(getattr(self, 'customers', []))}")
        
        if hasattr(self, 'products') and len(self.products) > 0:
            print(f"🔍 DEBUG: First product: {self.products[0].get('name', 'NO_NAME')}")
            print(f"🔍 DEBUG: All products:")
            for i, product in enumerate(self.products):
                print(f"  {i+1}. {product.get('name', 'NO_NAME')} - {product.get('barcode', 'NO_BARCODE')}")
        else:
            print("🔍 DEBUG: No products found or products list is empty")

if __name__ == "__main__":
    test = TestProTechInit()
