#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import json
import os
import tkinter as tk
from tkinter import ttk

def test_inventory_direct():
    """Test inventory display directly"""
    print("🔍 Testing inventory display directly...")
    
    # Load data
    data_file = "protech_simple_data.json"
    products = []
    suppliers = []
    
    if os.path.exists(data_file):
        with open(data_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
            products = data.get('products', [])
            suppliers = data.get('suppliers', [])
    
    print(f"📊 Loaded {len(products)} products and {len(suppliers)} suppliers")
    
    # Create simple GUI to test display
    root = tk.Tk()
    root.title("Test Inventory Display")
    root.geometry("800x600")
    
    # Create treeview
    columns = ('Barcode', 'Name', 'Category', 'Supplier', 'Price', 'Stock')
    tree = ttk.Treeview(root, columns=columns, show='headings', height=15)
    
    # Configure columns
    for col in columns:
        tree.heading(col, text=col)
        tree.column(col, width=120, anchor='center')
    
    # Add data
    for product in products:
        # Find supplier
        supplier = next((s for s in suppliers if s['id'] == product.get('supplier_id')), None)
        supplier_name = supplier['name_ar'] if supplier else 'غير محدد'
        
        tree.insert('', 'end', values=(
            product.get('barcode', 'غير محدد'),
            f"{product['name']} | {product['name_ar']}",
            product['category'],
            supplier_name,
            f"${product['price']:.2f}",
            f"{product['stock']}"
        ))
    
    tree.pack(fill='both', expand=True, padx=10, pady=10)
    
    # Add info label
    info_label = tk.Label(root, text=f"عرض {len(products)} منتج / Displaying {len(products)} products", 
                         font=('Arial', 12, 'bold'))
    info_label.pack(pady=10)
    
    # Add close button
    tk.Button(root, text="إغلاق / Close", command=root.destroy, 
              font=('Arial', 10, 'bold'), bg='#ef4444', fg='white').pack(pady=5)
    
    print(f"✅ GUI created with {len(products)} products displayed")
    
    # Show products list
    print("\n📋 Products list:")
    for i, product in enumerate(products):
        print(f"  {i+1}. {product.get('name', 'NO_NAME')} - {product.get('barcode', 'NO_BARCODE')}")
    
    root.mainloop()

if __name__ == "__main__":
    test_inventory_direct()
