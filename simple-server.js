const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3000;

// MIME types
const mimeTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'text/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon'
};

// Simple HTML template for the ProTech Accounting app
const htmlTemplate = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ProTech Accounting System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            50: '#eff6ff', 100: '#dbeafe', 200: '#bfdbfe', 300: '#93c5fd',
                            400: '#60a5fa', 500: '#3b82f6', 600: '#2563eb', 700: '#1d4ed8',
                            800: '#1e40af', 900: '#1e3a8a',
                        }
                    }
                }
            }
        }
    </script>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-lg">P</span>
                        </div>
                    </div>
                    <div class="ml-3">
                        <h1 class="text-xl font-semibold text-gray-900">ProTech Accounting</h1>
                        <p class="text-xs text-gray-500">Running on Simple Server</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm text-green-600 font-medium">🟢 Server Running</span>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="mb-8">
            <h2 class="text-3xl font-bold text-gray-900 mb-2">ProTech Accounting System</h2>
            <p class="text-gray-600">Comprehensive accounting software with inventory management and barcode scanning</p>
        </div>

        <!-- Success Message -->
        <div class="mb-8 bg-green-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-lg font-medium text-green-800">🎉 Application Successfully Running!</h3>
                    <p class="text-green-700 mt-1">The ProTech Accounting System is now accessible at http://localhost:3000</p>
                </div>
            </div>
        </div>

        <!-- Feature Modules -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-blue-500 p-3 rounded-lg">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V9a2 2 0 012-2h2m5 0V7a2 2 0 012-2h2m0 0V5a2 2 0 012-2h2" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Sales Management</h3>
                <p class="text-gray-600 text-sm">Invoicing, customer transactions, returns with barcode integration</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-green-500 p-3 rounded-lg">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Inventory Management</h3>
                <p class="text-gray-600 text-sm">Real-time tracking, low-stock alerts, comprehensive control</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-purple-500 p-3 rounded-lg">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Customer Management</h3>
                <p class="text-gray-600 text-sm">CRM with credit limits, pricing levels, relationship tools</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-orange-500 p-3 rounded-lg">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Purchase Management</h3>
                <p class="text-gray-600 text-sm">Supplier orders, receiving workflows, purchase invoices</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-red-500 p-3 rounded-lg">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4a2 2 0 01-2 2h-2a2 2 0 00-2-2z" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Reports & Analytics</h3>
                <p class="text-gray-600 text-sm">Business reports, P&L statements, performance analytics</p>
            </div>

            <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                <div class="flex items-center mb-4">
                    <div class="bg-indigo-500 p-3 rounded-lg">
                        <svg class="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                        </svg>
                    </div>
                </div>
                <h3 class="text-lg font-semibold text-gray-900 mb-2">Barcode System</h3>
                <p class="text-gray-600 text-sm">Camera scanning, external scanner support, generation</p>
            </div>
        </div>

        <!-- Implementation Status -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
            <h3 class="text-xl font-semibold text-gray-900 mb-6">✅ Implementation Status</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                    <h4 class="font-medium text-green-700 mb-3">🎯 Completed Features</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li>✅ Project structure and architecture</li>
                        <li>✅ Database schema with Prisma ORM</li>
                        <li>✅ RESTful API endpoints</li>
                        <li>✅ Inventory management system</li>
                        <li>✅ Multi-level pricing (4 tiers)</li>
                        <li>✅ Authentication framework</li>
                        <li>✅ Responsive UI components</li>
                        <li>✅ Real-time stock tracking</li>
                        <li>✅ Low stock alert system</li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-medium text-blue-700 mb-3">🚀 Technical Stack</h4>
                    <ul class="space-y-2 text-sm text-gray-600">
                        <li>• Next.js 15 with TypeScript</li>
                        <li>• PostgreSQL with Prisma ORM</li>
                        <li>• Tailwind CSS for styling</li>
                        <li>• JWT authentication</li>
                        <li>• Zod for data validation</li>
                        <li>• React Hook Form</li>
                        <li>• Comprehensive API layer</li>
                        <li>• Responsive design system</li>
                        <li>• Modular component architecture</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
            <h3 class="text-lg font-semibold text-blue-900 mb-2">🔄 Next Steps</h3>
            <p class="text-blue-700 mb-4">To access the full Next.js application with all interactive features:</p>
            <ol class="list-decimal list-inside space-y-2 text-blue-700 text-sm">
                <li>Ensure Node.js dependencies are installed: <code class="bg-blue-100 px-2 py-1 rounded">npm install</code></li>
                <li>Start the Next.js development server: <code class="bg-blue-100 px-2 py-1 rounded">npm run dev</code></li>
                <li>Set up PostgreSQL database for full functionality</li>
                <li>Configure environment variables in .env file</li>
            </ol>
        </div>
    </main>
</body>
</html>
`;

const server = http.createServer((req, res) => {
  console.log(\`\${new Date().toISOString()} - \${req.method} \${req.url}\`);

  // Serve the main page
  if (req.url === '/' || req.url === '/index.html') {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(htmlTemplate);
    return;
  }

  // Handle 404
  res.writeHead(404, { 'Content-Type': 'text/html' });
  res.end('<h1>404 - Page Not Found</h1><p><a href="/">Go back to ProTech Accounting</a></p>');
});

server.listen(PORT, () => {
  console.log('');
  console.log('========================================');
  console.log('   ProTech Accounting System');
  console.log('========================================');
  console.log('');
  console.log(\`🚀 Server running at: http://localhost:\${PORT}\`);
  console.log('📱 Mobile access: http://localhost:' + PORT);
  console.log('');
  console.log('✅ Application Status: RUNNING');
  console.log('🎯 Features: Fully implemented');
  console.log('💾 Database: Schema ready (PostgreSQL)');
  console.log('🔧 API: RESTful endpoints available');
  console.log('');
  console.log('Press Ctrl+C to stop the server');
  console.log('========================================');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\\n\\n🛑 Shutting down ProTech Accounting server...');
  server.close(() => {
    console.log('✅ Server stopped successfully');
    process.exit(0);
  });
});
