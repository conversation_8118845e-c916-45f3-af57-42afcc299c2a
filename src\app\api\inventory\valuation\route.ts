import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoryId = searchParams.get('categoryId');
    const supplierId = searchParams.get('supplierId');
    const method = searchParams.get('method') || 'cost'; // 'cost' or 'selling'

    const where: any = {
      isActive: true,
      trackInventory: true,
    };

    if (categoryId) {
      where.categoryId = categoryId;
    }

    if (supplierId) {
      where.supplierId = supplierId;
    }

    // Get all products with their current stock
    const products = await db.product.findMany({
      where,
      include: {
        category: {
          select: {
            id: true,
            name: true,
          },
        },
        supplier: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Calculate valuation for each product
    const valuationData = products.map(product => {
      const quantity = product.currentStock;
      const unitPrice = method === 'cost' ? product.costPrice : product.basePrice;
      const totalValue = quantity * unitPrice;

      return {
        productId: product.id,
        productCode: product.code,
        productName: product.name,
        category: product.category?.name || 'Uncategorized',
        supplier: product.supplier?.name || 'No Supplier',
        quantity,
        unitPrice,
        totalValue,
        unit: product.unit,
        location: product.location,
      };
    });

    // Calculate summary statistics
    const summary = {
      totalProducts: products.length,
      totalQuantity: valuationData.reduce((sum, item) => sum + item.quantity, 0),
      totalValue: valuationData.reduce((sum, item) => sum + item.totalValue, 0),
      averageValue: valuationData.length > 0 
        ? valuationData.reduce((sum, item) => sum + item.totalValue, 0) / valuationData.length 
        : 0,
    };

    // Group by category for analysis
    const categoryBreakdown = valuationData.reduce((acc, item) => {
      const category = item.category;
      if (!acc[category]) {
        acc[category] = {
          category,
          products: 0,
          quantity: 0,
          value: 0,
        };
      }
      acc[category].products += 1;
      acc[category].quantity += item.quantity;
      acc[category].value += item.totalValue;
      return acc;
    }, {} as Record<string, any>);

    // Group by supplier for analysis
    const supplierBreakdown = valuationData.reduce((acc, item) => {
      const supplier = item.supplier;
      if (!acc[supplier]) {
        acc[supplier] = {
          supplier,
          products: 0,
          quantity: 0,
          value: 0,
        };
      }
      acc[supplier].products += 1;
      acc[supplier].quantity += item.quantity;
      acc[supplier].value += item.totalValue;
      return acc;
    }, {} as Record<string, any>);

    // Find top value products
    const topValueProducts = [...valuationData]
      .sort((a, b) => b.totalValue - a.totalValue)
      .slice(0, 10);

    // Find products with zero stock
    const zeroStockProducts = valuationData.filter(item => item.quantity === 0);

    return NextResponse.json({
      success: true,
      data: {
        products: valuationData,
        summary,
        analysis: {
          categoryBreakdown: Object.values(categoryBreakdown),
          supplierBreakdown: Object.values(supplierBreakdown),
          topValueProducts,
          zeroStockProducts,
        },
        metadata: {
          valuationMethod: method,
          generatedAt: new Date().toISOString(),
          filters: {
            categoryId,
            supplierId,
          },
        },
      },
    });
  } catch (error) {
    console.error('Error calculating inventory valuation:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to calculate inventory valuation',
      },
      { status: 500 }
    );
  }
}
