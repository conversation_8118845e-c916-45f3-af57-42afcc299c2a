#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Simple Profits Report
إضافة تقرير أرباح بسيط

Simple profits report without complex formatting
تقرير أرباح بسيط بدون تنسيق معقد
"""

import os
import shutil
from datetime import datetime

def add_simple_profits():
    """إضافة تقرير أرباح بسيط"""
    try:
        print("💰 إضافة تقرير أرباح بسيط")
        print("💰 Adding Simple Profits Report")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.simple_profits_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple profits method
        profits_method = '''
    def show_profits_report(self):
        """عرض تقرير الأرباح"""
        try:
            self.report_title.config(text="Profits Report")
            
            # Get data
            sales = self.get_real_sales_data()
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            
            # Calculate totals
            total_revenue = 0
            total_cost = 0
            
            # Calculate revenue
            for sale in sales:
                total_revenue += float(sale.get('total', 0))
            
            # Calculate cost
            for sale in sales:
                for item in sale.get('items', []):
                    item_name = item.get('name', '')
                    item_quantity = float(item.get('quantity', 0))
                    
                    # Find product cost
                    for product in products:
                        if product.get('name') == item_name:
                            base_price = float(product.get('base_price', 0))
                            total_cost += item_quantity * base_price
                            break
            
            # Calculate profits
            gross_profit = total_revenue - total_cost
            profit_margin = (gross_profit / total_revenue * 100) if total_revenue > 0 else 0
            
            # Calculate inventory value
            inventory_value = 0
            for product in products:
                quantity = float(product.get('quantity', 0))
                base_price = float(product.get('base_price', 0))
                inventory_value += quantity * base_price
            
            # Calculate customer balances
            customer_debt = 0
            customer_credit = 0
            for customer in customers:
                balance = float(customer.get('balance', 0))
                if balance > 0:
                    customer_credit += balance
                else:
                    customer_debt += abs(balance)
            
            # Calculate net worth
            net_worth = inventory_value + customer_credit + total_revenue - customer_debt
            
            # Exchange rate
            exchange_rate = 89500
            
            # Create report
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            report = "PROFITS & FINANCIAL REPORT\\n"
            report += "="*50 + "\\n\\n"
            report += f"Date: {current_time}\\n"
            report += f"Exchange Rate: 1 USD = {exchange_rate:,} LBP\\n\\n"
            
            # Revenue section
            report += "REVENUE:\\n"
            report += f"Total Sales: {total_revenue:,.0f} LBP\\n"
            report += f"Total Sales: ${total_revenue/exchange_rate:,.2f} USD\\n\\n"
            
            # Cost section
            report += "COSTS:\\n"
            report += f"Total Cost: {total_cost:,.0f} LBP\\n"
            report += f"Total Cost: ${total_cost/exchange_rate:,.2f} USD\\n\\n"
            
            # Profit section
            report += "PROFITS:\\n"
            report += f"Gross Profit: {gross_profit:,.0f} LBP\\n"
            report += f"Gross Profit: ${gross_profit/exchange_rate:,.2f} USD\\n"
            report += f"Profit Margin: {profit_margin:.1f}%\\n\\n"
            
            # Assets section
            report += "ASSETS:\\n"
            report += f"Inventory Value: {inventory_value:,.0f} LBP\\n"
            report += f"Inventory Value: ${inventory_value/exchange_rate:,.2f} USD\\n"
            report += f"Customer Debts: {customer_credit:,.0f} LBP\\n"
            report += f"Customer Debts: ${customer_credit/exchange_rate:,.2f} USD\\n\\n"
            
            # Net worth section
            report += "NET WORTH:\\n"
            report += f"Store Net Worth: {net_worth:,.0f} LBP\\n"
            report += f"Store Net Worth: ${net_worth/exchange_rate:,.2f} USD\\n\\n"
            
            # Analysis
            net_worth_usd = net_worth / exchange_rate
            if net_worth_usd >= 50000:
                report += "Status: EXCELLENT (Over $50,000)\\n"
            elif net_worth_usd >= 25000:
                report += "Status: VERY GOOD ($25,000 - $50,000)\\n"
            elif net_worth_usd >= 10000:
                report += "Status: GOOD ($10,000 - $25,000)\\n"
            elif net_worth_usd >= 5000:
                report += "Status: AVERAGE ($5,000 - $10,000)\\n"
            else:
                report += "Status: NEEDS IMPROVEMENT (Under $5,000)\\n"
            
            # Recommendations
            report += "\\nRECOMMENDATIONS:\\n"
            if profit_margin < 15:
                report += "- Improve profit margins\\n"
            if net_worth_usd < 10000:
                report += "- Increase working capital\\n"
            report += "- Monitor cash flow\\n"
            report += "- Follow up on receivables\\n"
            report += "- Update prices regularly\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
            print("✅ تم عرض تقرير الأرباح")
            
        except Exception as e:
            print(f"❌ خطأ في تقرير الأرباح: {e}")
            error_msg = "Error loading profits report"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_msg)
'''
        
        # Add the method before the last method
        last_method = content.rfind("\n    def show_store_balance_usd(")
        if last_method != -1:
            content = content[:last_method] + profits_method + content[last_method:]
        else:
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + profits_method + content[last_method:]
        
        # Add button with simple text
        if "btn6 = tk.Button(sidebar, text=\"رصيد المحل\"" in content:
            btn6_pos = content.find("btn6.pack(pady=3, padx=10, fill='x')")
            if btn6_pos != -1:
                btn6_end = content.find("\\n", btn6_pos) + 1
                
                new_button = "\\n            btn_profits = tk.Button(sidebar, text='Profits', font=('Arial', 10), bg='#27ae60', fg='white', width=18, height=2, command=self.show_profits_report)\\n            btn_profits.pack(pady=3, padx=10, fill='x')\\n"
                
                content = content[:btn6_end] + new_button + content[btn6_end:]
                print("✅ تم إضافة زر Profits")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة تقرير الأرباح البسيط")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة تقرير الأرباح: {e}")
        return False

def main():
    """Main function"""
    print("💰 إضافة تقرير الأرباح والرصيد المالي لـ ProTech")
    print("💰 Adding Profits and Financial Balance Report to ProTech")
    print("="*70)
    
    if add_simple_profits():
        print("\n🎉 تم إضافة تقرير الأرباح بنجاح!")
        
        print("\n💰 الميزات الجديدة:")
        print("• 📊 تحليل الأرباح")
        print("• 💼 الرصيد المالي")
        print("• 📈 هامش الربح")
        print("• 💎 صافي ثروة المحل")
        print("• 🔄 عرض بالدولار والليرة")
        
        print("\n📊 التقرير يشمل:")
        print("• إجمالي المبيعات")
        print("• إجمالي التكاليف")
        print("• إجمالي الأرباح")
        print("• هامش الربح")
        print("• قيمة المخزون")
        print("• ديون العملاء")
        print("• صافي ثروة المحل")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. انقر على زر 'Profits'")
        print("4. استعرض تقرير الأرباح والرصيد")
        
        print("\n🏆 مستويات التقييم:")
        print("• 🏆 ممتاز: أكثر من $50,000")
        print("• ✅ جيد جداً: $25,000 - $50,000")
        print("• 🟡 جيد: $10,000 - $25,000")
        print("• 🟠 متوسط: $5,000 - $10,000")
        print("• 🔴 يحتاج تحسين: أقل من $5,000")
        
    else:
        print("\n❌ فشل في إضافة تقرير الأرباح")
    
    print("\n🔧 تم الانتهاء من إضافة تقرير الأرباح")

if __name__ == "__main__":
    main()
