@echo off
title ProTech Accounting System
color 0A

echo.
echo ========================================
echo    ProTech Accounting System
echo ========================================
echo.

REM Set Node.js path
set "NODE_PATH=C:\Program Files\nodejs"
set "PATH=%NODE_PATH%;%PATH%"

echo [1/4] Checking Node.js installation...
"%NODE_PATH%\node.exe" --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found at %NODE_PATH%
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✓ Node.js found: 
"%NODE_PATH%\node.exe" --version

echo.
echo [2/4] Preparing minimal dependencies...
if exist package-minimal.json (
    copy /Y package-minimal.json package.json >nul
    echo ✓ Using minimal package configuration
) else (
    echo ✓ Using existing package configuration
)

echo.
echo [3/4] Installing dependencies...
echo This may take a few minutes...
"%NODE_PATH%\npm.cmd" install --no-optional --no-audit --no-fund --silent

if %errorlevel% neq 0 (
    echo.
    echo WARNING: Some dependencies failed to install
    echo Trying alternative installation...
    "%NODE_PATH%\npm.cmd" install --legacy-peer-deps --no-optional --silent
)

echo.
echo [4/4] Starting development server...
echo.
echo ┌─────────────────────────────────────────┐
echo │  ProTech Accounting System              │
echo │                                         │
echo │  🌐 URL: http://localhost:3000          │
echo │  📱 Mobile: http://localhost:3000       │
echo │                                         │
echo │  Press Ctrl+C to stop the server       │
echo └─────────────────────────────────────────┘
echo.

REM Start the development server
"%NODE_PATH%\npm.cmd" run dev

echo.
echo Server stopped.
pause
