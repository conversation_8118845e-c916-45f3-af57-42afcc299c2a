#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Apply Reports Directly
تطبيق التقارير مباشرة

Apply advanced reports system directly to ProTech
تطبيق نظام التقارير المتقدم مباشرة على ProTech
"""

import os
import shutil
from datetime import datetime

def apply_reports_directly():
    """تطبيق التقارير مباشرة"""
    try:
        print("📊 تطبيق نظام التقارير المتقدم مباشرة")
        print("📊 Applying Advanced Reports System Directly")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.direct_reports_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple reports method to add
        reports_method = '''
    def create_reports_table_page(self):
        """إنشاء صفحة التقارير بالجداول"""
        try:
            # Clear current page
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            
            self.current_page = 'table_reports'
            
            # Main container
            main_container = tk.Frame(self.main_frame, bg='#f0f0f0')
            main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Title
            title_label = tk.Label(main_container, text="Reports with Tables", 
                                 font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#2c3e50')
            title_label.pack(pady=(0, 20))
            
            # Reports buttons frame
            buttons_frame = tk.Frame(main_container, bg='#f0f0f0')
            buttons_frame.pack(fill='x', pady=(0, 20))
            
            # Create report buttons
            btn1 = tk.Button(buttons_frame, text="Products Table", 
                           command=self.show_products_in_table,
                           font=("Arial", 10, "bold"), bg='#3498db', fg='white',
                           width=20, height=2)
            btn1.grid(row=0, col=0, padx=10, pady=5)
            
            btn2 = tk.Button(buttons_frame, text="Customers Table", 
                           command=self.show_customers_in_table,
                           font=("Arial", 10, "bold"), bg='#e74c3c', fg='white',
                           width=20, height=2)
            btn2.grid(row=0, col=1, padx=10, pady=5)
            
            btn3 = tk.Button(buttons_frame, text="Sales Table", 
                           command=self.show_sales_in_table,
                           font=("Arial", 10, "bold"), bg='#2ecc71', fg='white',
                           width=20, height=2)
            btn3.grid(row=0, col=2, padx=10, pady=5)
            
            btn4 = tk.Button(buttons_frame, text="Financial Report", 
                           command=self.show_financial_in_table,
                           font=("Arial", 10, "bold"), bg='#9b59b6', fg='white',
                           width=20, height=2)
            btn4.grid(row=1, col=0, padx=10, pady=5)
            
            btn5 = tk.Button(buttons_frame, text="Profits Table", 
                           command=self.show_profits_in_table,
                           font=("Arial", 10, "bold"), bg='#27ae60', fg='white',
                           width=20, height=2)
            btn5.grid(row=1, col=1, padx=10, pady=5)
            
            btn6 = tk.Button(buttons_frame, text="Export CSV", 
                           command=self.export_current_table,
                           font=("Arial", 10, "bold"), bg='#34495e', fg='white',
                           width=20, height=2)
            btn6.grid(row=1, col=2, padx=10, pady=5)
            
            # Configure grid weights
            for i in range(3):
                buttons_frame.grid_columnconfigure(i, weight=1)
            
            # Table display frame
            self.table_display_frame = tk.Frame(main_container, bg='#ffffff', relief='ridge', bd=2)
            self.table_display_frame.pack(fill='both', expand=True)
            
            # Initial message
            welcome_label = tk.Label(self.table_display_frame, 
                                   text="Select a report button above to view data in table format",
                                   font=("Arial", 12), bg='#ffffff', fg='#7f8c8d')
            welcome_label.pack(expand=True)
            
            print("✅ تم إنشاء صفحة التقارير بالجداول")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء صفحة التقارير: {e}")
    
    def show_products_in_table(self):
        """عرض المنتجات في جدول"""
        try:
            # Clear table frame
            for widget in self.table_display_frame.winfo_children():
                widget.destroy()
            
            # Create scrollable text widget
            text_frame = tk.Frame(self.table_display_frame)
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Text widget with scrollbar
            text_widget = tk.Text(text_frame, font=("Courier", 10), wrap='none')
            scrollbar_v = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            scrollbar_h = tk.Scrollbar(text_frame, orient="horizontal", command=text_widget.xview)
            
            text_widget.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            text_widget.pack(side="left", fill="both", expand=True)
            
            # Get products data
            products = self.get_real_products_data()
            
            # Create table content
            table_content = "PRODUCTS TABLE\\n"
            table_content += "="*100 + "\\n\\n"
            table_content += f"{'Name':<20} {'Category':<15} {'Quantity':<10} {'Unit':<10} {'Base Price':<12} {'Shop Price':<12}\\n"
            table_content += "-"*100 + "\\n"
            
            for product in products:
                name = str(product.get('name', ''))[:18]
                category = str(product.get('category', ''))[:13]
                quantity = str(product.get('quantity', 0))[:8]
                unit = str(product.get('unit', ''))[:8]
                base_price = f"{float(product.get('base_price', 0)):,.0f}"[:10]
                shop_price = f"{float(product.get('shop_owner_price', 0)):,.0f}"[:10]
                
                table_content += f"{name:<20} {category:<15} {quantity:<10} {unit:<10} {base_price:<12} {shop_price:<12}\\n"
            
            table_content += "\\n" + "="*100 + "\\n"
            table_content += f"Total Products: {len(products)}\\n"
            
            text_widget.insert(1.0, table_content)
            text_widget.config(state='disabled')
            
            self.current_table_data = table_content
            print("✅ تم عرض جدول المنتجات")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول المنتجات: {e}")
    
    def show_customers_in_table(self):
        """عرض العملاء في جدول"""
        try:
            # Clear table frame
            for widget in self.table_display_frame.winfo_children():
                widget.destroy()
            
            # Create scrollable text widget
            text_frame = tk.Frame(self.table_display_frame)
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, font=("Courier", 10), wrap='none')
            scrollbar_v = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            scrollbar_h = tk.Scrollbar(text_frame, orient="horizontal", command=text_widget.xview)
            
            text_widget.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            text_widget.pack(side="left", fill="both", expand=True)
            
            # Get customers data
            customers = self.get_real_customers_data()
            
            # Create table content
            table_content = "CUSTOMERS TABLE\\n"
            table_content += "="*80 + "\\n\\n"
            table_content += f"{'Name':<25} {'Phone':<15} {'Type':<15} {'Balance':<15}\\n"
            table_content += "-"*80 + "\\n"
            
            for customer in customers:
                name = str(customer.get('name', ''))[:23]
                phone = str(customer.get('phone', ''))[:13]
                ctype = str(customer.get('type', ''))[:13]
                balance = f"{float(customer.get('balance', 0)):,.0f}"[:13]
                
                table_content += f"{name:<25} {phone:<15} {ctype:<15} {balance:<15}\\n"
            
            table_content += "\\n" + "="*80 + "\\n"
            table_content += f"Total Customers: {len(customers)}\\n"
            
            text_widget.insert(1.0, table_content)
            text_widget.config(state='disabled')
            
            self.current_table_data = table_content
            print("✅ تم عرض جدول العملاء")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول العملاء: {e}")
    
    def show_sales_in_table(self):
        """عرض المبيعات في جدول"""
        try:
            # Clear table frame
            for widget in self.table_display_frame.winfo_children():
                widget.destroy()
            
            # Create scrollable text widget
            text_frame = tk.Frame(self.table_display_frame)
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, font=("Courier", 10), wrap='none')
            scrollbar_v = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            scrollbar_h = tk.Scrollbar(text_frame, orient="horizontal", command=text_widget.xview)
            
            text_widget.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            text_widget.pack(side="left", fill="both", expand=True)
            
            # Get sales data
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Create table content
            table_content = "SALES TABLE\\n"
            table_content += "="*100 + "\\n\\n"
            table_content += f"{'Invoice':<15} {'Customer':<20} {'Date':<12} {'Total LBP':<15} {'Total USD':<12} {'Status':<10}\\n"
            table_content += "-"*100 + "\\n"
            
            for sale in sales:
                invoice = str(sale.get('invoice_number', ''))[:13]
                customer = str(sale.get('customer_name', ''))[:18]
                date = str(sale.get('date', ''))[:10]
                total_lbp = f"{float(sale.get('total', 0)):,.0f}"[:13]
                total_usd = f"${float(sale.get('total', 0))/exchange_rate:,.2f}"[:10]
                status = str(sale.get('status', ''))[:8]
                
                table_content += f"{invoice:<15} {customer:<20} {date:<12} {total_lbp:<15} {total_usd:<12} {status:<10}\\n"
            
            table_content += "\\n" + "="*100 + "\\n"
            table_content += f"Total Sales: {len(sales)}\\n"
            
            total_sales_lbp = sum(float(sale.get('total', 0)) for sale in sales)
            total_sales_usd = total_sales_lbp / exchange_rate
            table_content += f"Total Revenue: {total_sales_lbp:,.0f} LBP (${total_sales_usd:,.2f} USD)\\n"
            
            text_widget.insert(1.0, table_content)
            text_widget.config(state='disabled')
            
            self.current_table_data = table_content
            print("✅ تم عرض جدول المبيعات")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول المبيعات: {e}")
    
    def show_financial_in_table(self):
        """عرض التقرير المالي في جدول"""
        try:
            # Clear table frame
            for widget in self.table_display_frame.winfo_children():
                widget.destroy()
            
            # Create scrollable text widget
            text_frame = tk.Frame(self.table_display_frame)
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, font=("Courier", 10), wrap='none')
            scrollbar_v = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            scrollbar_h = tk.Scrollbar(text_frame, orient="horizontal", command=text_widget.xview)
            
            text_widget.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            text_widget.pack(side="left", fill="both", expand=True)
            
            # Calculate financial data
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Calculate totals
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            total_sales = sum(float(s.get('total', 0)) for s in sales)
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            customer_credit = sum(abs(float(c.get('balance', 0))) for c in customers if float(c.get('balance', 0)) < 0)
            net_worth = inventory_value + customer_debt + total_sales - customer_credit
            
            # Create table content
            table_content = "FINANCIAL REPORT\\n"
            table_content += "="*80 + "\\n\\n"
            table_content += f"{'Category':<25} {'Amount LBP':<20} {'Amount USD':<15} {'%':<10}\\n"
            table_content += "-"*80 + "\\n"
            
            total_assets = inventory_value + customer_debt + total_sales
            
            items = [
                ("Inventory Value", inventory_value),
                ("Total Sales", total_sales),
                ("Customer Debts", customer_debt),
                ("Customer Credits", customer_credit),
                ("Net Worth", net_worth)
            ]
            
            for name, amount in items:
                amount_lbp = f"{amount:,.0f}"[:18]
                amount_usd = f"${amount/exchange_rate:,.2f}"[:13]
                percentage = f"{(amount/total_assets*100):.1f}%" if total_assets > 0 and name != "Net Worth" else ""
                
                table_content += f"{name:<25} {amount_lbp:<20} {amount_usd:<15} {percentage:<10}\\n"
            
            table_content += "\\n" + "="*80 + "\\n"
            table_content += f"Exchange Rate: 1 USD = {exchange_rate:,} LBP\\n"
            
            # Status assessment
            net_worth_usd = net_worth / exchange_rate
            if net_worth_usd >= 50000:
                status = "EXCELLENT (Over $50,000)"
            elif net_worth_usd >= 25000:
                status = "VERY GOOD ($25,000 - $50,000)"
            elif net_worth_usd >= 10000:
                status = "GOOD ($10,000 - $25,000)"
            else:
                status = "NEEDS IMPROVEMENT (Under $10,000)"
            
            table_content += f"Financial Status: {status}\\n"
            
            text_widget.insert(1.0, table_content)
            text_widget.config(state='disabled')
            
            self.current_table_data = table_content
            print("✅ تم عرض التقرير المالي")
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقرير المالي: {e}")
    
    def show_profits_in_table(self):
        """عرض الأرباح في جدول"""
        try:
            # Clear table frame
            for widget in self.table_display_frame.winfo_children():
                widget.destroy()
            
            # Create scrollable text widget
            text_frame = tk.Frame(self.table_display_frame)
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            text_widget = tk.Text(text_frame, font=("Courier", 10), wrap='none')
            scrollbar_v = tk.Scrollbar(text_frame, orient="vertical", command=text_widget.yview)
            scrollbar_h = tk.Scrollbar(text_frame, orient="horizontal", command=text_widget.xview)
            
            text_widget.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            text_widget.pack(side="left", fill="both", expand=True)
            
            # Calculate profits
            sales = self.get_real_sales_data()
            products = self.get_real_products_data()
            exchange_rate = 89500
            
            # Create table content
            table_content = "PROFITS TABLE\\n"
            table_content += "="*100 + "\\n\\n"
            table_content += f"{'Invoice':<15} {'Revenue LBP':<15} {'Cost LBP':<15} {'Profit LBP':<15} {'Profit USD':<12} {'Margin %':<10}\\n"
            table_content += "-"*100 + "\\n"
            
            total_revenue = 0
            total_cost = 0
            total_profit = 0
            
            for sale in sales:
                revenue = float(sale.get('total', 0))
                total_revenue += revenue
                
                # Calculate cost for this sale
                cost = 0
                for item in sale.get('items', []):
                    item_name = item.get('name', '')
                    item_quantity = float(item.get('quantity', 0))
                    
                    # Find product cost
                    for product in products:
                        if product.get('name') == item_name:
                            base_price = float(product.get('base_price', 0))
                            cost += item_quantity * base_price
                            break
                
                total_cost += cost
                profit = revenue - cost
                total_profit += profit
                margin = (profit / revenue * 100) if revenue > 0 else 0
                
                invoice = str(sale.get('invoice_number', ''))[:13]
                revenue_str = f"{revenue:,.0f}"[:13]
                cost_str = f"{cost:,.0f}"[:13]
                profit_str = f"{profit:,.0f}"[:13]
                profit_usd = f"${profit/exchange_rate:,.2f}"[:10]
                margin_str = f"{margin:.1f}%"[:8]
                
                table_content += f"{invoice:<15} {revenue_str:<15} {cost_str:<15} {profit_str:<15} {profit_usd:<12} {margin_str:<10}\\n"
            
            table_content += "\\n" + "="*100 + "\\n"
            table_content += f"TOTALS:\\n"
            table_content += f"Total Revenue: {total_revenue:,.0f} LBP (${total_revenue/exchange_rate:,.2f} USD)\\n"
            table_content += f"Total Cost: {total_cost:,.0f} LBP (${total_cost/exchange_rate:,.2f} USD)\\n"
            table_content += f"Total Profit: {total_profit:,.0f} LBP (${total_profit/exchange_rate:,.2f} USD)\\n"
            
            overall_margin = (total_profit / total_revenue * 100) if total_revenue > 0 else 0
            table_content += f"Overall Profit Margin: {overall_margin:.1f}%\\n"
            
            text_widget.insert(1.0, table_content)
            text_widget.config(state='disabled')
            
            self.current_table_data = table_content
            print("✅ تم عرض جدول الأرباح")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول الأرباح: {e}")
    
    def export_current_table(self):
        """تصدير الجدول الحالي"""
        try:
            if hasattr(self, 'current_table_data'):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"protech_table_export_{timestamp}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.current_table_data)
                
                print(f"✅ تم تصدير الجدول إلى {filename}")
                
                # Show success message
                if hasattr(self, 'table_display_frame'):
                    success_label = tk.Label(self.table_display_frame, 
                                           text=f"Exported to {filename}", 
                                           bg='#2ecc71', fg='white', font=("Arial", 12, "bold"))
                    success_label.pack(pady=10)
                    
                    if hasattr(self, 'root'):
                        self.root.after(3000, success_label.destroy)
            else:
                print("❌ لا يوجد جدول لتصديره")
                
        except Exception as e:
            print(f"❌ خطأ في التصدير: {e}")
'''
        
        # Find where to add the method
        last_method = content.rfind("\n    def show_profits_report(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + reports_method + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + reports_method + content[last_method:]
        
        # Update reports button to use new method
        if "btn5 = tk.Button(sidebar, text=\"التقارير\"" in content:
            old_button = 'btn5 = tk.Button(sidebar, text="التقارير", font=("Arial", 12), bg=\'#9b59b6\', fg=\'white\', width=18, height=2, command=self.show_reports)'
            new_button = 'btn5 = tk.Button(sidebar, text="Table Reports", font=("Arial", 12), bg=\'#9b59b6\', fg=\'white\', width=18, height=2, command=self.create_reports_table_page)'
            content = content.replace(old_button, new_button)
            print("✅ تم تحديث زر التقارير")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تطبيق نظام التقارير بالجداول")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تطبيق التقارير: {e}")
        return False

def main():
    """Main function"""
    print("📊 تطبيق نظام التقارير بالجداول مباشرة على ProTech")
    print("📊 Applying Table Reports System Directly to ProTech")
    print("="*70)
    
    if apply_reports_directly():
        print("\n🎉 تم تطبيق نظام التقارير بالجداول بنجاح!")
        
        print("\n📊 الميزات الجديدة:")
        print("• 📋 5 تقارير في جداول منسقة")
        print("• 📤 تصدير فوري للجداول")
        print("• 📊 عرض بالليرة والدولار")
        print("• 🔍 تفاصيل شاملة للبيانات")
        print("• 📈 حسابات الأرباح والهوامش")
        
        print("\n📋 التقارير المتاحة:")
        print("• 🛍️ Products Table - جدول المنتجات الشامل")
        print("• 👥 Customers Table - جدول العملاء مع الأرصدة")
        print("• 💰 Sales Table - جدول المبيعات بالعملتين")
        print("• 💼 Financial Report - التقرير المالي الكامل")
        print("• 📈 Profits Table - جدول الأرباح التفصيلي")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Table Reports' في الشريط الجانبي")
        print("3. اختر التقرير المطلوب من الأزرار")
        print("4. استعرض البيانات في جدول منسق")
        print("5. انقر على 'Export CSV' لتصدير الجدول")
        
        print("\n💡 مميزات الجداول:")
        print("• تنسيق احترافي مع أعمدة محاذية")
        print("• شريط تمرير للجداول الطويلة")
        print("• إجماليات وإحصائيات")
        print("• عرض بالليرة والدولار")
        print("• تصدير سهل للملفات")
        
    else:
        print("\n❌ فشل في تطبيق نظام التقارير بالجداول")
    
    print("\n🔧 تم الانتهاء من تطبيق التقارير")

if __name__ == "__main__":
    main()
