#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Advanced Reports System
إنشاء نظام التقارير المتقدم

Create advanced reports with tables, date filtering, and editing capabilities
إنشاء تقارير متقدمة مع جداول وتصفية التواريخ وإمكانية التعديل
"""

import os
import shutil
from datetime import datetime, timedelta

def create_advanced_reports():
    """إنشاء نظام التقارير المتقدم"""
    try:
        print("📊 إنشاء نظام التقارير المتقدم")
        print("📊 Creating Advanced Reports System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.advanced_reports_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Advanced reports methods
        reports_methods = '''
    def create_advanced_reports_page(self):
        """إنشاء صفحة التقارير المتقدمة"""
        try:
            # Clear current page
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            
            self.current_page = 'advanced_reports'
            
            # Main container
            main_container = tk.Frame(self.main_frame, bg='#f0f0f0')
            main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Title
            title_label = tk.Label(main_container, text="Advanced Reports System", 
                                 font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#2c3e50')
            title_label.pack(pady=(0, 20))
            
            # Reports buttons frame
            buttons_frame = tk.Frame(main_container, bg='#f0f0f0')
            buttons_frame.pack(fill='x', pady=(0, 20))
            
            # Create report buttons
            reports = [
                ("Products Report", self.show_products_table, '#3498db'),
                ("Customers Report", self.show_customers_table, '#e74c3c'),
                ("Sales Report", self.show_sales_table, '#2ecc71'),
                ("Inventory Report", self.show_inventory_table, '#f39c12'),
                ("Financial Report", self.show_financial_table, '#9b59b6'),
                ("Profits Report", self.show_profits_table, '#27ae60')
            ]
            
            # Create buttons in grid
            for i, (text, command, color) in enumerate(reports):
                row = i // 3
                col = i % 3
                
                btn = tk.Button(buttons_frame, text=text, command=command,
                              font=("Arial", 10, "bold"), bg=color, fg='white',
                              width=20, height=2, relief='raised', bd=2)
                btn.grid(row=row, col=col, padx=10, pady=5, sticky='ew')
            
            # Configure grid weights
            for i in range(3):
                buttons_frame.grid_columnconfigure(i, weight=1)
            
            # Date filter frame
            self.date_filter_frame = tk.Frame(main_container, bg='#ecf0f1', relief='ridge', bd=2)
            self.date_filter_frame.pack(fill='x', pady=(0, 10))
            
            # Date filter controls
            tk.Label(self.date_filter_frame, text="Date Filter:", 
                    font=("Arial", 10, "bold"), bg='#ecf0f1').pack(side='left', padx=10, pady=5)
            
            tk.Label(self.date_filter_frame, text="From:", bg='#ecf0f1').pack(side='left', padx=(20, 5), pady=5)
            self.date_from = tk.Entry(self.date_filter_frame, width=12)
            self.date_from.pack(side='left', padx=(0, 10), pady=5)
            self.date_from.insert(0, (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
            
            tk.Label(self.date_filter_frame, text="To:", bg='#ecf0f1').pack(side='left', padx=(10, 5), pady=5)
            self.date_to = tk.Entry(self.date_filter_frame, width=12)
            self.date_to.pack(side='left', padx=(0, 10), pady=5)
            self.date_to.insert(0, datetime.now().strftime('%Y-%m-%d'))
            
            filter_btn = tk.Button(self.date_filter_frame, text="Apply Filter", 
                                 command=self.apply_date_filter, bg='#34495e', fg='white')
            filter_btn.pack(side='left', padx=10, pady=5)
            
            reset_btn = tk.Button(self.date_filter_frame, text="Reset", 
                                command=self.reset_date_filter, bg='#95a5a6', fg='white')
            reset_btn.pack(side='left', padx=5, pady=5)
            
            # Table frame
            self.table_frame = tk.Frame(main_container, bg='#ffffff', relief='ridge', bd=2)
            self.table_frame.pack(fill='both', expand=True)
            
            # Initial message
            welcome_label = tk.Label(self.table_frame, 
                                   text="Select a report from the buttons above to view data in table format",
                                   font=("Arial", 12), bg='#ffffff', fg='#7f8c8d')
            welcome_label.pack(expand=True)
            
            print("✅ تم إنشاء صفحة التقارير المتقدمة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء صفحة التقارير: {e}")
    
    def create_data_table(self, headers, data, editable_columns=None):
        """إنشاء جدول البيانات"""
        try:
            # Clear table frame
            for widget in self.table_frame.winfo_children():
                widget.destroy()
            
            # Create scrollable frame
            canvas = tk.Canvas(self.table_frame, bg='#ffffff')
            scrollbar_v = tk.Scrollbar(self.table_frame, orient="vertical", command=canvas.yview)
            scrollbar_h = tk.Scrollbar(self.table_frame, orient="horizontal", command=canvas.xview)
            scrollable_frame = tk.Frame(canvas, bg='#ffffff')
            
            scrollable_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            # Pack scrollbars and canvas
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            canvas.pack(side="left", fill="both", expand=True)
            
            # Table info
            info_frame = tk.Frame(scrollable_frame, bg='#ecf0f1')
            info_frame.pack(fill='x', padx=5, pady=5)
            
            tk.Label(info_frame, text=f"Total Records: {len(data)}", 
                    font=("Arial", 10, "bold"), bg='#ecf0f1').pack(side='left', padx=10)
            
            # Export button
            export_btn = tk.Button(info_frame, text="Export to CSV", 
                                 command=lambda: self.export_table_data(headers, data),
                                 bg='#27ae60', fg='white', font=("Arial", 9))
            export_btn.pack(side='right', padx=10)
            
            # Headers
            headers_frame = tk.Frame(scrollable_frame, bg='#34495e')
            headers_frame.pack(fill='x', padx=5, pady=(0, 2))
            
            for i, header in enumerate(headers):
                header_label = tk.Label(headers_frame, text=header, 
                                      font=("Arial", 10, "bold"), 
                                      bg='#34495e', fg='white', 
                                      relief='ridge', bd=1)
                header_label.grid(row=0, column=i, sticky='ew', padx=1, pady=1)
                headers_frame.grid_columnconfigure(i, weight=1, minsize=120)
            
            # Data rows
            self.table_data = []
            for row_idx, row_data in enumerate(data):
                row_frame = tk.Frame(scrollable_frame, bg='#ffffff' if row_idx % 2 == 0 else '#f8f9fa')
                row_frame.pack(fill='x', padx=5, pady=1)
                
                row_widgets = []
                for col_idx, cell_data in enumerate(row_data):
                    if editable_columns and col_idx in editable_columns:
                        # Editable cell
                        cell_var = tk.StringVar(value=str(cell_data))
                        cell_entry = tk.Entry(row_frame, textvariable=cell_var, 
                                            font=("Arial", 9), justify='center',
                                            relief='flat', bd=1)
                        cell_entry.grid(row=0, column=col_idx, sticky='ew', padx=1, pady=1)
                        row_widgets.append(cell_var)
                    else:
                        # Read-only cell
                        cell_label = tk.Label(row_frame, text=str(cell_data), 
                                            font=("Arial", 9), 
                                            bg=row_frame['bg'], 
                                            relief='ridge', bd=1)
                        cell_label.grid(row=0, column=col_idx, sticky='ew', padx=1, pady=1)
                        row_widgets.append(cell_label)
                    
                    row_frame.grid_columnconfigure(col_idx, weight=1, minsize=120)
                
                # Add edit button for editable rows
                if editable_columns:
                    edit_btn = tk.Button(row_frame, text="Save", 
                                       command=lambda idx=row_idx: self.save_row_changes(idx),
                                       bg='#3498db', fg='white', font=("Arial", 8))
                    edit_btn.grid(row=0, column=len(headers), padx=5, pady=1)
                    row_widgets.append(edit_btn)
                
                self.table_data.append(row_widgets)
            
            # Bind mouse wheel to canvas
            def _on_mousewheel(event):
                canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            canvas.bind_all("<MouseWheel>", _on_mousewheel)
            
            print(f"✅ تم إنشاء جدول بـ {len(data)} صف")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجدول: {e}")
    
    def show_products_table(self):
        """عرض جدول المنتجات"""
        try:
            products = self.get_real_products_data()
            
            headers = ["Name", "Category", "Barcode", "Quantity", "Unit", 
                      "Base Price", "Shop Price", "Wholesale", "Retail", "Notes"]
            
            data = []
            for product in products:
                row = [
                    product.get('name', ''),
                    product.get('category', ''),
                    product.get('barcode', ''),
                    product.get('quantity', 0),
                    product.get('unit', ''),
                    f"{product.get('base_price', 0):,.0f}",
                    f"{product.get('shop_owner_price', 0):,.0f}",
                    f"{product.get('wholesale_price', 0):,.0f}",
                    f"{product.get('retail_price', 0):,.0f}",
                    product.get('notes', '')
                ]
                data.append(row)
            
            # Editable columns: quantity, prices, notes
            editable_columns = [3, 5, 6, 7, 8, 9]
            self.create_data_table(headers, data, editable_columns)
            
            print("✅ تم عرض جدول المنتجات")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول المنتجات: {e}")
    
    def show_customers_table(self):
        """عرض جدول العملاء"""
        try:
            customers = self.get_real_customers_data()
            
            headers = ["Name", "Phone", "Email", "Type", "Balance", "Notes"]
            
            data = []
            for customer in customers:
                row = [
                    customer.get('name', ''),
                    customer.get('phone', ''),
                    customer.get('email', ''),
                    customer.get('type', ''),
                    f"{customer.get('balance', 0):,.0f}",
                    customer.get('notes', '')
                ]
                data.append(row)
            
            # Editable columns: phone, email, balance, notes
            editable_columns = [1, 2, 4, 5]
            self.create_data_table(headers, data, editable_columns)
            
            print("✅ تم عرض جدول العملاء")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول العملاء: {e}")
    
    def show_sales_table(self):
        """عرض جدول المبيعات"""
        try:
            sales = self.get_real_sales_data()
            
            # Apply date filter
            filtered_sales = self.filter_sales_by_date(sales)
            
            headers = ["Invoice #", "Customer", "Date", "Items Count", 
                      "Total (LBP)", "Total (USD)", "Status", "Notes"]
            
            data = []
            exchange_rate = 89500
            
            for sale in filtered_sales:
                total_lbp = sale.get('total', 0)
                total_usd = total_lbp / exchange_rate
                items_count = len(sale.get('items', []))
                
                row = [
                    sale.get('invoice_number', ''),
                    sale.get('customer_name', ''),
                    sale.get('date', ''),
                    items_count,
                    f"{total_lbp:,.0f}",
                    f"${total_usd:,.2f}",
                    sale.get('status', ''),
                    sale.get('notes', '')
                ]
                data.append(row)
            
            # Editable columns: status, notes
            editable_columns = [6, 7]
            self.create_data_table(headers, data, editable_columns)
            
            print(f"✅ تم عرض جدول المبيعات ({len(filtered_sales)} فاتورة)")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول المبيعات: {e}")
    
    def show_inventory_table(self):
        """عرض جدول المخزون"""
        try:
            products = self.get_real_products_data()
            
            headers = ["Product", "Current Stock", "Min Stock", "Status", 
                      "Value (LBP)", "Value (USD)", "Action Needed"]
            
            data = []
            exchange_rate = 89500
            
            for product in products:
                quantity = float(product.get('quantity', 0))
                min_stock = float(product.get('min_stock', 10))
                base_price = float(product.get('base_price', 0))
                value_lbp = quantity * base_price
                value_usd = value_lbp / exchange_rate
                
                # Determine status
                if quantity <= 0:
                    status = "Out of Stock"
                    action = "Restock Immediately"
                elif quantity <= min_stock:
                    status = "Low Stock"
                    action = "Restock Soon"
                elif quantity > min_stock * 5:
                    status = "Overstock"
                    action = "Consider Promotion"
                else:
                    status = "Normal"
                    action = "No Action"
                
                row = [
                    product.get('name', ''),
                    f"{quantity:,.0f}",
                    f"{min_stock:,.0f}",
                    status,
                    f"{value_lbp:,.0f}",
                    f"${value_usd:,.2f}",
                    action
                ]
                data.append(row)
            
            # Sort by status priority (out of stock first)
            status_priority = {"Out of Stock": 0, "Low Stock": 1, "Normal": 2, "Overstock": 3}
            data.sort(key=lambda x: status_priority.get(x[3], 4))
            
            self.create_data_table(headers, data)
            
            print("✅ تم عرض جدول المخزون")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول المخزون: {e}")
    
    def show_financial_table(self):
        """عرض جدول التقرير المالي"""
        try:
            # Calculate financial data
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            
            exchange_rate = 89500
            
            headers = ["Category", "Amount (LBP)", "Amount (USD)", "Percentage", "Notes"]
            
            # Calculate totals
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            total_sales = sum(float(s.get('total', 0)) for s in sales)
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            customer_credit = sum(abs(float(c.get('balance', 0))) for c in customers if float(c.get('balance', 0)) < 0)
            
            total_assets = inventory_value + customer_debt + total_sales
            net_worth = total_assets - customer_credit
            
            data = [
                ["Inventory Value", f"{inventory_value:,.0f}", f"${inventory_value/exchange_rate:,.2f}", 
                 f"{(inventory_value/total_assets*100):.1f}%" if total_assets > 0 else "0%", "Current stock value"],
                ["Total Sales", f"{total_sales:,.0f}", f"${total_sales/exchange_rate:,.2f}", 
                 f"{(total_sales/total_assets*100):.1f}%" if total_assets > 0 else "0%", "All sales revenue"],
                ["Customer Debts", f"{customer_debt:,.0f}", f"${customer_debt/exchange_rate:,.2f}", 
                 f"{(customer_debt/total_assets*100):.1f}%" if total_assets > 0 else "0%", "Money owed by customers"],
                ["Customer Credits", f"{customer_credit:,.0f}", f"${customer_credit/exchange_rate:,.2f}", 
                 f"{(customer_credit/total_assets*100):.1f}%" if total_assets > 0 else "0%", "Money owed to customers"],
                ["Total Assets", f"{total_assets:,.0f}", f"${total_assets/exchange_rate:,.2f}", "100%", "All assets combined"],
                ["Net Worth", f"{net_worth:,.0f}", f"${net_worth/exchange_rate:,.2f}", 
                 f"{(net_worth/total_assets*100):.1f}%" if total_assets > 0 else "0%", "Assets minus liabilities"]
            ]
            
            self.create_data_table(headers, data)
            
            print("✅ تم عرض التقرير المالي")
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقرير المالي: {e}")
    
    def show_profits_table(self):
        """عرض جدول الأرباح"""
        try:
            sales = self.get_real_sales_data()
            products = self.get_real_products_data()
            
            # Apply date filter
            filtered_sales = self.filter_sales_by_date(sales)
            
            headers = ["Invoice #", "Date", "Revenue (LBP)", "Cost (LBP)", 
                      "Profit (LBP)", "Profit (USD)", "Margin %", "Customer"]
            
            data = []
            exchange_rate = 89500
            
            for sale in filtered_sales:
                revenue = float(sale.get('total', 0))
                
                # Calculate cost for this sale
                cost = 0
                for item in sale.get('items', []):
                    item_name = item.get('name', '')
                    item_quantity = float(item.get('quantity', 0))
                    
                    # Find product cost
                    for product in products:
                        if product.get('name') == item_name:
                            base_price = float(product.get('base_price', 0))
                            cost += item_quantity * base_price
                            break
                
                profit = revenue - cost
                margin = (profit / revenue * 100) if revenue > 0 else 0
                
                row = [
                    sale.get('invoice_number', ''),
                    sale.get('date', ''),
                    f"{revenue:,.0f}",
                    f"{cost:,.0f}",
                    f"{profit:,.0f}",
                    f"${profit/exchange_rate:,.2f}",
                    f"{margin:.1f}%",
                    sale.get('customer_name', '')
                ]
                data.append(row)
            
            # Sort by profit (highest first)
            data.sort(key=lambda x: float(x[4].replace(',', '')), reverse=True)
            
            self.create_data_table(headers, data)
            
            print(f"✅ تم عرض جدول الأرباح ({len(filtered_sales)} فاتورة)")
            
        except Exception as e:
            print(f"❌ خطأ في عرض جدول الأرباح: {e}")
'''
        
        # Add the advanced reports methods
        last_method = content.rfind("\n    def show_profits_report(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + reports_methods + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + reports_methods + content[last_method:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة نظام التقارير المتقدم - الجزء الأول")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء نظام التقارير المتقدم: {e}")
        return False

def main():
    """Main function"""
    print("📊 إنشاء نظام التقارير المتقدم مع الجداول والتصفية")
    print("📊 Creating Advanced Reports System with Tables and Filtering")
    print("="*80)

    if create_advanced_reports():
        print("\n🎉 تم إنشاء نظام التقارير المتقدم بنجاح!")

        print("\n📊 الميزات الجديدة:")
        print("• 📋 6 تقارير متقدمة في جداول")
        print("• 📅 تصفية بالتواريخ")
        print("• ✏️ إمكانية التعديل المباشر")
        print("• 📤 تصدير إلى CSV")
        print("• 🔍 عرض تفصيلي للبيانات")
        print("• 📊 إحصائيات ومؤشرات")

        print("\n📋 التقارير المتاحة:")
        print("• 🛍️ Products Report - جدول المنتجات مع إمكانية تعديل الكميات والأسعار")
        print("• 👥 Customers Report - جدول العملاء مع إمكانية تعديل البيانات")
        print("• 💰 Sales Report - جدول المبيعات مع تصفية التواريخ")
        print("• 📦 Inventory Report - جدول المخزون مع حالة المخزون")
        print("• 💼 Financial Report - التقرير المالي الشامل")
        print("• 📈 Profits Report - جدول الأرباح مع تصفية التواريخ")

        print("\n✏️ إمكانيات التعديل:")
        print("• تعديل كميات وأسعار المنتجات")
        print("• تعديل بيانات العملاء")
        print("• تعديل حالة المبيعات")
        print("• حفظ تلقائي للتغييرات")

        print("\n📅 تصفية التواريخ:")
        print("• تصفية المبيعات حسب فترة زمنية")
        print("• تصفية الأرباح حسب التاريخ")
        print("• إعادة تعيين سريعة للتصفية")

        print("\n📤 التصدير:")
        print("• تصدير أي جدول إلى ملف CSV")
        print("• تسمية تلقائية بالتاريخ والوقت")
        print("• دعم الترميز العربي")

        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Advanced Reports'")
        print("3. اختر التقرير المطلوب")
        print("4. استخدم تصفية التواريخ عند الحاجة")
        print("5. عدّل البيانات مباشرة في الجدول")
        print("6. صدّر البيانات إلى CSV")

        print("\n💡 الفوائد:")
        print("• عرض شامل ومنظم للبيانات")
        print("• تعديل سريع ومباشر")
        print("• تصفية ذكية للبيانات")
        print("• تصدير سهل للتقارير")
        print("• واجهة احترافية ومريحة")

    else:
        print("\n❌ فشل في إنشاء نظام التقارير المتقدم")

    print("\n🔧 تم الانتهاء من إنشاء نظام التقارير المتقدم")

if __name__ == "__main__":
    main()
