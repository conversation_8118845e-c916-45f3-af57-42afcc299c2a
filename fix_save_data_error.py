#!/usr/bin/env python3
"""
Fix Save Data Error in ProTech
إصلاح خطأ حفظ البيانات في ProTech

Fix the save data function error in ProTech
إصلاح خطأ دالة حفظ البيانات في ProTech
"""

import os
import re
import shutil
from datetime import datetime

def backup_file():
    """Create backup of current file"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.save_fix_backup_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return None

def fix_save_data_function():
    """Fix the save_data function"""
    try:
        # Read the file
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and fix the save_data function
        old_save_data = r'def save_data\(self\):(.*?)(?=def|\Z)'
        
        new_save_data = '''def save_data(self):
        """Save data to file with error handling"""
        try:
            # Clear caches when data changes
            if hasattr(self, 'clear_caches'):
                self.clear_caches()

            # Prepare data to save
            data = {
                'suppliers': getattr(self, 'suppliers', []),
                'products': getattr(self, 'products', []),
                'customers': getattr(self, 'customers', []),
                'sales': getattr(self, 'sales', []),
                'last_updated': datetime.now().isoformat()
            }
            
            # Ensure data file path exists
            data_file = getattr(self, 'data_file', 'protech_simple_data.json')
            
            # Create directory if it doesn't exist
            data_dir = os.path.dirname(data_file) if os.path.dirname(data_file) else '.'
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # Save in background if not loading
            if not getattr(self, 'loading', False):
                threading.Thread(target=self.save_data_background, daemon=True).start()
            else:
                # Direct save during loading
                # Write to temporary file first for atomic operation
                temp_file = data_file + '.tmp'
                with open(temp_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                
                # Atomic move
                if os.path.exists(temp_file):
                    shutil.move(temp_file, data_file)
                
                print("✅ تم حفظ البيانات بنجاح")
            
            return True

        except PermissionError:
            print("❌ خطأ في الصلاحيات - تأكد من إغلاق الملف")
            return False
        except FileNotFoundError:
            print("❌ مجلد البيانات غير موجود - سيتم إنشاؤه")
            try:
                os.makedirs(os.path.dirname(getattr(self, 'data_file', 'protech_simple_data.json')), exist_ok=True)
                return self.save_data()  # Try again
            except:
                return False
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            # Try to save to backup location
            try:
                backup_file = 'protech_data_backup.json'
                data = {
                    'suppliers': getattr(self, 'suppliers', []),
                    'products': getattr(self, 'products', []),
                    'customers': getattr(self, 'customers', []),
                    'sales': getattr(self, 'sales', []),
                    'last_updated': datetime.now().isoformat()
                }
                with open(backup_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print(f"✅ تم حفظ البيانات في ملف احتياطي: {backup_file}")
                return True
            except:
                return False

    '''
        
        # Replace the function
        content = re.sub(old_save_data, new_save_data, content, flags=re.DOTALL)
        
        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح دالة حفظ البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح دالة الحفظ: {e}")
        return False

def fix_save_data_background():
    """Fix the save_data_background function"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and fix save_data_background function
        old_background = r'def save_data_background\(self\):(.*?)(?=def|\Z)'
        
        new_background = '''def save_data_background(self):
        """Background save with error handling"""
        try:
            # Prepare data
            data = {
                'suppliers': getattr(self, 'suppliers', []),
                'products': getattr(self, 'products', []),
                'customers': getattr(self, 'customers', []),
                'sales': getattr(self, 'sales', []),
                'last_updated': datetime.now().isoformat()
            }
            
            # Get data file path
            data_file = getattr(self, 'data_file', 'protech_simple_data.json')
            
            # Ensure directory exists
            data_dir = os.path.dirname(data_file) if os.path.dirname(data_file) else '.'
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)
            
            # Atomic write
            temp_file = data_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            # Move to final location
            if os.path.exists(temp_file):
                shutil.move(temp_file, data_file)
            
            print("💾 تم حفظ البيانات في الخلفية")
            
        except Exception as e:
            print(f"❌ خطأ في الحفظ التلقائي: {e}")
            # Try emergency save
            try:
                emergency_file = f'protech_emergency_save_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
                data = {
                    'suppliers': getattr(self, 'suppliers', []),
                    'products': getattr(self, 'products', []),
                    'customers': getattr(self, 'customers', []),
                    'sales': getattr(self, 'sales', []),
                    'last_updated': datetime.now().isoformat()
                }
                with open(emergency_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print(f"🚨 تم حفظ البيانات في ملف طوارئ: {emergency_file}")
            except:
                print("🚨 فشل في حفظ البيانات - يرجى حفظ عملك يدوياً")

    '''
        
        # Replace the function if it exists
        if 'def save_data_background(self):' in content:
            content = re.sub(old_background, new_background, content, flags=re.DOTALL)
            
            with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                f.write(content)
            
            print("✅ تم إصلاح دالة الحفظ التلقائي")
        else:
            print("ℹ️ دالة الحفظ التلقائي غير موجودة")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الحفظ التلقائي: {e}")
        return False

def add_missing_imports():
    """Add missing imports if needed"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check for required imports
        required_imports = ['import shutil', 'import os']
        missing_imports = []
        
        for imp in required_imports:
            if imp not in content:
                missing_imports.append(imp)
        
        if missing_imports:
            # Add missing imports after existing imports
            import_section = content.find('import json')
            if import_section != -1:
                insert_point = content.find('\n', import_section) + 1
                new_imports = '\n'.join(missing_imports) + '\n'
                content = content[:insert_point] + new_imports + content[insert_point:]
                
                with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"✅ تم إضافة المكتبات المفقودة: {', '.join(missing_imports)}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة المكتبات: {e}")
        return False

def test_file_compilation():
    """Test if the file compiles correctly"""
    try:
        import py_compile
        py_compile.compile('protech_simple_working.py', doraise=True)
        print("✅ الملف يعمل بدون أخطاء تركيبية")
        return True
    except Exception as e:
        print(f"❌ خطأ في تركيب الملف: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح خطأ حفظ البيانات في ProTech")
    print("🔧 Fixing Save Data Error in ProTech")
    print()
    
    try:
        # Step 1: Create backup
        backup_file()
        
        # Step 2: Add missing imports
        print("📦 فحص المكتبات المطلوبة...")
        add_missing_imports()
        
        # Step 3: Fix save_data function
        print("🔧 إصلاح دالة حفظ البيانات...")
        if fix_save_data_function():
            print("✅ تم إصلاح دالة الحفظ الرئيسية")
        
        # Step 4: Fix background save function
        print("🔧 إصلاح دالة الحفظ التلقائي...")
        if fix_save_data_background():
            print("✅ تم إصلاح دالة الحفظ التلقائي")
        
        # Step 5: Test compilation
        print("🧪 اختبار الملف...")
        if test_file_compilation():
            print("✅ الملف جاهز للتشغيل")
        
        print("\n" + "="*60)
        print("✅ تم إصلاح خطأ حفظ البيانات بنجاح!")
        print("✅ Save data error fixed successfully!")
        print("="*60)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إصلاح دالة حفظ البيانات الرئيسية")
        print("• إضافة معالجة شاملة للأخطاء")
        print("• حفظ ذري لضمان سلامة البيانات")
        print("• حفظ طوارئ عند فشل الحفظ العادي")
        print("• إنشاء المجلدات تلقائياً عند الحاجة")
        print("• معالجة أخطاء الصلاحيات")
        
        print("\n🚀 يمكنك الآن تشغيل البرنامج:")
        print("python protech_simple_working.py")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام في الإصلاح: {e}")
        return False

if __name__ == "__main__":
    main()
