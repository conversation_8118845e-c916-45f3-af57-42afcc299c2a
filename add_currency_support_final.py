#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Add Currency Support Final
إضافة دعم العملات النهائي

Add Lebanese Pound and USD support to reports
إضافة دعم الليرة اللبنانية والدولار للتقارير
"""

import os
import shutil
from datetime import datetime

def add_currency_support():
    """إضافة دعم العملات المتعددة"""
    try:
        print("💱 إضافة دعم الليرة اللبنانية والدولار")
        print("💱 Adding Lebanese Pound and USD Support")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.currency_final_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add currency methods
        currency_methods = '''
    def get_exchange_rates(self):
        """الحصول على أسعار الصرف"""
        return {
            'USD_TO_LBP': 89500,  # دولار إلى ليرة لبنانية
            'LBP_TO_USD': 1/89500  # ليرة لبنانية إلى دولار
        }
    
    def format_currency_lbp(self, amount):
        """تنسيق الليرة اللبنانية"""
        return f"{amount:,.0f} ل.ل"
    
    def format_currency_usd(self, amount):
        """تنسيق الدولار الأمريكي"""
        return f"${amount:,.2f}"
    
    def convert_to_usd(self, amount_lbp):
        """تحويل من ليرة لبنانية إلى دولار"""
        rates = self.get_exchange_rates()
        return amount_lbp * rates['LBP_TO_USD']
    
    def convert_to_lbp(self, amount_usd):
        """تحويل من دولار إلى ليرة لبنانية"""
        rates = self.get_exchange_rates()
        return amount_usd * rates['USD_TO_LBP']
    
    def show_store_balance_with_currencies(self):
        """عرض تقرير رصيد المحل بالعملتين"""
        try:
            self.report_title.config(text="رصيد المحل - ليرة لبنانية ودولار")
            
            # الحصول على البيانات الحقيقية
            products = self.get_real_products_data()
            sales = self.get_real_sales_data()
            customers = self.get_real_customers_data()
            
            # أسعار الصرف
            rates = self.get_exchange_rates()
            usd_to_lbp = rates['USD_TO_LBP']
            
            # حساب قيمة البضاعة
            total_inventory_cost_lbp = 0
            total_inventory_value_lbp = 0
            categories_summary = {}
            
            for product in products:
                stock = product.get('quantity', 0)
                base_price = product.get('base_price', 0)
                selling_price = product.get('shop_owner_price', base_price * 1.05)
                category = product.get('category', 'غير مصنف')
                
                cost_value_lbp = stock * base_price
                selling_value_lbp = stock * selling_price
                
                total_inventory_cost_lbp += cost_value_lbp
                total_inventory_value_lbp += selling_value_lbp
                
                if category not in categories_summary:
                    categories_summary[category] = {'cost': 0, 'selling': 0, 'count': 0}
                categories_summary[category]['cost'] += cost_value_lbp
                categories_summary[category]['selling'] += selling_value_lbp
                categories_summary[category]['count'] += stock
            
            # تحويل إلى الدولار
            total_inventory_cost_usd = self.convert_to_usd(total_inventory_cost_lbp)
            total_inventory_value_usd = self.convert_to_usd(total_inventory_value_lbp)
            
            # حساب المبيعات
            total_sales_lbp = sum(sale.get('total', 0) for sale in sales)
            total_sales_usd = self.convert_to_usd(total_sales_lbp)
            
            # حساب أرصدة العملاء
            total_customer_debt_lbp = 0
            total_customer_credit_lbp = 0
            
            for customer in customers:
                balance = customer.get('balance', 0)
                if balance > 0:
                    total_customer_credit_lbp += balance
                else:
                    total_customer_debt_lbp += abs(balance)
            
            total_customer_debt_usd = self.convert_to_usd(total_customer_debt_lbp)
            total_customer_credit_usd = self.convert_to_usd(total_customer_credit_lbp)
            
            # إنشاء التقرير
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            report = "رصيد المحل - الليرة اللبنانية والدولار الأمريكي\\n"
            report += "="*70 + "\\n\\n"
            report += "التاريخ: " + current_time + "\\n"
            report += "سعر الصرف: 1 USD = " + f"{usd_to_lbp:,.0f}" + " ل.ل\\n\\n"
            
            # قسم البضاعة
            report += "📦 رصيد البضاعة:\\n"
            report += "-"*50 + "\\n"
            report += "إجمالي المنتجات: " + str(len(products)) + " صنف\\n\\n"
            
            report += "قيمة البضاعة (سعر التكلفة):\\n"
            report += "• " + self.format_currency_lbp(total_inventory_cost_lbp) + "\\n"
            report += "• " + self.format_currency_usd(total_inventory_cost_usd) + "\\n\\n"
            
            report += "قيمة البضاعة (سعر البيع):\\n"
            report += "• " + self.format_currency_lbp(total_inventory_value_lbp) + "\\n"
            report += "• " + self.format_currency_usd(total_inventory_value_usd) + "\\n\\n"
            
            profit_lbp = total_inventory_value_lbp - total_inventory_cost_lbp
            profit_usd = self.convert_to_usd(profit_lbp)
            report += "الربح المتوقع من البضاعة:\\n"
            report += "• " + self.format_currency_lbp(profit_lbp) + "\\n"
            report += "• " + self.format_currency_usd(profit_usd) + "\\n\\n"
            
            # تفصيل الفئات
            if categories_summary:
                report += "تفصيل البضاعة حسب الفئات:\\n"
                for category, data in categories_summary.items():
                    cost_usd = self.convert_to_usd(data['cost'])
                    selling_usd = self.convert_to_usd(data['selling'])
                    
                    report += "• " + category + ":\\n"
                    report += "  الكمية: " + str(data['count']) + "\\n"
                    report += "  التكلفة: " + self.format_currency_lbp(data['cost']) + " / " + self.format_currency_usd(cost_usd) + "\\n"
                    report += "  البيع: " + self.format_currency_lbp(data['selling']) + " / " + self.format_currency_usd(selling_usd) + "\\n\\n"
            
            # قسم المبيعات
            report += "💰 المبيعات:\\n"
            report += "-"*50 + "\\n"
            report += "إجمالي المبيعات:\\n"
            report += "• " + self.format_currency_lbp(total_sales_lbp) + "\\n"
            report += "• " + self.format_currency_usd(total_sales_usd) + "\\n"
            report += "عدد الفواتير: " + str(len(sales)) + "\\n\\n"
            
            # أرصدة العملاء
            report += "👥 أرصدة العملاء:\\n"
            report += "-"*50 + "\\n"
            
            report += "ديون العملاء:\\n"
            report += "• " + self.format_currency_lbp(total_customer_debt_lbp) + "\\n"
            report += "• " + self.format_currency_usd(total_customer_debt_usd) + "\\n\\n"
            
            report += "أرصدة العملاء:\\n"
            report += "• " + self.format_currency_lbp(total_customer_credit_lbp) + "\\n"
            report += "• " + self.format_currency_usd(total_customer_credit_usd) + "\\n\\n"
            
            net_customer_balance_lbp = total_customer_credit_lbp - total_customer_debt_lbp
            net_customer_balance_usd = self.convert_to_usd(net_customer_balance_lbp)
            report += "صافي أرصدة العملاء:\\n"
            report += "• " + self.format_currency_lbp(net_customer_balance_lbp) + "\\n"
            report += "• " + self.format_currency_usd(net_customer_balance_usd) + "\\n\\n"
            
            # الرصيد الإجمالي
            report += "="*70 + "\\n"
            report += "📊 الرصيد الإجمالي للمحل:\\n"
            report += "="*70 + "\\n"
            
            total_assets_lbp = total_inventory_value_lbp + total_customer_credit_lbp + total_sales_lbp
            total_assets_usd = self.convert_to_usd(total_assets_lbp)
            net_worth_lbp = total_assets_lbp - total_customer_debt_lbp
            net_worth_usd = self.convert_to_usd(net_worth_lbp)
            
            report += "إجمالي الأصول:\\n"
            report += "• قيمة البضاعة: " + self.format_currency_lbp(total_inventory_value_lbp) + " / " + self.format_currency_usd(total_inventory_value_usd) + "\\n"
            report += "• المبيعات: " + self.format_currency_lbp(total_sales_lbp) + " / " + self.format_currency_usd(total_sales_usd) + "\\n"
            report += "• أرصدة العملاء: " + self.format_currency_lbp(total_customer_credit_lbp) + " / " + self.format_currency_usd(total_customer_credit_usd) + "\\n"
            report += "المجموع: " + self.format_currency_lbp(total_assets_lbp) + " / " + self.format_currency_usd(total_assets_usd) + "\\n\\n"
            
            report += "إجمالي الخصوم:\\n"
            report += "• ديون العملاء: " + self.format_currency_lbp(total_customer_debt_lbp) + " / " + self.format_currency_usd(total_customer_debt_usd) + "\\n\\n"
            
            report += "صافي رصيد المحل:\\n"
            report += "• " + self.format_currency_lbp(net_worth_lbp) + "\\n"
            report += "• " + self.format_currency_usd(net_worth_usd) + "\\n\\n"
            
            # تحليل العملة
            report += "💱 تحليل العملة:\\n"
            report += "-"*50 + "\\n"
            report += "سعر الصرف الحالي: 1 USD = " + f"{usd_to_lbp:,.0f}" + " ل.ل\\n"
            
            if net_worth_usd >= 10000:
                report += "رصيد ممتاز بالدولار (أكثر من 10,000 دولار)\\n"
            elif net_worth_usd >= 5000:
                report += "رصيد جيد بالدولار (5,000 - 10,000 دولار)\\n"
            elif net_worth_usd >= 1000:
                report += "رصيد متوسط بالدولار (1,000 - 5,000 دولار)\\n"
            else:
                report += "رصيد منخفض بالدولار (أقل من 1,000 دولار)\\n"
            
            # توصيات
            report += "\\n💡 التوصيات:\\n"
            report += "-"*50 + "\\n"
            
            if total_customer_debt_lbp > total_customer_credit_lbp:
                report += "• متابعة تحصيل ديون العملاء\\n"
            
            if net_worth_usd < 5000:
                report += "• العمل على زيادة رأس المال\\n"
            
            report += "• مراقبة أسعار الصرف يومياً\\n"
            report += "• تحديث أسعار المنتجات حسب سعر الصرف\\n"
            report += "• الاحتفاظ بجزء من الأرباح بالدولار\\n"
            report += "• مراجعة التقرير أسبوعياً\\n"
            report += "• استخدام التقرير لاتخاذ القرارات المالية\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
        except Exception as e:
            print("خطأ في عرض تقرير العملات:", str(e))
            error_report = "خطأ في تحميل تقرير العملات\\n"
            error_report += "تأكد من وجود البيانات وأعد المحاولة"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_report)
'''
        
        # Add currency methods before the last method
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + currency_methods + content[last_method:]
        else:
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + currency_methods + content[last_method:]
        
        # Add currency button
        if "btn6 = tk.Button(sidebar, text=\"رصيد المحل\"" in content:
            btn6_pos = content.find("btn6.pack(pady=3, padx=10, fill='x')")
            if btn6_pos != -1:
                btn6_end = content.find("\\n", btn6_pos) + 1
                
                new_button = '''
            btn_currency = tk.Button(sidebar, text="رصيد المحل (عملات)", 
                                   font=("Arial", 10), bg='#e67e22', fg='white',
                                   width=18, height=2, command=self.show_store_balance_with_currencies)
            btn_currency.pack(pady=3, padx=10, fill='x')
'''
                
                content = content[:btn6_end] + new_button + content[btn6_end:]
                print("✅ تم إضافة زر رصيد المحل (عملات)")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة دعم العملات المتعددة")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إضافة دعم العملات: {e}")
        return False

def main():
    """Main function"""
    print("💱 إضافة دعم الليرة اللبنانية والدولار الأمريكي")
    print("💱 Adding Lebanese Pound and USD Support")
    print("="*60)
    
    if add_currency_support():
        print("\n🎉 تم إضافة دعم العملات بنجاح!")
        
        print("\n💱 العملات المدعومة:")
        print("• 🇱🇧 الليرة اللبنانية (ل.ل)")
        print("• 🇺🇸 الدولار الأمريكي ($)")
        
        print("\n📊 التقارير المحدثة:")
        print("• تقرير رصيد المحل (عملات)")
        print("• عرض القيم بالعملتين")
        print("• تحويل تلقائي بين العملات")
        print("• تحليل العملة والتوصيات")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. انقر على 'رصيد المحل (عملات)'")
        print("4. استعرض التقرير بالعملتين")
        
        print("\n💡 الميزات:")
        print("• سعر صرف محدث (1 USD = 89,500 ل.ل)")
        print("• تحويل تلقائي بين العملات")
        print("• تحليل الوضع المالي")
        print("• توصيات مالية")
        print("• عرض واضح ومنظم")
        
    else:
        print("\n❌ فشل في إضافة دعم العملات")
        print("⚠️ تحقق من ملف ProTech")

if __name__ == "__main__":
    main()
