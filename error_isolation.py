
def safe_execute(func, *args, **kwargs):
    """Execute function safely with error isolation"""
    try:
        return func(*args, **kwargs)
    except Exception as e:
        print(f"⚠️ خطأ معزول: {e}")
        return None

def safe_print(*args, **kwargs):
    """Safe print function"""
    try:
        print(*args, **kwargs)
    except:
        pass

def safe_save(data, filename):
    """Safe save function"""
    try:
        import json
        import os
        
        # Try multiple save methods
        methods = [
            lambda: save_to_current_dir(data, filename),
            lambda: save_to_documents(data, filename),
            lambda: save_to_temp(data, filename)
        ]
        
        for method in methods:
            try:
                method()
                return True
            except:
                continue
        return False
    except:
        return False

def save_to_current_dir(data, filename):
    """Save to current directory"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def save_to_documents(data, filename):
    """Save to Documents folder"""
    import os
    docs_path = os.path.expanduser("~/Documents")
    full_path = os.path.join(docs_path, filename)
    with open(full_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)

def save_to_temp(data, filename):
    """Save to temp folder"""
    import tempfile
    import os
    temp_path = os.path.join(tempfile.gettempdir(), filename)
    with open(temp_path, 'w', encoding='utf-8') as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
