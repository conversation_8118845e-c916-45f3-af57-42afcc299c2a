#!/usr/bin/env python3
"""
Add Calculator Icon to ProTech
إضافة أيقونة آلة حاسبة لنظام ProTech

Modifies ProTech application to include calculator icon
تعديل تطبيق ProTech لإضافة أيقونة آلة حاسبة
"""

import os
import shutil
import re
from datetime import datetime

def create_calculator_icon_code():
    """Create Python code for calculator icon"""
    icon_code = '''
def set_calculator_icon(self):
    """Set calculator icon for the application"""
    try:
        # Try to use calculator.ico if it exists
        if os.path.exists("calculator.ico"):
            self.root.iconbitmap("calculator.ico")
            print("✅ تم تعيين أيقونة الآلة الحاسبة من ملف ICO")
            return True
        
        # Try to use Windows calculator icon
        calc_icon_paths = [
            r"C:\\Windows\\System32\\calc.exe",
            r"C:\\Windows\\System32\\shell32.dll",
            r"C:\\Windows\\System32\\imageres.dll"
        ]
        
        for icon_path in calc_icon_paths:
            if os.path.exists(icon_path):
                try:
                    self.root.iconbitmap(icon_path)
                    print(f"✅ تم تعيين أيقونة الآلة الحاسبة من {icon_path}")
                    return True
                except:
                    continue
        
        # Fallback: Create a simple icon using tkinter
        self.create_simple_calculator_icon()
        return True
        
    except Exception as e:
        print(f"⚠️ تعذر تعيين أيقونة الآلة الحاسبة: {e}")
        return False

def create_simple_calculator_icon(self):
    """Create a simple calculator icon using tkinter"""
    try:
        # Create a small window with calculator symbol
        icon_window = tk.Toplevel()
        icon_window.withdraw()
        
        # Set window properties to look like an icon
        icon_window.title("🧮 ProTech Calculator")
        icon_window.geometry("64x64")
        icon_window.resizable(False, False)
        
        # Create calculator-like appearance
        calc_frame = tk.Frame(icon_window, bg='#2563eb', relief='raised', bd=2)
        calc_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Calculator screen
        screen = tk.Label(calc_frame, text="ProTech", bg='#1f2937', fg='white', 
                         font=('Arial', 8, 'bold'))
        screen.pack(fill='x', padx=2, pady=2)
        
        # Calculator buttons (simplified)
        buttons_frame = tk.Frame(calc_frame, bg='#2563eb')
        buttons_frame.pack(fill='both', expand=True, padx=2, pady=2)
        
        # Create small buttons
        for i in range(3):
            for j in range(3):
                btn = tk.Button(buttons_frame, text=str(i*3+j+1), 
                               font=('Arial', 6), width=2, height=1,
                               bg='#f3f4f6', relief='raised', bd=1)
                btn.grid(row=i, column=j, padx=1, pady=1)
        
        # Use this window as icon reference
        icon_window.withdraw()
        
        print("✅ تم إنشاء أيقونة بسيطة للآلة الحاسبة")
        return True
        
    except Exception as e:
        print(f"⚠️ خطأ في إنشاء الأيقونة البسيطة: {e}")
        return False
'''
    return icon_code

def modify_protech_file(file_path):
    """Modify ProTech file to add calculator icon"""
    try:
        print(f"🔄 تعديل ملف ProTech: {file_path}")
        
        # Read the original file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        backup_path = f"{file_path}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(file_path, backup_path)
        print(f"💾 تم إنشاء نسخة احتياطية: {backup_path}")
        
        # Add import for os if not present
        if "import os" not in content:
            content = "import os\\n" + content
        
        # Find the class definition and __init__ method
        class_pattern = r'class\s+\w+.*?:'
        init_pattern = r'def\s+__init__\s*\(.*?\):'
        
        # Add the icon methods to the class
        icon_methods = create_calculator_icon_code()
        
        # Find a good place to insert the icon methods (before the last method or at the end of class)
        # Look for the end of __init__ method
        init_match = re.search(r'def\s+__init__\s*\([^)]*\):(.*?)(?=def\s+\w+|class\s+\w+|$)', content, re.DOTALL)
        
        if init_match:
            # Find the end of __init__ method
            init_end = init_match.end()
            
            # Insert icon setup call at the end of __init__
            init_content = init_match.group(0)
            
            # Add icon setup call before the end of __init__
            icon_setup_call = "\\n        # Set calculator icon\\n        self.set_calculator_icon()\\n"
            
            # Find the last line of __init__ (usually before next def or end of class)
            lines = init_content.split('\\n')
            
            # Add the call at the end of __init__
            modified_init = init_content + icon_setup_call
            
            # Replace the original __init__ with modified version
            content = content[:init_match.start()] + modified_init + content[init_end:]
            
            # Add the icon methods after the __init__ method
            content = content[:init_end + len(icon_setup_call)] + icon_methods + content[init_end + len(icon_setup_call):]
        
        else:
            # If we can't find __init__, just add the methods at the end of the file
            content += "\\n\\n" + icon_methods
        
        # Write the modified content
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تعديل ملف ProTech بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تعديل الملف: {e}")
        return False

def copy_icon_files():
    """Copy icon files to the program directory"""
    try:
        target_dir = r"C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        
        # Copy calculator icon files if they exist
        files_to_copy = [
            "calculator.ico",
            "set_calculator_icon.bat",
            "calculator_icon.eps"
        ]
        
        copied_files = []
        
        for file in files_to_copy:
            if os.path.exists(file):
                target_path = os.path.join(target_dir, file)
                shutil.copy2(file, target_path)
                copied_files.append(file)
                print(f"📁 تم نسخ {file} إلى مجلد البرنامج")
        
        if copied_files:
            print(f"✅ تم نسخ {len(copied_files)} ملف أيقونة")
        else:
            print("⚠️ لم يتم العثور على ملفات الأيقونة للنسخ")
        
        return len(copied_files) > 0
        
    except Exception as e:
        print(f"❌ خطأ في نسخ ملفات الأيقونة: {e}")
        return False

def create_icon_shortcut():
    """Create a shortcut with calculator icon"""
    try:
        shortcut_script = '''@echo off
REM ProTech Calculator Icon Shortcut
REM اختصار ProTech مع أيقونة الآلة الحاسبة

echo Creating ProTech shortcut with calculator icon...
echo إنشاء اختصار ProTech مع أيقونة الآلة الحاسبة...

REM Create VBS script to create shortcut
echo Set oWS = WScript.CreateObject("WScript.Shell") > CreateShortcut.vbs
echo sLinkFile = "%USERPROFILE%\\Desktop\\ProTech Calculator.lnk" >> CreateShortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> CreateShortcut.vbs
echo oLink.TargetPath = "%CD%\\protech_simple_working.py" >> CreateShortcut.vbs
echo oLink.Arguments = "" >> CreateShortcut.vbs
echo oLink.Description = "ProTech Accounting System with Calculator Icon" >> CreateShortcut.vbs
echo oLink.IconLocation = "C:\\Windows\\System32\\calc.exe,0" >> CreateShortcut.vbs
echo oLink.WorkingDirectory = "%CD%" >> CreateShortcut.vbs
echo oLink.Save >> CreateShortcut.vbs

REM Run the VBS script
cscript CreateShortcut.vbs

REM Clean up
del CreateShortcut.vbs

echo.
echo ✅ تم إنشاء اختصار ProTech مع أيقونة الآلة الحاسبة على سطح المكتب
echo ✅ ProTech shortcut with calculator icon created on desktop
echo.
pause
'''
        
        with open("create_protech_shortcut.bat", "w") as f:
            f.write(shortcut_script)
        
        print("✅ تم إنشاء ملف إنشاء الاختصار")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الاختصار: {e}")
        return False

def main():
    """Main function"""
    print("🧮 إضافة أيقونة آلة حاسبة لنظام ProTech")
    print("🧮 Adding Calculator Icon to ProTech System")
    print()
    
    try:
        # Step 1: Copy icon files
        print("📁 نسخ ملفات الأيقونة...")
        copy_icon_files()
        
        # Step 2: Modify ProTech file
        protech_file = r"C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program\\protech_simple_working.py"
        
        if os.path.exists(protech_file):
            print("🔧 تعديل ملف ProTech...")
            if modify_protech_file(protech_file):
                print("✅ تم تعديل ProTech بنجاح")
            else:
                print("❌ فشل في تعديل ProTech")
        else:
            print(f"❌ لم يتم العثور على ملف ProTech: {protech_file}")
        
        # Step 3: Create shortcut
        print("🔗 إنشاء اختصار مع أيقونة الآلة الحاسبة...")
        create_icon_shortcut()
        
        # Copy shortcut creator to program directory
        target_dir = r"C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        if os.path.exists("create_protech_shortcut.bat"):
            shutil.copy2("create_protech_shortcut.bat", 
                        os.path.join(target_dir, "create_protech_shortcut.bat"))
        
        print("\\n" + "="*60)
        print("✅ تم إضافة أيقونة الآلة الحاسبة لنظام ProTech بنجاح!")
        print("✅ Calculator icon successfully added to ProTech system!")
        print("="*60)
        
        print("\\n📋 الخطوات التالية / Next Steps:")
        print("1. تشغيل ProTech للتحقق من الأيقونة الجديدة")
        print("   Run ProTech to verify the new icon")
        print()
        print("2. تشغيل create_protech_shortcut.bat لإنشاء اختصار")
        print("   Run create_protech_shortcut.bat to create shortcut")
        print()
        print("3. إذا لم تظهر الأيقونة، استخدم الطريقة اليدوية:")
        print("   If icon doesn't appear, use manual method:")
        print("   - انقر بالزر الأيمن على الملف")
        print("   - اختر خصائص -> تغيير الأيقونة")
        print("   - اختر C:\\\\Windows\\\\System32\\\\calc.exe")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
