#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Clean and Add Statistics
تنظيف وإضافة الإحصائيات

Clean the file and add simple statistics system
تنظيف الملف وإضافة نظام إحصائيات بسيط
"""

import os
import shutil
from datetime import datetime

def clean_and_add_statistics():
    """تنظيف وإضافة نظام الإحصائيات"""
    try:
        print("🧹 تنظيف الملف وإضافة نظام الإحصائيات")
        print("🧹 Cleaning File and Adding Statistics System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.clean_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Remove any problematic analysis sections
        if 'analysis_text +=' in content:
            print("🧹 إزالة الأقسام المشكلة")
            
            # Find and remove problematic analysis sections
            lines = content.split('\\n')
            cleaned_lines = []
            skip_section = False
            
            for line in lines:
                if 'analysis_text +=' in line and 'Focus on increasing' in line:
                    skip_section = True
                    continue
                elif skip_section and (line.strip().startswith('if ') or line.strip().startswith('def ') or line.strip().startswith('except')):
                    skip_section = False
                
                if not skip_section:
                    cleaned_lines.append(line)
            
            content = '\\n'.join(cleaned_lines)
            print("✅ تم تنظيف الأقسام المشكلة")
        
        # Simple statistics method - clean version
        statistics_method = '''
    def create_advanced_statistics_page(self):
        """إنشاء صفحة الإحصائيات المتقدمة"""
        try:
            # Clear current page
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            
            self.current_page = 'advanced_statistics'
            
            # Main container
            main_container = tk.Frame(self.main_frame, bg='#f0f0f0')
            main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Title
            title_frame = tk.Frame(main_container, bg='#2c3e50', relief='ridge', bd=2)
            title_frame.pack(fill='x', pady=(0, 20))
            
            title_label = tk.Label(title_frame, text="📊 Advanced Business Statistics", 
                                 font=("Arial", 16, "bold"), bg='#2c3e50', fg='white')
            title_label.pack(pady=15)
            
            subtitle_label = tk.Label(title_frame, text="إحصائيات الأعمال المتقدمة", 
                                    font=("Arial", 12), bg='#2c3e50', fg='#ecf0f1')
            subtitle_label.pack(pady=(0, 15))
            
            # Get data for calculations
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Calculate key metrics
            total_products = len(products)
            total_customers = len(customers)
            total_sales = len(sales)
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            avg_sale = total_revenue / total_sales if total_sales > 0 else 0
            
            # Stats cards frame
            cards_frame = tk.Frame(main_container, bg='#f0f0f0')
            cards_frame.pack(fill='x', pady=(0, 20))
            
            # Create stats cards
            cards_data = [
                ("Products", f"{total_products:,}", "🛍️", "#3498db"),
                ("Customers", f"{total_customers:,}", "👥", "#e74c3c"),
                ("Sales", f"{total_sales:,}", "💰", "#2ecc71"),
                ("Revenue LBP", f"{total_revenue:,.0f}", "💵", "#f39c12"),
                ("Revenue USD", f"${total_revenue/exchange_rate:,.2f}", "💲", "#9b59b6"),
                ("Inventory", f"{inventory_value:,.0f}", "📦", "#1abc9c"),
                ("Debts", f"{customer_debt:,.0f}", "📋", "#e67e22"),
                ("Avg Sale", f"{avg_sale:,.0f}", "📈", "#34495e")
            ]
            
            # Create cards in grid
            for i, (title, value, icon, color) in enumerate(cards_data):
                row = i // 4
                col = i % 4
                
                card_frame = tk.Frame(cards_frame, bg=color, relief='raised', bd=3)
                card_frame.grid(row=row, col=col, padx=5, pady=5, sticky='ew')
                
                # Icon and title
                header_frame = tk.Frame(card_frame, bg=color)
                header_frame.pack(fill='x', padx=10, pady=(10, 5))
                
                icon_label = tk.Label(header_frame, text=icon, font=("Arial", 16), bg=color, fg='white')
                icon_label.pack(side='left')
                
                title_label = tk.Label(header_frame, text=title, font=("Arial", 9, "bold"), 
                                     bg=color, fg='white')
                title_label.pack(side='right')
                
                # Value
                value_label = tk.Label(card_frame, text=value, font=("Arial", 10, "bold"), 
                                     bg=color, fg='white')
                value_label.pack(pady=(0, 10))
                
                cards_frame.grid_columnconfigure(col, weight=1)
            
            # Table section
            table_section = tk.Frame(main_container, bg='#ffffff', relief='ridge', bd=2)
            table_section.pack(fill='both', expand=True)
            
            # Table header
            header_frame = tk.Frame(table_section, bg='#34495e')
            header_frame.pack(fill='x')
            
            header_label = tk.Label(header_frame, text="📊 Detailed Business Analysis", 
                                  font=("Arial", 14, "bold"), bg='#34495e', fg='white')
            header_label.pack(side='left', padx=15, pady=10)
            
            # Export button
            export_btn = tk.Button(header_frame, text="Export Data", 
                                 command=self.export_statistics_data,
                                 bg='#e74c3c', fg='white', font=("Arial", 9, "bold"))
            export_btn.pack(side='right', padx=15, pady=10)
            
            # Table content
            table_frame = tk.Frame(table_section, bg='#ffffff')
            table_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Create scrollable text widget for table
            text_widget = tk.Text(table_frame, font=("Courier", 9), wrap='none', height=20)
            scrollbar_v = tk.Scrollbar(table_frame, orient="vertical", command=text_widget.yview)
            scrollbar_h = tk.Scrollbar(table_frame, orient="horizontal", command=text_widget.xview)
            
            text_widget.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
            
            scrollbar_v.pack(side="right", fill="y")
            scrollbar_h.pack(side="bottom", fill="x")
            text_widget.pack(side="left", fill="both", expand=True)
            
            # Create detailed analysis content
            analysis_content = self.generate_business_analysis(products, customers, sales, exchange_rate)
            
            text_widget.insert(1.0, analysis_content)
            text_widget.config(state='disabled')
            
            # Store for export
            self.current_analysis_data = analysis_content
            
            print("✅ تم إنشاء صفحة الإحصائيات المتقدمة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء صفحة الإحصائيات: {e}")
    
    def generate_business_analysis(self, products, customers, sales, exchange_rate):
        """إنشاء تحليل الأعمال"""
        try:
            content = "COMPREHENSIVE BUSINESS ANALYSIS\\n"
            content += "="*80 + "\\n\\n"
            
            # Calculate metrics
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            
            # Financial overview
            content += "FINANCIAL OVERVIEW:\\n"
            content += "-"*50 + "\\n"
            content += f"Total Revenue:      {total_revenue:>15,.0f} LBP\\n"
            content += f"Total Revenue:      {total_revenue/exchange_rate:>15,.2f} USD\\n"
            content += f"Inventory Value:    {inventory_value:>15,.0f} LBP\\n"
            content += f"Customer Debts:     {customer_debt:>15,.0f} LBP\\n"
            content += f"Net Business Worth: {total_revenue + inventory_value:>15,.0f} LBP\\n\\n"
            
            # Product analysis
            content += "PRODUCT ANALYSIS:\\n"
            content += "-"*50 + "\\n"
            
            low_stock = [p for p in products if float(p.get('quantity', 0)) <= 10]
            high_value = [p for p in products if float(p.get('quantity', 0)) * float(p.get('base_price', 0)) > 500000]
            
            content += f"Total Products:     {len(products):>15}\\n"
            content += f"Low Stock Items:    {len(low_stock):>15}\\n"
            content += f"High Value Items:   {len(high_value):>15}\\n\\n"
            
            if low_stock:
                content += "LOW STOCK PRODUCTS:\\n"
                for product in low_stock[:5]:
                    name = product.get('name', '')[:20]
                    qty = float(product.get('quantity', 0))
                    content += f"  {name:<20} Qty: {qty:>5.0f}\\n"
                content += "\\n"
            
            # Customer analysis
            content += "CUSTOMER ANALYSIS:\\n"
            content += "-"*50 + "\\n"
            
            active_customers = [c for c in customers if float(c.get('balance', 0)) != 0]
            debt_customers = [c for c in customers if float(c.get('balance', 0)) > 0]
            
            content += f"Total Customers:    {len(customers):>15}\\n"
            content += f"Active Customers:   {len(active_customers):>15}\\n"
            content += f"Customers w/ Debt:  {len(debt_customers):>15}\\n\\n"
            
            # Sales analysis
            content += "SALES ANALYSIS:\\n"
            content += "-"*50 + "\\n"
            
            avg_sale = total_revenue / len(sales) if sales else 0
            
            content += f"Total Sales:        {len(sales):>15}\\n"
            content += f"Average Sale:       {avg_sale:>15,.0f} LBP\\n"
            content += f"Average Sale:       {avg_sale/exchange_rate:>15,.2f} USD\\n\\n"
            
            # Business health
            content += "BUSINESS HEALTH ASSESSMENT:\\n"
            content += "-"*50 + "\\n"
            
            net_worth_usd = (total_revenue + inventory_value) / exchange_rate
            
            if net_worth_usd >= 50000:
                health = "EXCELLENT"
            elif net_worth_usd >= 25000:
                health = "VERY GOOD"
            elif net_worth_usd >= 10000:
                health = "GOOD"
            else:
                health = "NEEDS IMPROVEMENT"
            
            content += f"Business Health:    {health:>15}\\n"
            content += f"Net Worth (USD):    ${net_worth_usd:>14,.2f}\\n\\n"
            
            # Recommendations
            content += "BUSINESS RECOMMENDATIONS:\\n"
            content += "-"*50 + "\\n"
            
            if len(low_stock) > 0:
                content += f"• Restock {len(low_stock)} low inventory items\\n"
            if avg_sale < 100000:
                content += "• Focus on increasing average sale value\\n"
            if len(debt_customers) > len(customers) * 0.3:
                content += "• Follow up on customer debt collection\\n"
            
            content += "• Monitor inventory turnover rates\\n"
            content += "• Track customer satisfaction levels\\n"
            content += "• Update pricing strategies regularly\\n"
            content += "• Analyze seasonal sales patterns\\n"
            
            return content
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التحليل: {e}")
            return "Error generating analysis"
    
    def export_statistics_data(self):
        """تصدير بيانات الإحصائيات"""
        try:
            if hasattr(self, 'current_analysis_data'):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"protech_business_analysis_{timestamp}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.current_analysis_data)
                
                print(f"✅ تم تصدير التحليل إلى {filename}")
                
                # Show success message
                try:
                    import tkinter.messagebox as msgbox
                    msgbox.showinfo("Export Success", f"Business analysis exported to {filename}")
                except:
                    pass
            else:
                print("❌ لا يوجد تحليل لتصديره")
                
        except Exception as e:
            print(f"❌ خطأ في تصدير التحليل: {e}")
'''
        
        # Add the statistics method
        last_method = content.rfind("\n    def export_current_table(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + statistics_method + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + statistics_method + content[last_method:]
        
        # Update the statistics button
        if 'btn6 = tk.Button(sidebar, text="رصيد المحل"' in content:
            # Find and replace the button
            start_pos = content.find('btn6 = tk.Button(sidebar, text="رصيد المحل"')
            if start_pos != -1:
                # Find the end of this button definition
                end_pos = start_pos
                paren_count = 0
                in_button = False
                
                for i, char in enumerate(content[start_pos:]):
                    if char == '(':
                        paren_count += 1
                        in_button = True
                    elif char == ')':
                        paren_count -= 1
                        if in_button and paren_count == 0:
                            end_pos = start_pos + i + 1
                            break
                
                old_button = content[start_pos:end_pos]
                new_button = '''btn6 = tk.Button(sidebar, text="Advanced Stats", 
                           font=("Arial", 10), bg='#16a085', fg='white', 
                           width=18, height=2, command=self.create_advanced_statistics_page)'''
                
                content = content.replace(old_button, new_button)
                print("✅ تم تحديث زر الإحصائيات")
            else:
                print("❌ لم يتم العثور على بداية تعريف الزر")
                return False
        else:
            print("❌ لم يتم العثور على زر رصيد المحل")
            return False
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تنظيف الملف وإضافة نظام الإحصائيات")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تنظيف وإضافة نظام الإحصائيات: {e}")
        return False

def main():
    """Main function"""
    print("🧹 تنظيف وإضافة نظام الإحصائيات لـ ProTech")
    print("🧹 Cleaning and Adding Statistics System for ProTech")
    print("="*70)
    
    if clean_and_add_statistics():
        print("\n🎉 تم تنظيف الملف وإضافة نظام الإحصائيات بنجاح!")
        
        print("\n📊 الميزات الجديدة:")
        print("• 📈 صفحة إحصائيات متقدمة ونظيفة")
        print("• 🎯 8 بطاقات إحصائيات ملونة")
        print("• 📋 تحليل أعمال شامل ومفصل")
        print("• 📊 تقييم صحة الأعمال")
        print("• 📤 تصدير التحليل")
        print("• 🔄 حسابات فورية ودقيقة")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Advanced Stats' في الشريط الجانبي")
        print("3. استعرض بطاقات الإحصائيات السريعة")
        print("4. اقرأ التحليل التفصيلي للأعمال")
        print("5. انقر على 'Export Data' لتصدير التحليل")
        print("6. راجع التوصيات لتحسين الأداء")
        
        print("\n💡 المعلومات المعروضة:")
        print("• النظرة المالية الشاملة")
        print("• تحليل المنتجات والمخزون")
        print("• تحليل العملاء والديون")
        print("• تحليل المبيعات والأداء")
        print("• تقييم صحة الأعمال")
        print("• توصيات عملية للتحسين")
        
    else:
        print("\n❌ فشل في تنظيف وإضافة نظام الإحصائيات")
    
    print("\n🔧 تم الانتهاء من العملية")

if __name__ == "__main__":
    main()
