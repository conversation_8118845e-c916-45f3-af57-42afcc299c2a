#!/usr/bin/env python3
"""
ProTech Icon Patch - تصحيح أيقونة ProTech
إضافة أيقونة آلة حاسبة مباشرة لنظام ProTech

Direct calculator icon addition to ProTech system
إضافة مباشرة لأيقونة آلة حاسبة لنظام ProTech
"""

import os
import shutil
import re
from datetime import datetime

def create_simple_icon_patch():
    """Create a simple icon patch for ProTech"""
    
    # Simple icon code that works with tkinter
    icon_patch = '''
        # Set calculator icon for ProTech
        try:
            # Try different methods to set calculator icon
            icon_methods = [
                lambda: self.root.iconbitmap(default="C:/Windows/System32/calc.exe"),
                lambda: self.root.wm_iconbitmap("C:/Windows/System32/calc.exe"),
                lambda: self.root.tk.call('wm', 'iconbitmap', self.root._w, "C:/Windows/System32/calc.exe"),
            ]
            
            for method in icon_methods:
                try:
                    method()
                    print("✅ تم تعيين أيقونة الآلة الحاسبة")
                    break
                except:
                    continue
            else:
                # Fallback: Set window title with calculator emoji
                current_title = self.root.title()
                self.root.title(f"🧮 {current_title}")
                print("✅ تم إضافة رمز الآلة الحاسبة للعنوان")
                
        except Exception as e:
            print(f"⚠️ تعذر تعيين أيقونة الآلة الحاسبة: {e}")
            # Fallback: Just add calculator emoji to title
            try:
                current_title = self.root.title()
                if not current_title.startswith("🧮"):
                    self.root.title(f"🧮 {current_title}")
            except:
                pass
'''
    
    return icon_patch

def patch_protech_file():
    """Patch the ProTech file with calculator icon"""
    try:
        protech_file = r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_working.py"
        
        if not os.path.exists(protech_file):
            print(f"❌ ملف ProTech غير موجود: {protech_file}")
            return False
        
        print(f"🔧 تطبيق تصحيح الأيقونة على: {protech_file}")
        
        # Read the file
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Create backup
        backup_path = f"{protech_file}.icon_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_path)
        print(f"💾 نسخة احتياطية: {backup_path}")
        
        # Find where to insert the icon code
        # Look for self.root.geometry or similar tkinter setup
        patterns_to_find = [
            r'self\.root\.geometry\([^)]+\)',
            r'self\.root\.title\([^)]+\)',
            r'self\.root\.configure\([^)]+\)',
            r'self\.root = tk\.Tk\(\)'
        ]
        
        insertion_point = -1
        for pattern in patterns_to_find:
            match = re.search(pattern, content)
            if match:
                insertion_point = match.end()
                break
        
        if insertion_point == -1:
            # If we can't find a good insertion point, add at the end of __init__
            init_match = re.search(r'def __init__\(.*?\):(.*?)(?=def|\Z)', content, re.DOTALL)
            if init_match:
                insertion_point = init_match.end() - 1
        
        if insertion_point != -1:
            # Insert the icon patch
            icon_code = create_simple_icon_patch()
            
            # Insert the code
            new_content = content[:insertion_point] + "\n" + icon_code + "\n" + content[insertion_point:]
            
            # Write the modified file
            with open(protech_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ تم تطبيق تصحيح الأيقونة بنجاح")
            return True
        else:
            print("❌ لم يتم العثور على نقطة إدراج مناسبة")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في تطبيق التصحيح: {e}")
        return False

def create_icon_launcher():
    """Create a launcher script with icon"""
    try:
        launcher_script = '''#!/usr/bin/env python3
"""
ProTech Calculator Icon Launcher
مشغل ProTech مع أيقونة الآلة الحاسبة
"""

import tkinter as tk
import subprocess
import sys
import os

def set_calculator_icon(root):
    """Set calculator icon for window"""
    try:
        # Try to set calculator icon
        icon_paths = [
            "C:/Windows/System32/calc.exe",
            "C:/Windows/System32/shell32.dll",
        ]
        
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                try:
                    root.iconbitmap(icon_path)
                    return True
                except:
                    continue
        
        # Fallback: Add calculator emoji to title
        current_title = root.title()
        if not current_title.startswith("🧮"):
            root.title(f"🧮 {current_title}")
        
        return True
        
    except Exception as e:
        print(f"Icon error: {e}")
        return False

def launch_protech():
    """Launch ProTech with calculator icon"""
    try:
        # Create a temporary window to test icon
        test_root = tk.Tk()
        test_root.withdraw()
        
        # Set the icon
        set_calculator_icon(test_root)
        
        # Close test window
        test_root.destroy()
        
        # Launch the actual ProTech
        protech_file = "protech_simple_working.py"
        if os.path.exists(protech_file):
            subprocess.run([sys.executable, protech_file])
        else:
            print("ProTech file not found!")
            
    except Exception as e:
        print(f"Launch error: {e}")

if __name__ == "__main__":
    launch_protech()
'''
        
        launcher_path = "protech_calculator_launcher.py"
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_script)
        
        # Copy to program directory
        target_dir = r"C:\Users\<USER>\OneDrive\Desktop\accounting program"
        target_path = os.path.join(target_dir, launcher_path)
        shutil.copy2(launcher_path, target_path)
        
        print(f"✅ تم إنشاء مشغل الأيقونة: {launcher_path}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء المشغل: {e}")
        return False

def create_desktop_shortcut():
    """Create desktop shortcut with calculator icon"""
    try:
        # Create a batch file that creates the shortcut
        shortcut_bat = '''@echo off
echo Creating ProTech shortcut with calculator icon...

powershell -Command "& {
    $WshShell = New-Object -comObject WScript.Shell
    $Shortcut = $WshShell.CreateShortcut('%USERPROFILE%\\Desktop\\ProTech Calculator.lnk')
    $Shortcut.TargetPath = 'python.exe'
    $Shortcut.Arguments = '%CD%\\protech_simple_working.py'
    $Shortcut.WorkingDirectory = '%CD%'
    $Shortcut.IconLocation = 'C:\\Windows\\System32\\calc.exe,0'
    $Shortcut.Description = 'ProTech Accounting System'
    $Shortcut.Save()
}"

echo ✅ ProTech shortcut created on desktop with calculator icon
pause
'''
        
        with open("create_shortcut.bat", "w") as f:
            f.write(shortcut_bat)
        
        # Copy to program directory
        target_dir = r"C:\Users\<USER>\OneDrive\Desktop\accounting program"
        shutil.copy2("create_shortcut.bat", os.path.join(target_dir, "create_shortcut.bat"))
        
        print("✅ تم إنشاء ملف إنشاء الاختصار")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف الاختصار: {e}")
        return False

def main():
    """Main function"""
    print("🧮 تطبيق أيقونة الآلة الحاسبة على ProTech")
    print("🧮 Applying Calculator Icon to ProTech")
    print()
    
    try:
        # Method 1: Patch the existing file
        print("🔧 الطريقة 1: تعديل الملف الموجود...")
        if patch_protech_file():
            print("✅ تم تطبيق التصحيح بنجاح")
        
        # Method 2: Create launcher
        print("🚀 الطريقة 2: إنشاء مشغل مع أيقونة...")
        if create_icon_launcher():
            print("✅ تم إنشاء المشغل بنجاح")
        
        # Method 3: Create desktop shortcut
        print("🔗 الطريقة 3: إنشاء اختصار سطح المكتب...")
        if create_desktop_shortcut():
            print("✅ تم إنشاء ملف الاختصار بنجاح")
        
        print("\n" + "="*60)
        print("✅ تم تطبيق أيقونة الآلة الحاسبة بنجاح!")
        print("✅ Calculator icon successfully applied!")
        print("="*60)
        
        print("\n📋 طرق التشغيل المتاحة:")
        print("1. تشغيل ProTech العادي (مع الأيقونة المدمجة)")
        print("   python protech_simple_working.py")
        print()
        print("2. تشغيل المشغل المخصص")
        print("   python protech_calculator_launcher.py")
        print()
        print("3. إنشاء اختصار سطح المكتب")
        print("   تشغيل create_shortcut.bat")
        print()
        print("📋 Available launch methods:")
        print("1. Normal ProTech (with embedded icon)")
        print("2. Custom launcher")
        print("3. Desktop shortcut creation")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
