/**
 * ProTech Accounting System - Enhanced JavaScript Functions
 * Comprehensive client-side functionality for the accounting system
 */

// Global ProTech object
window.ProTech = {
    config: {
        apiBaseUrl: '/api',
        refreshInterval: 30000, // 30 seconds
        searchDelay: 300, // 300ms debounce
        animationDuration: 200
    },
    
    cache: new Map(),
    
    // Initialize the application
    init: function() {
        console.log('🚀 ProTech Accounting System - Initializing...');
        
        this.setupEventListeners();
        this.initializeComponents();
        this.startPerformanceMonitoring();
        
        console.log('✅ ProTech System initialized successfully');
    },
    
    // Setup global event listeners
    setupEventListeners: function() {
        // Global keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
        
        // Global click tracking
        document.addEventListener('click', this.handleGlobalClicks.bind(this));
        
        // Form validation
        document.addEventListener('submit', this.handleFormSubmission.bind(this));
        
        // Auto-save functionality
        document.addEventListener('input', this.debounce(this.handleAutoSave.bind(this), 1000));
    },
    
    // Initialize UI components
    initializeComponents: function() {
        this.initializeModals();
        this.initializeTooltips();
        this.initializeSearchBoxes();
        this.initializeDataTables();
        this.initializeCharts();
    },
    
    // Performance monitoring
    startPerformanceMonitoring: function() {
        // Monitor page load time
        const loadTime = performance.now();
        console.log(`📊 Page load time: ${loadTime.toFixed(2)}ms`);
        
        // Monitor memory usage
        if (performance.memory) {
            const memoryMB = (performance.memory.usedJSHeapSize / 1024 / 1024).toFixed(2);
            console.log(`💾 Memory usage: ${memoryMB}MB`);
        }
        
        // Start periodic performance checks
        setInterval(this.checkPerformance.bind(this), 60000); // Every minute
    },
    
    // Check system performance
    checkPerformance: function() {
        const now = performance.now();
        
        // Check if page is responsive
        const testStart = performance.now();
        setTimeout(() => {
            const responseTime = performance.now() - testStart;
            if (responseTime > 100) {
                console.warn(`⚠️ Slow response detected: ${responseTime.toFixed(2)}ms`);
            }
        }, 0);
    },
    
    // Handle keyboard shortcuts
    handleKeyboardShortcuts: function(e) {
        // Ctrl+K for global search
        if (e.ctrlKey && e.key === 'k') {
            e.preventDefault();
            this.openGlobalSearch();
        }
        
        // Ctrl+N for new item (context-dependent)
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.handleNewItem();
        }
        
        // Escape to close modals
        if (e.key === 'Escape') {
            this.closeAllModals();
        }
        
        // F5 to refresh data
        if (e.key === 'F5') {
            e.preventDefault();
            this.refreshCurrentPage();
        }
    },
    
    // Handle global clicks
    handleGlobalClicks: function(e) {
        // Track navigation
        const link = e.target.closest('a[href]');
        if (link && !link.href.startsWith('javascript:')) {
            console.log(`🔗 Navigation: ${link.href}`);
        }
        
        // Handle button clicks
        const button = e.target.closest('button[data-action]');
        if (button) {
            const action = button.dataset.action;
            this.handleButtonAction(action, button);
        }
    },
    
    // Handle form submissions
    handleFormSubmission: function(e) {
        const form = e.target;
        
        // Validate form
        if (!this.validateForm(form)) {
            e.preventDefault();
            return false;
        }
        
        // Show loading state
        this.showFormLoading(form);
        
        console.log(`📝 Form submitted: ${form.id || 'unnamed'}`);
    },
    
    // Auto-save functionality
    handleAutoSave: function(e) {
        const input = e.target;
        
        if (input.dataset.autosave === 'true') {
            const data = {
                field: input.name,
                value: input.value,
                timestamp: new Date().toISOString()
            };
            
            localStorage.setItem(`autosave_${input.name}`, JSON.stringify(data));
            console.log(`💾 Auto-saved: ${input.name}`);
        }
    },
    
    // API Functions
    api: {
        // Generic API call
        call: async function(endpoint, options = {}) {
            const url = `${ProTech.config.apiBaseUrl}${endpoint}`;
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            };
            
            const finalOptions = { ...defaultOptions, ...options };
            
            try {
                const response = await fetch(url, finalOptions);
                const data = await response.json();
                
                if (!response.ok) {
                    throw new Error(data.error || `HTTP ${response.status}`);
                }
                
                return data;
            } catch (error) {
                console.error(`❌ API Error (${endpoint}):`, error);
                ProTech.showError(`API Error: ${error.message}`);
                throw error;
            }
        },
        
        // Get products
        getProducts: function(filters = {}) {
            const params = new URLSearchParams(filters);
            return this.call(`/products?${params}`);
        },
        
        // Get dashboard stats
        getDashboardStats: function() {
            return this.call('/dashboard-stats');
        },
        
        // Adjust stock
        adjustStock: function(productId, newQuantity, reason, notes = '') {
            return this.call('/adjust-stock', {
                method: 'POST',
                body: JSON.stringify({
                    product_id: productId,
                    new_quantity: newQuantity,
                    reason: reason,
                    notes: notes
                })
            });
        },
        
        // Global search
        search: function(query) {
            return this.call(`/search?q=${encodeURIComponent(query)}`);
        }
    },
    
    // UI Helper Functions
    showSuccess: function(message) {
        this.showNotification(message, 'success');
    },
    
    showError: function(message) {
        this.showNotification(message, 'error');
    },
    
    showWarning: function(message) {
        this.showNotification(message, 'warning');
    },
    
    showInfo: function(message) {
        this.showNotification(message, 'info');
    },
    
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm transform transition-all duration-300 translate-x-full`;
        
        const colors = {
            success: 'bg-green-100 border-green-400 text-green-700',
            error: 'bg-red-100 border-red-400 text-red-700',
            warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
            info: 'bg-blue-100 border-blue-400 text-blue-700'
        };
        
        notification.className += ` ${colors[type]} border`;
        
        const icons = {
            success: '✅',
            error: '❌',
            warning: '⚠️',
            info: 'ℹ️'
        };
        
        notification.innerHTML = `
            <div class="flex items-center">
                <span class="mr-2">${icons[type]}</span>
                <span class="flex-1">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" class="ml-2 text-gray-500 hover:text-gray-700">
                    ×
                </button>
            </div>
        `;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.classList.remove('translate-x-full');
        }, 100);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            notification.classList.add('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    },
    
    // Modal functions
    openModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
            document.body.style.overflow = 'hidden';
            
            // Focus first input
            const firstInput = modal.querySelector('input, select, textarea');
            if (firstInput) {
                setTimeout(() => firstInput.focus(), 100);
            }
        }
    },
    
    closeModal: function(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.add('hidden');
            document.body.style.overflow = '';
        }
    },
    
    closeAllModals: function() {
        const modals = document.querySelectorAll('.modal, [id$="Modal"]');
        modals.forEach(modal => {
            modal.classList.add('hidden');
        });
        document.body.style.overflow = '';
    },
    
    // Utility functions
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    formatCurrency: function(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(amount);
    },
    
    formatDate: function(date) {
        return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        }).format(new Date(date));
    },
    
    // Component initialization functions
    initializeModals: function() {
        // Auto-close modals when clicking outside
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('modal-backdrop')) {
                this.closeAllModals();
            }
        });
    },
    
    initializeTooltips: function() {
        // Simple tooltip implementation
        const tooltipElements = document.querySelectorAll('[title]');
        tooltipElements.forEach(element => {
            element.addEventListener('mouseenter', this.showTooltip.bind(this));
            element.addEventListener('mouseleave', this.hideTooltip.bind(this));
        });
    },
    
    initializeSearchBoxes: function() {
        const searchBoxes = document.querySelectorAll('input[type="search"], input[placeholder*="search"], input[placeholder*="Search"]');
        searchBoxes.forEach(searchBox => {
            searchBox.addEventListener('input', this.debounce((e) => {
                this.handleSearch(e.target);
            }, this.config.searchDelay));
        });
    },
    
    initializeDataTables: function() {
        // Add sorting functionality to tables
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            this.makeTableSortable(table);
        });
    },
    
    initializeCharts: function() {
        // Initialize any charts on the page
        console.log('📊 Charts initialized');
    },
    
    // Additional helper functions
    showTooltip: function(e) {
        // Tooltip implementation
    },
    
    hideTooltip: function(e) {
        // Hide tooltip implementation
    },
    
    handleSearch: function(searchInput) {
        const query = searchInput.value.trim();
        console.log(`🔍 Search: ${query}`);
        
        if (query.length >= 2) {
            // Perform search
            this.performSearch(query, searchInput);
        }
    },
    
    performSearch: async function(query, searchInput) {
        try {
            const results = await this.api.search(query);
            this.displaySearchResults(results, searchInput);
        } catch (error) {
            console.error('Search error:', error);
        }
    },
    
    displaySearchResults: function(results, searchInput) {
        // Display search results
        console.log('Search results:', results);
    },
    
    makeTableSortable: function(table) {
        const headers = table.querySelectorAll('th');
        headers.forEach((header, index) => {
            header.style.cursor = 'pointer';
            header.addEventListener('click', () => {
                this.sortTable(table, index);
            });
        });
    },
    
    sortTable: function(table, columnIndex) {
        // Table sorting implementation
        console.log(`📊 Sorting table by column ${columnIndex}`);
    },
    
    // Page-specific functions
    openGlobalSearch: function() {
        console.log('🔍 Opening global search');
    },
    
    handleNewItem: function() {
        const currentPage = window.location.pathname;
        console.log(`➕ New item for page: ${currentPage}`);
    },
    
    refreshCurrentPage: function() {
        console.log('🔄 Refreshing current page data');
        window.location.reload();
    },
    
    handleButtonAction: function(action, button) {
        console.log(`🔘 Button action: ${action}`);
        
        switch (action) {
            case 'refresh':
                this.refreshCurrentPage();
                break;
            case 'export':
                this.exportData();
                break;
            case 'print':
                window.print();
                break;
            default:
                console.log(`Unknown action: ${action}`);
        }
    },
    
    validateForm: function(form) {
        // Form validation logic
        return true;
    },
    
    showFormLoading: function(form) {
        // Show loading state for form
        const submitButton = form.querySelector('button[type="submit"]');
        if (submitButton) {
            submitButton.disabled = true;
            submitButton.innerHTML = 'جاري المعالجة... / Processing...';
        }
    },
    
    exportData: function() {
        console.log('📤 Exporting data');
    }
};

// Initialize when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    ProTech.init();
});

// Global functions for backward compatibility
function showSuccess(message) {
    ProTech.showSuccess(message);
}

function showError(message) {
    ProTech.showError(message);
}

function formatCurrency(amount) {
    return ProTech.formatCurrency(amount);
}
