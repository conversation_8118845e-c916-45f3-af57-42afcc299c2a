import { z } from 'zod';

// User validation schemas
export const userSchema = z.object({
  email: z.string().email('Invalid email address'),
  username: z.string().min(3, 'Username must be at least 3 characters'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
  role: z.enum(['ADMIN', 'MANAGER', 'USER']).default('USER'),
  isActive: z.boolean().default(true),
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

// Customer validation schemas
export const customerSchema = z.object({
  code: z.string().min(1, 'Customer code is required'),
  name: z.string().min(1, 'Customer name is required'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  taxNumber: z.string().optional(),
  creditLimit: z.number().min(0, 'Credit limit must be positive').default(0),
  category: z.enum(['RETAIL', 'WHOLESALE', 'DISTRIBUTOR', 'VIP']).default('RETAIL'),
  priceLevel: z.number().min(1).max(4).default(1),
  notes: z.string().optional(),
});

// Supplier validation schemas
export const supplierSchema = z.object({
  code: z.string().min(1, 'Supplier code is required'),
  name: z.string().min(1, 'Supplier name is required'),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  phone: z.string().optional(),
  address: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  taxNumber: z.string().optional(),
  paymentTerms: z.string().optional(),
  notes: z.string().optional(),
});

// Category validation schemas
export const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  description: z.string().optional(),
  parentId: z.string().optional(),
});

// Product validation schemas
export const productSchema = z.object({
  code: z.string().min(1, 'Product code is required'),
  name: z.string().min(1, 'Product name is required'),
  description: z.string().optional(),
  barcode: z.string().optional(),
  categoryId: z.string().optional(),
  supplierId: z.string().optional(),
  unit: z.string().min(1, 'Unit is required').default('PCS'),
  costPrice: z.number().min(0, 'Cost price must be positive').default(0),
  basePrice: z.number().min(0, 'Base price must be positive').default(0),
  currentStock: z.number().int('Stock must be a whole number').default(0),
  minStock: z.number().int('Min stock must be a whole number').min(0).default(0),
  maxStock: z.number().int('Max stock must be a whole number').min(0).default(0),
  location: z.string().optional(),
  trackInventory: z.boolean().default(true),
  allowNegative: z.boolean().default(false),
  weight: z.number().min(0).optional(),
  dimensions: z.string().optional(),
  notes: z.string().optional(),
});

// Invoice validation schemas
export const invoiceItemSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  description: z.string().optional(),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitPrice: z.number().min(0, 'Unit price must be positive'),
  discount: z.number().min(0).max(100, 'Discount cannot exceed 100%').default(0),
  taxRate: z.number().min(0).max(100, 'Tax rate cannot exceed 100%').default(0),
});

export const invoiceSchema = z.object({
  customerId: z.string().min(1, 'Customer is required'),
  date: z.date().default(() => new Date()),
  dueDate: z.date().optional(),
  status: z.enum(['DRAFT', 'SENT', 'PAID', 'OVERDUE', 'CANCELLED', 'REFUNDED']).default('DRAFT'),
  notes: z.string().optional(),
  terms: z.string().optional(),
  items: z.array(invoiceItemSchema).min(1, 'At least one item is required'),
});

// Purchase validation schemas
export const purchaseItemSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  description: z.string().optional(),
  quantity: z.number().min(1, 'Quantity must be at least 1'),
  unitCost: z.number().min(0, 'Unit cost must be positive'),
  discount: z.number().min(0).max(100, 'Discount cannot exceed 100%').default(0),
  taxRate: z.number().min(0).max(100, 'Tax rate cannot exceed 100%').default(0),
});

export const purchaseSchema = z.object({
  supplierId: z.string().min(1, 'Supplier is required'),
  date: z.date().default(() => new Date()),
  expectedDate: z.date().optional(),
  status: z.enum(['DRAFT', 'SENT', 'CONFIRMED', 'RECEIVED', 'CANCELLED']).default('DRAFT'),
  notes: z.string().optional(),
  terms: z.string().optional(),
  items: z.array(purchaseItemSchema).min(1, 'At least one item is required'),
});

// Payment validation schemas
export const paymentSchema = z.object({
  customerId: z.string().optional(),
  invoiceId: z.string().optional(),
  amount: z.number().min(0.01, 'Amount must be greater than 0'),
  method: z.enum(['CASH', 'CARD', 'BANK_TRANSFER', 'CHECK', 'CREDIT']),
  reference: z.string().optional(),
  notes: z.string().optional(),
  date: z.date().default(() => new Date()),
});

// Inventory movement validation schemas
export const inventoryMovementSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  type: z.enum(['IN', 'OUT', 'ADJUSTMENT', 'TRANSFER']),
  quantity: z.number().int('Quantity must be a whole number'),
  unitCost: z.number().min(0).optional(),
  reason: z.string().optional(),
  referenceType: z.string().optional(),
  referenceId: z.string().optional(),
  notes: z.string().optional(),
});

// Settings validation schemas
export const settingSchema = z.object({
  key: z.string().min(1, 'Setting key is required'),
  value: z.string().min(1, 'Setting value is required'),
  type: z.string().default('string'),
  category: z.string().default('general'),
});

// Search and pagination schemas
export const paginationSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  search: z.string().optional(),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('asc'),
});

// Barcode validation schemas
export const barcodeSchema = z.object({
  code: z.string().min(1, 'Barcode is required'),
  format: z.enum(['CODE128', 'EAN13', 'UPCA']).default('CODE128'),
});

// Report validation schemas
export const reportDateRangeSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
  customerId: z.string().optional(),
  supplierId: z.string().optional(),
  categoryId: z.string().optional(),
  productId: z.string().optional(),
});

// File upload validation schemas
export const fileUploadSchema = z.object({
  file: z.instanceof(File),
  maxSize: z.number().default(5 * 1024 * 1024), // 5MB
  allowedTypes: z.array(z.string()).default(['image/jpeg', 'image/png', 'image/gif']),
});

// Bulk operations validation schemas
export const bulkDeleteSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one item must be selected'),
});

export const bulkUpdateSchema = z.object({
  ids: z.array(z.string()).min(1, 'At least one item must be selected'),
  data: z.record(z.any()),
});

// Price calculation validation schemas
export const priceCalculationSchema = z.object({
  costPrice: z.number().min(0, 'Cost price must be positive'),
  basePrice: z.number().min(0, 'Base price must be positive'),
  priceLevel: z.number().min(1).max(4),
});

// Stock adjustment validation schemas
export const stockAdjustmentSchema = z.object({
  productId: z.string().min(1, 'Product is required'),
  newQuantity: z.number().int('Quantity must be a whole number').min(0),
  reason: z.string().min(1, 'Reason is required'),
  notes: z.string().optional(),
});

// Export validation schemas
export const exportSchema = z.object({
  format: z.enum(['CSV', 'EXCEL', 'PDF']).default('CSV'),
  dateRange: reportDateRangeSchema.optional(),
  filters: z.record(z.any()).optional(),
});

// Import validation schemas
export const importSchema = z.object({
  file: z.instanceof(File),
  type: z.enum(['products', 'customers', 'suppliers']),
  skipFirstRow: z.boolean().default(true),
  mapping: z.record(z.string()),
});

// API response validation schemas
export const apiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  message: z.string().optional(),
  error: z.string().optional(),
});

export const paginatedResponseSchema = z.object({
  data: z.array(z.any()),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

// Type exports for TypeScript
export type UserInput = z.infer<typeof userSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type CustomerInput = z.infer<typeof customerSchema>;
export type SupplierInput = z.infer<typeof supplierSchema>;
export type CategoryInput = z.infer<typeof categorySchema>;
export type ProductInput = z.infer<typeof productSchema>;
export type InvoiceInput = z.infer<typeof invoiceSchema>;
export type InvoiceItemInput = z.infer<typeof invoiceItemSchema>;
export type PurchaseInput = z.infer<typeof purchaseSchema>;
export type PurchaseItemInput = z.infer<typeof purchaseItemSchema>;
export type PaymentInput = z.infer<typeof paymentSchema>;
export type InventoryMovementInput = z.infer<typeof inventoryMovementSchema>;
export type SettingInput = z.infer<typeof settingSchema>;
export type PaginationInput = z.infer<typeof paginationSchema>;
export type BarcodeInput = z.infer<typeof barcodeSchema>;
export type ReportDateRangeInput = z.infer<typeof reportDateRangeSchema>;
export type FileUploadInput = z.infer<typeof fileUploadSchema>;
export type BulkDeleteInput = z.infer<typeof bulkDeleteSchema>;
export type BulkUpdateInput = z.infer<typeof bulkUpdateSchema>;
export type PriceCalculationInput = z.infer<typeof priceCalculationSchema>;
export type StockAdjustmentInput = z.infer<typeof stockAdjustmentSchema>;
export type ExportInput = z.infer<typeof exportSchema>;
export type ImportInput = z.infer<typeof importSchema>;
