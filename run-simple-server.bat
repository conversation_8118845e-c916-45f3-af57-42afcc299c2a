@echo off
title ProTech Accounting - Simple Server
color 0A

echo.
echo ========================================
echo    ProTech Accounting System
echo    Simple Node.js Server
echo ========================================
echo.

REM Check if Node.js exists
if not exist "C:\Program Files\nodejs\node.exe" (
    echo ERROR: Node.js not found at C:\Program Files\nodejs\
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

echo ✓ Node.js found
echo ✓ Starting simple server...
echo.

REM Start the server
echo Starting ProTech Accounting server on port 3000...
echo.
echo ┌─────────────────────────────────────────┐
echo │  ProTech Accounting System              │
echo │                                         │
echo │  🌐 URL: http://localhost:3000          │
echo │  📱 Access from any device on network   │
echo │                                         │
echo │  Press Ctrl+C to stop the server       │
echo └─────────────────────────────────────────┘
echo.

REM Run the server
"C:\Program Files\nodejs\node.exe" simple-server.js

echo.
echo Server stopped.
pause
