# ProTech Accounting System | نظام ProTech للمحاسبة

**نظام محاسبة شامل مع إدارة المخزون ومسح الباركود**
**Comprehensive accounting software with inventory management and barcode scanning**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-3.1.1-green.svg)](https://flask.palletsprojects.com)
[![Status](https://img.shields.io/badge/Status-Production%20Ready-brightgreen.svg)]()

## Features

### Core Functionality
- **Sales Management**: Invoicing, customer transactions, returns
- **Purchase Management**: Supplier orders, receiving, payments
- **Inventory Management**: Real-time tracking with low-stock alerts
- **Customer Relationship Management (CRM)**
- **Supplier Management**
- **Multi-level Pricing System**: 4 price tiers with customer categories
- **Barcode Scanning and Generation**: Camera-based and external scanner support

### Technical Features
- **Responsive Web Application**: Built with React and Next.js
- **Bilingual Support**: Arabic RTL and English LTR
- **Modern UI/UX**: Tailwind CSS with customizable components
- **Database**: PostgreSQL with Prisma ORM
- **Barcode Support**: Code 128, EAN-13, UPC-A formats
- **Reporting System**: Comprehensive business analytics
- **Real-time Updates**: Live inventory and sales tracking

## Tech Stack

- **Frontend**: Next.js 15, React 18, TypeScript
- **Styling**: Tailwind CSS
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT-based authentication
- **Barcode**: JSBarcode for generation, camera integration for scanning
- **Charts**: Recharts for analytics
- **Forms**: React Hook Form with Zod validation
- **Icons**: Heroicons and Lucide React

## Prerequisites

Before running this application, make sure you have the following installed:

- Node.js (v18 or higher)
- PostgreSQL (v12 or higher)
- npm or yarn package manager

## Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd protech-accounting
   ```

2. **Install dependencies**
   ```bash
   npm install
   # or
   yarn install
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   ```
   
   Edit the `.env` file and update the following variables:
   ```env
   DATABASE_URL="postgresql://username:password@localhost:5432/protech_accounting"
   NEXTAUTH_SECRET="your-secret-key-here"
   JWT_SECRET="your-jwt-secret-here"
   ```

4. **Set up the database**
   
   Create a PostgreSQL database:
   ```sql
   CREATE DATABASE protech_accounting;
   ```
   
   Run Prisma migrations:
   ```bash
   npx prisma generate
   npx prisma db push
   ```

5. **Seed the database (optional)**
   ```bash
   npm run db:seed
   ```

6. **Start the development server**
   ```bash
   npm run dev
   ```

   The application will be available at `http://localhost:3000`

## Project Structure

```
src/
├── app/                    # Next.js app directory
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # Reusable components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── layout/           # Layout components
│   └── modules/          # Feature-specific components
├── lib/                  # Utility libraries
│   ├── db.ts            # Database connection
│   ├── utils.ts         # Utility functions
│   └── validations.ts   # Zod schemas
├── types/               # TypeScript type definitions
└── hooks/               # Custom React hooks
```

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run start` - Start production server
- `npm run lint` - Run ESLint
- `npm run db:generate` - Generate Prisma client
- `npm run db:push` - Push schema to database
- `npm run db:migrate` - Run database migrations
- `npm run db:studio` - Open Prisma Studio

## Database Schema

The application uses a comprehensive database schema with the following main entities:

- **Users**: System users with role-based access
- **Customers**: Customer information with credit limits and pricing levels
- **Suppliers**: Supplier management and relationships
- **Products**: Product catalog with variants and pricing tiers
- **Categories**: Hierarchical product categorization
- **Invoices**: Sales invoices with line items
- **Purchases**: Purchase orders and receiving
- **Payments**: Payment tracking and methods
- **Inventory**: Stock movements and tracking

## Pricing System

The system supports a 4-tier pricing structure:

1. **Base Price**: Cost price + default profit margin
2. **Price Level 1**: Base selling price + 5%
3. **Price Level 2**: Base selling price + 15%
4. **Price Level 3**: Base selling price + 20%
5. **Price Level 4**: Base selling price + 30%

Customers are assigned to different categories (Retail, Wholesale, Distributor, VIP) with corresponding price levels.

## Barcode System

- **Generation**: Automatic barcode generation for products
- **Formats**: Support for Code 128, EAN-13, and UPC-A
- **Scanning**: Camera-based scanning with external scanner support
- **Printing**: Customizable barcode label templates

## Internationalization

The application supports:
- **English (LTR)**: Left-to-right layout
- **Arabic (RTL)**: Right-to-left layout with proper text direction

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Support

For support and questions, please contact the development team or create an issue in the repository.

## Roadmap

- [ ] Mobile application (React Native)
- [ ] Advanced reporting and analytics
- [ ] Multi-location inventory management
- [ ] Integration with external accounting systems
- [ ] Advanced barcode scanning features
- [ ] Automated backup and restore
- [ ] API documentation and third-party integrations
