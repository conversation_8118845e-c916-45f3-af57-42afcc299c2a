#!/usr/bin/env python3
"""
ProTech Accounting System - Simple Desktop Application
نظام ProTech للمحاسبة - تطبيق سطح مكتب بسيط

A simple but complete desktop accounting application
تطبيق محاسبة بسيط ولكن كامل لسطح المكتب
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime

class ProTechSimpleDesktop:
    def __init__(self):
        # Create main window
        self.root = tk.Tk()
        self.root.title("نظام ProTech للمحاسبة - ProTech Accounting System")
        self.root.geometry("1000x700")
        self.root.configure(bg='#f0f8ff')
        
        # Data storage
        self.data_file = "protech_data.json"
        self.load_data()
        
        # Create interface
        self.create_interface()
        
        # Show welcome message
        self.show_welcome()
    
    def load_data(self):
        """Load data from JSON file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    self.products = data.get('products', [])
                    self.customers = data.get('customers', [])
                    self.sales = data.get('sales', [])
            else:
                self.create_sample_data()
        except Exception as e:
            print(f"Error loading data: {e}")
            self.create_sample_data()
    
    def save_data(self):
        """Save data to JSON file"""
        try:
            data = {
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'last_updated': datetime.now().isoformat()
            }
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"فشل في حفظ البيانات\nFailed to save data:\n{str(e)}")
    
    def create_sample_data(self):
        """Create sample data"""
        self.products = [
            {
                'id': 1,
                'code': 'LAPTOP001',
                'name': 'Business Laptop',
                'name_ar': 'لابتوب الأعمال',
                'price': 1000.0,
                'stock': 50,
                'min_stock': 10
            },
            {
                'id': 2,
                'code': 'MOUSE001',
                'name': 'Wireless Mouse',
                'name_ar': 'فأرة لاسلكية',
                'price': 25.0,
                'stock': 200,
                'min_stock': 50
            },
            {
                'id': 3,
                'code': 'PHONE001',
                'name': 'Smartphone',
                'name_ar': 'هاتف ذكي',
                'price': 550.0,
                'stock': 5,
                'min_stock': 10
            }
        ]
        
        self.customers = [
            {
                'id': 1,
                'name': 'John Smith',
                'name_ar': 'جون سميث',
                'email': '<EMAIL>',
                'balance': 1250.0
            },
            {
                'id': 2,
                'name': 'ABC Corporation',
                'name_ar': 'شركة ABC',
                'email': '<EMAIL>',
                'balance': 8750.0
            }
        ]
        
        self.sales = []
        self.save_data()
    
    def create_interface(self):
        """Create the main interface"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#2563eb', height=80)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)
        
        tk.Label(
            header_frame,
            text="🎉 نظام ProTech للمحاسبة",
            font=('Arial', 20, 'bold'),
            fg='white',
            bg='#2563eb'
        ).pack(side='left', padx=20, pady=20)
        
        tk.Label(
            header_frame,
            text="ProTech Accounting System - Desktop Application",
            font=('Arial', 12),
            fg='#bfdbfe',
            bg='#2563eb'
        ).pack(side='left', padx=(0, 20), pady=(35, 20))
        
        # Current time
        time_label = tk.Label(
            header_frame,
            text=f"🕒 {datetime.now().strftime('%Y-%m-%d %H:%M')}",
            font=('Arial', 11),
            fg='white',
            bg='#2563eb'
        )
        time_label.pack(side='right', padx=20, pady=20)
        
        # Main content
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Left panel - Navigation
        nav_frame = tk.Frame(main_frame, bg='#3b82f6', width=200)
        nav_frame.pack(side='left', fill='y', padx=(0, 10))
        nav_frame.pack_propagate(False)
        
        # Navigation buttons
        nav_buttons = [
            ("🏠 لوحة التحكم\nDashboard", self.show_dashboard),
            ("📦 المخزون\nInventory", self.show_inventory),
            ("👥 العملاء\nCustomers", self.show_customers),
            ("💰 المبيعات\nSales", self.show_sales),
            ("📊 التقارير\nReports", self.show_reports),
            ("❓ المساعدة\nHelp", self.show_help)
        ]
        
        for text, command in nav_buttons:
            btn = tk.Button(
                nav_frame,
                text=text,
                font=('Arial', 11, 'bold'),
                fg='white',
                bg='#3b82f6',
                activebackground='#1d4ed8',
                relief='flat',
                width=18,
                height=3,
                command=command
            )
            btn.pack(pady=5, padx=10, fill='x')
        
        # Right panel - Content
        self.content_frame = tk.Frame(main_frame, bg='white', relief='ridge', bd=2)
        self.content_frame.pack(side='right', fill='both', expand=True)
        
        # Status bar
        status_frame = tk.Frame(self.root, bg='#374151', height=30)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز / Ready",
            font=('Arial', 10),
            fg='white',
            bg='#374151'
        )
        self.status_label.pack(side='left', padx=10, pady=5)
        
        tk.Label(
            status_frame,
            text="ProTech Desktop v1.0 | © 2024",
            font=('Arial', 9),
            fg='#9ca3af',
            bg='#374151'
        ).pack(side='right', padx=10, pady=5)
    
    def clear_content(self):
        """Clear content frame"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()
    
    def update_status(self, message):
        """Update status message"""
        self.status_label.config(text=message)
    
    def show_welcome(self):
        """Show welcome message"""
        messagebox.showinfo(
            "مرحباً / Welcome",
            "مرحباً بك في نظام ProTech للمحاسبة!\n\nWelcome to ProTech Accounting System!\n\n✅ تطبيق سطح مكتب مستقل\n✅ Standalone desktop application\n\n🚀 جاهز للاستخدام!"
        )
        self.show_dashboard()
    
    def show_dashboard(self):
        """Show dashboard"""
        self.clear_content()
        self.update_status("عرض لوحة التحكم / Showing dashboard")
        
        # Title
        tk.Label(
            self.content_frame,
            text="📊 لوحة التحكم / Dashboard",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)
        
        # Statistics
        stats_frame = tk.Frame(self.content_frame, bg='white')
        stats_frame.pack(fill='x', padx=20, pady=10)
        
        # Calculate statistics
        total_products = len(self.products)
        low_stock = len([p for p in self.products if p['stock'] <= p['min_stock']])
        total_customers = len(self.customers)
        total_sales = len(self.sales)
        
        # Create stat cards
        stats = [
            ("📦", "المنتجات\nProducts", total_products, "#3b82f6"),
            ("⚠️", "مخزون منخفض\nLow Stock", low_stock, "#ef4444"),
            ("👥", "العملاء\nCustomers", total_customers, "#10b981"),
            ("💰", "المبيعات\nSales", total_sales, "#8b5cf6")
        ]
        
        for icon, title, value, color in stats:
            card = tk.Frame(stats_frame, bg=color, relief='raised', bd=2)
            card.pack(side='left', fill='both', expand=True, padx=5)
            
            tk.Label(card, text=icon, font=('Arial', 20), fg='white', bg=color).pack(pady=5)
            tk.Label(card, text=title, font=('Arial', 10, 'bold'), fg='white', bg=color).pack()
            tk.Label(card, text=str(value), font=('Arial', 16, 'bold'), fg='white', bg=color).pack(pady=5)
        
        # Welcome message
        welcome_frame = tk.Frame(self.content_frame, bg='#dcfce7', relief='ridge', bd=2)
        welcome_frame.pack(fill='x', padx=20, pady=20)
        
        tk.Label(
            welcome_frame,
            text="🎉 مرحباً بك في نظام ProTech للمحاسبة",
            font=('Arial', 16, 'bold'),
            fg='#16a34a',
            bg='#dcfce7'
        ).pack(pady=10)
        
        tk.Label(
            welcome_frame,
            text="Welcome to ProTech Accounting System",
            font=('Arial', 14),
            fg='#15803d',
            bg='#dcfce7'
        ).pack(pady=(0, 10))
        
        # Quick actions
        actions_frame = tk.LabelFrame(
            self.content_frame,
            text="إجراءات سريعة / Quick Actions",
            font=('Arial', 12, 'bold'),
            bg='white'
        )
        actions_frame.pack(fill='x', padx=20, pady=10)
        
        btn_frame = tk.Frame(actions_frame, bg='white')
        btn_frame.pack(pady=10)
        
        quick_buttons = [
            ("📦 عرض المخزون", self.show_inventory, "#3b82f6"),
            ("👥 عرض العملاء", self.show_customers, "#10b981"),
            ("💰 تسجيل بيع", self.show_sales, "#8b5cf6"),
            ("📊 التقارير", self.show_reports, "#f59e0b")
        ]
        
        for text, command, color in quick_buttons:
            tk.Button(
                btn_frame,
                text=text,
                font=('Arial', 11, 'bold'),
                bg=color,
                fg='white',
                width=15,
                height=2,
                command=command
            ).pack(side='left', padx=5)
    
    def show_inventory(self):
        """Show inventory"""
        self.clear_content()
        self.update_status("عرض المخزون / Showing inventory")
        
        # Title
        tk.Label(
            self.content_frame,
            text="📦 إدارة المخزون / Inventory Management",
            font=('Arial', 16, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=10)
        
        # Products table
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Create treeview
        columns = ('Code', 'Name', 'Name_AR', 'Price', 'Stock', 'Status')
        tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        
        # Define headings
        headings = ['الكود', 'الاسم', 'الاسم بالعربية', 'السعر', 'المخزون', 'الحالة']
        for col, heading in zip(columns, headings):
            tree.heading(col, text=heading)
            tree.column(col, width=120, anchor='center')
        
        # Add data
        for product in self.products:
            status = "منخفض" if product['stock'] <= product['min_stock'] else "جيد"
            tree.insert('', 'end', values=(
                product['code'],
                product['name'],
                product['name_ar'],
                f"${product['price']:.2f}",
                product['stock'],
                status
            ))
        
        # Add scrollbar
        scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=tree.yview)
        tree.configure(yscrollcommand=scrollbar.set)
        
        tree.pack(side='left', fill='both', expand=True)
        scrollbar.pack(side='right', fill='y')
        
        # Buttons
        btn_frame = tk.Frame(self.content_frame, bg='white')
        btn_frame.pack(fill='x', padx=20, pady=10)
        
        tk.Button(
            btn_frame,
            text="➕ إضافة منتج / Add Product",
            font=('Arial', 11, 'bold'),
            bg='#10b981',
            fg='white',
            command=self.add_product
        ).pack(side='left', padx=5)
        
        tk.Button(
            btn_frame,
            text="🔄 تحديث / Refresh",
            font=('Arial', 11, 'bold'),
            bg='#3b82f6',
            fg='white',
            command=self.show_inventory
        ).pack(side='left', padx=5)

    def add_product(self):
        """Add new product"""
        dialog = tk.Toplevel(self.root)
        dialog.title("إضافة منتج جديد / Add New Product")
        dialog.geometry("400x500")
        dialog.configure(bg='#f0f8ff')
        dialog.transient(self.root)
        dialog.grab_set()

        # Center dialog
        dialog.update_idletasks()
        x = (dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (dialog.winfo_screenheight() // 2) - (500 // 2)
        dialog.geometry(f"400x500+{x}+{y}")

        # Title
        tk.Label(
            dialog,
            text="📦 إضافة منتج جديد",
            font=('Arial', 16, 'bold'),
            fg='#1f2937',
            bg='#f0f8ff'
        ).pack(pady=20)

        # Form
        form_frame = tk.Frame(dialog, bg='#f0f8ff')
        form_frame.pack(fill='both', expand=True, padx=20)

        entries = {}
        fields = [
            ('code', 'كود المنتج / Product Code'),
            ('name', 'اسم المنتج (إنجليزي) / Product Name (English)'),
            ('name_ar', 'اسم المنتج (عربي) / Product Name (Arabic)'),
            ('price', 'السعر / Price'),
            ('stock', 'الكمية / Stock'),
            ('min_stock', 'الحد الأدنى / Min Stock')
        ]

        for field, label in fields:
            tk.Label(
                form_frame,
                text=label,
                font=('Arial', 11, 'bold'),
                fg='#374151',
                bg='#f0f8ff'
            ).pack(anchor='w', pady=(10, 5))

            entry = tk.Entry(form_frame, font=('Arial', 11), width=40)
            entry.pack(fill='x', pady=(0, 10))
            entries[field] = entry

        # Buttons
        btn_frame = tk.Frame(dialog, bg='#f0f8ff')
        btn_frame.pack(fill='x', padx=20, pady=20)

        def save_product():
            try:
                new_product = {
                    'id': len(self.products) + 1,
                    'code': entries['code'].get().strip(),
                    'name': entries['name'].get().strip(),
                    'name_ar': entries['name_ar'].get().strip(),
                    'price': float(entries['price'].get().strip()),
                    'stock': int(entries['stock'].get().strip()),
                    'min_stock': int(entries['min_stock'].get().strip())
                }

                # Validate
                if not all([new_product['code'], new_product['name'], new_product['name_ar']]):
                    messagebox.showerror("خطأ / Error", "يرجى ملء جميع الحقول\nPlease fill all fields")
                    return

                # Check if code exists
                if any(p['code'] == new_product['code'] for p in self.products):
                    messagebox.showerror("خطأ / Error", "كود المنتج موجود مسبقاً\nProduct code already exists")
                    return

                self.products.append(new_product)
                self.save_data()
                messagebox.showinfo("نجح / Success", "تم إضافة المنتج بنجاح\nProduct added successfully")
                dialog.destroy()
                self.show_inventory()

            except ValueError:
                messagebox.showerror("خطأ / Error", "قيم غير صحيحة\nInvalid values")
            except Exception as e:
                messagebox.showerror("خطأ / Error", f"خطأ: {str(e)}")

        tk.Button(
            btn_frame,
            text="💾 حفظ / Save",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            command=save_product
        ).pack(side='left', padx=5)

        tk.Button(
            btn_frame,
            text="❌ إلغاء / Cancel",
            font=('Arial', 12, 'bold'),
            bg='#6b7280',
            fg='white',
            command=dialog.destroy
        ).pack(side='right', padx=5)

    def show_customers(self):
        """Show customers"""
        self.clear_content()
        self.update_status("عرض العملاء / Showing customers")

        tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء / Customer Management",
            font=('Arial', 16, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Customers list
        for customer in self.customers:
            customer_frame = tk.Frame(self.content_frame, bg='#f9fafb', relief='ridge', bd=1)
            customer_frame.pack(fill='x', padx=20, pady=5)

            tk.Label(
                customer_frame,
                text=f"👤 {customer['name']} | {customer['name_ar']}",
                font=('Arial', 12, 'bold'),
                fg='#1f2937',
                bg='#f9fafb'
            ).pack(side='left', padx=10, pady=10)

            tk.Label(
                customer_frame,
                text=f"💰 ${customer['balance']:.2f}",
                font=('Arial', 11, 'bold'),
                fg='#10b981',
                bg='#f9fafb'
            ).pack(side='right', padx=10, pady=10)

    def show_sales(self):
        """Show sales"""
        self.clear_content()
        self.update_status("عرض المبيعات / Showing sales")

        tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات / Sales Management",
            font=('Arial', 16, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        tk.Label(
            self.content_frame,
            text="🚧 قيد التطوير / Under Development",
            font=('Arial', 14),
            fg='#6b7280',
            bg='white'
        ).pack(pady=10)

    def show_reports(self):
        """Show reports"""
        self.clear_content()
        self.update_status("عرض التقارير / Showing reports")

        tk.Label(
            self.content_frame,
            text="📊 التقارير / Reports",
            font=('Arial', 16, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Simple report
        report_frame = tk.Frame(self.content_frame, bg='white')
        report_frame.pack(fill='both', expand=True, padx=20, pady=10)

        report_text = tk.Text(report_frame, font=('Arial', 11), bg='#f9fafb', wrap='word')
        report_text.pack(fill='both', expand=True)

        # Generate simple report
        total_products = len(self.products)
        total_value = sum(p['price'] * p['stock'] for p in self.products)
        low_stock_items = [p for p in self.products if p['stock'] <= p['min_stock']]

        report_content = f"""
📊 تقرير النظام / System Report
{'='*50}

📦 إحصائيات المخزون / Inventory Statistics:
   • إجمالي المنتجات / Total Products: {total_products}
   • قيمة المخزون / Inventory Value: ${total_value:,.2f}
   • منتجات منخفضة المخزون / Low Stock Items: {len(low_stock_items)}

👥 إحصائيات العملاء / Customer Statistics:
   • إجمالي العملاء / Total Customers: {len(self.customers)}
   • إجمالي الأرصدة / Total Balances: ${sum(c['balance'] for c in self.customers):,.2f}

💰 إحصائيات المبيعات / Sales Statistics:
   • إجمالي المبيعات / Total Sales: {len(self.sales)}

⚠️ تنبيهات / Alerts:
"""

        if low_stock_items:
            report_content += "\n   منتجات منخفضة المخزون / Low Stock Products:\n"
            for item in low_stock_items:
                report_content += f"   • {item['name']} ({item['name_ar']}) - المخزون: {item['stock']}\n"
        else:
            report_content += "\n   ✅ جميع المنتجات لديها مخزون كافي / All products have sufficient stock\n"

        report_content += f"\n\n📅 تاريخ التقرير / Report Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        report_text.insert('1.0', report_content)
        report_text.config(state='disabled')

    def show_help(self):
        """Show help"""
        self.clear_content()
        self.update_status("عرض المساعدة / Showing help")

        tk.Label(
            self.content_frame,
            text="❓ المساعدة / Help",
            font=('Arial', 16, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        help_frame = tk.Frame(self.content_frame, bg='white')
        help_frame.pack(fill='both', expand=True, padx=20, pady=10)

        help_text = tk.Text(help_frame, font=('Arial', 11), bg='#f9fafb', wrap='word')
        help_text.pack(fill='both', expand=True)

        help_content = """
🎉 مرحباً بك في نظام ProTech للمحاسبة
Welcome to ProTech Accounting System

📋 كيفية الاستخدام / How to Use:

1. 🏠 لوحة التحكم / Dashboard
   - عرض الإحصائيات العامة
   - الإجراءات السريعة

2. 📦 إدارة المخزون / Inventory
   - عرض جميع المنتجات
   - إضافة منتجات جديدة
   - مراقبة المخزون المنخفض

3. 👥 إدارة العملاء / Customers
   - عرض قائمة العملاء
   - متابعة الأرصدة

4. 💰 المبيعات / Sales
   - تسجيل عمليات البيع (قيد التطوير)

5. 📊 التقارير / Reports
   - تقارير شاملة عن النظام
   - إحصائيات مفصلة

🔧 نصائح / Tips:
- البيانات تُحفظ تلقائياً في ملف JSON
- استخدم الأزرار في القائمة الجانبية للتنقل
- راجع التقارير بانتظام

📞 الدعم / Support:
📧 <EMAIL>
🌐 www.protech.com
📱 +966-11-123-4567
        """

        help_text.insert('1.0', help_content)
        help_text.config(state='disabled')

    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"خطأ في التطبيق\nApplication error:\n{str(e)}")
        finally:
            self.save_data()

def main():
    """Main function"""
    try:
        print("🖥️ Starting ProTech Simple Desktop Application...")
        print("🖥️ تشغيل تطبيق ProTech البسيط لسطح المكتب...")

        app = ProTechSimpleDesktop()
        app.run()

    except Exception as e:
        print(f"❌ Error starting application: {e}")
        try:
            messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق\nFailed to start application:\n{str(e)}")
        except:
            print("Failed to show error dialog")

if __name__ == '__main__':
    main()
