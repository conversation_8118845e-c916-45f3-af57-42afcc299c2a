#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Min Stock Issue in ProTech
إصلاح مشكلة الحد الأدنى للمخزون في ProTech

Fix the min_stock field issue that prevents app from starting
إصلاح مشكلة حقل min_stock التي تمنع بدء التطبيق
"""

import os
import json
import shutil
from datetime import datetime

def fix_min_stock_in_data():
    """Fix min_stock field in data file"""
    try:
        print("🔧 إصلاح مشكلة الحد الأدنى للمخزون في البيانات...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        if not os.path.exists(data_path):
            print("❌ ملف البيانات غير موجود!")
            return False
        
        # Create backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{data_path}.min_stock_fix_backup_{timestamp}"
        shutil.copy2(data_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}")
        
        # Read current data
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Fix products data - add min_stock field if missing
        if 'products' in data:
            for product in data['products']:
                if 'min_stock' not in product:
                    # Set default min_stock based on current stock
                    current_stock = product.get('stock', 0)
                    if current_stock > 50:
                        product['min_stock'] = 10
                    elif current_stock > 20:
                        product['min_stock'] = 5
                    elif current_stock > 10:
                        product['min_stock'] = 3
                    else:
                        product['min_stock'] = 1
                    
                    print(f"✅ أضيف min_stock={product['min_stock']} للمنتج: {product.get('name', 'غير محدد')}")
        
        # Ensure all required fields exist in products
        required_fields = {
            'id': 0,
            'name': 'منتج غير محدد',
            'price': 0.0,
            'stock': 0,
            'min_stock': 5,
            'category': 'عام',
            'supplier': 'غير محدد',
            'unit': 'قطعة',
            'base_price': 0.0,
            'shop_owner_price': 0.0,
            'authorized_distributor_price': 0.0,
            'wholesale_price': 0.0,
            'retail_price': 0.0,
            'notes': '',
            'barcode': ''
        }
        
        if 'products' in data:
            for i, product in enumerate(data['products']):
                for field, default_value in required_fields.items():
                    if field not in product:
                        product[field] = default_value
                        if field != 'id':  # Don't log ID additions
                            print(f"✅ أضيف {field}={default_value} للمنتج {i+1}")
                
                # Ensure ID is unique
                if 'id' not in product or product['id'] == 0:
                    product['id'] = i + 1
        
        # Update timestamp
        data['last_updated'] = datetime.now().isoformat()
        
        # Write fixed data
        with open(data_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print("✅ تم إصلاح مشكلة الحد الأدنى للمخزون في البيانات")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح البيانات: {e}")
        return False

def fix_min_stock_in_code():
    """Fix min_stock handling in code"""
    try:
        print("🔧 إصلاح معالجة الحد الأدنى للمخزون في الكود...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        code_file = "protech_simple_working.py"
        code_path = os.path.join(data_dir, code_file)
        
        if not os.path.exists(code_path):
            print("❌ ملف البرنامج غير موجود!")
            return False
        
        # Create backup
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_path = f"{code_path}.min_stock_code_fix_{timestamp}"
        shutil.copy2(code_path, backup_path)
        print(f"✅ تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}")
        
        # Read current code
        with open(code_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix min_stock access patterns
        fixes_applied = 0
        
        # Pattern 1: p['min_stock'] -> p.get('min_stock', 5)
        import re
        
        patterns_to_fix = [
            (r"p\['min_stock'\]", "p.get('min_stock', 5)"),
            (r"product\['min_stock'\]", "product.get('min_stock', 5)"),
            (r"item\['min_stock'\]", "item.get('min_stock', 5)"),
        ]
        
        for pattern, replacement in patterns_to_fix:
            old_content = content
            content = re.sub(pattern, replacement, content)
            if content != old_content:
                fixes_applied += 1
                print(f"✅ تم إصلاح نمط: {pattern}")
        
        # Write fixed code
        with open(code_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم تطبيق {fixes_applied} إصلاح في الكود")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح الكود: {e}")
        return False

def test_min_stock_fix():
    """Test the min_stock fix"""
    try:
        print("🧪 اختبار إصلاح الحد الأدنى للمخزون...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        # Test data integrity
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check if all products have min_stock
        if 'products' in data:
            missing_min_stock = []
            for i, product in enumerate(data['products']):
                if 'min_stock' not in product:
                    missing_min_stock.append(i)
            
            if missing_min_stock:
                print(f"❌ منتجات بدون min_stock: {missing_min_stock}")
                return False
            else:
                print(f"✅ جميع المنتجات ({len(data['products'])}) لديها min_stock")
        
        # Test code compilation
        import subprocess
        import sys
        
        code_path = os.path.join(data_dir, "protech_simple_working.py")
        result = subprocess.run([sys.executable, '-m', 'py_compile', code_path], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار تجميع الكود: نجح")
            return True
        else:
            print(f"❌ اختبار تجميع الكود: فشل")
            print(f"الخطأ: {result.stderr}")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {e}")
        return False

def create_enhanced_sample_data():
    """Create enhanced sample data with all required fields"""
    try:
        print("📝 إنشاء بيانات نموذجية محسنة...")
        
        enhanced_data = {
            "suppliers": [
                {
                    "id": 1,
                    "name": "شركة الإمدادات المتقدمة",
                    "name_ar": "شركة الإمدادات المتقدمة",
                    "phone": "01234567890",
                    "address": "شارع التجارة، المدينة الصناعية",
                    "email": "<EMAIL>"
                },
                {
                    "id": 2,
                    "name": "مؤسسة التوريد الذهبي",
                    "name_ar": "مؤسسة التوريد الذهبي",
                    "phone": "01987654321",
                    "address": "طريق الملك فهد، الرياض",
                    "email": "<EMAIL>"
                }
            ],
            "products": [
                {
                    "id": 1,
                    "name": "جهاز كمبيوتر محمول",
                    "name_ar": "جهاز كمبيوتر محمول",
                    "price": 3500.0,
                    "stock": 25,
                    "min_stock": 5,
                    "barcode": "LAP001",
                    "category": "إلكترونيات",
                    "supplier": "شركة الإمدادات المتقدمة",
                    "supplier_id": 1,
                    "unit": "قطعة",
                    "base_price": 3000.0,
                    "shop_owner_price": 3150.0,
                    "authorized_distributor_price": 3450.0,
                    "wholesale_price": 3600.0,
                    "retail_price": 3900.0,
                    "cost": 2800.0,
                    "notes": "جهاز عالي الجودة مع ضمان سنتين"
                },
                {
                    "id": 2,
                    "name": "طابعة ليزر ملونة",
                    "name_ar": "طابعة ليزر ملونة",
                    "price": 1200.0,
                    "stock": 15,
                    "min_stock": 3,
                    "barcode": "PRT002",
                    "category": "مكتبية",
                    "supplier": "مؤسسة التوريد الذهبي",
                    "supplier_id": 2,
                    "unit": "قطعة",
                    "base_price": 1000.0,
                    "shop_owner_price": 1050.0,
                    "authorized_distributor_price": 1150.0,
                    "wholesale_price": 1200.0,
                    "retail_price": 1300.0,
                    "cost": 900.0,
                    "notes": "طابعة سريعة وموفرة للحبر"
                },
                {
                    "id": 3,
                    "name": "ماوس لاسلكي",
                    "name_ar": "ماوس لاسلكي",
                    "price": 85.0,
                    "stock": 100,
                    "min_stock": 20,
                    "barcode": "MOU003",
                    "category": "ملحقات",
                    "supplier": "شركة الإمدادات المتقدمة",
                    "supplier_id": 1,
                    "unit": "قطعة",
                    "base_price": 70.0,
                    "shop_owner_price": 74.0,
                    "authorized_distributor_price": 81.0,
                    "wholesale_price": 84.0,
                    "retail_price": 91.0,
                    "cost": 60.0,
                    "notes": "ماوس عالي الدقة لاسلكي"
                }
            ],
            "customers": [
                {
                    "id": 1,
                    "name": "محمد أحمد التاجر",
                    "phone": "01122334455",
                    "type": "صاحب محل",
                    "balance": 0
                },
                {
                    "id": 2,
                    "name": "شركة التوزيع الكبرى",
                    "phone": "01199887766",
                    "type": "موزع معتمد",
                    "balance": 0
                },
                {
                    "id": 3,
                    "name": "مؤسسة البيع بالجملة",
                    "phone": "01155443322",
                    "type": "جملة",
                    "balance": 0
                },
                {
                    "id": 4,
                    "name": "عميل التجزئة",
                    "phone": "01166778899",
                    "type": "تجزئة",
                    "balance": 0
                }
            ],
            "sales": [
                {
                    "id": 1,
                    "date": "2025-06-19",
                    "customer": "محمد أحمد التاجر",
                    "customer_type": "صاحب محل",
                    "products": [
                        {
                            "name": "جهاز كمبيوتر محمول",
                            "quantity": 1,
                            "unit_price": 3150.0,
                            "total": 3150.0
                        }
                    ],
                    "total": 3150.0,
                    "payment": 3150.0,
                    "balance": 0
                },
                {
                    "id": 2,
                    "date": "2025-06-19",
                    "customer": "شركة التوزيع الكبرى",
                    "customer_type": "موزع معتمد",
                    "products": [
                        {
                            "name": "طابعة ليزر ملونة",
                            "quantity": 2,
                            "unit_price": 1150.0,
                            "total": 2300.0
                        }
                    ],
                    "total": 2300.0,
                    "payment": 2300.0,
                    "balance": 0
                }
            ],
            "last_updated": datetime.now().isoformat()
        }
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        enhanced_file = "protech_enhanced_data.json"
        enhanced_path = os.path.join(data_dir, enhanced_file)
        
        with open(enhanced_path, 'w', encoding='utf-8') as f:
            json.dump(enhanced_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ تم إنشاء ملف البيانات المحسنة: {enhanced_file}")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء البيانات المحسنة: {e}")
        return False

def main():
    """Main min_stock fix function"""
    print("🔧 مصلح مشكلة الحد الأدنى للمخزون في ProTech")
    print("🔧 ProTech Min Stock Issue Fixer")
    print("="*60)
    
    try:
        # Fix data
        if fix_min_stock_in_data():
            print("\n✅ تم إصلاح البيانات بنجاح!")
            
            # Fix code
            if fix_min_stock_in_code():
                print("✅ تم إصلاح الكود بنجاح!")
                
                # Test the fix
                if test_min_stock_fix():
                    print("✅ اختبار الإصلاح نجح!")
                    
                    # Create enhanced data
                    if create_enhanced_sample_data():
                        print("✅ تم إنشاء بيانات محسنة!")
                    
                    print("\n🎉 إصلاح مشكلة min_stock مكتمل بنجاح!")
                    print("🎉 Min stock issue fix completed successfully!")
                    
                    return True
        
        print("\n❌ فشل في إصلاح مشكلة min_stock")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام في إصلاح min_stock: {e}")
        return False

if __name__ == "__main__":
    main()
