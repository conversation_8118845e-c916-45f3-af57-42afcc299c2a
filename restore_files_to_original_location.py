#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Restore Files to Original Location
إرجاع الملفات إلى مكانها الأصلي

Restore ProTech files from desktop back to original augment-projects location
إرجاع ملفات ProTech من سطح المكتب إلى مكان augment-projects الأصلي
"""

import os
import shutil
from datetime import datetime

def restore_files_to_original_location():
    """Restore files from desktop to original location"""
    try:
        print("🔄 إرجاع ملفات ProTech إلى مكانها الأصلي")
        print("🔄 Restoring ProTech Files to Original Location")
        print("="*60)
        
        # Define paths
        desktop_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop"
        original_dir = "C:\\Users\\<USER>\\Documents\\augment-projects\\protech"
        
        # Create original directory if it doesn't exist
        if not os.path.exists(original_dir):
            os.makedirs(original_dir)
            print(f"📁 تم إنشاء المجلد: {original_dir}")
        
        # Files to restore (moved before 10:00 AM)
        files_to_restore = [
            "protech_simple_working.py",
            "ProTech_Shortcut.bat", 
            "README_ProTech.txt",
            "run_protech.bat"
        ]
        
        # Also check for backup files
        backup_files = [
            "protech_simple_working.py.performance_backup_20250619_125207",
            "protech_simple_working.py.desktop_backup_20250619_151149",
            "ProTech_Shortcut.bat.desktop_backup_20250619_151149"
        ]
        
        restored_files = []
        failed_files = []
        
        print("\\n📋 إرجاع الملفات الأساسية:")
        
        # Restore main files
        for file in files_to_restore:
            desktop_path = os.path.join(desktop_dir, file)
            original_path = os.path.join(original_dir, file)
            
            try:
                if os.path.exists(desktop_path):
                    # Check if file was modified before 10:00 AM
                    mod_time = datetime.fromtimestamp(os.path.getmtime(desktop_path))
                    cutoff_time = datetime(2025, 6, 19, 10, 0, 0)
                    
                    if mod_time < cutoff_time:
                        # Create backup if file exists in original location
                        if os.path.exists(original_path):
                            backup_name = f"{file}.restore_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                            backup_path = os.path.join(original_dir, backup_name)
                            shutil.copy2(original_path, backup_path)
                            print(f"💾 نسخة احتياطية: {backup_name}")
                        
                        # Move file back
                        shutil.copy2(desktop_path, original_path)
                        restored_files.append(file)
                        
                        # Get file size
                        size = os.path.getsize(original_path) / 1024
                        print(f"✅ {file}: {size:.1f} KB - {mod_time.strftime('%H:%M:%S')}")
                        
                        # Remove from desktop
                        os.remove(desktop_path)
                        print(f"🗑️ تم حذف من سطح المكتب: {file}")
                        
                    else:
                        print(f"⏰ {file}: تم تعديله بعد 10:00 - لم يتم نقله")
                else:
                    print(f"⚠️ {file}: غير موجود على سطح المكتب")
                    failed_files.append(file)
                    
            except Exception as e:
                print(f"❌ {file}: فشل الإرجاع - {e}")
                failed_files.append(file)
        
        print("\\n📋 إرجاع ملفات النسخ الاحتياطية:")
        
        # Restore backup files
        for file in backup_files:
            desktop_path = os.path.join(desktop_dir, file)
            original_path = os.path.join(original_dir, file)
            
            try:
                if os.path.exists(desktop_path):
                    shutil.copy2(desktop_path, original_path)
                    restored_files.append(file)
                    
                    size = os.path.getsize(original_path) / 1024
                    print(f"✅ {file}: {size:.1f} KB")
                    
                    # Remove from desktop
                    os.remove(desktop_path)
                    print(f"🗑️ تم حذف من سطح المكتب: {file}")
                else:
                    print(f"⚠️ {file}: غير موجود")
                    
            except Exception as e:
                print(f"❌ {file}: فشل الإرجاع - {e}")
        
        # Check for any other ProTech files on desktop
        print("\\n🔍 البحث عن ملفات ProTech أخرى:")
        
        try:
            desktop_files = os.listdir(desktop_dir)
            other_protech_files = [f for f in desktop_files if 'protech' in f.lower() or 'ProTech' in f]
            
            for file in other_protech_files:
                if file not in files_to_restore and file not in backup_files:
                    desktop_path = os.path.join(desktop_dir, file)
                    
                    if os.path.isfile(desktop_path):
                        mod_time = datetime.fromtimestamp(os.path.getmtime(desktop_path))
                        cutoff_time = datetime(2025, 6, 19, 10, 0, 0)
                        
                        if mod_time < cutoff_time:
                            original_path = os.path.join(original_dir, file)
                            
                            try:
                                shutil.copy2(desktop_path, original_path)
                                restored_files.append(file)
                                
                                size = os.path.getsize(original_path) / 1024
                                print(f"✅ {file}: {size:.1f} KB - {mod_time.strftime('%H:%M:%S')}")
                                
                                os.remove(desktop_path)
                                print(f"🗑️ تم حذف من سطح المكتب: {file}")
                                
                            except Exception as e:
                                print(f"❌ {file}: فشل الإرجاع - {e}")
                        else:
                            print(f"⏰ {file}: تم تعديله بعد 10:00 - تم تركه")
        
        except Exception as e:
            print(f"❌ خطأ في البحث: {e}")
        
        # Create restoration report
        print("\\n📄 إنشاء تقرير الإرجاع:")
        
        report_content = f"""تقرير إرجاع ملفات ProTech
========================

التاريخ والوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

الملفات المرجعة ({len(restored_files)}):
"""
        
        for file in restored_files:
            report_content += f"✅ {file}\\n"
        
        if failed_files:
            report_content += f"\\nالملفات الفاشلة ({len(failed_files)}):\\n"
            for file in failed_files:
                report_content += f"❌ {file}\\n"
        
        report_content += f"""
المسار الأصلي: {original_dir}
المسار المصدر: {desktop_dir}

الحالة: {'نجح' if len(restored_files) > 0 else 'فشل'}
"""
        
        report_path = os.path.join(original_dir, f"restoration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt")
        
        try:
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"✅ تقرير الإرجاع: {os.path.basename(report_path)}")
        except Exception as e:
            print(f"❌ فشل في إنشاء التقرير: {e}")
        
        # Summary
        print("\\n" + "="*60)
        print("📊 ملخص الإرجاع:")
        print(f"✅ ملفات مرجعة: {len(restored_files)}")
        print(f"❌ ملفات فاشلة: {len(failed_files)}")
        
        if restored_files:
            print("\\n📋 الملفات المرجعة:")
            for file in restored_files:
                print(f"  • {file}")
        
        if failed_files:
            print("\\n⚠️ الملفات الفاشلة:")
            for file in failed_files:
                print(f"  • {file}")
        
        print(f"\\n📁 المكان الأصلي: {original_dir}")
        
        print("\\n🎯 الحالة النهائية:")
        if len(restored_files) > 0:
            print("🟢 إرجاع ناجح - الملفات في مكانها الأصلي")
            print("🟢 Successful restoration - files in original location")
        else:
            print("🟡 لم يتم العثور على ملفات للإرجاع")
            print("🟡 No files found to restore")
        
        print("="*60)
        
        return len(restored_files) > 0
        
    except Exception as e:
        print(f"❌ خطأ في إرجاع الملفات: {e}")
        return False

def verify_original_location():
    """Verify files in original location"""
    try:
        print("\\n🔍 التحقق من الملفات في المكان الأصلي:")
        
        original_dir = "C:\\Users\\<USER>\\Documents\\augment-projects\\protech"
        
        if not os.path.exists(original_dir):
            print(f"❌ المجلد الأصلي غير موجود: {original_dir}")
            return False
        
        files = os.listdir(original_dir)
        protech_files = [f for f in files if 'protech' in f.lower() or 'ProTech' in f or f.endswith('.py') or f.endswith('.bat')]
        
        print(f"📁 المجلد: {original_dir}")
        print(f"📊 ملفات ProTech: {len(protech_files)}")
        
        for file in sorted(protech_files):
            file_path = os.path.join(original_dir, file)
            if os.path.isfile(file_path):
                size = os.path.getsize(file_path) / 1024
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"✅ {file}: {size:.1f} KB - {mod_time.strftime('%H:%M:%S')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """Main function"""
    success = restore_files_to_original_location()
    
    if success:
        verify_original_location()
        print("\\n🎉 تم إرجاع ملفات ProTech إلى مكانها الأصلي بنجاح!")
        print("🎉 ProTech files restored to original location successfully!")
    else:
        print("\\n❌ فشل في إرجاع ملفات ProTech")
        print("❌ Failed to restore ProTech files")

if __name__ == "__main__":
    main()
