========================================
    ProTech Accounting System
    نظام ProTech للمحاسبة المتقدم
========================================

📋 محتويات المجلد / Folder Contents:
=====================================

1. protech_simple_working.py - الملف الرئيسي للبرنامج / Main program file
2. protech_data.json - ملف البيانات / Data file
3. run_protech.bat - ملف تشغيل سريع / Quick run file
4. README_ProTech.txt - هذا الملف / This file

🚀 طريقة التشغيل / How to Run:
===============================

الطريقة الأولى (الأسهل):
1. انقر نقرة مزدوجة على ملف "run_protech.bat"
2. سيتم تشغيل البرنامج تلقائياً

الطريقة الثانية:
1. تأكد من تثبيت Python على جهازك
2. انقر نقرة مزدوجة على ملف "protech_simple_working.py"

الطريقة الثالثة (من سطر الأوامر):
1. افتح Command Prompt في مجلد البرنامج
2. اكتب: python protech_simple_working.py

📦 متطلبات النظام / System Requirements:
=========================================

- Windows 10/11
- Python 3.7 أو أحدث / Python 3.7 or newer
- مكتبات Python المطلوبة / Required Python libraries:
  * tkinter (مدمجة مع Python)
  * json (مدمجة مع Python)
  * datetime (مدمجة مع Python)
  * os (مدمجة مع Python)

🎯 ميزات النظام / System Features:
==================================

✅ إدارة المنتجات والمخزون
✅ إدارة العملاء والموردين
✅ نظام المبيعات والفواتير
✅ تقارير شاملة ومتقدمة
✅ ملخص الفواتير التفاعلي
✅ نسخ احتياطية تلقائية
✅ واجهة ثنائية اللغة (عربي/إنجليزي)
✅ تحديث تلقائي للبيانات
✅ تحسينات الأداء المتقدمة

🔧 استكشاف الأخطاء / Troubleshooting:
======================================

❌ إذا لم يعمل البرنامج:
1. تأكد من تثبيت Python
2. تحقق من وجود جميع الملفات في نفس المجلد
3. تأكد من صلاحيات الكتابة في المجلد

❌ إذا ظهرت رسالة خطأ:
1. تأكد من عدم فتح البرنامج في نافذة أخرى
2. تحقق من ملف البيانات protech_data.json
3. أعد تشغيل البرنامج

📞 الدعم الفني / Technical Support:
===================================

للحصول على المساعدة:
- تحقق من سجل الأخطاء في البرنامج
- راجع التقارير لفهم حالة النظام
- استخدم ميزة النسخ الاحتياطية لحماية البيانات

🎉 نصائح للاستخدام الأمثل / Best Practices:
=============================================

1. قم بعمل نسخة احتياطية دورية من البيانات
2. استخدم التقارير لمراقبة الأداء
3. حدث المخزون بانتظام
4. راجع الفواتير المعلقة دورياً
5. استخدم ميزة البحث للوصول السريع للبيانات

📅 تاريخ الإصدار / Release Date: 2025-01-19
🏢 المطور / Developer: ProTech Solutions
📧 الإصدار / Version: v2.0 Enhanced

========================================
جميع الحقوق محفوظة © 2025 ProTech Solutions
All Rights Reserved © 2025 ProTech Solutions
========================================
