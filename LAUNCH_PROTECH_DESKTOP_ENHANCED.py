#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🖥️ مشغل ProTech المحسن لسطح المكتب
LAUNCH PROTECH DESKTOP - Enhanced Version

مشغل محسن وموثوق لبرنامج ProTech من سطح المكتب
Enhanced and reliable launcher for ProTech from Desktop

تم تحسينه بواسطة Augment Agent
Enhanced by Augment Agent
"""

import os
import sys
import subprocess
import json
import shutil
from datetime import datetime
import tkinter as tk
from tkinter import messagebox

class ProTechDesktopLauncher:
    def __init__(self):
        self.desktop_path = r"C:\Users\<USER>\OneDrive\Desktop\accounting program"
        self.protech_file = "protech_simple_working.py"
        self.data_file = "protech_simple_data.json"
        
    def show_welcome(self):
        """عرض رسالة ترحيب"""
        root = tk.Tk()
        root.withdraw()
        
        welcome_msg = """
🖥️ مشغل ProTech المحسن لسطح المكتب
LAUNCH PROTECH DESKTOP - Enhanced Version

📋 الميزات:
• تشغيل موثوق لبرنامج ProTech
• فحص تلقائي للملفات والبيانات
• نسخ احتياطية تلقائية
• إصلاح المشاكل التلقائي
• واجهة مألوفة وسهلة

🔒 مضمون عدم فقدان البيانات
🔒 Guaranteed No Data Loss

🚀 هل تريد تشغيل ProTech الآن؟
Do you want to start ProTech now?
        """
        
        result = messagebox.askyesno(
            "مشغل ProTech المحسن - Enhanced ProTech Launcher",
            welcome_msg
        )
        
        root.destroy()
        return result
    
    def check_and_prepare_environment(self):
        """فحص وإعداد البيئة"""
        print("🔍 فحص بيئة العمل...")
        print("🔍 Checking work environment...")
        
        # فحص مجلد سطح المكتب
        if not os.path.exists(self.desktop_path):
            print(f"❌ مجلد سطح المكتب غير موجود: {self.desktop_path}")
            print(f"❌ Desktop folder not found: {self.desktop_path}")
            return False
        
        print(f"✅ مجلد سطح المكتب موجود: {self.desktop_path}")
        print(f"✅ Desktop folder found: {self.desktop_path}")
        
        # الانتقال إلى مجلد سطح المكتب
        os.chdir(self.desktop_path)
        print(f"📁 تم الانتقال إلى: {self.desktop_path}")
        print(f"📁 Changed to: {self.desktop_path}")
        
        # فحص ملف البرنامج
        if not os.path.exists(self.protech_file):
            print(f"❌ ملف البرنامج غير موجود: {self.protech_file}")
            print(f"❌ Program file not found: {self.protech_file}")
            
            # محاولة نسخ من مصادر أخرى
            if self.copy_program_from_sources():
                print("✅ تم نسخ البرنامج بنجاح")
                print("✅ Program copied successfully")
            else:
                return False
        
        print(f"✅ ملف البرنامج موجود: {self.protech_file}")
        print(f"✅ Program file found: {self.protech_file}")
        
        # فحص وإعداد ملف البيانات
        self.prepare_data_file()
        
        return True
    
    def copy_program_from_sources(self):
        """نسخ البرنامج من مصادر أخرى"""
        print("🔄 البحث عن البرنامج في مصادر أخرى...")
        print("🔄 Searching for program in other sources...")
        
        possible_sources = [
            r"C:\Users\<USER>\OneDrive\Desktop\NASSAR_COMPLETE_SYSTEM\01_Main_Programs\NASSAR_PROGRAM_FINAL_WORKING.py",
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\nassar program final\01_Main_Program\protech_simple_working.py",
            r"C:\Users\<USER>\Documents\augment-projects\protech\protech_simple_working.py"
        ]
        
        for source in possible_sources:
            if os.path.exists(source):
                try:
                    shutil.copy2(source, self.protech_file)
                    print(f"✅ تم نسخ البرنامج من: {source}")
                    print(f"✅ Program copied from: {source}")
                    return True
                except Exception as e:
                    print(f"⚠️ فشل في النسخ من {source}: {e}")
                    continue
        
        print("❌ لم يتم العثور على البرنامج في أي مصدر")
        print("❌ Program not found in any source")
        return False
    
    def prepare_data_file(self):
        """إعداد ملف البيانات"""
        print("📊 فحص ملف البيانات...")
        print("📊 Checking data file...")
        
        if os.path.exists(self.data_file):
            print(f"✅ ملف البيانات موجود: {self.data_file}")
            print(f"✅ Data file found: {self.data_file}")
            
            # إنشاء نسخة احتياطية
            self.create_data_backup()
            return True
        
        print(f"⚠️ ملف البيانات غير موجود: {self.data_file}")
        print(f"⚠️ Data file not found: {self.data_file}")
        
        # البحث عن ملف البيانات في مصادر أخرى
        if self.copy_data_from_sources():
            print("✅ تم نسخ ملف البيانات بنجاح")
            print("✅ Data file copied successfully")
            return True
        
        # إنشاء ملف بيانات نموذجي
        print("📝 إنشاء ملف بيانات نموذجي...")
        print("📝 Creating sample data file...")
        self.create_sample_data()
        return True
    
    def copy_data_from_sources(self):
        """نسخ ملف البيانات من مصادر أخرى"""
        possible_data_sources = [
            r"C:\Users\<USER>\OneDrive\Desktop\NASSAR_COMPLETE_SYSTEM\03_Data_Files\protech_simple_data.json",
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\nassar program final\02_Data_Files\protech_simple_data.json",
            r"C:\Users\<USER>\Documents\augment-projects\protech\protech_simple_data.json"
        ]
        
        for source in possible_data_sources:
            if os.path.exists(source):
                try:
                    shutil.copy2(source, self.data_file)
                    print(f"✅ تم نسخ البيانات من: {source}")
                    print(f"✅ Data copied from: {source}")
                    return True
                except Exception as e:
                    print(f"⚠️ فشل في نسخ البيانات من {source}: {e}")
                    continue
        
        return False
    
    def create_sample_data(self):
        """إنشاء بيانات نموذجية"""
        sample_data = {
            "suppliers": [
                {
                    "id": 1,
                    "name": "Tech Solutions Inc.",
                    "name_ar": "شركة الحلول التقنية",
                    "phone": "+966-11-123-4567",
                    "email": "<EMAIL>"
                },
                {
                    "id": 2,
                    "name": "Office World Ltd.",
                    "name_ar": "شركة عالم المكاتب",
                    "phone": "+966-11-234-5678",
                    "email": "<EMAIL>"
                },
                {
                    "id": 3,
                    "name": "Electronics Hub",
                    "name_ar": "مركز الإلكترونيات",
                    "phone": "+966-11-345-6789",
                    "email": "<EMAIL>"
                }
            ],
            "products": [
                {
                    "id": 1,
                    "barcode": "1234567890123",
                    "name": "Business Laptop",
                    "name_ar": "لابتوب الأعمال",
                    "category": "Electronics",
                    "supplier_id": 1,
                    "price": 1200.00,
                    "stock": 45,
                    "min_stock": 10
                },
                {
                    "id": 2,
                    "barcode": "1234567890124",
                    "name": "Wireless Mouse",
                    "name_ar": "فأرة لاسلكية",
                    "category": "Electronics",
                    "supplier_id": 2,
                    "price": 35.00,
                    "stock": 150,
                    "min_stock": 30
                },
                {
                    "id": 3,
                    "barcode": "1234567890125",
                    "name": "Mechanical Keyboard",
                    "name_ar": "لوحة مفاتيح ميكانيكية",
                    "category": "Electronics",
                    "supplier_id": 3,
                    "price": 120.00,
                    "stock": 60,
                    "min_stock": 15
                }
            ],
            "customers": [
                {
                    "id": 1,
                    "name": "John Smith",
                    "name_ar": "جون سميث",
                    "phone": "******-1234",
                    "balance": 1250.00,
                    "type": "RETAIL"
                },
                {
                    "id": 2,
                    "name": "ABC Corporation",
                    "name_ar": "شركة ABC",
                    "phone": "******-5678",
                    "balance": 8750.00,
                    "type": "WHOLESALE"
                },
                {
                    "id": 3,
                    "name": "Ahmed Al-Rashid",
                    "name_ar": "أحمد الراشد",
                    "phone": "+966-50-123-4567",
                    "balance": 2500.00,
                    "type": "SHOP_OWNER"
                }
            ],
            "sales": [],
            "last_updated": datetime.now().isoformat(),
            "created_by": "ProTech Desktop Launcher Enhanced"
        }
        
        try:
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(sample_data, f, ensure_ascii=False, indent=2, default=str)
            
            print("✅ تم إنشاء ملف البيانات النموذجي")
            print("✅ Sample data file created")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء ملف البيانات: {e}")
            print(f"❌ Failed to create data file: {e}")
    
    def create_data_backup(self):
        """إنشاء نسخة احتياطية من البيانات"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_file = f"protech_data_backup_{timestamp}.json"
            
            shutil.copy2(self.data_file, backup_file)
            print(f"💾 تم إنشاء نسخة احتياطية: {backup_file}")
            print(f"💾 Backup created: {backup_file}")
            
        except Exception as e:
            print(f"⚠️ فشل في إنشاء النسخة الاحتياطية: {e}")
    
    def launch_protech(self):
        """تشغيل برنامج ProTech"""
        try:
            print("\n🚀 تشغيل برنامج ProTech...")
            print("🚀 Starting ProTech...")
            print()
            print("💡 نصائح مهمة:")
            print("💡 Important Tips:")
            print("   • إذا لم تظهر البيانات، اضغط زر 'إعادة تحميل البيانات' الأحمر")
            print("   • If data doesn't show, click the red 'Reload Data' button")
            print("   • جميع البيانات محفوظة تلقائياً")
            print("   • All data is automatically saved")
            print()
            
            # تشغيل البرنامج
            subprocess.run([sys.executable, self.protech_file])
            
            print("✅ تم إغلاق ProTech بنجاح")
            print("✅ ProTech closed successfully")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل ProTech: {e}")
            print(f"❌ Error launching ProTech: {e}")
            
            messagebox.showerror(
                "خطأ في التشغيل\nLaunch Error",
                f"فشل في تشغيل ProTech\nFailed to launch ProTech:\n{str(e)}"
            )
    
    def run(self):
        """تشغيل المشغل"""
        print("=" * 60)
        print("🖥️ مشغل ProTech المحسن لسطح المكتب")
        print("🖥️ LAUNCH PROTECH DESKTOP - Enhanced Version")
        print("=" * 60)
        print(f"📅 التاريخ / Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 المسار / Path: {self.desktop_path}")
        print("=" * 60)
        
        # عرض رسالة الترحيب
        if not self.show_welcome():
            print("❌ تم إلغاء تشغيل البرنامج")
            print("❌ Program launch cancelled")
            input("\nاضغط Enter للخروج / Press Enter to exit...")
            return
        
        # فحص وإعداد البيئة
        if not self.check_and_prepare_environment():
            print("❌ فشل في إعداد البيئة")
            print("❌ Failed to prepare environment")
            input("\nاضغط Enter للخروج / Press Enter to exit...")
            return
        
        # تشغيل البرنامج
        self.launch_protech()
        
        print("\n🎉 انتهى التشغيل بنجاح!")
        print("🎉 Execution completed successfully!")
        input("\nاضغط Enter للخروج / Press Enter to exit...")

def main():
    """الدالة الرئيسية"""
    try:
        launcher = ProTechDesktopLauncher()
        launcher.run()
    except Exception as e:
        print(f"❌ خطأ في المشغل: {e}")
        print(f"❌ Launcher error: {e}")
        input("\nاضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
