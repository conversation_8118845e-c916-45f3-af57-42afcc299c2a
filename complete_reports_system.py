#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Complete Reports System
إكمال نظام التقارير

Complete the advanced reports system with helper methods and button updates
إكمال نظام التقارير المتقدم مع الطرق المساعدة وتحديث الأزرار
"""

import os
import shutil
from datetime import datetime

def complete_reports_system():
    """إكمال نظام التقارير"""
    try:
        print("🔧 إكمال نظام التقارير المتقدم")
        print("🔧 Completing Advanced Reports System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.complete_reports_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Helper methods for reports
        helper_methods = '''
    def filter_sales_by_date(self, sales):
        """تصفية المبيعات حسب التاريخ"""
        try:
            if not hasattr(self, 'date_from') or not hasattr(self, 'date_to'):
                return sales
            
            date_from_str = self.date_from.get().strip()
            date_to_str = self.date_to.get().strip()
            
            if not date_from_str or not date_to_str:
                return sales
            
            from datetime import datetime
            try:
                date_from = datetime.strptime(date_from_str, '%Y-%m-%d')
                date_to = datetime.strptime(date_to_str, '%Y-%m-%d')
            except:
                return sales
            
            filtered_sales = []
            for sale in sales:
                sale_date_str = sale.get('date', '')
                if sale_date_str:
                    try:
                        # Try different date formats
                        for fmt in ['%Y-%m-%d', '%Y-%m-%d %H:%M:%S', '%d/%m/%Y']:
                            try:
                                sale_date = datetime.strptime(sale_date_str.split()[0], fmt)
                                break
                            except:
                                continue
                        else:
                            filtered_sales.append(sale)
                            continue
                        
                        if date_from <= sale_date <= date_to:
                            filtered_sales.append(sale)
                    except:
                        filtered_sales.append(sale)
                else:
                    filtered_sales.append(sale)
            
            return filtered_sales
            
        except Exception as e:
            print(f"Error filtering dates: {e}")
            return sales
    
    def apply_date_filter(self):
        """تطبيق تصفية التاريخ"""
        try:
            if hasattr(self, 'current_table_type'):
                if self.current_table_type == 'sales':
                    self.show_sales_table()
                elif self.current_table_type == 'profits':
                    self.show_profits_table()
            print("Date filter applied")
        except Exception as e:
            print(f"Error applying filter: {e}")
    
    def reset_date_filter(self):
        """إعادة تعيين تصفية التاريخ"""
        try:
            if hasattr(self, 'date_from') and hasattr(self, 'date_to'):
                from datetime import datetime, timedelta
                self.date_from.delete(0, tk.END)
                self.date_to.delete(0, tk.END)
                self.date_from.insert(0, (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d'))
                self.date_to.insert(0, datetime.now().strftime('%Y-%m-%d'))
                self.apply_date_filter()
            print("Date filter reset")
        except Exception as e:
            print(f"Error resetting filter: {e}")
    
    def save_row_changes(self, row_index):
        """حفظ تغييرات الصف"""
        try:
            if not hasattr(self, 'table_data') or row_index >= len(self.table_data):
                return
            
            row_widgets = self.table_data[row_index]
            
            if hasattr(self, 'current_table_type'):
                if self.current_table_type == 'products':
                    self.save_product_changes(row_index, row_widgets)
                elif self.current_table_type == 'customers':
                    self.save_customer_changes(row_index, row_widgets)
                elif self.current_table_type == 'sales':
                    self.save_sale_changes(row_index, row_widgets)
            
            print(f"Row {row_index + 1} changes saved")
            
        except Exception as e:
            print(f"Error saving changes: {e}")
    
    def save_product_changes(self, row_index, row_widgets):
        """حفظ تغييرات المنتج"""
        try:
            products = self.get_real_products_data()
            if row_index < len(products):
                product = products[row_index]
                
                # Update editable fields
                if len(row_widgets) > 3 and hasattr(row_widgets[3], 'get'):
                    try:
                        product['quantity'] = float(row_widgets[3].get() or 0)
                    except:
                        pass
                
                if len(row_widgets) > 5 and hasattr(row_widgets[5], 'get'):
                    try:
                        product['base_price'] = float(row_widgets[5].get().replace(',', '') or 0)
                    except:
                        pass
                
                # Save data
                if hasattr(self, 'save_data_automatically'):
                    self.save_data_automatically()
            
        except Exception as e:
            print(f"Error saving product: {e}")
    
    def save_customer_changes(self, row_index, row_widgets):
        """حفظ تغييرات العميل"""
        try:
            customers = self.get_real_customers_data()
            if row_index < len(customers):
                customer = customers[row_index]
                
                # Update editable fields
                if len(row_widgets) > 1 and hasattr(row_widgets[1], 'get'):
                    customer['phone'] = row_widgets[1].get()
                
                if len(row_widgets) > 4 and hasattr(row_widgets[4], 'get'):
                    try:
                        customer['balance'] = float(row_widgets[4].get().replace(',', '') or 0)
                    except:
                        pass
                
                # Save data
                if hasattr(self, 'save_data_automatically'):
                    self.save_data_automatically()
            
        except Exception as e:
            print(f"Error saving customer: {e}")
    
    def save_sale_changes(self, row_index, row_widgets):
        """حفظ تغييرات المبيعة"""
        try:
            sales = self.get_real_sales_data()
            if row_index < len(sales):
                sale = sales[row_index]
                
                # Update editable fields
                if len(row_widgets) > 6 and hasattr(row_widgets[6], 'get'):
                    sale['status'] = row_widgets[6].get()
                
                # Save data
                if hasattr(self, 'save_data_automatically'):
                    self.save_data_automatically()
            
        except Exception as e:
            print(f"Error saving sale: {e}")
    
    def export_table_data(self, headers, data):
        """تصدير بيانات الجدول إلى CSV"""
        try:
            import csv
            from datetime import datetime
            
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"protech_export_{timestamp}.csv"
            
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(headers)
                writer.writerows(data)
            
            print(f"Data exported to {filename}")
            
            # Show success message
            if hasattr(self, 'table_frame'):
                try:
                    success_label = tk.Label(self.table_frame, 
                                           text=f"Exported to {filename}", 
                                           bg='#2ecc71', fg='white', font=("Arial", 10, "bold"))
                    success_label.pack(pady=5)
                    
                    if hasattr(self, 'root'):
                        self.root.after(3000, success_label.destroy)
                except:
                    pass
            
        except Exception as e:
            print(f"Export error: {e}")
    
    def update_table_methods(self):
        """تحديث طرق الجداول لتتبع النوع"""
        try:
            # Store original methods
            if hasattr(self, 'show_products_table'):
                original_products = self.show_products_table
                def show_products_with_type():
                    self.current_table_type = 'products'
                    return original_products()
                self.show_products_table = show_products_with_type
            
            if hasattr(self, 'show_customers_table'):
                original_customers = self.show_customers_table
                def show_customers_with_type():
                    self.current_table_type = 'customers'
                    return original_customers()
                self.show_customers_table = show_customers_with_type
            
            if hasattr(self, 'show_sales_table'):
                original_sales = self.show_sales_table
                def show_sales_with_type():
                    self.current_table_type = 'sales'
                    return original_sales()
                self.show_sales_table = show_sales_with_type
            
            if hasattr(self, 'show_profits_table'):
                original_profits = self.show_profits_table
                def show_profits_with_type():
                    self.current_table_type = 'profits'
                    return original_profits()
                self.show_profits_table = show_profits_with_type
            
        except Exception as e:
            print(f"Error updating table methods: {e}")
'''
        
        # Add helper methods
        last_method = content.rfind("\n    def show_profits_table(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + helper_methods + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + helper_methods + content[last_method:]
        
        # Update reports button
        if "btn5 = tk.Button(sidebar, text=\"التقارير\"" in content:
            old_button = 'btn5 = tk.Button(sidebar, text="التقارير", font=("Arial", 12), bg=\'#9b59b6\', fg=\'white\', width=18, height=2, command=self.show_reports)'
            new_button = 'btn5 = tk.Button(sidebar, text="Advanced Reports", font=("Arial", 12), bg=\'#9b59b6\', fg=\'white\', width=18, height=2, command=self.create_advanced_reports_page)'
            content = content.replace(old_button, new_button)
            print("✅ تم تحديث زر التقارير")
        
        # Add initialization call for table methods
        init_method = content.find("def __init__(self")
        if init_method != -1:
            init_end = content.find("\n    def ", init_method + 1)
            if init_end == -1:
                init_end = content.find("\nclass ", init_method + 1)
            if init_end == -1:
                init_end = len(content)
            
            # Add table methods initialization
            table_init = "\n        # تهيئة طرق الجداول\n        self.current_table_type = None\n        if hasattr(self, 'root'):\n            self.root.after(1000, self.update_table_methods)\n"
            content = content[:init_end] + table_init + content[init_end:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إكمال نظام التقارير المتقدم")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إكمال نظام التقارير: {e}")
        return False

def main():
    """Main function"""
    print("📊 إكمال نظام التقارير المتقدم لـ ProTech")
    print("📊 Completing Advanced Reports System for ProTech")
    print("="*70)
    
    if complete_reports_system():
        print("\n🎉 تم إكمال نظام التقارير المتقدم بنجاح!")
        
        print("\n📊 النظام الكامل يشمل:")
        print("• 📋 6 تقارير متقدمة في جداول تفاعلية")
        print("• 📅 تصفية ذكية للتواريخ")
        print("• ✏️ تعديل مباشر للبيانات")
        print("• 📤 تصدير فوري إلى CSV")
        print("• 🔄 حفظ تلقائي للتغييرات")
        print("• 📊 إحصائيات وتحليلات")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Advanced Reports' في الشريط الجانبي")
        print("3. اختر التقرير المطلوب من الأزرار الملونة")
        print("4. استخدم تصفية التواريخ للمبيعات والأرباح")
        print("5. عدّل البيانات مباشرة في الجدول")
        print("6. احفظ التغييرات بالنقر على 'Save'")
        print("7. صدّر البيانات بالنقر على 'Export to CSV'")
        
        print("\n💡 مميزات خاصة:")
        print("• جداول قابلة للتمرير مع شريط تمرير")
        print("• ألوان متناوبة للصفوف لسهولة القراءة")
        print("• عدادات إجمالية للسجلات")
        print("• رسائل نجاح عند التصدير")
        print("• تحديث فوري للبيانات")
        
    else:
        print("\n❌ فشل في إكمال نظام التقارير المتقدم")
    
    print("\n🔧 تم الانتهاء من إكمال نظام التقارير")

if __name__ == "__main__":
    main()
