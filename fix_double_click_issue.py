#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Double Click Issue for ProTech
إصلاح مشكلة النقر المزدوج لـ ProTech

Fix the issue when double-clicking ProTech doesn't work
إصلاح مشكلة عدم عمل ProTech عند النقر المزدوج
"""

import os
import sys
import subprocess
from datetime import datetime

def diagnose_double_click_issue():
    """Diagnose why double-click doesn't work"""
    try:
        print("🔍 تشخيص مشكلة النقر المزدوج")
        print("🔍 Diagnosing Double-Click Issue")
        print("="*50)
        
        # Check Python installation
        print("\n🐍 فحص تثبيت Python:")
        
        try:
            result = subprocess.run(['python', '--version'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                python_version = result.stdout.strip()
                print(f"✅ Python مثبت: {python_version}")
            else:
                print("❌ Python غير مثبت أو غير متاح في PATH")
                return "python_not_found"
        except FileNotFoundError:
            print("❌ Python غير موجود في النظام")
            return "python_not_installed"
        
        # Check file associations
        print("\n🔗 فحص ارتباطات الملفات:")
        
        try:
            result = subprocess.run(['assoc', '.py'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                association = result.stdout.strip()
                print(f"✅ ارتباط .py: {association}")
                
                if 'Python' in association:
                    print("✅ ملفات .py مرتبطة بـ Python")
                else:
                    print("⚠️ ملفات .py غير مرتبطة بـ Python بشكل صحيح")
                    return "wrong_association"
            else:
                print("❌ لا يوجد ارتباط لملفات .py")
                return "no_association"
        except Exception as e:
            print(f"❌ خطأ في فحص الارتباطات: {e}")
        
        # Check if file opens in editor instead of running
        print("\n📝 فحص سلوك النقر المزدوج:")
        
        try:
            result = subprocess.run(['ftype', 'Python.File'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                file_type = result.stdout.strip()
                print(f"📄 نوع الملف: {file_type}")
                
                if 'python.exe' in file_type.lower():
                    print("✅ ملفات Python تُشغل بـ python.exe")
                elif 'edit' in file_type.lower() or 'notepad' in file_type.lower():
                    print("⚠️ ملفات Python تُفتح في محرر النصوص")
                    return "opens_in_editor"
                else:
                    print("⚠️ سلوك غير معروف لملفات Python")
            else:
                print("❌ لا يمكن تحديد نوع الملف")
        except Exception as e:
            print(f"❌ خطأ في فحص نوع الملف: {e}")
        
        # Check current directory issue
        print("\n📁 فحص مشكلة المجلد الحالي:")
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        if os.path.exists(desktop_path):
            print(f"✅ مجلد البرنامج موجود: {desktop_path}")
            
            # Check for required files
            required_files = [
                "protech_simple_working.py",
                "protech_simple_data.json"
            ]
            
            missing_files = []
            for file in required_files:
                file_path = os.path.join(desktop_path, file)
                if os.path.exists(file_path):
                    print(f"✅ {file}: موجود")
                else:
                    print(f"❌ {file}: مفقود")
                    missing_files.append(file)
            
            if missing_files:
                return "missing_files"
        else:
            print("❌ مجلد البرنامج غير موجود")
            return "missing_directory"
        
        # Check for GUI issues
        print("\n🖥️ فحص مشاكل الواجهة:")
        
        try:
            # Test tkinter import
            result = subprocess.run([
                'python', '-c', 
                'import tkinter; print("✅ tkinter متاح")'
            ], capture_output=True, text=True, cwd=desktop_path)
            
            if result.returncode == 0:
                print("✅ tkinter يعمل بشكل صحيح")
            else:
                print("❌ مشكلة في tkinter")
                print(f"الخطأ: {result.stderr}")
                return "tkinter_issue"
        except Exception as e:
            print(f"❌ خطأ في اختبار tkinter: {e}")
        
        return "unknown"
        
    except Exception as e:
        print(f"❌ خطأ في التشخيص: {e}")
        return "error"

def create_desktop_launcher():
    """Create desktop launcher that works with double-click"""
    try:
        print("\n🚀 إنشاء مشغل سطح المكتب...")
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        
        # Create batch launcher
        launcher_content = f'''@echo off
title ProTech - نظام المحاسبة
echo 🚀 تشغيل ProTech...
echo 🚀 Starting ProTech...

cd /d "{desktop_path}"

echo المجلد الحالي: %CD%
echo Current Directory: %CD%

echo فحص الملفات المطلوبة...
echo Checking required files...

if not exist "protech_simple_working.py" (
    echo ❌ ملف البرنامج الرئيسي غير موجود
    echo ❌ Main program file not found
    pause
    exit /b 1
)

if not exist "protech_simple_data.json" (
    echo 📄 إنشاء ملف البيانات...
    echo 📄 Creating data file...
    echo {{"suppliers":[],"products":[],"customers":[],"sales":[]}} > protech_simple_data.json
)

echo ✅ جميع الملفات جاهزة
echo ✅ All files ready

echo تشغيل ProTech...
echo Starting ProTech...

python protech_simple_working.py

if errorlevel 1 (
    echo ❌ خطأ في تشغيل ProTech
    echo ❌ Error running ProTech
    echo.
    echo محاولة تشغيل بديل...
    echo Trying alternative execution...
    
    python -u protech_simple_working.py
    
    if errorlevel 1 (
        echo ❌ فشل التشغيل البديل
        echo ❌ Alternative execution failed
        echo.
        echo فتح البرنامج في محرر النصوص للمراجعة...
        echo Opening program in text editor for review...
        notepad protech_simple_working.py
    )
)

echo.
echo اضغط أي مفتاح للخروج...
echo Press any key to exit...
pause > nul
'''
        
        launcher_path = os.path.join(desktop_path, "تشغيل_ProTech.bat")
        
        with open(launcher_path, 'w', encoding='utf-8') as f:
            f.write(launcher_content)
        
        print(f"✅ تم إنشاء المشغل: {launcher_path}")
        
        # Create Python launcher
        python_launcher = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Desktop Launcher
مشغل ProTech لسطح المكتب
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def launch_protech():
    """Launch ProTech with proper error handling"""
    try:
        # Change to correct directory
        desktop_path = r"{desktop_path}"
        os.chdir(desktop_path)
        
        # Check for main file
        if not os.path.exists("protech_simple_working.py"):
            messagebox.showerror("خطأ", "ملف البرنامج الرئيسي غير موجود")
            return False
        
        # Create data file if missing
        if not os.path.exists("protech_simple_data.json"):
            import json
            empty_data = {{"suppliers":[],"products":[],"customers":[],"sales":[]}}
            with open("protech_simple_data.json", 'w', encoding='utf-8') as f:
                json.dump(empty_data, f, ensure_ascii=False, indent=2)
        
        # Launch ProTech
        subprocess.Popen([sys.executable, "protech_simple_working.py"])
        return True
        
    except Exception as e:
        messagebox.showerror("خطأ في التشغيل", f"فشل في تشغيل ProTech:\\n{{e}}")
        return False

def main():
    """Main function"""
    # Create simple launcher GUI
    root = tk.Tk()
    root.title("ProTech Launcher")
    root.geometry("300x150")
    root.resizable(False, False)
    
    # Center the window
    root.eval('tk::PlaceWindow . center')
    
    label = tk.Label(root, text="مشغل ProTech", font=("Arial", 16))
    label.pack(pady=20)
    
    def start_protech():
        if launch_protech():
            root.destroy()
    
    start_btn = tk.Button(root, text="تشغيل ProTech", command=start_protech,
                         font=("Arial", 12), bg="#4CAF50", fg="white",
                         width=15, height=2)
    start_btn.pack(pady=10)
    
    exit_btn = tk.Button(root, text="إغلاق", command=root.destroy,
                        font=("Arial", 10), width=10)
    exit_btn.pack(pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    main()
'''
        
        python_launcher_path = os.path.join(desktop_path, "ProTech_Launcher.py")
        
        with open(python_launcher_path, 'w', encoding='utf-8') as f:
            f.write(python_launcher)
        
        print(f"✅ تم إنشاء المشغل Python: {python_launcher_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء المشغل: {e}")
        return False

def fix_file_associations():
    """Fix Python file associations"""
    try:
        print("\n🔗 إصلاح ارتباطات الملفات...")
        
        # Create registry fix script
        reg_fix = '''Windows Registry Editor Version 5.00

[HKEY_CLASSES_ROOT\\.py]
@="Python.File"
"Content Type"="text/plain"

[HKEY_CLASSES_ROOT\\Python.File]
@="Python File"

[HKEY_CLASSES_ROOT\\Python.File\\shell]

[HKEY_CLASSES_ROOT\\Python.File\\shell\\open]

[HKEY_CLASSES_ROOT\\Python.File\\shell\\open\\command]
@="\\"C:\\\\Python\\\\python.exe\\" \\"%1\\" %*"

[HKEY_CLASSES_ROOT\\Python.File\\shell\\edit]

[HKEY_CLASSES_ROOT\\Python.File\\shell\\edit\\command]
@="notepad.exe \\"%1\\""
'''
        
        reg_file = "fix_python_associations.reg"
        
        with open(reg_file, 'w', encoding='utf-8') as f:
            f.write(reg_fix)
        
        print(f"✅ تم إنشاء ملف إصلاح التسجيل: {reg_file}")
        print("💡 شغل هذا الملف كمدير لإصلاح الارتباطات")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء إصلاح الارتباطات: {e}")
        return False

def create_shortcut():
    """Create desktop shortcut"""
    try:
        print("\n🔗 إنشاء اختصار سطح المكتب...")
        
        shortcut_script = '''
Set WshShell = CreateObject("WScript.Shell")
Set Shortcut = WshShell.CreateShortcut(WshShell.SpecialFolders("Desktop") & "\\ProTech.lnk")
Shortcut.TargetPath = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program\\تشغيل_ProTech.bat"
Shortcut.WorkingDirectory = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
Shortcut.Description = "ProTech - نظام المحاسبة"
Shortcut.Save
'''
        
        with open("create_shortcut.vbs", 'w', encoding='utf-8') as f:
            f.write(shortcut_script)
        
        # Run the script
        try:
            subprocess.run(['cscript', '//NoLogo', 'create_shortcut.vbs'], 
                         capture_output=True)
            print("✅ تم إنشاء اختصار سطح المكتب")
        except:
            print("⚠️ فشل في تشغيل سكريبت الاختصار")
        
        return True
        
    except Exception as e:
        print(f"❌ فشل في إنشاء الاختصار: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح مشكلة النقر المزدوج لـ ProTech")
    print("🔧 Fixing ProTech Double-Click Issue")
    print("="*60)
    
    # Diagnose the issue
    issue_type = diagnose_double_click_issue()
    
    print(f"\n🎯 نوع المشكلة المكتشفة: {issue_type}")
    
    # Apply fixes based on issue type
    fixes_applied = []
    
    # Always create desktop launcher
    if create_desktop_launcher():
        fixes_applied.append("مشغل سطح المكتب")
    
    # Create shortcut
    if create_shortcut():
        fixes_applied.append("اختصار سطح المكتب")
    
    # Fix associations if needed
    if issue_type in ["wrong_association", "no_association", "opens_in_editor"]:
        if fix_file_associations():
            fixes_applied.append("إصلاح ارتباطات الملفات")
    
    # Summary
    print("\n" + "="*60)
    print("📊 ملخص الإصلاحات:")
    
    if fixes_applied:
        print(f"✅ تم تطبيق {len(fixes_applied)} إصلاح:")
        for fix in fixes_applied:
            print(f"  • {fix}")
    else:
        print("❌ لم يتم تطبيق أي إصلاحات")
    
    print("\n💡 طرق التشغيل الجديدة:")
    print("1. انقر مزدوج على 'تشغيل_ProTech.bat'")
    print("2. انقر مزدوج على 'ProTech_Launcher.py'")
    print("3. استخدم اختصار سطح المكتب 'ProTech'")
    print("4. شغل fix_python_associations.reg كمدير (إذا لزم)")
    
    print("\n🎯 سبب المشكلة:")
    
    reasons = {
        "python_not_found": "Python غير متاح في PATH",
        "python_not_installed": "Python غير مثبت",
        "wrong_association": "ارتباط خاطئ لملفات .py",
        "no_association": "لا يوجد ارتباط لملفات .py",
        "opens_in_editor": "ملفات .py تُفتح في محرر النصوص",
        "missing_files": "ملفات مطلوبة مفقودة",
        "missing_directory": "مجلد البرنامج مفقود",
        "tkinter_issue": "مشكلة في مكتبة tkinter",
        "unknown": "سبب غير معروف"
    }
    
    reason = reasons.get(issue_type, "سبب غير محدد")
    print(f"📋 {reason}")
    
    print("\n🎉 الآن يمكنك تشغيل ProTech بالنقر المزدوج!")

if __name__ == "__main__":
    main()
