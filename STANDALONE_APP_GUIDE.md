# نظام ProTech للمحاسبة - تطبيق سطح مكتب مستقل
# ProTech Accounting System - Standalone Desktop Application

## 🖥️ تطبيق محاسبة كامل ومستقل / Complete Standalone Accounting Application

تطبيق محاسبة شامل يعمل بالكامل على سطح المكتب بدون الحاجة لمتصفح أو اتصال بالإنترنت.

A comprehensive accounting application that runs entirely on desktop without the need for a browser or internet connection.

---

## 🚀 كيفية التشغيل / How to Run

### الطريقة الأولى: التشغيل المباشر (الأسهل)
```
انقر نقراً مزدوجاً على: Run ProTech Standalone.bat
Double-click on: Run ProTech Standalone.bat
```

### الطريقة الثانية: سطر الأوامر
```bash
python protech_standalone_app.py
```

---

## 📋 المتطلبات / Requirements

- **Python 3.7+** مثبت على النظام
- **tkinter** (مدمج مع Python)
- **sqlite3** (مدمج مع Python)
- **لا يحتاج إنترنت** / No internet required
- **لا يحتاج متصفح** / No browser required

---

## 🌟 المميزات الرئيسية / Key Features

### ✅ **تطبيق مستقل بالكامل**
- يعمل بدون متصفح
- لا يحتاج اتصال إنترنت
- قاعدة بيانات SQLite مدمجة
- واجهة رسومية احترافية

### ✅ **إدارة شاملة للمخزون**
- إضافة وتعديل وحذف المنتجات
- تتبع الكميات والأسعار
- تنبيهات المخزون المنخفض
- تصنيف المنتجات

### ✅ **واجهة ثنائية اللغة**
- دعم كامل للعربية (RTL)
- دعم الإنجليزية (LTR)
- تبديل سلس بين اللغتين
- خطوط واضحة ومقروءة

### ✅ **تصميم احترافي**
- واجهة حديثة وجذابة
- ألوان متناسقة
- أيقونات واضحة
- تنظيم منطقي للعناصر

---

## 📊 الوظائف المتاحة / Available Functions

### 🏠 **لوحة التحكم / Dashboard**
- **إحصائيات فورية**: عدد المنتجات، المخزون المنخفض، العملاء، قيمة المخزون
- **النشاط الحديث**: عرض آخر العمليات
- **نظرة سريعة**: حالة النظام العامة

### 📦 **إدارة المخزون / Inventory Management**
- **إضافة منتجات جديدة**: نموذج شامل لبيانات المنتج
- **تعديل المنتجات**: تحديث البيانات والأسعار
- **حذف المنتجات**: إزالة آمنة مع تأكيد
- **عرض جدولي**: جدول منظم مع ألوان تمييز الحالة
- **تصنيف تلقائي**: 
  - 🟢 مخزون جيد (أخضر)
  - 🟡 مخزون منخفض (أصفر)
  - 🔴 نفد المخزون (أحمر)

### 👥 **إدارة العملاء / Customer Management**
- إضافة عملاء جدد
- تعديل بيانات العملاء
- متابعة الأرصدة
- تصنيف العملاء (جملة/تجزئة)

### 🏢 **إدارة الموردين / Supplier Management**
- إدارة بيانات الموردين
- معلومات الاتصال
- تتبع جهات الاتصال

### 💰 **المبيعات / Sales**
- تسجيل عمليات البيع
- إدارة الفواتير
- متابعة المدفوعات
- حساب الأرباح

### 📊 **التقارير / Reports**
- تقارير المبيعات
- تقارير المخزون
- التحليلات المالية
- إحصائيات الأداء

### ⚙️ **الإعدادات / Settings**
- إعدادات النظام
- تخصيص الواجهة
- إعدادات قاعدة البيانات
- النسخ الاحتياطي

### ❓ **المساعدة / Help**
- دليل الاستخدام الشامل
- نصائح وإرشادات
- معلومات الاتصال
- الدعم الفني

---

## 🎯 كيفية الاستخدام / How to Use

### خطوات البداية:
1. **شغل التطبيق** - انقر على `Run ProTech Standalone.bat`
2. **استكشف لوحة التحكم** - اطلع على الإحصائيات
3. **أضف منتجات** - اذهب لإدارة المخزون وأضف منتجات
4. **أضف عملاء** - سجل بيانات العملاء
5. **ابدأ البيع** - استخدم نظام المبيعات
6. **راجع التقارير** - تابع الأداء والإحصائيات

### نصائح مهمة:
- **احفظ البيانات بانتظام** - التطبيق يحفظ تلقائياً
- **راجع المخزون المنخفض** - تابع التنبيهات
- **استخدم الأكواد** - لتسهيل البحث والتتبع
- **راجع التقارير** - لمتابعة الأداء

---

## 🗂️ ملفات النظام / System Files

```
📁 ProTech Standalone App/
├── 🖥️ Run ProTech Standalone.bat      # ملف التشغيل المباشر
├── 🐍 protech_standalone_app.py       # التطبيق الرئيسي
├── 🗄️ protech_accounting.db           # قاعدة البيانات (تُنشأ تلقائياً)
├── 📖 STANDALONE_APP_GUIDE.md         # هذا الدليل
└── 📋 README files...                 # ملفات التوثيق الأخرى
```

---

## 🔧 استكشاف الأخطاء / Troubleshooting

### مشكلة: Python غير موجود
```
الحل / Solution:
1. حمل Python من python.org
2. ثبت Python مع تفعيل "Add to PATH"
3. أعد تشغيل الكمبيوتر
4. جرب التشغيل مرة أخرى
```

### مشكلة: خطأ في قاعدة البيانات
```
الحل / Solution:
1. احذف ملف protech_accounting.db
2. أعد تشغيل التطبيق
3. سيتم إنشاء قاعدة بيانات جديدة
```

### مشكلة: واجهة لا تظهر بشكل صحيح
```
الحل / Solution:
1. تأكد من دقة الشاشة مناسبة
2. جرب تشغيل التطبيق كمدير
3. تأكد من تحديث Windows
```

---

## 📞 الدعم والمساعدة / Support & Help

### للحصول على المساعدة:
- **📧 البريد الإلكتروني**: <EMAIL>
- **🌐 الموقع الإلكتروني**: www.protech.com
- **📱 الهاتف**: +966-11-123-4567
- **💬 الدردشة**: متاح على الموقع

### للإبلاغ عن مشاكل:
- صف المشكلة بالتفصيل
- أرفق لقطة شاشة إن أمكن
- اذكر نظام التشغيل المستخدم
- اذكر إصدار Python

---

## 📄 الترخيص / License

© 2024 ProTech Accounting System. جميع الحقوق محفوظة.
© 2024 ProTech Accounting System. All rights reserved.

---

## 🎉 استمتع بالاستخدام!
## 🎉 Enjoy Using ProTech!

نظام ProTech للمحاسبة - حلول محاسبية احترافية لأعمالك
ProTech Accounting System - Professional accounting solutions for your business
