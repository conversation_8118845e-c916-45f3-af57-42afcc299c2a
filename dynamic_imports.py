
def safe_import(module_name, fallback=None):
    """Safely import modules with fallbacks"""
    try:
        return __import__(module_name)
    except ImportError:
        if fallback:
            try:
                return __import__(fallback)
            except ImportError:
                pass
        
        # Create dummy module
        class DummyModule:
            def __getattr__(self, name):
                return lambda *args, **kwargs: None
        
        return DummyModule()

def conditional_import():
    """Import modules conditionally"""
    modules = {}
    
    # Try to import common modules
    try:
        modules['tkinter'] = __import__('tkinter')
    except:
        modules['tkinter'] = None
    
    try:
        modules['json'] = __import__('json')
    except:
        modules['json'] = None
    
    try:
        modules['os'] = __import__('os')
    except:
        modules['os'] = None
    
    return modules

# Global modules
available_modules = conditional_import()
