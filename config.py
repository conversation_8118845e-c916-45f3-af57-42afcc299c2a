"""
ProTech Accounting System - Configuration File
إعدادات نظام ProTech للمحاسبة
"""

import os
from datetime import timedelta

class Config:
    """Base configuration class"""
    
    # Basic Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'protech-accounting-secret-key-2024'
    
    # Database Configuration
    DATABASE_URL = os.environ.get('DATABASE_URL') or 'sqlite:///protech.db'
    SQLALCHEMY_DATABASE_URI = DATABASE_URL
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Application Settings
    APP_NAME = 'ProTech Accounting System'
    APP_NAME_AR = 'نظام ProTech للمحاسبة'
    VERSION = '1.0.0'
    
    # Internationalization
    LANGUAGES = {
        'en': 'English',
        'ar': 'العربية'
    }
    DEFAULT_LANGUAGE = 'en'
    
    # Security Settings
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = None
    SESSION_COOKIE_SECURE = False  # Set to True in production with HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    
    # File Upload Settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'csv', 'xlsx'}
    
    # API Settings
    API_RATE_LIMIT = "100 per hour"
    API_PAGINATION_DEFAULT = 20
    API_PAGINATION_MAX = 100
    
    # Cache Settings
    CACHE_TYPE = "simple"
    CACHE_DEFAULT_TIMEOUT = 300  # 5 minutes
    
    # Mail Settings (for future email functionality)
    MAIL_SERVER = os.environ.get('MAIL_SERVER') or 'localhost'
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # Logging Configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FILE = 'protech.log'
    LOG_MAX_BYTES = 10 * 1024 * 1024  # 10MB
    LOG_BACKUP_COUNT = 5
    
    # Business Settings
    COMPANY_NAME = os.environ.get('COMPANY_NAME') or 'ProTech Solutions'
    COMPANY_NAME_AR = os.environ.get('COMPANY_NAME_AR') or 'شركة ProTech للحلول'
    COMPANY_ADDRESS = os.environ.get('COMPANY_ADDRESS') or '123 Business Street'
    COMPANY_PHONE = os.environ.get('COMPANY_PHONE') or '******-0123'
    COMPANY_EMAIL = os.environ.get('COMPANY_EMAIL') or '<EMAIL>'
    COMPANY_WEBSITE = os.environ.get('COMPANY_WEBSITE') or 'https://protech.com'
    
    # Currency Settings
    DEFAULT_CURRENCY = 'USD'
    CURRENCY_SYMBOL = '$'
    CURRENCY_POSITION = 'before'  # 'before' or 'after'
    
    # Tax Settings
    DEFAULT_TAX_RATE = 15.0  # 15%
    TAX_INCLUSIVE = False
    
    # Inventory Settings
    LOW_STOCK_THRESHOLD = 10
    AUTO_REORDER = False
    BARCODE_PREFIX = 'PT'
    
    # Invoice Settings
    INVOICE_PREFIX = 'INV'
    INVOICE_NUMBER_LENGTH = 6
    INVOICE_DUE_DAYS = 30
    
    # Performance Settings
    PERFORMANCE_MONITORING = True
    SLOW_QUERY_THRESHOLD = 0.5  # seconds
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        pass

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    TESTING = False
    
    # Development-specific settings
    SQLALCHEMY_ECHO = True  # Log SQL queries
    WTF_CSRF_ENABLED = False  # Disable CSRF for easier testing
    
    # Development database
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or \
        'sqlite:///' + os.path.join(os.path.dirname(os.path.abspath(__file__)), 'protech_dev.db')

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    DEBUG = True
    WTF_CSRF_ENABLED = False
    
    # In-memory database for testing
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    
    # Disable rate limiting for tests
    API_RATE_LIMIT = None

class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    TESTING = False
    
    # Production security settings
    SESSION_COOKIE_SECURE = True
    WTF_CSRF_ENABLED = True
    
    # Production database
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or \
        'postgresql://user:password@localhost/protech_production'
    
    # Production logging
    LOG_LEVEL = 'WARNING'
    
    @classmethod
    def init_app(cls, app):
        """Production-specific initialization"""
        Config.init_app(app)
        
        # Log to syslog in production
        import logging
        from logging.handlers import SysLogHandler
        syslog_handler = SysLogHandler()
        syslog_handler.setLevel(logging.WARNING)
        app.logger.addHandler(syslog_handler)

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# Get configuration based on environment
def get_config():
    """Get configuration based on environment variable"""
    config_name = os.environ.get('FLASK_ENV') or 'default'
    return config.get(config_name, config['default'])

# Application constants
class Constants:
    """Application constants"""
    
    # User roles
    ROLES = {
        'ADMIN': 'Administrator',
        'MANAGER': 'Manager', 
        'CASHIER': 'Cashier',
        'VIEWER': 'Viewer'
    }
    
    # Product categories
    PRODUCT_CATEGORIES = [
        'Electronics',
        'Office Supplies',
        'Furniture',
        'Software',
        'Hardware',
        'Accessories',
        'Services'
    ]
    
    # Customer categories
    CUSTOMER_CATEGORIES = [
        'RETAIL',
        'WHOLESALE', 
        'DISTRIBUTOR',
        'VIP'
    ]
    
    # Payment methods
    PAYMENT_METHODS = [
        'CASH',
        'CREDIT_CARD',
        'DEBIT_CARD',
        'BANK_TRANSFER',
        'CHECK',
        'CREDIT'
    ]
    
    # Invoice statuses
    INVOICE_STATUSES = [
        'DRAFT',
        'PENDING',
        'PAID',
        'PARTIAL',
        'OVERDUE',
        'CANCELLED'
    ]
    
    # Inventory movement types
    MOVEMENT_TYPES = [
        'IN',
        'OUT',
        'ADJUSTMENT',
        'TRANSFER',
        'RETURN'
    ]
    
    # Units of measurement
    UNITS = [
        'PCS',  # Pieces
        'KG',   # Kilograms
        'LB',   # Pounds
        'L',    # Liters
        'GAL',  # Gallons
        'M',    # Meters
        'FT',   # Feet
        'BOX',  # Box
        'SET',  # Set
        'PACK'  # Pack
    ]
