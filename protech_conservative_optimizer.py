#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Conservative Optimizer
محسن ProTech المحافظ

Very conservative optimization that only fixes critical issues without breaking code
تحسين محافظ جداً يصلح فقط المشاكل الحرجة دون كسر الكود
"""

import os
import re
import shutil
from datetime import datetime

class ProTechConservativeOptimizer:
    """Conservative optimizer for ProTech code"""
    
    def __init__(self):
        self.code_file = "protech_simple_working.py"
        self.fixes_applied = []
        
    def create_backup(self):
        """Create backup before optimization"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f'{self.code_file}.conservative_backup_{timestamp}'
            shutil.copy2(self.code_file, backup_name)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
            return backup_name
        except Exception as e:
            print(f"❌ خطأ في النسخة الاحتياطية: {e}")
            return None
    
    def fix_critical_syntax_only(self):
        """Fix only critical syntax issues that prevent running"""
        try:
            print("🔧 إصلاح مشاكل التركيب الحرجة فقط...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            fixes_count = 0
            
            # Fix 1: Empty except blocks that cause IndentationError
            for i in range(len(lines)):
                line = lines[i].strip()
                
                # Check for except blocks followed by empty lines
                if line.startswith('except') and ':' in line:
                    # Check next line
                    if i + 1 < len(lines):
                        next_line = lines[i + 1]
                        
                        # If next line is empty or doesn't have proper indentation
                        if not next_line.strip():
                            # Get indentation of except line
                            except_indent = len(lines[i]) - len(lines[i].lstrip())
                            
                            # Add proper pass statement
                            pass_line = ' ' * (except_indent + 4) + 'pass\n'
                            lines[i + 1] = pass_line
                            fixes_count += 1
            
            # Fix 2: Try blocks without content
            for i in range(len(lines)):
                line = lines[i].strip()
                
                if line == 'try:':
                    # Check if next line has content
                    if i + 1 < len(lines):
                        next_line = lines[i + 1]
                        
                        if not next_line.strip():
                            # Get indentation of try line
                            try_indent = len(lines[i]) - len(lines[i].lstrip())
                            
                            # Add pass statement
                            pass_line = ' ' * (try_indent + 4) + 'pass\n'
                            lines[i + 1] = pass_line
                            fixes_count += 1
            
            # Write fixed content
            with open(self.code_file, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            if fixes_count > 0:
                self.fixes_applied.append(f"إصلاح {fixes_count} مشكلة تركيب حرجة")
                print(f"✅ تم إصلاح {fixes_count} مشكلة تركيب حرجة")
            else:
                print("✅ لا توجد مشاكل تركيب حرجة")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح التركيب: {e}")
            return False
    
    def remove_only_broken_imports(self):
        """Remove only imports that cause errors"""
        try:
            print("📦 إزالة الاستيرادات المكسورة فقط...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Test each import individually
            lines = content.split('\n')
            broken_imports = []
            
            for i, line in enumerate(lines):
                if line.strip().startswith(('import ', 'from ')) and 'tkinter' not in line:
                    try:
                        # Try to execute the import
                        exec(line.strip())
                    except ImportError:
                        broken_imports.append(i)
                    except Exception:
                        # Skip complex imports
                        pass
            
            # Remove broken imports
            if broken_imports:
                for i in reversed(broken_imports):
                    lines.pop(i)
                
                # Write fixed content
                with open(self.code_file, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(lines))
                
                self.fixes_applied.append(f"إزالة {len(broken_imports)} استيراد مكسور")
                print(f"✅ تم إزالة {len(broken_imports)} استيراد مكسور")
            else:
                print("✅ لا توجد استيرادات مكسورة")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح الاستيرادات: {e}")
            return False
    
    def add_minimal_missing_returns(self):
        """Add return statements only where absolutely necessary"""
        try:
            print("↩️ إضافة return statements الضرورية فقط...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            returns_added = 0
            
            # Only add returns to getter functions that clearly need them
            for i in range(len(lines)):
                line = lines[i].strip()
                
                # Only fix functions that start with 'get_' or 'calculate_'
                if (line.startswith('def get_') or line.startswith('def calculate_')) and ':' in line:
                    # Find end of function
                    j = i + 1
                    has_return = False
                    function_end = len(lines)
                    
                    while j < len(lines):
                        next_line = lines[j]
                        
                        # Check if we've reached the end of the function
                        if next_line.strip() and not next_line.startswith('    ') and not next_line.startswith('\t'):
                            function_end = j
                            break
                        
                        # Check if there's already a return statement
                        if 'return' in next_line:
                            has_return = True
                            break
                        
                        j += 1
                    
                    # Add return only if function doesn't have one and is short
                    if not has_return and (function_end - i) < 20:  # Only short functions
                        # Find last line of function
                        last_line_idx = function_end - 1
                        while last_line_idx > i and not lines[last_line_idx].strip():
                            last_line_idx -= 1
                        
                        if last_line_idx > i:
                            # Get indentation
                            last_line = lines[last_line_idx]
                            indent = len(last_line) - len(last_line.lstrip())
                            
                            # Add return statement
                            return_line = ' ' * indent + 'return None\n'
                            lines.insert(last_line_idx + 1, return_line)
                            returns_added += 1
            
            # Write fixed content
            with open(self.code_file, 'w', encoding='utf-8') as f:
                f.writelines(lines)
            
            if returns_added > 0:
                self.fixes_applied.append(f"إضافة {returns_added} return ضروري")
                print(f"✅ تم إضافة {returns_added} return ضروري")
            else:
                print("✅ لا توجد return statements مفقودة ضرورية")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إضافة return statements: {e}")
            return False
    
    def fix_obvious_typos_only(self):
        """Fix only obvious typos that cause errors"""
        try:
            print("✏️ إصلاح الأخطاء الإملائية الواضحة فقط...")
            
            with open(self.code_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Fix only obvious typos
            obvious_fixes = [
                # Fix common variable name typos
                ('self.root.mainloop()', 'self.root.mainloop()'),  # This is correct, no change
                
                # Fix obvious method name typos (only if they exist)
                ('self.destory()', 'self.destroy()'),
                ('self.configue()', 'self.configure()'),
                ('self.upate()', 'self.update()'),
            ]
            
            fixes_count = 0
            for wrong, correct in obvious_fixes:
                if wrong in content:
                    content = content.replace(wrong, correct)
                    fixes_count += 1
            
            # Write fixed content
            with open(self.code_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            if fixes_count > 0:
                self.fixes_applied.append(f"إصلاح {fixes_count} خطأ إملائي واضح")
                print(f"✅ تم إصلاح {fixes_count} خطأ إملائي واضح")
            else:
                print("✅ لا توجد أخطاء إملائية واضحة")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إصلاح الأخطاء الإملائية: {e}")
            return False
    
    def test_code_carefully(self):
        """Test the code very carefully"""
        try:
            print("🧪 اختبار الكود بحذر...")
            
            import subprocess
            import sys
            
            # Test compilation only
            result = subprocess.run([sys.executable, '-m', 'py_compile', self.code_file], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ اختبار التجميع: نجح")
                return True
            else:
                print(f"❌ اختبار التجميع: فشل")
                print(f"الخطأ: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في الاختبار: {e}")
            return False
    
    def run_conservative_optimization(self):
        """Run very conservative optimization"""
        try:
            print("🛡️ بدء التحسين المحافظ لـ ProTech")
            print("🛡️ Starting ProTech Conservative Optimization")
            print("="*60)
            
            # Create backup
            self.create_backup()
            
            # Apply only critical fixes
            critical_fixes = [
                ("إصلاح مشاكل التركيب الحرجة", self.fix_critical_syntax_only),
                ("إزالة الاستيرادات المكسورة", self.remove_only_broken_imports),
                ("إضافة return statements الضرورية", self.add_minimal_missing_returns),
                ("إصلاح الأخطاء الإملائية الواضحة", self.fix_obvious_typos_only),
            ]
            
            successful_fixes = 0
            
            for name, fix_func in critical_fixes:
                print(f"\n🔧 {name}...")
                if fix_func():
                    successful_fixes += 1
                    print(f"✅ {name}: نجح")
                else:
                    print(f"❌ {name}: فشل")
                
                # Test after each fix
                if not self.test_code_carefully():
                    print(f"⚠️ الكود لا يعمل بعد {name}")
                    break
            
            # Final test
            if self.test_code_carefully():
                print("\n🎉 التحسين المحافظ مكتمل بنجاح!")
                print("🎉 Conservative optimization completed successfully!")
                
                if self.fixes_applied:
                    print(f"\n📊 الإصلاحات المطبقة:")
                    for fix in self.fixes_applied:
                        print(f"  • {fix}")
                else:
                    print("\n✅ الكود كان سليماً ولم يحتج إصلاحات")
                
                print(f"\n✅ نجح {successful_fixes}/{len(critical_fixes)} إصلاح")
                
                return True
            else:
                print("\n⚠️ التحسين فشل - الكود لا يعمل")
                print("⚠️ Optimization failed - code doesn't work")
                return False
            
        except Exception as e:
            print(f"❌ خطأ في التحسين المحافظ: {e}")
            return False

def main():
    """Main conservative optimization function"""
    optimizer = ProTechConservativeOptimizer()
    success = optimizer.run_conservative_optimization()
    
    if success:
        print("\n🚀 ProTech جاهز للاستخدام!")
        print("🚀 ProTech is ready to use!")
    else:
        print("\n🔧 قد تحتاج استعادة النسخة الاحتياطية")
        print("🔧 May need to restore backup")
    
    return success

if __name__ == "__main__":
    main()
