#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الباركود / Barcode Test
Test script to verify barcode functionality
"""

import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_barcode_functionality():
    """Test barcode functionality"""
    
    # Create test window
    test_window = tk.Tk()
    test_window.title("اختبار الباركود / Barcode Test")
    test_window.geometry("600x400")
    test_window.configure(bg='white')
    
    # Header
    header_frame = tk.Frame(test_window, bg='#059669', height=60)
    header_frame.pack(fill='x')
    header_frame.pack_propagate(False)
    
    tk.Label(
        header_frame,
        text="🧪 اختبار الباركود / Barcode Test",
        font=('Arial', 16, 'bold'),
        fg='white',
        bg='#059669'
    ).pack(expand=True)
    
    # Instructions
    instructions_frame = tk.Frame(test_window, bg='white')
    instructions_frame.pack(fill='x', padx=20, pady=20)
    
    instructions_text = """
📋 تعليمات الاختبار / Test Instructions:

1. افتح البرنامج الرئيسي / Open main program
2. اذهب إلى صفحة المبيعات / Go to Sales page  
3. جرب الباركودات التالية / Try the following barcodes:

📱 الباركودات المتاحة / Available Barcodes:
• 1234567890123 - Business Laptop ($1,200)
• 1234567890124 - Wireless Mouse ($35)
• 1234567890125 - Mechanical Keyboard ($120)
• 1234567890126 - 24" Monitor ($280)
• 1234567890127 - Smartphone ($550)
• 1234567890128 - Office Desk ($450)
• 1234567890129 - Ergonomic Chair ($650)
• 1234567890130 - Laser Printer ($250)

🔍 خطوات الاختبار / Test Steps:
1. أدخل الباركود في الحقل / Enter barcode in field
2. اضغط Enter أو زر "إضافة" / Press Enter or "Add" button
3. تحقق من ظهور المنتج في الجدول / Check product appears in table
4. تحقق من تحديث الإجمالي / Check total updates

✅ النتائج المتوقعة / Expected Results:
• ظهور المنتج في الجدول / Product appears in table
• تحديث الإجمالي / Total updates
• مسح حقل الباركود / Barcode field clears
• رسالة نجاح / Success message
"""
    
    text_widget = tk.Text(
        instructions_frame,
        font=('Arial', 10),
        wrap='word',
        bg='#f9fafb',
        height=15
    )
    text_widget.insert('1.0', instructions_text)
    text_widget.config(state='disabled')
    text_widget.pack(fill='both', expand=True)
    
    # Buttons
    buttons_frame = tk.Frame(test_window, bg='white')
    buttons_frame.pack(fill='x', padx=20, pady=10)
    
    def open_main_program():
        """Open main program"""
        try:
            import subprocess
            subprocess.Popen([sys.executable, 'protech_simple_working.py'])
            messagebox.showinfo(
                "تم فتح البرنامج / Program Opened",
                "تم فتح البرنامج الرئيسي\nMain program opened\n\nاذهب إلى صفحة المبيعات لاختبار الباركود\nGo to Sales page to test barcode"
            )
        except Exception as e:
            messagebox.showerror(
                "خطأ / Error",
                f"فشل في فتح البرنامج\nFailed to open program:\n{str(e)}"
            )
    
    def copy_barcode(barcode):
        """Copy barcode to clipboard"""
        test_window.clipboard_clear()
        test_window.clipboard_append(barcode)
        messagebox.showinfo(
            "تم النسخ / Copied",
            f"تم نسخ الباركود: {barcode}\nBarcode copied: {barcode}\n\nالصقه في حقل الباركود\nPaste it in barcode field"
        )
    
    tk.Button(
        buttons_frame,
        text="🚀 فتح البرنامج الرئيسي / Open Main Program",
        font=('Arial', 12, 'bold'),
        bg='#059669',
        fg='white',
        command=open_main_program,
        cursor='hand2'
    ).pack(side='left', padx=10)
    
    tk.Button(
        buttons_frame,
        text="📋 نسخ باركود تجريبي / Copy Test Barcode",
        font=('Arial', 12, 'bold'),
        bg='#3b82f6',
        fg='white',
        command=lambda: copy_barcode('1234567890123'),
        cursor='hand2'
    ).pack(side='left', padx=10)
    
    tk.Button(
        buttons_frame,
        text="❌ إغلاق / Close",
        font=('Arial', 12, 'bold'),
        bg='#6b7280',
        fg='white',
        command=test_window.destroy,
        cursor='hand2'
    ).pack(side='right', padx=10)
    
    # Center window
    test_window.update_idletasks()
    x = (test_window.winfo_screenwidth() // 2) - (test_window.winfo_width() // 2)
    y = (test_window.winfo_screenheight() // 2) - (test_window.winfo_height() // 2)
    test_window.geometry(f"+{x}+{y}")
    
    test_window.mainloop()

if __name__ == "__main__":
    test_barcode_functionality()
