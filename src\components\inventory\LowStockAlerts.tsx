'use client';

import React, { useState, useEffect } from 'react';
import { 
  ExclamationTriangleIcon, 
  ExclamationCircleIcon,
  InformationCircleIcon,
  ChevronRightIcon 
} from '@heroicons/react/24/outline';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card';
import Button from '@/components/ui/Button';

interface LowStockProduct {
  id: string;
  code: string;
  name: string;
  currentStock: number;
  minStock: number;
  unit: string;
  alertLevel: 'critical' | 'warning' | 'low';
  stockPercentage: number;
  daysUntilOutOfStock: number | null;
  category?: {
    id: string;
    name: string;
  };
  supplier?: {
    id: string;
    name: string;
    email?: string;
    phone?: string;
  };
}

interface LowStockAlertsProps {
  threshold?: number;
  onProductClick?: (product: LowStockProduct) => void;
  onCreatePurchaseOrder?: (product: LowStockProduct) => void;
}

export default function LowStockAlerts({ 
  threshold = 10, 
  onProductClick,
  onCreatePurchaseOrder 
}: LowStockAlertsProps) {
  const [products, setProducts] = useState<LowStockProduct[]>([]);
  const [summary, setSummary] = useState({
    totalLowStock: 0,
    critical: 0,
    warning: 0,
    low: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchLowStockProducts();
  }, [threshold]);

  const fetchLowStockProducts = async () => {
    try {
      setLoading(true);
      const response = await fetch(`/api/inventory/low-stock?threshold=${threshold}&limit=20`);
      const data = await response.json();

      if (data.success) {
        setProducts(data.data.products);
        setSummary(data.data.summary);
      } else {
        setError(data.error || 'Failed to fetch low stock products');
      }
    } catch (err) {
      setError('Failed to fetch low stock products');
      console.error('Error fetching low stock products:', err);
    } finally {
      setLoading(false);
    }
  };

  const getAlertIcon = (alertLevel: string) => {
    switch (alertLevel) {
      case 'critical':
        return <ExclamationCircleIcon className="h-5 w-5 text-red-500" />;
      case 'warning':
        return <ExclamationTriangleIcon className="h-5 w-5 text-orange-500" />;
      default:
        return <InformationCircleIcon className="h-5 w-5 text-blue-500" />;
    }
  };

  const getAlertColor = (alertLevel: string) => {
    switch (alertLevel) {
      case 'critical':
        return 'border-l-red-500 bg-red-50';
      case 'warning':
        return 'border-l-orange-500 bg-orange-50';
      default:
        return 'border-l-blue-500 bg-blue-50';
    }
  };

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Low Stock Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Low Stock Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <ExclamationCircleIcon className="mx-auto h-12 w-12 text-red-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Error</h3>
            <p className="mt-1 text-sm text-gray-500">{error}</p>
            <Button onClick={fetchLowStockProducts} className="mt-4">
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Low Stock Alerts</CardTitle>
          <div className="flex space-x-4 text-sm">
            <span className="flex items-center">
              <div className="w-3 h-3 bg-red-500 rounded-full mr-1"></div>
              Critical ({summary.critical})
            </span>
            <span className="flex items-center">
              <div className="w-3 h-3 bg-orange-500 rounded-full mr-1"></div>
              Warning ({summary.warning})
            </span>
            <span className="flex items-center">
              <div className="w-3 h-3 bg-blue-500 rounded-full mr-1"></div>
              Low ({summary.low})
            </span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        {products.length === 0 ? (
          <div className="text-center py-8">
            <InformationCircleIcon className="mx-auto h-12 w-12 text-green-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">All Good!</h3>
            <p className="mt-1 text-sm text-gray-500">
              No products are currently below the stock threshold.
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {products.map((product) => (
              <div
                key={product.id}
                className={`border-l-4 p-4 rounded-r-lg ${getAlertColor(product.alertLevel)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-start space-x-3">
                    {getAlertIcon(product.alertLevel)}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <h4 className="text-sm font-medium text-gray-900 truncate">
                          {product.name}
                        </h4>
                        <span className="text-xs text-gray-500">
                          ({product.code})
                        </span>
                      </div>
                      <div className="mt-1 flex items-center space-x-4 text-xs text-gray-600">
                        <span>
                          Stock: {product.currentStock} {product.unit}
                        </span>
                        <span>
                          Min: {product.minStock} {product.unit}
                        </span>
                        {product.category && (
                          <span>
                            Category: {product.category.name}
                          </span>
                        )}
                      </div>
                      {product.daysUntilOutOfStock !== null && (
                        <div className="mt-1 text-xs text-gray-600">
                          Estimated {product.daysUntilOutOfStock} days until out of stock
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {product.supplier && onCreatePurchaseOrder && (
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => onCreatePurchaseOrder(product)}
                      >
                        Order
                      </Button>
                    )}
                    {onProductClick && (
                      <button
                        onClick={() => onProductClick(product)}
                        className="text-gray-400 hover:text-gray-500"
                      >
                        <ChevronRightIcon className="h-5 w-5" />
                      </button>
                    )}
                  </div>
                </div>
                
                {/* Stock level bar */}
                <div className="mt-3">
                  <div className="flex items-center justify-between text-xs text-gray-600 mb-1">
                    <span>Stock Level</span>
                    <span>{product.stockPercentage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className={`h-2 rounded-full ${
                        product.alertLevel === 'critical'
                          ? 'bg-red-500'
                          : product.alertLevel === 'warning'
                          ? 'bg-orange-500'
                          : 'bg-blue-500'
                      }`}
                      style={{ width: `${Math.min(product.stockPercentage, 100)}%` }}
                    />
                  </div>
                </div>
              </div>
            ))}
            
            {summary.totalLowStock > products.length && (
              <div className="text-center pt-4">
                <p className="text-sm text-gray-500">
                  Showing {products.length} of {summary.totalLowStock} low stock items
                </p>
                <Button variant="outline" size="sm" className="mt-2">
                  View All Low Stock Items
                </Button>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
