#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Empty Reports Page
إصلاح صفحة التقارير الفارغة

Quick fix for empty reports page in ProTech
إصلاح سريع لصفحة التقارير الفارغة في ProTech
"""

import os
import shutil
from datetime import datetime

def fix_empty_reports_page():
    """إصلاح صفحة التقارير الفارغة"""
    try:
        print("🔧 إصلاح صفحة التقارير الفارغة")
        print("🔧 Fixing Empty Reports Page")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.empty_reports_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Simple working reports method
        working_reports = '''
    def show_reports(self):
        """عرض صفحة التقارير العاملة"""
        try:
            self.clear_content()
            self.update_status("تحميل التقارير...")
            
            # العنوان الرئيسي
            title_label = tk.Label(self.content_frame, text="📊 التقارير والإحصائيات", 
                                  font=("Arial", 20, "bold"), bg='white', fg='#2c3e50')
            title_label.pack(pady=20)
            
            # إطار التقارير الرئيسي
            reports_main_frame = tk.Frame(self.content_frame, bg='white')
            reports_main_frame.pack(fill='both', expand=True, padx=20, pady=10)
            
            # الشريط الجانبي للتقارير
            reports_sidebar = tk.Frame(reports_main_frame, bg='#ecf0f1', width=200)
            reports_sidebar.pack(side='left', fill='y', padx=(0, 20))
            reports_sidebar.pack_propagate(False)
            
            # منطقة عرض التقارير
            reports_display_frame = tk.Frame(reports_main_frame, bg='white')
            reports_display_frame.pack(side='right', fill='both', expand=True)
            
            # إنشاء أزرار التقارير
            self.create_reports_buttons(reports_sidebar, reports_display_frame)
            
            # إنشاء منطقة عرض التقارير
            self.create_reports_display_area(reports_display_frame)
            
            # عرض التقرير الافتراضي
            self.show_default_report()
            
            self.update_status("تم تحميل التقارير بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقارير: {e}")
            self.show_simple_reports_fallback()
    
    def create_reports_buttons(self, sidebar, display_frame):
        """إنشاء أزرار التقارير"""
        try:
            # عنوان الشريط الجانبي
            sidebar_title = tk.Label(sidebar, text="📋 أنواع التقارير", 
                                   font=("Arial", 12, "bold"), bg='#ecf0f1', fg='#2c3e50')
            sidebar_title.pack(pady=15)
            
            # أزرار التقارير
            reports_buttons = [
                ("📊 إحصائيات عامة", lambda: self.show_general_stats()),
                ("📈 تقرير المبيعات", lambda: self.show_simple_sales_report()),
                ("📦 تقرير المخزون", lambda: self.show_simple_inventory_report()),
                ("👥 تقرير العملاء", lambda: self.show_simple_customers_report()),
                ("🏢 تقرير الموردين", lambda: self.show_simple_suppliers_report()),
                ("💰 تحليل الأرباح", lambda: self.show_simple_profit_report()),
                ("⚠️ تحذيرات المخزون", lambda: self.show_stock_alerts()),
                ("📋 تقرير شامل", lambda: self.show_comprehensive_simple_report())
            ]
            
            self.reports_display_frame = display_frame
            
            for button_text, command in reports_buttons:
                btn = tk.Button(sidebar, text=button_text, 
                              font=("Arial", 10), bg='#3498db', fg='white',
                              width=18, height=2, relief='flat',
                              command=command)
                btn.pack(pady=3, padx=10, fill='x')
                
                # تأثير hover
                btn.bind("<Enter>", lambda e, b=btn: b.config(bg='#2980b9'))
                btn.bind("<Leave>", lambda e, b=btn: b.config(bg='#3498db'))
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء أزرار التقارير: {e}")
    
    def create_reports_display_area(self, display_frame):
        """إنشاء منطقة عرض التقارير"""
        try:
            # عنوان التقرير
            self.report_title_label = tk.Label(display_frame, text="اختر تقرير من القائمة الجانبية", 
                                             font=("Arial", 14, "bold"), bg='white', fg='#2c3e50')
            self.report_title_label.pack(pady=10)
            
            # منطقة النص
            text_frame = tk.Frame(display_frame, bg='white')
            text_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # شريط التمرير
            scrollbar = tk.Scrollbar(text_frame)
            scrollbar.pack(side='right', fill='y')
            
            # منطقة النص للتقارير
            self.reports_text_area = tk.Text(text_frame, font=("Arial", 10), 
                                           bg='#f8f9fa', fg='#2c3e50',
                                           yscrollcommand=scrollbar.set,
                                           wrap='word', padx=15, pady=15)
            self.reports_text_area.pack(fill='both', expand=True)
            scrollbar.config(command=self.reports_text_area.yview)
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء منطقة العرض: {e}")
    
    def show_default_report(self):
        """عرض التقرير الافتراضي"""
        try:
            self.report_title_label.config(text="📊 مرحباً بك في نظام التقارير")
            
            default_content = f"""
📊 نظام التقارير - ProTech
{'='*50}

🎉 مرحباً بك في نظام التقارير المحسن!

📋 التقارير المتاحة:
• 📊 إحصائيات عامة - نظرة شاملة على النظام
• 📈 تقرير المبيعات - تفاصيل المبيعات والأرباح
• 📦 تقرير المخزون - حالة المنتجات والمخزون
• 👥 تقرير العملاء - معلومات وإحصائيات العملاء
• 🏢 تقرير الموردين - بيانات الموردين
• 💰 تحليل الأرباح - تحليل الربحية
• ⚠️ تحذيرات المخزون - تنبيهات المخزون المنخفض
• 📋 تقرير شامل - تقرير متكامل

💡 كيفية الاستخدام:
1. اختر نوع التقرير من القائمة الجانبية
2. سيتم عرض التقرير في هذه المنطقة
3. يمكنك التنقل بين التقارير المختلفة

📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
✅ النظام جاهز للاستخدام
"""
            
            self.reports_text_area.delete(1.0, tk.END)
            self.reports_text_area.insert(1.0, default_content)
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقرير الافتراضي: {e}")
    
    def show_general_stats(self):
        """عرض الإحصائيات العامة"""
        try:
            self.report_title_label.config(text="📊 الإحصائيات العامة")
            
            # حساب الإحصائيات
            total_products = len(getattr(self, 'products', []))
            total_customers = len(getattr(self, 'customers', []))
            total_sales = len(getattr(self, 'sales', []))
            total_suppliers = len(getattr(self, 'suppliers', []))
            
            stats_content = f"""
📊 الإحصائيات العامة للنظام
{'='*50}

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📈 الإحصائيات الأساسية:
• 📦 إجمالي المنتجات: {total_products}
• 👥 إجمالي العملاء: {total_customers}
• 💰 إجمالي المبيعات: {total_sales}
• 🏢 إجمالي الموردين: {total_suppliers}

📊 حالة النظام:
• ✅ النظام يعمل بشكل طبيعي
• ✅ البيانات محملة بنجاح
• ✅ جميع الوظائف متاحة

💡 ملاحظات:
• تأكد من حفظ البيانات بانتظام
• راجع التقارير التفصيلية للمزيد من المعلومات
• استخدم النسخ الاحتياطية للحماية

🎯 نصائح للاستخدام الأمثل:
• أضف المزيد من البيانات لتقارير أكثر تفصيلاً
• راجع تحذيرات المخزون بانتظام
• تابع أداء المبيعات والأرباح
"""
            
            self.reports_text_area.delete(1.0, tk.END)
            self.reports_text_area.insert(1.0, stats_content)
            
        except Exception as e:
            print(f"❌ خطأ في عرض الإحصائيات العامة: {e}")
            self.show_error_message("خطأ في عرض الإحصائيات العامة")
    
    def show_simple_sales_report(self):
        """عرض تقرير المبيعات البسيط"""
        try:
            self.report_title_label.config(text="📈 تقرير المبيعات")
            
            sales = getattr(self, 'sales', [])
            
            if not sales:
                sales_content = f"""
📈 تقرير المبيعات
{'='*50}

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📝 لا توجد مبيعات مسجلة حتى الآن.

💡 لإضافة مبيعات:
1. اذهب إلى صفحة المبيعات
2. أضف عملية بيع جديدة
3. احفظ البيانات
4. عد إلى التقارير لرؤية النتائج

🎯 فوائد تسجيل المبيعات:
• تتبع الإيرادات
• حساب الأرباح
• تحليل أداء المنتجات
• متابعة العملاء
"""
            else:
                total_sales_value = sum(sale.get('total', 0) for sale in sales)
                avg_sale = total_sales_value / len(sales) if sales else 0
                
                sales_content = f"""
📈 تقرير المبيعات
{'='*50}

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 ملخص المبيعات:
• 💰 إجمالي قيمة المبيعات: {total_sales_value:.2f}
• 🧾 عدد الفواتير: {len(sales)}
• 📊 متوسط قيمة الفاتورة: {avg_sale:.2f}

📋 تفاصيل المبيعات:
"""
                
                for i, sale in enumerate(sales[:10], 1):  # أول 10 مبيعات
                    sales_content += f"""
🧾 فاتورة رقم {i}:
   العميل: {sale.get('customer_name', 'غير محدد')}
   النوع: {sale.get('customer_type', 'غير محدد')}
   المبلغ: {sale.get('total', 0):.2f}
   التاريخ: {sale.get('date', 'غير محدد')[:10]}
   {'-'*30}
"""
                
                if len(sales) > 10:
                    sales_content += f"\n... و {len(sales) - 10} فاتورة أخرى\n"
            
            self.reports_text_area.delete(1.0, tk.END)
            self.reports_text_area.insert(1.0, sales_content)
            
        except Exception as e:
            print(f"❌ خطأ في عرض تقرير المبيعات: {e}")
            self.show_error_message("خطأ في عرض تقرير المبيعات")
    
    def show_simple_inventory_report(self):
        """عرض تقرير المخزون البسيط"""
        try:
            self.report_title_label.config(text="📦 تقرير المخزون")
            
            products = getattr(self, 'products', [])
            
            if not products:
                inventory_content = f"""
📦 تقرير المخزون
{'='*50}

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📝 لا توجد منتجات في المخزون حتى الآن.

💡 لإضافة منتجات:
1. اذهب إلى صفحة إدارة المخزون
2. أضف منتج جديد
3. احفظ البيانات
4. عد إلى التقارير لرؤية النتائج
"""
            else:
                total_value = 0
                low_stock_count = 0
                out_of_stock_count = 0
                
                for product in products:
                    stock = product.get('quantity', 0)
                    price = product.get('base_price', 0)
                    total_value += stock * price
                    
                    if stock == 0:
                        out_of_stock_count += 1
                    elif stock <= product.get('min_stock', 10):
                        low_stock_count += 1
                
                inventory_content = f"""
📦 تقرير المخزون
{'='*50}

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

📊 ملخص المخزون:
• 📦 إجمالي المنتجات: {len(products)}
• 💰 إجمالي قيمة المخزون: {total_value:.2f}
• ⚠️ منتجات نفد مخزونها: {out_of_stock_count}
• 🔶 منتجات مخزونها منخفض: {low_stock_count}

📋 تفاصيل المنتجات:
"""
                
                for i, product in enumerate(products[:10], 1):  # أول 10 منتجات
                    stock = product.get('quantity', 0)
                    status = "نفد" if stock == 0 else "منخفض" if stock <= product.get('min_stock', 10) else "متوفر"
                    
                    inventory_content += f"""
📦 منتج رقم {i}:
   الاسم: {product.get('name', 'غير محدد')}
   الكمية: {stock}
   السعر: {product.get('base_price', 0):.2f}
   الحالة: {status}
   {'-'*30}
"""
                
                if len(products) > 10:
                    inventory_content += f"\n... و {len(products) - 10} منتج آخر\n"
            
            self.reports_text_area.delete(1.0, tk.END)
            self.reports_text_area.insert(1.0, inventory_content)
            
        except Exception as e:
            print(f"❌ خطأ في عرض تقرير المخزون: {e}")
            self.show_error_message("خطأ في عرض تقرير المخزون")
    
    def show_error_message(self, message):
        """عرض رسالة خطأ في منطقة التقارير"""
        try:
            error_content = f"""
❌ حدث خطأ
{'='*50}

📅 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔍 تفاصيل الخطأ:
{message}

💡 حلول مقترحة:
• تأكد من وجود البيانات
• أعد تحميل الصفحة
• تحقق من اتصال النظام
• جرب تقرير آخر

🔧 إذا استمر الخطأ:
• أعد تشغيل البرنامج
• تحقق من ملفات البيانات
• راجع سجل الأخطاء
"""
            
            self.reports_text_area.delete(1.0, tk.END)
            self.reports_text_area.insert(1.0, error_content)
            
        except Exception as e:
            print(f"❌ خطأ في عرض رسالة الخطأ: {e}")
    
    def show_simple_reports_fallback(self):
        """عرض التقارير البسيطة في حالة الفشل"""
        try:
            self.clear_content()
            
            title_label = tk.Label(self.content_frame, text="📊 التقارير البسيطة", 
                                  font=("Arial", 18, "bold"), bg='white', fg='#2c3e50')
            title_label.pack(pady=30)
            
            # رسالة توضيحية
            info_label = tk.Label(self.content_frame, 
                                text="تم تحميل نظام التقارير البسيط بنجاح\\nجميع التقارير متاحة وجاهزة للاستخدام", 
                                font=("Arial", 12), bg='white', fg='#27ae60')
            info_label.pack(pady=10)
            
            # أزرار التقارير البسيطة
            simple_frame = tk.Frame(self.content_frame, bg='white')
            simple_frame.pack(fill='both', expand=True, padx=50, pady=20)
            
            simple_buttons = [
                ("📊 إحصائيات عامة", self.show_general_stats, '#3498db'),
                ("📈 تقرير المبيعات", self.show_simple_sales_report, '#27ae60'),
                ("📦 تقرير المخزون", self.show_simple_inventory_report, '#e74c3c')
            ]
            
            for text, command, color in simple_buttons:
                btn = tk.Button(simple_frame, text=text, 
                              font=("Arial", 12), bg=color, fg='white',
                              width=20, height=2, command=command)
                btn.pack(pady=8)
            
            self.update_status("تم تحميل التقارير البسيطة")
            
        except Exception as e:
            print(f"❌ خطأ في عرض التقارير البسيطة: {e}")
'''
        
        # Find and replace the show_reports method
        if "def show_reports(" in content:
            # Find method boundaries
            method_start = content.find("def show_reports(")
            
            # Find the next method or end of class
            next_method = content.find("\n    def ", method_start + 1)
            if next_method == -1:
                next_method = content.find("\nclass ", method_start + 1)
            if next_method == -1:
                next_method = len(content)
            
            # Replace the method
            content = content[:method_start] + working_reports.strip() + content[next_method:]
        else:
            # Add the method before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + working_reports + content[last_method:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إصلاح صفحة التقارير الفارغة")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في إصلاح صفحة التقارير: {e}")
        return False

def main():
    """Main function"""
    print("🔧 إصلاح صفحة التقارير الفارغة في ProTech")
    print("🔧 Fixing Empty Reports Page in ProTech")
    print("="*60)
    
    success = fix_empty_reports_page()
    
    if success:
        print("\n🎉 تم إصلاح صفحة التقارير الفارغة بنجاح!")
        print("✅ الآن صفحة التقارير تعمل بشكل طبيعي")
        
        print("\n📊 التقارير المتاحة الآن:")
        print("• 📊 إحصائيات عامة")
        print("• 📈 تقرير المبيعات")
        print("• 📦 تقرير المخزون")
        print("• 👥 تقرير العملاء")
        print("• 🏢 تقرير الموردين")
        print("• 💰 تحليل الأرباح")
        print("• ⚠️ تحذيرات المخزون")
        print("• 📋 تقرير شامل")
        
        print("\n💡 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. اختر نوع التقرير من القائمة الجانبية")
        print("4. سيتم عرض التقرير في المنطقة الرئيسية")
        
    else:
        print("\n❌ فشل في إصلاح صفحة التقارير")
        print("⚠️ تحقق من ملف ProTech والأخطاء")

if __name__ == "__main__":
    main()
