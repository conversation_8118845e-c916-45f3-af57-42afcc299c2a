#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Move ProTech Files to Desktop
نقل ملفات ProTech إلى سطح المكتب

Move essential ProTech files from accounting program folder to desktop
نقل ملفات ProTech الأساسية من مجلد accounting program إلى سطح المكتب
"""

import os
import shutil
from datetime import datetime

def move_files_to_desktop():
    """Move essential ProTech files to desktop"""
    try:
        print("📁 نقل ملفات ProTech إلى سطح المكتب")
        print("📁 Moving ProTech Files to Desktop")
        print("="*50)
        
        # Define paths
        source_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        desktop_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop"
        
        # Essential files to move
        essential_files = [
            "protech_simple_working.py",
            "protech_simple_data.json",
            "protech_config.json",
            "protech_settings.json",
            "ProTech_Shortcut.bat"
        ]
        
        # Optional files to move
        optional_files = [
            "protech_minimal_working.py",
            "protech_calculator_launcher.py",
            "protech_database.db"
        ]
        
        moved_files = []
        failed_files = []
        
        print("\\n📋 نقل الملفات الأساسية:")
        
        # Move essential files
        for file in essential_files:
            source_path = os.path.join(source_dir, file)
            dest_path = os.path.join(desktop_dir, file)
            
            try:
                if os.path.exists(source_path):
                    # Create backup if file exists on desktop
                    if os.path.exists(dest_path):
                        backup_name = f"{file}.desktop_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        backup_path = os.path.join(desktop_dir, backup_name)
                        shutil.copy2(dest_path, backup_path)
                        print(f"💾 نسخة احتياطية: {backup_name}")
                    
                    # Move file
                    shutil.copy2(source_path, dest_path)
                    moved_files.append(file)
                    
                    # Get file size
                    size = os.path.getsize(dest_path) / 1024
                    print(f"✅ {file}: {size:.1f} KB")
                else:
                    print(f"⚠️ {file}: غير موجود في المصدر")
                    failed_files.append(file)
                    
            except Exception as e:
                print(f"❌ {file}: فشل النقل - {e}")
                failed_files.append(file)
        
        print("\\n📋 نقل الملفات الاختيارية:")
        
        # Move optional files
        for file in optional_files:
            source_path = os.path.join(source_dir, file)
            dest_path = os.path.join(desktop_dir, file)
            
            try:
                if os.path.exists(source_path):
                    if os.path.exists(dest_path):
                        backup_name = f"{file}.desktop_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        backup_path = os.path.join(desktop_dir, backup_name)
                        shutil.copy2(dest_path, backup_path)
                    
                    shutil.copy2(source_path, dest_path)
                    moved_files.append(file)
                    
                    size = os.path.getsize(dest_path) / 1024
                    print(f"✅ {file}: {size:.1f} KB")
                else:
                    print(f"⚠️ {file}: غير موجود")
                    
            except Exception as e:
                print(f"❌ {file}: فشل النقل - {e}")
        
        # Create desktop launcher
        print("\\n🚀 إنشاء مشغل سطح المكتب:")
        
        launcher_content = f'''@echo off
cd /d "C:\\Users\\<USER>\\OneDrive\\Desktop"
echo 🚀 تشغيل ProTech من سطح المكتب...
echo 🚀 Starting ProTech from Desktop...
python protech_simple_working.py
pause
'''
        
        launcher_path = os.path.join(desktop_dir, "ProTech_Desktop_Launcher.bat")
        
        try:
            with open(launcher_path, 'w', encoding='utf-8') as f:
                f.write(launcher_content)
            print("✅ ProTech_Desktop_Launcher.bat")
        except Exception as e:
            print(f"❌ فشل في إنشاء المشغل: {e}")
        
        # Create quick access folder
        print("\\n📁 إنشاء مجلد الوصول السريع:")
        
        quick_access_dir = os.path.join(desktop_dir, "ProTech_Quick_Access")
        
        try:
            if not os.path.exists(quick_access_dir):
                os.makedirs(quick_access_dir)
            
            # Copy important backup files
            backup_files = [
                "protech_backup.json",
                "protech_simple_working.py.backup_20250619_105038"
            ]
            
            for backup_file in backup_files:
                source_backup = os.path.join(source_dir, backup_file)
                if os.path.exists(source_backup):
                    dest_backup = os.path.join(quick_access_dir, backup_file)
                    shutil.copy2(source_backup, dest_backup)
                    print(f"✅ نسخ احتياطي: {backup_file}")
            
            print(f"✅ مجلد الوصول السريع: ProTech_Quick_Access")
            
        except Exception as e:
            print(f"❌ فشل في إنشاء مجلد الوصول السريع: {e}")
        
        # Summary
        print("\\n" + "="*50)
        print("📊 ملخص النقل:")
        print(f"✅ ملفات منقولة: {len(moved_files)}")
        print(f"❌ ملفات فاشلة: {len(failed_files)}")
        
        if moved_files:
            print("\\n📋 الملفات المنقولة:")
            for file in moved_files:
                print(f"  • {file}")
        
        if failed_files:
            print("\\n⚠️ الملفات الفاشلة:")
            for file in failed_files:
                print(f"  • {file}")
        
        print("\\n🎯 الحالة النهائية:")
        if len(moved_files) >= 3:  # At least 3 essential files
            print("🟢 نقل ناجح - ProTech متاح على سطح المكتب")
            print("🟢 Successful move - ProTech available on desktop")
            
            print("\\n💡 طرق التشغيل:")
            print("1. انقر مزدوج على ProTech_Desktop_Launcher.bat")
            print("2. أو افتح Command Prompt في سطح المكتب وشغل:")
            print("   python protech_simple_working.py")
            
        else:
            print("🟡 نقل جزئي - تحقق من الملفات المنقولة")
            print("🟡 Partial move - check moved files")
        
        print("="*50)
        
        return len(moved_files) >= 3
        
    except Exception as e:
        print(f"❌ خطأ في نقل الملفات: {e}")
        return False

def verify_desktop_files():
    """Verify files on desktop"""
    try:
        print("\\n🔍 التحقق من الملفات على سطح المكتب:")
        
        desktop_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop"
        
        files_to_check = [
            "protech_simple_working.py",
            "protech_simple_data.json",
            "ProTech_Desktop_Launcher.bat"
        ]
        
        for file in files_to_check:
            file_path = os.path.join(desktop_dir, file)
            if os.path.exists(file_path):
                size = os.path.getsize(file_path) / 1024
                mod_time = datetime.fromtimestamp(os.path.getmtime(file_path))
                print(f"✅ {file}: {size:.1f} KB - {mod_time.strftime('%H:%M:%S')}")
            else:
                print(f"❌ {file}: غير موجود")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {e}")
        return False

def main():
    """Main function"""
    success = move_files_to_desktop()
    
    if success:
        verify_desktop_files()
        print("\\n🎉 تم نقل ملفات ProTech إلى سطح المكتب بنجاح!")
        print("🎉 ProTech files moved to desktop successfully!")
    else:
        print("\\n❌ فشل في نقل ملفات ProTech")
        print("❌ Failed to move ProTech files")

if __name__ == "__main__":
    main()
