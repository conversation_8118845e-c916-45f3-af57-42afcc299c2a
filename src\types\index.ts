// Database types (matching Prisma schema)
export type UserRole = 'ADMIN' | 'MANAGER' | 'USER';
export type CustomerCategory = 'RETAIL' | 'WHOLESALE' | 'DISTRIBUTOR' | 'VIP';
export type InvoiceStatus = 'DRAFT' | 'SENT' | 'PAID' | 'OVERDUE' | 'CANCELLED' | 'REFUNDED';
export type PurchaseStatus = 'DRAFT' | 'SENT' | 'CONFIRMED' | 'RECEIVED' | 'CANCELLED';
export type PaymentMethod = 'CASH' | 'CARD' | 'BANK_TRANSFER' | 'CHECK' | 'CREDIT';
export type MovementType = 'IN' | 'OUT' | 'ADJUSTMENT' | 'TRANSFER';

// User types
export interface User {
  id: string;
  email: string;
  username: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

// Customer types
export interface Customer {
  id: string;
  code: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  creditLimit: number;
  currentBalance: number;
  category: CustomerCategory;
  priceLevel: number;
  isActive: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Product types
export interface Product {
  id: string;
  code: string;
  name: string;
  description?: string;
  barcode?: string;
  categoryId?: string;
  supplierId?: string;
  unit: string;
  costPrice: number;
  basePrice: number;
  priceLevel1: number;
  priceLevel2: number;
  priceLevel3: number;
  priceLevel4: number;
  currentStock: number;
  minStock: number;
  maxStock: number;
  location?: string;
  isActive: boolean;
  hasVariants: boolean;
  trackInventory: boolean;
  allowNegative: boolean;
  weight?: number;
  dimensions?: string;
  imageUrl?: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
  category?: Category;
  supplier?: Supplier;
}

// Category types
export interface Category {
  id: string;
  name: string;
  description?: string;
  parentId?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  parent?: Category;
  children?: Category[];
}

// Supplier types
export interface Supplier {
  id: string;
  code: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  paymentTerms?: string;
  isActive: boolean;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Invoice types
export interface Invoice {
  id: string;
  number: string;
  customerId: string;
  userId: string;
  date: Date;
  dueDate?: Date;
  status: InvoiceStatus;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  notes?: string;
  terms?: string;
  createdAt: Date;
  updatedAt: Date;
  customer?: Customer;
  createdBy?: User;
  items?: InvoiceItem[];
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  productId: string;
  description?: string;
  quantity: number;
  unitPrice: number;
  discount: number;
  taxRate: number;
  lineTotal: number;
  createdAt: Date;
  product?: Product;
}

// Purchase types
export interface Purchase {
  id: string;
  number: string;
  supplierId: string;
  userId: string;
  date: Date;
  expectedDate?: Date;
  receivedDate?: Date;
  status: PurchaseStatus;
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paidAmount: number;
  balanceAmount: number;
  notes?: string;
  terms?: string;
  createdAt: Date;
  updatedAt: Date;
  supplier?: Supplier;
  createdBy?: User;
  items?: PurchaseItem[];
}

export interface PurchaseItem {
  id: string;
  purchaseId: string;
  productId: string;
  description?: string;
  quantity: number;
  receivedQty: number;
  unitCost: number;
  discount: number;
  taxRate: number;
  lineTotal: number;
  createdAt: Date;
  product?: Product;
}

// Payment types
export interface Payment {
  id: string;
  customerId?: string;
  invoiceId?: string;
  amount: number;
  method: PaymentMethod;
  reference?: string;
  notes?: string;
  date: Date;
  createdAt: Date;
  customer?: Customer;
  invoice?: Invoice;
}

// Inventory types
export interface InventoryMovement {
  id: string;
  productId: string;
  userId: string;
  type: MovementType;
  quantity: number;
  unitCost?: number;
  totalCost?: number;
  reason?: string;
  referenceType?: string;
  referenceId?: string;
  notes?: string;
  createdAt: Date;
  product?: Product;
  user?: User;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T = any> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Form types
export interface ProductFormData {
  code: string;
  name: string;
  description?: string;
  barcode?: string;
  categoryId?: string;
  supplierId?: string;
  unit: string;
  costPrice: number;
  basePrice: number;
  currentStock: number;
  minStock: number;
  maxStock: number;
  location?: string;
  trackInventory: boolean;
  allowNegative: boolean;
  weight?: number;
  dimensions?: string;
  notes?: string;
}

export interface CustomerFormData {
  code: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  country?: string;
  taxNumber?: string;
  creditLimit: number;
  category: CustomerCategory;
  priceLevel: number;
  notes?: string;
}

// Barcode types
export interface BarcodeData {
  code: string;
  format: string;
  text: string;
}

export interface BarcodeConfig {
  format: 'CODE128' | 'EAN13' | 'UPCA';
  width: number;
  height: number;
  displayValue: boolean;
  fontSize: number;
  textAlign: 'left' | 'center' | 'right';
  textPosition: 'bottom' | 'top';
  textMargin: number;
  fontOptions: string;
  font: string;
  fontColor: string;
  backgroundColor: string;
  lineColor: string;
  margin: number;
  marginTop: number;
  marginBottom: number;
  marginLeft: number;
  marginRight: number;
}

// Report types
export interface SalesReport {
  period: string;
  totalSales: number;
  totalInvoices: number;
  averageOrderValue: number;
  topProducts: Array<{
    productId: string;
    productName: string;
    quantity: number;
    revenue: number;
  }>;
  topCustomers: Array<{
    customerId: string;
    customerName: string;
    totalSpent: number;
    orderCount: number;
  }>;
}

export interface InventoryReport {
  totalProducts: number;
  totalValue: number;
  lowStockItems: number;
  outOfStockItems: number;
  topMovingProducts: Array<{
    productId: string;
    productName: string;
    movementCount: number;
    currentStock: number;
  }>;
}

// Settings types
export interface AppSettings {
  companyName: string;
  companyAddress: string;
  companyPhone: string;
  companyEmail: string;
  taxNumber: string;
  defaultCurrency: string;
  defaultTaxRate: number;
  defaultLanguage: string;
  barcodePrefix: string;
  invoicePrefix: string;
  purchasePrefix: string;
  autoGenerateBarcodes: boolean;
  trackInventory: boolean;
  allowNegativeStock: boolean;
  lowStockThreshold: number;
}
