#!/usr/bin/env python3
"""
ProTech Calculator Icon Launcher
مشغل ProTech مع أيقونة الآلة الحاسبة
"""

import tkinter as tk
import subprocess
import sys
import os

def set_calculator_icon(root):
    """Set calculator icon for window"""
    try:
        # Try to set calculator icon
        icon_paths = [
            "C:/Windows/System32/calc.exe",
            "C:/Windows/System32/shell32.dll",
        ]
        
        for icon_path in icon_paths:
            if os.path.exists(icon_path):
                try:
                    root.iconbitmap(icon_path)
                    return True
                except:
                    continue
        
        # Fallback: Add calculator emoji to title
        current_title = root.title()
        if not current_title.startswith("🧮"):
            root.title(f"🧮 {current_title}")
        
        return True
        
    except Exception as e:
        print(f"Icon error: {e}")
        return False

def launch_protech():
    """Launch ProTech with calculator icon"""
    try:
        # Create a temporary window to test icon
        test_root = tk.Tk()
        test_root.withdraw()
        
        # Set the icon
        set_calculator_icon(test_root)
        
        # Close test window
        test_root.destroy()
        
        # Launch the actual ProTech
        protech_file = "protech_simple_working.py"
        if os.path.exists(protech_file):
            subprocess.run([sys.executable, protech_file])
        else:
            print("ProTech file not found!")
            
    except Exception as e:
        print(f"Launch error: {e}")

if __name__ == "__main__":
    launch_protech()
