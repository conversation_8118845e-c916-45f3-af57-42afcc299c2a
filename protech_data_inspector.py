#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ProTech Data Inspector
فاحص بيانات ProTech

Comprehensive data and save system inspection for ProTech
فحص شامل لنظام البيانات والحفظ في ProTech
"""

import os
import json
import shutil
from datetime import datetime
import glob

class ProTechDataInspector:
    """Data inspector for ProTech system"""
    
    def __init__(self):
        self.data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        self.main_data_file = "protech_simple_data.json"
        self.inspection_results = {
            'data_integrity': {},
            'backup_status': {},
            'save_system': {},
            'file_structure': {},
            'recommendations': []
        }
    
    def inspect_main_data_file(self):
        """Inspect main data file"""
        try:
            print("📊 فحص ملف البيانات الرئيسي...")
            
            data_path = os.path.join(self.data_dir, self.main_data_file)
            
            if not os.path.exists(data_path):
                self.inspection_results['data_integrity']['main_file'] = {
                    'status': 'missing',
                    'message': 'ملف البيانات الرئيسي مفقود'
                }
                print("❌ ملف البيانات الرئيسي مفقود!")
                return False
            
            # Check file size
            file_size = os.path.getsize(data_path)
            
            # Check if file is readable
            try:
                with open(data_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # Validate data structure
                required_keys = ['suppliers', 'products', 'customers', 'sales']
                missing_keys = []
                
                for key in required_keys:
                    if key not in data:
                        missing_keys.append(key)
                
                # Check data types
                data_types_ok = True
                for key in required_keys:
                    if key in data and not isinstance(data[key], list):
                        data_types_ok = False
                        break
                
                # Count records
                record_counts = {}
                for key in required_keys:
                    if key in data:
                        record_counts[key] = len(data[key])
                
                self.inspection_results['data_integrity']['main_file'] = {
                    'status': 'ok' if not missing_keys and data_types_ok else 'issues',
                    'file_size': file_size,
                    'missing_keys': missing_keys,
                    'data_types_ok': data_types_ok,
                    'record_counts': record_counts,
                    'last_updated': data.get('last_updated', 'غير محدد')
                }
                
                print(f"✅ ملف البيانات الرئيسي: {file_size} بايت")
                print(f"📊 عدد السجلات: {record_counts}")
                
                if missing_keys:
                    print(f"⚠️ مفاتيح مفقودة: {missing_keys}")
                
                return True
                
            except json.JSONDecodeError as e:
                self.inspection_results['data_integrity']['main_file'] = {
                    'status': 'corrupted',
                    'error': str(e),
                    'message': 'ملف البيانات تالف'
                }
                print(f"❌ ملف البيانات تالف: {e}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص ملف البيانات: {e}")
            return False
    
    def inspect_backup_system(self):
        """Inspect backup system"""
        try:
            print("💾 فحص نظام النسخ الاحتياطية...")
            
            # Find all backup files
            backup_patterns = [
                "protech_backup_*.json",
                "protech_emergency_save_*.json",
                "protech_simple_data.json.migrated_*",
                "protech_*.json"
            ]
            
            all_backups = []
            for pattern in backup_patterns:
                backup_path = os.path.join(self.data_dir, pattern)
                all_backups.extend(glob.glob(backup_path))
            
            # Analyze backups
            backup_analysis = {
                'total_backups': len(all_backups),
                'emergency_saves': 0,
                'regular_backups': 0,
                'migration_backups': 0,
                'latest_backup': None,
                'oldest_backup': None,
                'total_size': 0
            }
            
            backup_times = []
            
            for backup_file in all_backups:
                file_size = os.path.getsize(backup_file)
                backup_analysis['total_size'] += file_size
                
                # Get file modification time
                mod_time = os.path.getmtime(backup_file)
                backup_times.append((backup_file, mod_time))
                
                # Categorize backup type
                filename = os.path.basename(backup_file)
                if 'emergency_save' in filename:
                    backup_analysis['emergency_saves'] += 1
                elif 'migrated' in filename:
                    backup_analysis['migration_backups'] += 1
                else:
                    backup_analysis['regular_backups'] += 1
            
            # Find latest and oldest backups
            if backup_times:
                backup_times.sort(key=lambda x: x[1])
                backup_analysis['oldest_backup'] = {
                    'file': os.path.basename(backup_times[0][0]),
                    'date': datetime.fromtimestamp(backup_times[0][1]).strftime('%Y-%m-%d %H:%M:%S')
                }
                backup_analysis['latest_backup'] = {
                    'file': os.path.basename(backup_times[-1][0]),
                    'date': datetime.fromtimestamp(backup_times[-1][1]).strftime('%Y-%m-%d %H:%M:%S')
                }
            
            self.inspection_results['backup_status'] = backup_analysis
            
            print(f"💾 إجمالي النسخ الاحتياطية: {backup_analysis['total_backups']}")
            print(f"🚨 حفظ طوارئ: {backup_analysis['emergency_saves']}")
            print(f"📁 نسخ عادية: {backup_analysis['regular_backups']}")
            print(f"📦 حجم إجمالي: {backup_analysis['total_size']/1024:.1f} KB")
            
            if backup_analysis['latest_backup']:
                print(f"🕐 آخر نسخة: {backup_analysis['latest_backup']['date']}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص النسخ الاحتياطية: {e}")
            return False
    
    def inspect_save_system(self):
        """Inspect save system functionality"""
        try:
            print("💾 فحص نظام الحفظ...")
            
            # Check if main data file is writable
            data_path = os.path.join(self.data_dir, self.main_data_file)
            
            save_system_status = {
                'main_file_writable': False,
                'temp_file_creation': False,
                'backup_creation': False,
                'auto_save_functional': False
            }
            
            # Test 1: Check if main file is writable
            try:
                if os.path.exists(data_path):
                    # Try to open in append mode (safe test)
                    with open(data_path, 'a', encoding='utf-8') as f:
                        pass
                    save_system_status['main_file_writable'] = True
                    print("✅ ملف البيانات قابل للكتابة")
                else:
                    print("⚠️ ملف البيانات غير موجود")
            except PermissionError:
                print("❌ ملف البيانات غير قابل للكتابة (مشكلة صلاحيات)")
            except Exception as e:
                print(f"❌ خطأ في اختبار الكتابة: {e}")
            
            # Test 2: Test temp file creation
            try:
                temp_path = os.path.join(self.data_dir, "test_temp_file.tmp")
                with open(temp_path, 'w', encoding='utf-8') as f:
                    f.write("test")
                
                if os.path.exists(temp_path):
                    os.remove(temp_path)
                    save_system_status['temp_file_creation'] = True
                    print("✅ إنشاء ملفات مؤقتة يعمل")
            except Exception as e:
                print(f"❌ خطأ في إنشاء ملف مؤقت: {e}")
            
            # Test 3: Test backup creation
            try:
                if os.path.exists(data_path):
                    test_backup_path = os.path.join(self.data_dir, "test_backup.json")
                    shutil.copy2(data_path, test_backup_path)
                    
                    if os.path.exists(test_backup_path):
                        os.remove(test_backup_path)
                        save_system_status['backup_creation'] = True
                        print("✅ إنشاء نسخ احتياطية يعمل")
            except Exception as e:
                print(f"❌ خطأ في إنشاء نسخة احتياطية: {e}")
            
            # Test 4: Check auto-save indicators
            auto_save_indicators = [
                os.path.exists(os.path.join(self.data_dir, "protech_simple_data.json.tmp")),
                len(glob.glob(os.path.join(self.data_dir, "protech_emergency_save_*.json"))) > 0
            ]
            
            if any(auto_save_indicators):
                save_system_status['auto_save_functional'] = True
                print("✅ نظام الحفظ التلقائي نشط")
            else:
                print("⚠️ لا توجد مؤشرات على نشاط الحفظ التلقائي")
            
            self.inspection_results['save_system'] = save_system_status
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص نظام الحفظ: {e}")
            return False
    
    def inspect_file_structure(self):
        """Inspect file structure and organization"""
        try:
            print("📁 فحص هيكل الملفات...")
            
            # Check directory structure
            expected_dirs = [
                'data', 'backups', 'logs', 'reports', 'temp',
                'archives', 'exports'
            ]
            
            existing_dirs = []
            missing_dirs = []
            
            for dir_name in expected_dirs:
                dir_path = os.path.join(self.data_dir, dir_name)
                if os.path.exists(dir_path) and os.path.isdir(dir_path):
                    existing_dirs.append(dir_name)
                else:
                    missing_dirs.append(dir_name)
            
            # Count files by type
            file_counts = {
                'python_files': len(glob.glob(os.path.join(self.data_dir, "*.py"))),
                'json_files': len(glob.glob(os.path.join(self.data_dir, "*.json"))),
                'log_files': len(glob.glob(os.path.join(self.data_dir, "*.log"))),
                'backup_files': len(glob.glob(os.path.join(self.data_dir, "*.backup"))),
                'bat_files': len(glob.glob(os.path.join(self.data_dir, "*.bat"))),
                'total_files': len([f for f in os.listdir(self.data_dir) if os.path.isfile(os.path.join(self.data_dir, f))])
            }
            
            # Calculate total directory size
            total_size = 0
            for root, dirs, files in os.walk(self.data_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    try:
                        total_size += os.path.getsize(file_path)
                    except:
                        pass
            
            file_structure = {
                'existing_dirs': existing_dirs,
                'missing_dirs': missing_dirs,
                'file_counts': file_counts,
                'total_size_mb': total_size / (1024 * 1024)
            }
            
            self.inspection_results['file_structure'] = file_structure
            
            print(f"📁 مجلدات موجودة: {len(existing_dirs)}/{len(expected_dirs)}")
            print(f"📄 إجمالي الملفات: {file_counts['total_files']}")
            print(f"💾 حجم المجلد: {file_structure['total_size_mb']:.1f} MB")
            
            if missing_dirs:
                print(f"⚠️ مجلدات مفقودة: {missing_dirs}")
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص هيكل الملفات: {e}")
            return False
    
    def generate_recommendations(self):
        """Generate recommendations based on inspection"""
        try:
            print("💡 إنشاء التوصيات...")
            
            recommendations = []
            
            # Data integrity recommendations
            if self.inspection_results['data_integrity'].get('main_file', {}).get('status') == 'missing':
                recommendations.append({
                    'priority': 'high',
                    'category': 'data_integrity',
                    'title': 'إنشاء ملف بيانات جديد',
                    'description': 'ملف البيانات الرئيسي مفقود ويحتاج إنشاء'
                })
            
            # Backup recommendations
            backup_count = self.inspection_results['backup_status'].get('total_backups', 0)
            if backup_count < 3:
                recommendations.append({
                    'priority': 'medium',
                    'category': 'backup',
                    'title': 'زيادة عدد النسخ الاحتياطية',
                    'description': f'عدد النسخ الاحتياطية قليل ({backup_count})'
                })
            
            # Save system recommendations
            save_system = self.inspection_results['save_system']
            if not save_system.get('main_file_writable', False):
                recommendations.append({
                    'priority': 'high',
                    'category': 'save_system',
                    'title': 'إصلاح صلاحيات الكتابة',
                    'description': 'ملف البيانات غير قابل للكتابة'
                })
            
            # File structure recommendations
            missing_dirs = self.inspection_results['file_structure'].get('missing_dirs', [])
            if missing_dirs:
                recommendations.append({
                    'priority': 'low',
                    'category': 'file_structure',
                    'title': 'إنشاء مجلدات مفقودة',
                    'description': f'مجلدات مفقودة: {", ".join(missing_dirs)}'
                })
            
            # Performance recommendations
            total_size = self.inspection_results['file_structure'].get('total_size_mb', 0)
            if total_size > 100:
                recommendations.append({
                    'priority': 'medium',
                    'category': 'performance',
                    'title': 'تنظيف الملفات القديمة',
                    'description': f'حجم المجلد كبير ({total_size:.1f} MB)'
                })
            
            self.inspection_results['recommendations'] = recommendations
            
            print(f"💡 تم إنشاء {len(recommendations)} توصية")
            
            return recommendations
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التوصيات: {e}")
            return []
    
    def run_comprehensive_inspection(self):
        """Run comprehensive data inspection"""
        try:
            print("🔍 بدء الفحص الشامل للبيانات والحفظ")
            print("🔍 Starting Comprehensive Data & Save Inspection")
            print("="*60)
            
            # Run all inspections
            inspections = [
                ("فحص ملف البيانات الرئيسي", self.inspect_main_data_file),
                ("فحص نظام النسخ الاحتياطية", self.inspect_backup_system),
                ("فحص نظام الحفظ", self.inspect_save_system),
                ("فحص هيكل الملفات", self.inspect_file_structure),
            ]
            
            successful_inspections = 0
            
            for name, inspection_func in inspections:
                print(f"\n🔍 {name}...")
                if inspection_func():
                    successful_inspections += 1
                    print(f"✅ {name}: مكتمل")
                else:
                    print(f"❌ {name}: فشل")
            
            # Generate recommendations
            recommendations = self.generate_recommendations()
            
            # Generate summary report
            self.generate_inspection_report()
            
            print("\n" + "="*60)
            print(f"📊 ملخص الفحص:")
            print(f"✅ فحوصات مكتملة: {successful_inspections}/{len(inspections)}")
            print(f"💡 توصيات: {len(recommendations)}")
            
            # Show critical issues
            critical_issues = [r for r in recommendations if r['priority'] == 'high']
            if critical_issues:
                print(f"🚨 مشاكل حرجة: {len(critical_issues)}")
                for issue in critical_issues:
                    print(f"  • {issue['title']}")
            else:
                print("✅ لا توجد مشاكل حرجة")
            
            print("="*60)
            
            return successful_inspections >= 3
            
        except Exception as e:
            print(f"❌ خطأ في الفحص الشامل: {e}")
            return False
    
    def generate_inspection_report(self):
        """Generate detailed inspection report"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"protech_data_inspection_report_{timestamp}.json"
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'inspection_results': self.inspection_results,
                'summary': {
                    'data_integrity_ok': self.inspection_results['data_integrity'].get('main_file', {}).get('status') == 'ok',
                    'backup_count': self.inspection_results['backup_status'].get('total_backups', 0),
                    'save_system_ok': all(self.inspection_results['save_system'].values()),
                    'recommendations_count': len(self.inspection_results['recommendations'])
                }
            }
            
            report_path = os.path.join(self.data_dir, report_file)
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2)
            
            print(f"📄 تم إنشاء تقرير الفحص: {report_file}")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء التقرير: {e}")

def main():
    """Main inspection function"""
    inspector = ProTechDataInspector()
    success = inspector.run_comprehensive_inspection()
    
    if success:
        print("\n✅ فحص البيانات والحفظ مكتمل بنجاح!")
        print("✅ Data and save inspection completed successfully!")
    else:
        print("\n⚠️ فحص البيانات والحفظ مكتمل مع مشاكل")
        print("⚠️ Data and save inspection completed with issues")
    
    return success

if __name__ == "__main__":
    main()
