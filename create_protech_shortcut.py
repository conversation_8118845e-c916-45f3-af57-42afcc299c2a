#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import winshell
from win32com.client import Dispatch

def create_protech_shortcut():
    """Create a desktop shortcut for ProTech"""
    try:
        # Paths
        desktop = winshell.desktop()
        target = r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_working.py"
        working_dir = r"C:\Users\<USER>\OneDrive\Desktop\accounting program"
        shortcut_path = os.path.join(desktop, "ProTech Accounting System.lnk")
        
        # Create shortcut
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = target
        shortcut.WorkingDirectory = working_dir
        shortcut.Description = "ProTech Accounting System - نظام ProTech للمحاسبة"
        shortcut.save()
        
        print(f"✅ تم إنشاء الاختصار بنجاح!")
        print(f"✅ Shortcut created successfully!")
        print(f"📂 المسار: {shortcut_path}")
        print(f"📂 Path: {shortcut_path}")
        
    except ImportError:
        print("⚠️ المكتبات المطلوبة غير مثبتة")
        print("⚠️ Required libraries not installed")
        print("💡 استخدم الطريقة اليدوية لإنشاء الاختصار")
        print("💡 Use manual method to create shortcut")
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الاختصار: {e}")
        print(f"❌ Error creating shortcut: {e}")

if __name__ == "__main__":
    create_protech_shortcut()
