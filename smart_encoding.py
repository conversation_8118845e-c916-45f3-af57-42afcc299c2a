
import sys
import locale

def setup_smart_encoding():
    """Setup smart encoding"""
    try:
        # Set UTF-8 encoding
        if hasattr(sys, 'set_int_max_str_digits'):
            sys.set_int_max_str_digits(0)
        
        # Set environment variables
        import os
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        os.environ['PYTHONUTF8'] = '1'
        
        # Set locale
        try:
            locale.setlocale(locale.LC_ALL, 'en_US.UTF-8')
        except:
            try:
                locale.setlocale(locale.LC_ALL, 'C.UTF-8')
            except:
                pass
        
        return True
    except:
        return False

def safe_unicode_print(*args, **kwargs):
    """Safe unicode print"""
    try:
        # Convert all args to safe strings
        safe_args = []
        for arg in args:
            try:
                safe_args.append(str(arg))
            except:
                safe_args.append(repr(arg))
        
        print(*safe_args, **kwargs)
    except:
        try:
            print("تم طباعة رسالة (ترميز آمن)")
        except:
            print("Message printed (safe encoding)")

# Setup encoding on import
setup_smart_encoding()
