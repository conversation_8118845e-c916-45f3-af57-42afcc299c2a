import { NextRequest, NextResponse } from 'next/server';
import { db } from '@/lib/db';
import { inventoryMovementSchema, paginationSchema } from '@/lib/validations';
import { productOperations } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const productId = searchParams.get('productId');
    const type = searchParams.get('type');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');

    const where: any = {};

    if (productId) {
      where.productId = productId;
    }

    if (type) {
      where.type = type;
    }

    if (startDate || endDate) {
      where.createdAt = {};
      if (startDate) {
        where.createdAt.gte = new Date(startDate);
      }
      if (endDate) {
        where.createdAt.lte = new Date(endDate);
      }
    }

    const skip = (page - 1) * limit;

    const [movements, total] = await Promise.all([
      db.inventoryMovement.findMany({
        where,
        include: {
          product: {
            select: {
              id: true,
              code: true,
              name: true,
              unit: true,
            },
          },
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      db.inventoryMovement.count({ where }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      success: true,
      data: {
        data: movements,
        total,
        page,
        limit,
        totalPages,
      },
    });
  } catch (error) {
    console.error('Error fetching inventory movements:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch inventory movements',
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const validatedData = inventoryMovementSchema.parse(body);

    // Get current user (in a real app, this would come from authentication)
    const userId = 'admin-user-id'; // This should come from the authenticated user

    // Start a transaction to ensure data consistency
    const result = await db.$transaction(async (prisma) => {
      // Create the inventory movement
      const movement = await prisma.inventoryMovement.create({
        data: {
          ...validatedData,
          userId,
          totalCost: validatedData.unitCost 
            ? validatedData.unitCost * Math.abs(validatedData.quantity)
            : undefined,
        },
        include: {
          product: true,
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
            },
          },
        },
      });

      // Update product stock based on movement type
      const product = await prisma.product.findUnique({
        where: { id: validatedData.productId },
      });

      if (!product) {
        throw new Error('Product not found');
      }

      let newStock = product.currentStock;

      switch (validatedData.type) {
        case 'IN':
          newStock += validatedData.quantity;
          break;
        case 'OUT':
          newStock -= validatedData.quantity;
          break;
        case 'ADJUSTMENT':
          newStock = validatedData.quantity;
          break;
        case 'TRANSFER':
          // For transfers, quantity can be positive or negative
          newStock += validatedData.quantity;
          break;
      }

      // Check if negative stock is allowed
      if (newStock < 0 && !product.allowNegative) {
        throw new Error('Insufficient stock. Cannot go below zero.');
      }

      // Update product stock
      await prisma.product.update({
        where: { id: validatedData.productId },
        data: { currentStock: newStock },
      });

      return movement;
    });

    return NextResponse.json({
      success: true,
      data: result,
      message: 'Inventory movement recorded successfully',
    });
  } catch (error) {
    console.error('Error creating inventory movement:', error);
    
    if (error instanceof Error) {
      return NextResponse.json(
        {
          success: false,
          error: error.message,
        },
        { status: 400 }
      );
    }

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to record inventory movement',
      },
      { status: 500 }
    );
  }
}
