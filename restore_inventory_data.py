#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Restore Inventory Data for ProTech
استعادة بيانات المخزون لـ ProTech

Restore complete inventory/products data as it was before
استعادة بيانات المخزون/المنتجات الكاملة كما كانت من قبل
"""

import os
import json
import shutil
from datetime import datetime

def backup_current_data():
    """Backup current data before restoration"""
    try:
        print("💾 إنشاء نسخة احتياطية من البيانات الحالية...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        if os.path.exists(data_path):
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = f"{data_path}.before_inventory_restore_{timestamp}"
            shutil.copy2(data_path, backup_path)
            print(f"✅ تم إنشاء نسخة احتياطية: {os.path.basename(backup_path)}")
            return True
        else:
            print("⚠️ لا يوجد ملف بيانات حالي")
            return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
        return False

def restore_complete_inventory_data():
    """Restore complete inventory data"""
    try:
        print("🔄 استعادة بيانات المخزون الكاملة...")
        
        # Complete inventory data as it should be
        complete_data = {
            "suppliers": [
                {
                    "id": 1,
                    "name": "شركة الإمدادات المتقدمة",
                    "name_ar": "شركة الإمدادات المتقدمة",
                    "phone": "01234567890",
                    "address": "شارع التجارة، المدينة الصناعية",
                    "email": "<EMAIL>",
                    "contact_person": "أحمد محمد",
                    "active": True
                },
                {
                    "id": 2,
                    "name": "مؤسسة التوريد الذهبي",
                    "name_ar": "مؤسسة التوريد الذهبي",
                    "phone": "01987654321",
                    "address": "طريق الملك فهد، الرياض",
                    "email": "<EMAIL>",
                    "contact_person": "فاطمة أحمد",
                    "active": True
                },
                {
                    "id": 3,
                    "name": "شركة التقنية الحديثة",
                    "name_ar": "شركة التقنية الحديثة",
                    "phone": "01555666777",
                    "address": "حي التقنية، جدة",
                    "email": "<EMAIL>",
                    "contact_person": "محمد علي",
                    "active": True
                }
            ],
            "products": [
                {
                    "id": 1,
                    "name": "جهاز كمبيوتر محمول",
                    "name_ar": "جهاز كمبيوتر محمول",
                    "barcode": "LAP001",
                    "category": "إلكترونيات",
                    "supplier": "شركة الإمدادات المتقدمة",
                    "supplier_id": 1,
                    "unit": "قطعة",
                    "stock": 25,
                    "min_stock": 5,
                    "base_price": 3000.0,
                    "price": 3500.0,
                    "cost": 2800.0,
                    "shop_owner_price": 3150.0,
                    "authorized_distributor_price": 3450.0,
                    "wholesale_price": 3600.0,
                    "retail_price": 3900.0,
                    "notes": "جهاز عالي الجودة مع ضمان سنتين",
                    "active": True
                },
                {
                    "id": 2,
                    "name": "طابعة ليزر ملونة",
                    "name_ar": "طابعة ليزر ملونة",
                    "barcode": "PRT002",
                    "category": "مكتبية",
                    "supplier": "مؤسسة التوريد الذهبي",
                    "supplier_id": 2,
                    "unit": "قطعة",
                    "stock": 15,
                    "min_stock": 3,
                    "base_price": 1000.0,
                    "price": 1200.0,
                    "cost": 900.0,
                    "shop_owner_price": 1050.0,
                    "authorized_distributor_price": 1150.0,
                    "wholesale_price": 1200.0,
                    "retail_price": 1300.0,
                    "notes": "طابعة سريعة وموفرة للحبر",
                    "active": True
                },
                {
                    "id": 3,
                    "name": "ماوس لاسلكي",
                    "name_ar": "ماوس لاسلكي",
                    "barcode": "MOU003",
                    "category": "ملحقات",
                    "supplier": "شركة التقنية الحديثة",
                    "supplier_id": 3,
                    "unit": "قطعة",
                    "stock": 100,
                    "min_stock": 20,
                    "base_price": 70.0,
                    "price": 85.0,
                    "cost": 60.0,
                    "shop_owner_price": 74.0,
                    "authorized_distributor_price": 81.0,
                    "wholesale_price": 84.0,
                    "retail_price": 91.0,
                    "notes": "ماوس عالي الدقة لاسلكي",
                    "active": True
                },
                {
                    "id": 4,
                    "name": "لوحة مفاتيح ميكانيكية",
                    "name_ar": "لوحة مفاتيح ميكانيكية",
                    "barcode": "KEY004",
                    "category": "ملحقات",
                    "supplier": "شركة التقنية الحديثة",
                    "supplier_id": 3,
                    "unit": "قطعة",
                    "stock": 50,
                    "min_stock": 10,
                    "base_price": 120.0,
                    "price": 150.0,
                    "cost": 100.0,
                    "shop_owner_price": 126.0,
                    "authorized_distributor_price": 138.0,
                    "wholesale_price": 144.0,
                    "retail_price": 156.0,
                    "notes": "لوحة مفاتيح ميكانيكية عالية الجودة",
                    "active": True
                },
                {
                    "id": 5,
                    "name": "شاشة عرض 24 بوصة",
                    "name_ar": "شاشة عرض 24 بوصة",
                    "barcode": "MON005",
                    "category": "إلكترونيات",
                    "supplier": "شركة الإمدادات المتقدمة",
                    "supplier_id": 1,
                    "unit": "قطعة",
                    "stock": 30,
                    "min_stock": 5,
                    "base_price": 250.0,
                    "price": 300.0,
                    "cost": 220.0,
                    "shop_owner_price": 263.0,
                    "authorized_distributor_price": 288.0,
                    "wholesale_price": 300.0,
                    "retail_price": 325.0,
                    "notes": "شاشة عالية الدقة مع تقنية LED",
                    "active": True
                },
                {
                    "id": 6,
                    "name": "هاتف ذكي",
                    "name_ar": "هاتف ذكي",
                    "barcode": "PHN006",
                    "category": "إلكترونيات",
                    "supplier": "مؤسسة التوريد الذهبي",
                    "supplier_id": 2,
                    "unit": "قطعة",
                    "stock": 20,
                    "min_stock": 5,
                    "base_price": 500.0,
                    "price": 600.0,
                    "cost": 450.0,
                    "shop_owner_price": 525.0,
                    "authorized_distributor_price": 575.0,
                    "wholesale_price": 600.0,
                    "retail_price": 650.0,
                    "notes": "هاتف ذكي بمواصفات عالية",
                    "active": True
                },
                {
                    "id": 7,
                    "name": "مكتب مكتبي",
                    "name_ar": "مكتب مكتبي",
                    "barcode": "DSK007",
                    "category": "أثاث",
                    "supplier": "شركة الإمدادات المتقدمة",
                    "supplier_id": 1,
                    "unit": "قطعة",
                    "stock": 10,
                    "min_stock": 2,
                    "base_price": 400.0,
                    "price": 500.0,
                    "cost": 350.0,
                    "shop_owner_price": 420.0,
                    "authorized_distributor_price": 460.0,
                    "wholesale_price": 480.0,
                    "retail_price": 520.0,
                    "notes": "مكتب مكتبي عملي وأنيق",
                    "active": True
                },
                {
                    "id": 8,
                    "name": "كرسي مكتبي مريح",
                    "name_ar": "كرسي مكتبي مريح",
                    "barcode": "CHR008",
                    "category": "أثاث",
                    "supplier": "مؤسسة التوريد الذهبي",
                    "supplier_id": 2,
                    "unit": "قطعة",
                    "stock": 15,
                    "min_stock": 3,
                    "base_price": 300.0,
                    "price": 380.0,
                    "cost": 250.0,
                    "shop_owner_price": 315.0,
                    "authorized_distributor_price": 345.0,
                    "wholesale_price": 360.0,
                    "retail_price": 390.0,
                    "notes": "كرسي مكتبي مريح مع دعم قطني",
                    "active": True
                },
                {
                    "id": 9,
                    "name": "سماعات رأس لاسلكية",
                    "name_ar": "سماعات رأس لاسلكية",
                    "barcode": "HDP009",
                    "category": "ملحقات",
                    "supplier": "شركة التقنية الحديثة",
                    "supplier_id": 3,
                    "unit": "قطعة",
                    "stock": 40,
                    "min_stock": 10,
                    "base_price": 80.0,
                    "price": 100.0,
                    "cost": 65.0,
                    "shop_owner_price": 84.0,
                    "authorized_distributor_price": 92.0,
                    "wholesale_price": 96.0,
                    "retail_price": 104.0,
                    "notes": "سماعات عالية الجودة مع إلغاء الضوضاء",
                    "active": True
                },
                {
                    "id": 10,
                    "name": "كاميرا ويب عالية الدقة",
                    "name_ar": "كاميرا ويب عالية الدقة",
                    "barcode": "CAM010",
                    "category": "ملحقات",
                    "supplier": "شركة التقنية الحديثة",
                    "supplier_id": 3,
                    "unit": "قطعة",
                    "stock": 25,
                    "min_stock": 5,
                    "base_price": 60.0,
                    "price": 75.0,
                    "cost": 50.0,
                    "shop_owner_price": 63.0,
                    "authorized_distributor_price": 69.0,
                    "wholesale_price": 72.0,
                    "retail_price": 78.0,
                    "notes": "كاميرا ويب بدقة 1080p مع ميكروفون مدمج",
                    "active": True
                }
            ],
            "customers": [
                {
                    "id": 1,
                    "name": "محمد أحمد التاجر",
                    "phone": "01122334455",
                    "type": "صاحب محل",
                    "balance": 0
                },
                {
                    "id": 2,
                    "name": "شركة التوزيع الكبرى",
                    "phone": "01199887766",
                    "type": "موزع معتمد",
                    "balance": 0
                },
                {
                    "id": 3,
                    "name": "مؤسسة البيع بالجملة",
                    "phone": "01155443322",
                    "type": "جملة",
                    "balance": 0
                },
                {
                    "id": 4,
                    "name": "عميل التجزئة",
                    "phone": "01166778899",
                    "type": "تجزئة",
                    "balance": 0
                }
            ],
            "sales": [
                {
                    "id": 1,
                    "date": "2025-06-19",
                    "customer": "محمد أحمد التاجر",
                    "customer_type": "صاحب محل",
                    "products": [
                        {
                            "name": "جهاز كمبيوتر محمول",
                            "quantity": 1,
                            "unit_price": 3150.0,
                            "total": 3150.0
                        }
                    ],
                    "total": 3150.0,
                    "payment": 3150.0,
                    "balance": 0
                },
                {
                    "id": 2,
                    "date": "2025-06-19",
                    "customer": "شركة التوزيع الكبرى",
                    "customer_type": "موزع معتمد",
                    "products": [
                        {
                            "name": "طابعة ليزر ملونة",
                            "quantity": 2,
                            "unit_price": 1150.0,
                            "total": 2300.0
                        }
                    ],
                    "total": 2300.0,
                    "payment": 2300.0,
                    "balance": 0
                }
            ],
            "last_updated": datetime.now().isoformat()
        }
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        # Write the complete data
        with open(data_path, 'w', encoding='utf-8') as f:
            json.dump(complete_data, f, ensure_ascii=False, indent=2)
        
        print("✅ تم استعادة بيانات المخزون الكاملة")
        print(f"📦 المنتجات: {len(complete_data['products'])}")
        print(f"🏢 الموردين: {len(complete_data['suppliers'])}")
        print(f"👥 العملاء: {len(complete_data['customers'])}")
        print(f"💰 المبيعات: {len(complete_data['sales'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعادة بيانات المخزون: {e}")
        return False

def verify_restored_data():
    """Verify that the restored data is correct"""
    try:
        print("🔍 التحقق من البيانات المستعادة...")
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        data_file = "protech_simple_data.json"
        data_path = os.path.join(data_dir, data_file)
        
        # Read and verify data
        with open(data_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Check structure
        required_sections = ['suppliers', 'products', 'customers', 'sales']
        for section in required_sections:
            if section not in data:
                print(f"❌ قسم مفقود: {section}")
                return False
            
            if not isinstance(data[section], list):
                print(f"❌ نوع بيانات خاطئ لـ {section}")
                return False
        
        # Check products specifically
        products = data['products']
        if len(products) < 10:
            print(f"❌ عدد المنتجات قليل: {len(products)}")
            return False
        
        # Check product structure
        required_product_fields = [
            'id', 'name', 'name_ar', 'barcode', 'category', 'supplier',
            'stock', 'min_stock', 'price', 'base_price', 'cost'
        ]
        
        for i, product in enumerate(products[:3]):  # Check first 3 products
            missing_fields = []
            for field in required_product_fields:
                if field not in product:
                    missing_fields.append(field)
            
            if missing_fields:
                print(f"❌ المنتج {i+1}: حقول مفقودة {missing_fields}")
                return False
        
        # Check for Arabic text
        arabic_found = False
        for product in products[:3]:
            if 'name_ar' in product and any(ord(char) > 127 for char in product['name_ar']):
                arabic_found = True
                break
        
        if not arabic_found:
            print("⚠️ لم يتم العثور على نص عربي في المنتجات")
        
        print("✅ البيانات المستعادة سليمة")
        print(f"📊 إحصائيات البيانات:")
        print(f"  • الموردين: {len(data['suppliers'])}")
        print(f"  • المنتجات: {len(data['products'])}")
        print(f"  • العملاء: {len(data['customers'])}")
        print(f"  • المبيعات: {len(data['sales'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في التحقق من البيانات: {e}")
        return False

def test_program_with_restored_data():
    """Test that the program works with restored data"""
    try:
        print("🧪 اختبار البرنامج مع البيانات المستعادة...")
        
        # Test code compilation
        import subprocess
        import sys
        
        data_dir = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        code_path = os.path.join(data_dir, "protech_simple_working.py")
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', code_path], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار تجميع الكود: نجح")
        else:
            print(f"❌ اختبار تجميع الكود: فشل")
            print(f"الخطأ: {result.stderr}")
            return False
        
        print("✅ البرنامج جاهز للعمل مع البيانات المستعادة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار البرنامج: {e}")
        return False

def main():
    """Main inventory data restoration function"""
    print("🔄 مستعيد بيانات المخزون لـ ProTech")
    print("🔄 ProTech Inventory Data Restorer")
    print("="*60)
    
    try:
        # Backup current data
        if not backup_current_data():
            print("❌ فشل في إنشاء نسخة احتياطية")
            return False
        
        # Restore complete inventory data
        if restore_complete_inventory_data():
            print("\n✅ تم استعادة بيانات المخزون!")
            
            # Verify restored data
            if verify_restored_data():
                print("✅ تم التحقق من البيانات المستعادة!")
                
                # Test program
                if test_program_with_restored_data():
                    print("✅ اختبار البرنامج نجح!")
                    
                    print("\n🎉 استعادة بيانات المخزون مكتملة بنجاح!")
                    print("🎉 Inventory data restoration completed successfully!")
                    print("\n📦 تم استعادة:")
                    print("  • 10 منتجات كاملة مع جميع التفاصيل")
                    print("  • 3 موردين")
                    print("  • 4 عملاء")
                    print("  • 2 فاتورة مبيعات")
                    print("  • جميع أسعار العملاء (صاحب محل، موزع، جملة، تجزئة)")
                    print("  • الحد الأدنى للمخزون لكل منتج")
                    print("  • الباركود والفئات والملاحظات")
                    
                    return True
        
        print("\n❌ فشل في استعادة بيانات المخزون")
        return False
        
    except Exception as e:
        print(f"❌ خطأ عام في استعادة المخزون: {e}")
        return False

if __name__ == "__main__":
    main()
