#!/usr/bin/env python3
"""
ProTech with Persistent Storage - نظام ProTech مع التخزين الدائم
تشغيل نظام ProTech الحالي مع نظام التخزين الدائم المنظم

Run existing ProTech system with organized persistent storage
تشغيل نظام ProTech الموجود مع نظام التخزين الدائم المنظم
"""

import os
import sys
import subprocess
import threading
import time
import json
from datetime import datetime
import traceback

# Add current directory to Python path
sys.path.insert(0, '.')

try:
    from persistent_storage_manager import PersistentStorageManager
    from protech_persistent_integration import ProTechPersistentIntegration
except ImportError as e:
    print(f"❌ خطأ في استيراد وحدات التخزين الدائم: {e}")
    print("❌ Error importing persistent storage modules")
    print("يرجى التأكد من وجود الملفات المطلوبة:")
    print("- persistent_storage_manager.py")
    print("- protech_persistent_integration.py")
    sys.exit(1)

class ProTechPersistentLauncher:
    """Launcher for ProTech with persistent storage integration"""
    
    def __init__(self):
        self.storage_manager = None
        self.integration = None
        self.protech_process = None
        self.monitoring_active = False
        
        print("🚀 تشغيل نظام ProTech مع التخزين الدائم المنظم...")
        print("🚀 Starting ProTech with Organized Persistent Storage...")

    def initialize_storage(self):
        """Initialize persistent storage system"""
        try:
            print("🔄 تهيئة نظام التخزين الدائم...")
            
            # Initialize storage manager
            self.storage_manager = PersistentStorageManager(
                base_path=".", 
                app_name="ProTech"
            )
            
            # Initialize integration
            self.integration = ProTechPersistentIntegration()
            
            print("✅ تم تهيئة نظام التخزين الدائم بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة التخزين الدائم: {e}")
            traceback.print_exc()
            return False

    def check_protech_system(self):
        """Check if ProTech system files exist"""
        try:
            protech_files = [
                "protech_simple_working.py"
            ]
            
            missing_files = []
            for file in protech_files:
                if not os.path.exists(file):
                    missing_files.append(file)
            
            if missing_files:
                print("❌ ملفات نظام ProTech مفقودة:")
                for file in missing_files:
                    print(f"  - {file}")
                return False
            
            print("✅ تم العثور على جميع ملفات نظام ProTech")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في فحص ملفات النظام: {e}")
            return False

    def migrate_existing_data(self):
        """Migrate existing data to persistent storage"""
        try:
            # Look for existing data files
            data_files = [
                "protech_simple_data.json",
                "protech_data.json",
                "data.json"
            ]
            
            for data_file in data_files:
                if os.path.exists(data_file):
                    print(f"📥 ترحيل البيانات من {data_file}...")
                    
                    with open(data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Migrate data using integration
                    migrated_count = 0
                    for data_type, items in data.items():
                        if data_type in ['suppliers', 'products', 'customers', 'sales', 'invoices']:
                            if items and self.integration.save_app_data(data_type, items):
                                migrated_count += 1
                                print(f"  ✅ تم ترحيل {data_type}: {len(items) if isinstance(items, list) else 1} عنصر")
                    
                    if migrated_count > 0:
                        print(f"✅ تم ترحيل {migrated_count} نوع من البيانات")
                        
                        # Create backup of original file
                        backup_name = f"{data_file}.backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                        import shutil
                        shutil.copy2(data_file, backup_name)
                        print(f"💾 تم إنشاء نسخة احتياطية: {backup_name}")
                    
                    break  # Use first found file
            
        except Exception as e:
            print(f"❌ خطأ في ترحيل البيانات: {e}")

    def start_protech_system(self):
        """Start ProTech system"""
        try:
            print("🚀 تشغيل نظام ProTech...")
            
            # Start ProTech in a separate process
            self.protech_process = subprocess.Popen([
                sys.executable, "protech_simple_working.py"
            ], cwd=".")
            
            print("✅ تم تشغيل نظام ProTech بنجاح")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل نظام ProTech: {e}")
            return False

    def start_monitoring(self):
        """Start monitoring thread"""
        try:
            self.monitoring_active = True
            self.monitor_thread = threading.Thread(target=self.monitor_worker, daemon=True)
            self.monitor_thread.start()
            print("✅ تم تشغيل مراقبة النظام")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل المراقبة: {e}")

    def monitor_worker(self):
        """Background monitoring worker"""
        while self.monitoring_active:
            try:
                time.sleep(60)  # Check every minute
                
                # Check if ProTech process is still running
                if self.protech_process and self.protech_process.poll() is not None:
                    print("ℹ️ تم إغلاق نظام ProTech")
                    self.monitoring_active = False
                    break
                
                # Create periodic backup
                if self.storage_manager:
                    self.storage_manager.create_backup(backup_type="auto")
                
            except Exception as e:
                print(f"❌ خطأ في المراقبة: {e}")

    def wait_for_completion(self):
        """Wait for ProTech system to complete"""
        try:
            if self.protech_process:
                print("⏳ انتظار إغلاق نظام ProTech...")
                self.protech_process.wait()
                print("✅ تم إغلاق نظام ProTech")
            
        except KeyboardInterrupt:
            print("\n🔄 تم إيقاف النظام بواسطة المستخدم")
            self.cleanup()
        except Exception as e:
            print(f"❌ خطأ في انتظار النظام: {e}")

    def cleanup(self):
        """Cleanup and close systems"""
        try:
            print("🔄 إغلاق الأنظمة...")
            
            # Stop monitoring
            self.monitoring_active = False
            
            # Terminate ProTech process if still running
            if self.protech_process and self.protech_process.poll() is None:
                self.protech_process.terminate()
                time.sleep(2)
                if self.protech_process.poll() is None:
                    self.protech_process.kill()
            
            # Close storage systems
            if self.integration:
                self.integration.close()
            
            if self.storage_manager:
                self.storage_manager.close()
            
            print("✅ تم إغلاق جميع الأنظمة بنجاح")
            
        except Exception as e:
            print(f"❌ خطأ في إغلاق الأنظمة: {e}")

    def show_system_info(self):
        """Show system information"""
        try:
            print("\n" + "="*60)
            print("📊 معلومات النظام / System Information")
            print("="*60)
            
            if self.storage_manager:
                stats = self.storage_manager.get_storage_stats()
                print(f"💾 إجمالي عمليات الحفظ: {stats.get('total_saves', 0)}")
                print(f"📥 إجمالي عمليات التحميل: {stats.get('total_loads', 0)}")
                print(f"🗄️ عدد النسخ الاحتياطية: {stats.get('backup_count', 0)}")
                
                if 'sqlite_size' in stats:
                    print(f"📊 حجم قاعدة البيانات: {stats['sqlite_size']:,} بايت")
                
                if 'compression_ratio' in stats:
                    print(f"📦 معدل الضغط: {stats['compression_ratio']:.2%}")
            
            print("="*60)
            
        except Exception as e:
            print(f"❌ خطأ في عرض معلومات النظام: {e}")

    def run(self):
        """Main run method"""
        try:
            # Check system requirements
            if not self.check_protech_system():
                return False
            
            # Initialize storage
            if not self.initialize_storage():
                return False
            
            # Migrate existing data
            self.migrate_existing_data()
            
            # Start ProTech system
            if not self.start_protech_system():
                return False
            
            # Start monitoring
            self.start_monitoring()
            
            # Show system info
            self.show_system_info()
            
            # Wait for completion
            self.wait_for_completion()
            
            # Cleanup
            self.cleanup()
            
            return True
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل النظام: {e}")
            traceback.print_exc()
            self.cleanup()
            return False

def main():
    """Main function"""
    print("🖥️ تشغيل نظام ProTech مع التخزين الدائم المنظم")
    print("🖥️ Starting ProTech with Organized Persistent Storage")
    print()
    
    try:
        launcher = ProTechPersistentLauncher()
        success = launcher.run()
        
        if success:
            print("\n✅ تم تشغيل النظام بنجاح!")
            print("✅ System completed successfully!")
        else:
            print("\n❌ فشل في تشغيل النظام")
            print("❌ System failed to start")
            
    except KeyboardInterrupt:
        print("\n🔄 تم إيقاف النظام بواسطة المستخدم")
        print("🔄 System stopped by user")
    except Exception as e:
        print(f"\n❌ خطأ عام في النظام: {e}")
        print(f"❌ General system error: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
