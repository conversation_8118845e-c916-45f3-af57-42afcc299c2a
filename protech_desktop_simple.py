#!/usr/bin/env python3
"""
ProTech Accounting System - Simple Desktop Application
نظام ProTech للمحاسبة - تطبيق سطح مكتب بسيط
"""

import tkinter as tk
from tkinter import messagebox
import webbrowser
import subprocess
import sys
import os
import threading
import time

class ProTechDesktop:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("نظام ProTech للمحاسبة - ProTech Accounting System")
        self.root.geometry("600x500")
        self.root.configure(bg='#f0f8ff')
        self.root.resizable(True, True)
        
        # Variables
        self.server_process = None
        self.server_running = False
        
        # Create GUI
        self.create_gui()
        
        # Center window
        self.center_window()
    
    def center_window(self):
        """Center window on screen"""
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        x = (self.root.winfo_screenwidth() // 2) - (width // 2)
        y = (self.root.winfo_screenheight() // 2) - (height // 2)
        self.root.geometry(f'{width}x{height}+{x}+{y}')
    
    def create_gui(self):
        """Create the GUI"""
        
        # Header
        header_frame = tk.Frame(self.root, bg='#2563eb', height=80)
        header_frame.pack(fill='x', padx=10, pady=10)
        header_frame.pack_propagate(False)
        
        title_label = tk.Label(
            header_frame,
            text="🎉 نظام ProTech للمحاسبة",
            font=('Arial', 18, 'bold'),
            fg='white',
            bg='#2563eb'
        )
        title_label.pack(pady=5)
        
        subtitle_label = tk.Label(
            header_frame,
            text="ProTech Accounting System - Desktop App",
            font=('Arial', 11),
            fg='#bfdbfe',
            bg='#2563eb'
        )
        subtitle_label.pack()
        
        # Main content
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Status
        status_frame = tk.Frame(main_frame, bg='#ffffff', relief='ridge', bd=2)
        status_frame.pack(fill='x', pady=10)
        
        tk.Label(
            status_frame,
            text="حالة النظام / System Status",
            font=('Arial', 12, 'bold'),
            bg='#ffffff'
        ).pack(pady=5)
        
        self.status_label = tk.Label(
            status_frame,
            text="⚪ النظام متوقف / System Stopped",
            font=('Arial', 11),
            fg='#dc2626',
            bg='#ffffff'
        )
        self.status_label.pack(pady=5)
        
        # Control buttons
        button_frame = tk.Frame(main_frame, bg='#f0f8ff')
        button_frame.pack(pady=20)
        
        # Start button
        self.start_btn = tk.Button(
            button_frame,
            text="🚀 تشغيل النظام\nStart System",
            font=('Arial', 12, 'bold'),
            bg='#16a34a',
            fg='white',
            width=18,
            height=3,
            command=self.start_system,
            cursor='hand2'
        )
        self.start_btn.pack(pady=5)
        
        # Open browser button
        self.browser_btn = tk.Button(
            button_frame,
            text="🌐 فتح التطبيق\nOpen Application",
            font=('Arial', 12, 'bold'),
            bg='#2563eb',
            fg='white',
            width=18,
            height=3,
            command=self.open_application,
            state='disabled',
            cursor='hand2'
        )
        self.browser_btn.pack(pady=5)
        
        # Stop button
        self.stop_btn = tk.Button(
            button_frame,
            text="🛑 إيقاف النظام\nStop System",
            font=('Arial', 12, 'bold'),
            bg='#dc2626',
            fg='white',
            width=18,
            height=3,
            command=self.stop_system,
            state='disabled',
            cursor='hand2'
        )
        self.stop_btn.pack(pady=5)
        
        # Information
        info_frame = tk.Frame(main_frame, bg='#ffffff', relief='ridge', bd=2)
        info_frame.pack(fill='both', expand=True, pady=10)
        
        tk.Label(
            info_frame,
            text="معلومات التطبيق / Application Info",
            font=('Arial', 12, 'bold'),
            bg='#ffffff'
        ).pack(pady=5)
        
        info_text = tk.Text(
            info_frame,
            height=8,
            font=('Arial', 10),
            bg='#f8fafc',
            fg='#374151',
            wrap='word',
            state='disabled'
        )
        info_text.pack(fill='both', expand=True, padx=10, pady=5)
        
        # Add info content
        info_content = """
🎯 التعليمات / Instructions:

1. اضغط "تشغيل النظام" لبدء الخادم
   Click "Start System" to start the server

2. انتظر حتى يصبح النظام جاهزاً (أخضر)
   Wait until the system is ready (green)

3. اضغط "فتح التطبيق" للوصول للنظام
   Click "Open Application" to access the system

4. استخدم النظام من خلال المتصفح
   Use the system through the browser

5. اضغط "إيقاف النظام" عند الانتهاء
   Click "Stop System" when finished

🌐 الرابط / URL: http://localhost:5000
        """
        
        info_text.config(state='normal')
        info_text.insert('1.0', info_content)
        info_text.config(state='disabled')
        
        # Footer
        footer_frame = tk.Frame(self.root, bg='#374151', height=30)
        footer_frame.pack(fill='x', side='bottom')
        footer_frame.pack_propagate(False)
        
        tk.Label(
            footer_frame,
            text="© 2024 ProTech Accounting System",
            font=('Arial', 9),
            fg='white',
            bg='#374151'
        ).pack(pady=5)
    
    def start_system(self):
        """Start the system"""
        try:
            self.status_label.config(
                text="🟡 جاري التشغيل... / Starting...",
                fg='#f59e0b'
            )
            self.start_btn.config(state='disabled')
            self.root.update()
            
            # Start server in background
            threading.Thread(target=self.run_server, daemon=True).start()
            
            # Check status after delay
            self.root.after(4000, self.check_server)
            
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"فشل في التشغيل:\n{str(e)}")
            self.reset_buttons()
    
    def run_server(self):
        """Run the Flask server"""
        try:
            cmd = [sys.executable, "basic_flask.py"]
            self.server_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=os.getcwd()
            )
        except Exception as e:
            print(f"Server error: {e}")
    
    def check_server(self):
        """Check if server is running"""
        try:
            import urllib.request
            urllib.request.urlopen('http://localhost:5000', timeout=5)
            
            # Server is running
            self.server_running = True
            self.status_label.config(
                text="🟢 النظام يعمل / System Running",
                fg='#16a34a'
            )
            
            self.browser_btn.config(state='normal')
            self.stop_btn.config(state='normal')
            
            messagebox.showinfo(
                "نجح التشغيل / Success",
                "تم تشغيل النظام بنجاح!\n\nSystem started successfully!\n\nيمكنك الآن فتح التطبيق."
            )
            
        except:
            # Try again
            self.root.after(2000, self.check_server)
    
    def open_application(self):
        """Open the application in browser"""
        try:
            webbrowser.open('http://localhost:5000')
            messagebox.showinfo(
                "تم الفتح / Opened",
                "تم فتح التطبيق في المتصفح\n\nApplication opened in browser"
            )
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"فشل في فتح المتصفح:\n{str(e)}")
    
    def stop_system(self):
        """Stop the system"""
        try:
            if self.server_process:
                self.server_process.terminate()
                self.server_process = None
            
            self.server_running = False
            self.status_label.config(
                text="⚪ النظام متوقف / System Stopped",
                fg='#dc2626'
            )
            
            self.reset_buttons()
            
            messagebox.showinfo(
                "تم الإيقاف / Stopped",
                "تم إيقاف النظام بنجاح\n\nSystem stopped successfully"
            )
            
        except Exception as e:
            messagebox.showerror("خطأ / Error", f"فشل في الإيقاف:\n{str(e)}")
    
    def reset_buttons(self):
        """Reset button states"""
        self.start_btn.config(state='normal')
        self.browser_btn.config(state='disabled')
        self.stop_btn.config(state='disabled')
    
    def on_closing(self):
        """Handle window closing"""
        if self.server_running:
            result = messagebox.askyesno(
                "تأكيد الإغلاق / Confirm Close",
                "النظام لا يزال يعمل. هل تريد إيقافه؟\n\nSystem is still running. Stop it?"
            )
            if result:
                self.stop_system()
                time.sleep(1)
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """Run the application"""
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        self.root.mainloop()

def main():
    """Main function"""
    print("🖥️ Starting ProTech Desktop Application...")
    print("🖥️ تشغيل تطبيق ProTech لسطح المكتب...")
    
    try:
        app = ProTechDesktop()
        app.run()
    except Exception as e:
        print(f"❌ Error: {e}")
        messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق:\n{str(e)}")

if __name__ == '__main__':
    main()
