#!/usr/bin/env python3
"""
Create Calculator Icon for ProTech
إنشاء أيقونة آلة حاسبة لنظام ProTech

Creates a calculator icon for the ProTech application
إنشاء أيقونة آلة حاسبة لتطبيق ProTech
"""

import tkinter as tk
from tkinter import Canvas
import os

def create_calculator_icon():
    """Create a calculator icon using tkinter canvas"""
    try:
        # Create a temporary window for the icon
        root = tk.Tk()
        root.withdraw()  # Hide the window
        
        # Create canvas for drawing the icon
        canvas = Canvas(root, width=64, height=64, bg='white')
        
        # Draw calculator body (rounded rectangle)
        canvas.create_rectangle(8, 8, 56, 56, fill='#2563eb', outline='#1d4ed8', width=2)
        
        # Draw calculator screen
        canvas.create_rectangle(12, 12, 52, 24, fill='#1f2937', outline='#374151', width=1)
        
        # Draw calculator buttons (4x4 grid)
        button_size = 8
        button_spacing = 10
        start_x = 14
        start_y = 28
        
        # Button colors
        number_color = '#f3f4f6'
        operator_color = '#fbbf24'
        
        # Draw buttons
        for row in range(4):
            for col in range(4):
                x = start_x + col * button_spacing
                y = start_y + row * button_spacing
                
                # Color coding: last column for operators
                if col == 3:
                    color = operator_color
                else:
                    color = number_color
                
                canvas.create_rectangle(x, y, x + button_size, y + button_size, 
                                      fill=color, outline='#6b7280', width=1)
        
        # Save as PostScript first, then convert
        canvas.update()
        canvas.postscript(file="calculator_icon.eps")
        
        root.destroy()
        
        print("✅ تم إنشاء أيقونة الآلة الحاسبة")
        print("✅ Calculator icon created")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الأيقونة: {e}")
        return False

def create_ico_file():
    """Create ICO file for Windows"""
    try:
        # Try to use PIL if available
        try:
            from PIL import Image, ImageDraw
            
            # Create a 64x64 image
            img = Image.new('RGBA', (64, 64), (255, 255, 255, 0))
            draw = ImageDraw.Draw(img)
            
            # Draw calculator body
            draw.rectangle([8, 8, 56, 56], fill=(37, 99, 235, 255), outline=(29, 78, 216, 255))
            
            # Draw screen
            draw.rectangle([12, 12, 52, 24], fill=(31, 41, 55, 255), outline=(55, 65, 81, 255))
            
            # Draw buttons
            button_size = 8
            button_spacing = 10
            start_x = 14
            start_y = 28
            
            for row in range(4):
                for col in range(4):
                    x = start_x + col * button_spacing
                    y = start_y + row * button_spacing
                    
                    if col == 3:
                        color = (251, 191, 36, 255)  # Orange for operators
                    else:
                        color = (243, 244, 246, 255)  # Light gray for numbers
                    
                    draw.rectangle([x, y, x + button_size, y + button_size], 
                                 fill=color, outline=(107, 114, 128, 255))
            
            # Save as ICO
            img.save("calculator.ico", format='ICO', sizes=[(64, 64), (32, 32), (16, 16)])
            
            print("✅ تم إنشاء ملف ICO للأيقونة")
            print("✅ ICO file created for icon")
            return True
            
        except ImportError:
            print("⚠️ PIL غير متوفر - سيتم استخدام طريقة بديلة")
            print("⚠️ PIL not available - using alternative method")
            return create_simple_icon()
            
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف ICO: {e}")
        return False

def create_simple_icon():
    """Create a simple text-based icon"""
    try:
        # Create a simple batch file that sets the icon
        icon_script = '''@echo off
REM Calculator Icon Script for ProTech
REM This script helps set calculator icon for ProTech

echo Creating calculator icon association...
echo.
echo To manually set the calculator icon:
echo 1. Right-click on the ProTech executable or shortcut
echo 2. Select "Properties"
echo 3. Click "Change Icon..."
echo 4. Browse to C:\\Windows\\System32\\calc.exe
echo 5. Select the calculator icon
echo 6. Click OK
echo.
echo Alternatively, you can use the Windows Calculator icon directly.
pause
'''
        
        with open("set_calculator_icon.bat", "w") as f:
            f.write(icon_script)
        
        print("✅ تم إنشاء ملف مساعد لتعيين الأيقونة")
        print("✅ Helper file created for setting icon")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء الملف المساعد: {e}")
        return False

def main():
    """Main function"""
    print("🎨 إنشاء أيقونة آلة حاسبة لنظام ProTech")
    print("🎨 Creating Calculator Icon for ProTech System")
    print()
    
    try:
        # Try to create ICO file first
        if create_ico_file():
            print("✅ تم إنشاء أيقونة ICO بنجاح")
        else:
            # Fallback to simple method
            create_simple_icon()
        
        # Also create canvas-based icon
        create_calculator_icon()
        
        print("\n📋 تعليمات تطبيق الأيقونة:")
        print("📋 Instructions for applying the icon:")
        print()
        print("1. إذا تم إنشاء calculator.ico:")
        print("   - انسخ الملف إلى مجلد البرنامج")
        print("   - استخدمه في كود Python")
        print()
        print("2. للتطبيق اليدوي:")
        print("   - انقر بالزر الأيمن على ملف البرنامج")
        print("   - اختر 'خصائص' -> 'تغيير الأيقونة'")
        print("   - اختر أيقونة الآلة الحاسبة من Windows")
        print()
        print("1. If calculator.ico was created:")
        print("   - Copy the file to the program folder")
        print("   - Use it in Python code")
        print()
        print("2. For manual application:")
        print("   - Right-click on the program file")
        print("   - Choose 'Properties' -> 'Change Icon'")
        print("   - Select calculator icon from Windows")
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")

if __name__ == "__main__":
    main()
