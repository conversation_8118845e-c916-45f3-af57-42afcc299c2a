#!/usr/bin/env python3

print("Starting ProTech Accounting System...")

try:
    import flask
    print(f"Flask version: {flask.__version__}")
except ImportError:
    print("Flask not found. Installing...")
    import subprocess
    subprocess.run(["pip", "install", "flask"])

print("Importing app...")
from app import app

print("Starting Flask server...")
print("URL: http://localhost:5000")

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
