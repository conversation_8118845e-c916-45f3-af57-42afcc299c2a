#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔒 GUARANTEED LAUNCHER - Nassar Accounting Program
مشغل مضمون - برنامج ناصر للمحاسبة

هذا المشغل يضمن عدم فقدان البيانات نهائياً
This launcher guarantees no data loss whatsoever
"""

import os
import sys
import json
import shutil
import subprocess
from datetime import datetime

class GuaranteedLauncher:
    def __init__(self):
        self.base_paths = [
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program",
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\nassar program final",
            os.path.dirname(os.path.abspath(__file__))
        ]
        
        self.data_sources = [
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\nassar program final\02_Data_Files\protech_simple_data.json",
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_data.json",
            r"C:\Users\<USER>\Documents\augment-projects\protech\protech_simple_data.json"
        ]
        
        self.program_sources = [
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\nassar program final\01_Main_Program\protech_simple_working.py",
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\protech_simple_working.py",
            r"C:\Users\<USER>\Documents\augment-projects\protech\protech_simple_working.py"
        ]
    
    def find_best_data_file(self):
        """Find the data file with the most recent and complete data"""
        best_file = None
        best_score = 0
        
        print("🔍 البحث عن أفضل ملف بيانات...")
        print("🔍 Searching for best data file...")
        
        for data_file in self.data_sources:
            if os.path.exists(data_file):
                try:
                    with open(data_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # Calculate score based on data completeness
                    score = 0
                    score += len(data.get('products', [])) * 10
                    score += len(data.get('customers', [])) * 5
                    score += len(data.get('suppliers', [])) * 5
                    score += len(data.get('sales', [])) * 3
                    
                    # Bonus for recent modification
                    mod_time = os.path.getmtime(data_file)
                    if mod_time > (datetime.now().timestamp() - 86400):  # Last 24 hours
                        score += 50
                    
                    print(f"📊 {data_file}: Score {score}")
                    print(f"   Products: {len(data.get('products', []))}")
                    print(f"   Customers: {len(data.get('customers', []))}")
                    print(f"   Suppliers: {len(data.get('suppliers', []))}")
                    
                    if score > best_score:
                        best_score = score
                        best_file = data_file
                        
                except Exception as e:
                    print(f"❌ خطأ في قراءة {data_file}: {e}")
        
        if best_file:
            print(f"✅ أفضل ملف بيانات: {best_file}")
            print(f"✅ Best data file: {best_file}")
        
        return best_file
    
    def find_best_program_file(self):
        """Find the most recent program file"""
        best_file = None
        best_time = 0
        
        print("\n🔍 البحث عن أحدث ملف برنامج...")
        print("🔍 Searching for latest program file...")
        
        for program_file in self.program_sources:
            if os.path.exists(program_file):
                mod_time = os.path.getmtime(program_file)
                size = os.path.getsize(program_file)
                
                print(f"📁 {program_file}")
                print(f"   Size: {size:,} bytes")
                print(f"   Modified: {datetime.fromtimestamp(mod_time)}")
                
                if mod_time > best_time and size > 500000:  # At least 500KB
                    best_time = mod_time
                    best_file = program_file
        
        if best_file:
            print(f"✅ أحدث ملف برنامج: {best_file}")
            print(f"✅ Latest program file: {best_file}")
        
        return best_file
    
    def setup_guaranteed_environment(self):
        """Setup guaranteed working environment"""
        print("\n🔧 إعداد البيئة المضمونة...")
        print("🔧 Setting up guaranteed environment...")
        
        # Find best files
        best_data = self.find_best_data_file()
        best_program = self.find_best_program_file()
        
        if not best_data or not best_program:
            print("❌ لم يتم العثور على الملفات المطلوبة")
            print("❌ Required files not found")
            return False
        
        # Setup working directory
        work_dir = r"C:\Users\<USER>\OneDrive\Desktop\accounting program"
        
        try:
            # Copy best data file to working directory
            target_data = os.path.join(work_dir, "protech_simple_data.json")
            shutil.copy2(best_data, target_data)
            print(f"✅ تم نسخ البيانات إلى: {target_data}")
            
            # Copy best program file to working directory
            target_program = os.path.join(work_dir, "protech_simple_working.py")
            shutil.copy2(best_program, target_program)
            print(f"✅ تم نسخ البرنامج إلى: {target_program}")
            
            # Create backup
            backup_data = os.path.join(work_dir, f"protech_simple_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            shutil.copy2(target_data, backup_data)
            print(f"✅ تم إنشاء نسخة احتياطية: {backup_data}")
            
            # Also copy to nassar program final for consistency
            nassar_data_dir = r"C:\Users\<USER>\OneDrive\Desktop\accounting program\nassar program final\02_Data_Files"
            if os.path.exists(nassar_data_dir):
                nassar_data_file = os.path.join(nassar_data_dir, "protech_simple_data.json")
                shutil.copy2(target_data, nassar_data_file)
                print(f"✅ تم نسخ البيانات إلى مجلد ناصر: {nassar_data_file}")
            
            return work_dir, target_program
            
        except Exception as e:
            print(f"❌ خطأ في إعداد البيئة: {e}")
            return False
    
    def launch_program(self, work_dir, program_file):
        """Launch the program with guaranteed data"""
        try:
            print(f"\n🚀 تشغيل البرنامج من: {work_dir}")
            print(f"🚀 Launching program from: {work_dir}")
            
            # Change to working directory
            os.chdir(work_dir)
            
            # Launch program
            subprocess.run([sys.executable, "protech_simple_working.py"])
            
            print("✅ تم إغلاق البرنامج")
            print("✅ Program closed")
            
        except Exception as e:
            print(f"❌ خطأ في تشغيل البرنامج: {e}")
            print(f"❌ Error launching program: {e}")
    
    def run(self):
        """Main run method"""
        print("=" * 70)
        print("🔒 المشغل المضمون - برنامج ناصر للمحاسبة")
        print("🔒 GUARANTEED LAUNCHER - Nassar Accounting Program")
        print("=" * 70)
        print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        # Setup environment
        result = self.setup_guaranteed_environment()
        
        if not result:
            print("\n❌ فشل في إعداد البيئة")
            print("❌ Failed to setup environment")
            input("\nPress Enter to exit...")
            return
        
        work_dir, program_file = result
        
        print(f"\n✅ البيئة جاهزة!")
        print(f"✅ Environment ready!")
        print(f"📁 مجلد العمل: {work_dir}")
        print(f"📁 Working directory: {work_dir}")
        
        # Launch program
        self.launch_program(work_dir, program_file)
        
        print("\n🎉 انتهى التشغيل بنجاح!")
        print("🎉 Execution completed successfully!")
        input("\nPress Enter to exit...")

def main():
    """Main function"""
    launcher = GuaranteedLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
