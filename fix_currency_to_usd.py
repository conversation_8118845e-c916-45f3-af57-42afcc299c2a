#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Fix Currency to USD
تعديل العملة إلى الدولار

Change store balance report currency from Riyal to USD
تغيير عملة تقرير رصيد المحل من الريال إلى الدولار
"""

import os
import shutil
from datetime import datetime

def fix_currency_to_usd():
    """تعديل العملة إلى الدولار"""
    try:
        print("💱 تعديل العملة من الريال إلى الدولار")
        print("💱 Changing Currency from Riyal to USD")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.currency_fix_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Currency conversion methods
        currency_methods = '''
    def get_usd_exchange_rate(self):
        """الحصول على سعر صرف الدولار"""
        return 89500  # 1 USD = 89,500 LBP
    
    def format_usd_amount(self, lbp_amount):
        """تنسيق المبلغ بالدولار"""
        try:
            usd_amount = lbp_amount / self.get_usd_exchange_rate()
            return f"${usd_amount:,.2f}"
        except:
            return "$0.00"
    
    def format_lbp_amount(self, lbp_amount):
        """تنسيق المبلغ بالليرة اللبنانية"""
        try:
            return f"{lbp_amount:,.0f} ل.ل"
        except:
            return "0 ل.ل"
    
    def show_store_balance_usd(self):
        """عرض رصيد المحل بالدولار"""
        try:
            self.report_title.config(text="رصيد المحل بالدولار الأمريكي")
            
            # Get data
            products = self.get_real_products_data()
            sales = self.get_real_sales_data()
            customers = self.get_real_customers_data()
            
            # Calculate totals in LBP first
            total_inventory_cost_lbp = 0
            total_inventory_value_lbp = 0
            total_sales_lbp = 0
            total_customer_debt_lbp = 0
            total_customer_credit_lbp = 0
            
            # Calculate inventory values
            for product in products:
                try:
                    quantity = float(product.get('quantity', 0))
                    base_price = float(product.get('base_price', 0))
                    shop_price = float(product.get('shop_owner_price', base_price * 1.05))
                    
                    total_inventory_cost_lbp += quantity * base_price
                    total_inventory_value_lbp += quantity * shop_price
                except:
                    continue
            
            # Calculate sales total
            for sale in sales:
                try:
                    total_sales_lbp += float(sale.get('total', 0))
                except:
                    continue
            
            # Calculate customer balances
            for customer in customers:
                try:
                    balance = float(customer.get('balance', 0))
                    if balance > 0:
                        total_customer_credit_lbp += balance
                    else:
                        total_customer_debt_lbp += abs(balance)
                except:
                    continue
            
            # Convert to USD
            exchange_rate = self.get_usd_exchange_rate()
            
            inventory_cost_usd = total_inventory_cost_lbp / exchange_rate
            inventory_value_usd = total_inventory_value_lbp / exchange_rate
            sales_usd = total_sales_lbp / exchange_rate
            customer_debt_usd = total_customer_debt_lbp / exchange_rate
            customer_credit_usd = total_customer_credit_lbp / exchange_rate
            
            # Calculate net worth
            net_worth_lbp = total_inventory_value_lbp + total_customer_credit_lbp + total_sales_lbp - total_customer_debt_lbp
            net_worth_usd = net_worth_lbp / exchange_rate
            
            # Create report
            current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            report = "تقرير رصيد المحل بالدولار الأمريكي\\n"
            report += "="*60 + "\\n\\n"
            report += f"التاريخ: {current_time}\\n"
            report += f"سعر الصرف: 1 USD = {exchange_rate:,} ليرة لبنانية\\n\\n"
            
            # Inventory section
            report += "المخزون والبضاعة:\\n"
            report += "-"*30 + "\\n"
            report += f"عدد المنتجات: {len(products)}\\n\\n"
            
            report += "قيمة التكلفة:\\n"
            report += f"• {self.format_lbp_amount(total_inventory_cost_lbp)}\\n"
            report += f"• {self.format_usd_amount(total_inventory_cost_lbp)}\\n\\n"
            
            report += "قيمة البيع المتوقعة:\\n"
            report += f"• {self.format_lbp_amount(total_inventory_value_lbp)}\\n"
            report += f"• {self.format_usd_amount(total_inventory_value_lbp)}\\n\\n"
            
            profit_lbp = total_inventory_value_lbp - total_inventory_cost_lbp
            report += "الربح المتوقع من المخزون:\\n"
            report += f"• {self.format_lbp_amount(profit_lbp)}\\n"
            report += f"• {self.format_usd_amount(profit_lbp)}\\n\\n"
            
            # Sales section
            report += "المبيعات:\\n"
            report += "-"*30 + "\\n"
            report += f"عدد الفواتير: {len(sales)}\\n\\n"
            
            report += "إجمالي المبيعات:\\n"
            report += f"• {self.format_lbp_amount(total_sales_lbp)}\\n"
            report += f"• {self.format_usd_amount(total_sales_lbp)}\\n\\n"
            
            # Customers section
            report += "العملاء:\\n"
            report += "-"*30 + "\\n"
            report += f"عدد العملاء: {len(customers)}\\n\\n"
            
            if total_customer_debt_lbp > 0:
                report += "ديون العملاء (مستحقة للمحل):\\n"
                report += f"• {self.format_lbp_amount(total_customer_debt_lbp)}\\n"
                report += f"• {self.format_usd_amount(total_customer_debt_lbp)}\\n\\n"
            
            if total_customer_credit_lbp > 0:
                report += "أرصدة العملاء (مستحقة للعملاء):\\n"
                report += f"• {self.format_lbp_amount(total_customer_credit_lbp)}\\n"
                report += f"• {self.format_usd_amount(total_customer_credit_lbp)}\\n\\n"
            
            # Total balance
            report += "="*60 + "\\n"
            report += "صافي رصيد المحل:\\n"
            report += "="*60 + "\\n\\n"
            
            report += "إجمالي قيمة المحل:\\n"
            report += f"• {self.format_lbp_amount(net_worth_lbp)}\\n"
            report += f"• {self.format_usd_amount(net_worth_lbp)} 💰\\n\\n"
            
            # Analysis in USD
            report += "التحليل المالي:\\n"
            report += "-"*30 + "\\n"
            
            if net_worth_usd >= 50000:
                report += "🟢 وضع ممتاز (أكثر من $50,000)\\n"
            elif net_worth_usd >= 25000:
                report += "🟡 وضع جيد جداً ($25,000 - $50,000)\\n"
            elif net_worth_usd >= 10000:
                report += "🟠 وضع جيد ($10,000 - $25,000)\\n"
            elif net_worth_usd >= 5000:
                report += "🔵 وضع متوسط ($5,000 - $10,000)\\n"
            else:
                report += "🔴 وضع يحتاج تحسين (أقل من $5,000)\\n"
            
            # Recommendations
            report += "\\nالتوصيات:\\n"
            report += "-"*30 + "\\n"
            
            if customer_debt_usd > customer_credit_usd:
                report += "• متابعة تحصيل ديون العملاء\\n"
            
            if net_worth_usd < 10000:
                report += "• العمل على زيادة رأس المال\\n"
                report += "• تحسين هامش الربح\\n"
            
            if inventory_value_usd > sales_usd * 2:
                report += "• تقليل المخزون الزائد\\n"
                report += "• تحسين دوران المخزون\\n"
            
            report += "• مراقبة سعر صرف الدولار يومياً\\n"
            report += "• تحديث الأسعار حسب سعر الصرف\\n"
            report += "• الاحتفاظ ببعض الأرباح بالدولار\\n"
            report += "• مراجعة هذا التقرير شهرياً\\n"
            
            # Exchange rate note
            report += "\\n" + "="*60 + "\\n"
            report += "ملاحظة هامة:\\n"
            report += f"• سعر الصرف المستخدم: 1 USD = {exchange_rate:,} ل.ل\\n"
            report += "• يُنصح بتحديث سعر الصرف يومياً\\n"
            report += "• الأرقام تقريبية وقد تختلف حسب سعر الصرف الفعلي\\n"
            
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, report)
            
            print("✅ تم عرض تقرير رصيد المحل بالدولار")
            
        except Exception as e:
            print(f"❌ خطأ في عرض تقرير الدولار: {e}")
            error_msg = "خطأ في تحميل تقرير رصيد المحل بالدولار\\n"
            error_msg += "يرجى التحقق من البيانات والمحاولة مرة أخرى"
            self.report_text.delete(1.0, tk.END)
            self.report_text.insert(1.0, error_msg)
'''
        
        # Find and replace the store balance method
        # Look for existing store balance methods
        patterns_to_replace = [
            "def show_store_balance_riyal(",
            "def show_store_balance(",
            "def show_basic_store_balance(",
            "رصيد المحل بالريال",
            "Store Balance in Riyal",
            "SAR",
            "ريال سعودي"
        ]
        
        # Add the new USD methods
        last_method = content.rfind("\n    def show_basic_reports_fallback(")
        if last_method != -1:
            content = content[:last_method] + currency_methods + content[last_method:]
        else:
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + currency_methods + content[last_method:]
        
        # Replace any existing riyal references with USD
        replacements = [
            ("ريال سعودي", "دولار أمريكي"),
            ("SAR", "USD"),
            ("رصيد المحل بالريال", "رصيد المحل بالدولار"),
            ("Store Balance in Riyal", "Store Balance in USD"),
            ("show_store_balance_riyal", "show_store_balance_usd"),
            ("def show_store_balance(", "def show_store_balance_old("),
        ]
        
        for old_text, new_text in replacements:
            content = content.replace(old_text, new_text)
        
        # Update button text if exists
        if 'btn6 = tk.Button(sidebar, text="رصيد المحل"' in content:
            # Find the button command and update it
            button_pattern = 'btn6 = tk.Button(sidebar, text="رصيد المحل"'
            button_pos = content.find(button_pattern)
            if button_pos != -1:
                # Find the command part
                command_start = content.find("command=", button_pos)
                if command_start != -1:
                    command_end = content.find(")", command_start)
                    if command_end != -1:
                        # Replace the command
                        old_command = content[command_start:command_end]
                        new_command = "command=self.show_store_balance_usd"
                        content = content.replace(old_command, new_command)
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم تعديل العملة إلى الدولار")
        
        # Test compilation
        import subprocess
        import sys
        
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore backup
            shutil.copy2(backup_name, protech_file)
            print("🔄 تم استعادة النسخة الاحتياطية")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في تعديل العملة: {e}")
        return False

def main():
    """Main function"""
    print("💱 تعديل عملة رصيد المحل من الريال إلى الدولار")
    print("💱 Changing Store Balance Currency from Riyal to USD")
    print("="*70)
    
    if fix_currency_to_usd():
        print("\n🎉 تم تعديل العملة بنجاح!")
        
        print("\n💱 التغييرات المطبقة:")
        print("• 🔄 تغيير العملة من الريال السعودي إلى الدولار الأمريكي")
        print("• 💰 عرض الأرقام بالدولار والليرة اللبنانية")
        print("• 📊 تحليل مالي محسن بالدولار")
        print("• 🔢 سعر صرف: 1 USD = 89,500 ليرة لبنانية")
        
        print("\n📊 ميزات التقرير الجديد:")
        print("• عرض قيمة المخزون بالدولار")
        print("• إجمالي المبيعات بالدولار")
        print("• أرصدة العملاء بالدولار")
        print("• صافي رصيد المحل بالدولار")
        print("• تحليل مالي متقدم")
        print("• توصيات مالية مفيدة")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. اذهب إلى صفحة التقارير")
        print("3. انقر على زر 'رصيد المحل'")
        print("4. ستظهر الأرقام بالدولار والليرة")
        
        print("\n💡 ملاحظات مهمة:")
        print("• سعر الصرف قابل للتحديث")
        print("• الأرقام تظهر بالعملتين للمقارنة")
        print("• التحليل المالي محسن للدولار")
        print("• توصيات مالية مخصصة")
        
    else:
        print("\n❌ فشل في تعديل العملة")
    
    print("\n🔧 تم الانتهاء من تعديل العملة")

if __name__ == "__main__":
    main()
