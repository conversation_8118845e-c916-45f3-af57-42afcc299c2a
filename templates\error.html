<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>خطأ - نظام ProTech للمحاسبة</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .ltr { direction: ltr; text-align: left; }
        .rtl { direction: rtl; text-align: right; }
    </style>
</head>
<body class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="max-w-md w-full mx-auto">
        <div class="bg-white rounded-lg shadow-lg p-8 text-center">
            <!-- Error Icon -->
            <div class="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-6">
                <svg class="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
            </div>

            <!-- Error Code -->
            {% if error_code %}
            <div class="mb-4">
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                    خطأ {{ error_code }}
                </span>
            </div>
            {% endif %}

            <!-- Error Message in Arabic -->
            <h1 class="text-2xl font-bold text-gray-900 mb-4 rtl">
                {{ error or 'حدث خطأ غير متوقع' }}
            </h1>

            <!-- Error Message in English -->
            {% if error_en %}
            <p class="text-gray-600 mb-6 ltr">
                {{ error_en }}
            </p>
            {% endif %}

            <!-- Error Details -->
            <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h3 class="text-sm font-medium text-gray-900 mb-2 rtl">تفاصيل الخطأ:</h3>
                <div class="text-sm text-gray-600 space-y-1">
                    {% if error_code == 404 %}
                    <p class="rtl">• الصفحة المطلوبة غير موجودة</p>
                    <p class="rtl">• تحقق من صحة الرابط</p>
                    <p class="rtl">• قد تكون الصفحة قد تم نقلها أو حذفها</p>
                    {% elif error_code == 500 %}
                    <p class="rtl">• حدث خطأ داخلي في الخادم</p>
                    <p class="rtl">• يرجى المحاولة مرة أخرى لاحقاً</p>
                    <p class="rtl">• إذا استمر الخطأ، يرجى الاتصال بالدعم الفني</p>
                    {% elif error_code == 403 %}
                    <p class="rtl">• غير مسموح لك بالوصول إلى هذه الصفحة</p>
                    <p class="rtl">• تحقق من صلاحياتك</p>
                    <p class="rtl">• قد تحتاج إلى تسجيل الدخول</p>
                    {% else %}
                    <p class="rtl">• حدث خطأ غير متوقع</p>
                    <p class="rtl">• يرجى المحاولة مرة أخرى</p>
                    {% endif %}
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="space-y-3">
                <button onclick="goBack()" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                    العودة للصفحة السابقة
                </button>
                
                <a href="{{ url_for('dashboard') }}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out text-decoration-none">
                    العودة للصفحة الرئيسية
                </a>
                
                <button onclick="refreshPage()" class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition duration-150 ease-in-out">
                    تحديث الصفحة
                </button>
            </div>

            <!-- Support Information -->
            <div class="mt-8 pt-6 border-t border-gray-200">
                <p class="text-xs text-gray-500 rtl">
                    إذا كنت تواجه مشاكل مستمرة، يرجى الاتصال بالدعم الفني
                </p>
                <div class="mt-2 flex items-center justify-center space-x-4 space-x-reverse">
                    <span class="text-xs text-gray-400">نظام ProTech للمحاسبة</span>
                    <span class="text-xs text-gray-400">•</span>
                    <span class="text-xs text-gray-400">Python Flask</span>
                </div>
            </div>
        </div>

        <!-- Additional Help -->
        <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
            <div class="flex items-start">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="mr-3">
                    <h3 class="text-sm font-medium text-blue-800 rtl">نصائح مفيدة:</h3>
                    <div class="mt-1 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1 rtl">
                            <li>تأكد من اتصالك بالإنترنت</li>
                            <li>امسح ذاكرة التخزين المؤقت للمتصفح</li>
                            <li>جرب استخدام متصفح آخر</li>
                            <li>تحقق من إعدادات الجافا سكريبت</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = "{{ url_for('dashboard') }}";
            }
        }

        function refreshPage() {
            window.location.reload();
        }

        // Auto-redirect after 30 seconds for 404 errors
        {% if error_code == 404 %}
        setTimeout(function() {
            if (confirm('هل تريد العودة للصفحة الرئيسية؟')) {
                window.location.href = "{{ url_for('dashboard') }}";
            }
        }, 30000);
        {% endif %}

        // Log error for debugging
        console.error('ProTech Error:', {
            code: {{ error_code or 'null' }},
            message: '{{ error or "Unknown error" }}',
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent
        });
    </script>
</body>
</html>
