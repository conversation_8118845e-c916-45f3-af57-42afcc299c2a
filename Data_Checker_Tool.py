#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔍 Data Checker Tool - Nassar Program
أداة فحص البيانات - برنامج ناصر

أداة لفحص وتحليل بيانات البرنامج
Tool for checking and analyzing program data
"""

import os
import json
from datetime import datetime
import tkinter as tk
from tkinter import messagebox, scrolledtext

class DataCheckerTool:
    def __init__(self):
        self.base_path = self.find_base_path()
        self.data_file = os.path.join(self.base_path, "02_Data_Files", "protech_simple_data.json")
        
    def find_base_path(self):
        """Find the base path of nassar program final"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        
        # If we're in Tools folder, go up one level
        if os.path.basename(current_dir) == "04_Tools_Utilities":
            return os.path.dirname(current_dir)
        
        return current_dir
    
    def load_data(self):
        """Load and validate data file"""
        try:
            if not os.path.exists(self.data_file):
                return None, "ملف البيانات غير موجود\nData file not found"
            
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return data, None
            
        except json.JSONDecodeError as e:
            return None, f"خطأ في تنسيق JSON:\nJSON format error:\n{str(e)}"
        except Exception as e:
            return None, f"خطأ في قراءة الملف:\nFile read error:\n{str(e)}"
    
    def analyze_data(self, data):
        """Analyze the data and generate report"""
        report = []
        report.append("📊 تقرير تحليل البيانات / Data Analysis Report")
        report.append("=" * 60)
        report.append(f"📅 تاريخ التحليل / Analysis Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append(f"📁 ملف البيانات / Data File: {self.data_file}")
        report.append("")
        
        # Basic counts
        suppliers = data.get('suppliers', [])
        products = data.get('products', [])
        customers = data.get('customers', [])
        sales = data.get('sales', [])
        
        report.append("📈 الإحصائيات الأساسية / Basic Statistics:")
        report.append(f"   🏢 الموردين / Suppliers: {len(suppliers)}")
        report.append(f"   📦 المنتجات / Products: {len(products)}")
        report.append(f"   👥 العملاء / Customers: {len(customers)}")
        report.append(f"   💰 المبيعات / Sales: {len(sales)}")
        report.append("")
        
        # Products analysis
        if products:
            report.append("📦 تحليل المنتجات / Products Analysis:")
            total_stock_value = sum(p.get('price', 0) * p.get('stock', 0) for p in products)
            low_stock_products = [p for p in products if p.get('stock', 0) <= p.get('min_stock', 0)]
            
            report.append(f"   💵 قيمة المخزون الإجمالية / Total Stock Value: ${total_stock_value:,.2f}")
            report.append(f"   ⚠️ منتجات بمخزون منخفض / Low Stock Products: {len(low_stock_products)}")
            
            if low_stock_products:
                report.append("   📋 المنتجات بمخزون منخفض / Low Stock Items:")
                for product in low_stock_products[:5]:  # Show first 5
                    report.append(f"      • {product.get('name', 'N/A')} - المخزون/Stock: {product.get('stock', 0)}")
            report.append("")
        
        # Customers analysis
        if customers:
            report.append("👥 تحليل العملاء / Customers Analysis:")
            total_balance = sum(c.get('balance', 0) for c in customers)
            customer_types = {}
            for customer in customers:
                ctype = customer.get('type', 'Unknown')
                customer_types[ctype] = customer_types.get(ctype, 0) + 1
            
            report.append(f"   💰 إجمالي الأرصدة / Total Balances: ${total_balance:,.2f}")
            report.append("   📊 أنواع العملاء / Customer Types:")
            for ctype, count in customer_types.items():
                report.append(f"      • {ctype}: {count}")
            report.append("")
        
        # Sales analysis
        if sales:
            report.append("💰 تحليل المبيعات / Sales Analysis:")
            total_sales = sum(s.get('total', 0) for s in sales)
            total_paid = sum(s.get('paid', 0) for s in sales)
            outstanding = total_sales - total_paid
            
            report.append(f"   💵 إجمالي المبيعات / Total Sales: ${total_sales:,.2f}")
            report.append(f"   💳 إجمالي المدفوع / Total Paid: ${total_paid:,.2f}")
            report.append(f"   📋 المبلغ المستحق / Outstanding: ${outstanding:,.2f}")
            report.append("")
        
        # Data integrity checks
        report.append("🔍 فحص سلامة البيانات / Data Integrity Checks:")
        
        # Check for duplicate barcodes
        if products:
            barcodes = [p.get('barcode') for p in products if p.get('barcode')]
            duplicate_barcodes = len(barcodes) - len(set(barcodes))
            if duplicate_barcodes > 0:
                report.append(f"   ⚠️ باركودات مكررة / Duplicate Barcodes: {duplicate_barcodes}")
            else:
                report.append("   ✅ لا توجد باركودات مكررة / No duplicate barcodes")
        
        # Check for missing required fields
        missing_fields = []
        for i, product in enumerate(products):
            if not product.get('name'):
                missing_fields.append(f"Product {i+1}: missing name")
            if not product.get('barcode'):
                missing_fields.append(f"Product {i+1}: missing barcode")
        
        if missing_fields:
            report.append(f"   ⚠️ حقول مفقودة / Missing Fields: {len(missing_fields)}")
        else:
            report.append("   ✅ جميع الحقول المطلوبة موجودة / All required fields present")
        
        report.append("")
        report.append("✅ انتهى التحليل / Analysis Complete")
        
        return "\n".join(report)
    
    def show_data_report(self):
        """Show detailed data report"""
        data, error = self.load_data()
        
        if error:
            messagebox.showerror("خطأ / Error", error)
            return
        
        report = self.analyze_data(data)
        
        # Create report window
        report_window = tk.Toplevel()
        report_window.title("تقرير البيانات / Data Report")
        report_window.geometry("800x600")
        
        # Text widget with scrollbar
        text_widget = scrolledtext.ScrolledText(
            report_window,
            wrap=tk.WORD,
            font=('Courier New', 10),
            bg='#f8f9fa',
            fg='#212529'
        )
        text_widget.pack(fill='both', expand=True, padx=10, pady=10)
        
        # Insert report
        text_widget.insert('1.0', report)
        text_widget.config(state='disabled')  # Make read-only
        
        # Close button
        close_btn = tk.Button(
            report_window,
            text="إغلاق / Close",
            command=report_window.destroy,
            font=('Arial', 12, 'bold'),
            bg='#6c757d',
            fg='white'
        )
        close_btn.pack(pady=10)
    
    def quick_check(self):
        """Quick data check with summary"""
        data, error = self.load_data()
        
        if error:
            messagebox.showerror("خطأ / Error", error)
            return
        
        # Quick summary
        suppliers_count = len(data.get('suppliers', []))
        products_count = len(data.get('products', []))
        customers_count = len(data.get('customers', []))
        sales_count = len(data.get('sales', []))
        
        file_size = os.path.getsize(self.data_file)
        last_modified = datetime.fromtimestamp(os.path.getmtime(self.data_file))
        
        summary = f"""
📊 ملخص سريع للبيانات / Quick Data Summary

📁 معلومات الملف / File Info:
   📏 حجم الملف / File Size: {file_size:,} bytes
   📅 آخر تعديل / Last Modified: {last_modified.strftime('%Y-%m-%d %H:%M:%S')}

📈 الإحصائيات / Statistics:
   🏢 الموردين / Suppliers: {suppliers_count}
   📦 المنتجات / Products: {products_count}
   👥 العملاء / Customers: {customers_count}
   💰 المبيعات / Sales: {sales_count}

✅ حالة البيانات / Data Status: سليمة / Healthy
        """
        
        messagebox.showinfo("فحص سريع / Quick Check", summary)
    
    def run(self):
        """Run the data checker GUI"""
        root = tk.Tk()
        root.title("أداة فحص البيانات - برنامج ناصر / Data Checker Tool - Nassar Program")
        root.geometry("400x300")
        root.configure(bg='#f0f0f0')
        
        # Title
        title_label = tk.Label(
            root,
            text="🔍 أداة فحص البيانات\nData Checker Tool",
            font=('Arial', 16, 'bold'),
            bg='#f0f0f0',
            fg='#2563eb'
        )
        title_label.pack(pady=20)
        
        # Buttons frame
        buttons_frame = tk.Frame(root, bg='#f0f0f0')
        buttons_frame.pack(pady=20)
        
        # Quick check button
        quick_btn = tk.Button(
            buttons_frame,
            text="⚡ فحص سريع\nQuick Check",
            font=('Arial', 12, 'bold'),
            bg='#10b981',
            fg='white',
            command=self.quick_check,
            width=18,
            height=3
        )
        quick_btn.pack(pady=10)
        
        # Detailed report button
        report_btn = tk.Button(
            buttons_frame,
            text="📊 تقرير مفصل\nDetailed Report",
            font=('Arial', 12, 'bold'),
            bg='#3b82f6',
            fg='white',
            command=self.show_data_report,
            width=18,
            height=3
        )
        report_btn.pack(pady=10)
        
        # Exit button
        exit_btn = tk.Button(
            buttons_frame,
            text="❌ إغلاق\nClose",
            font=('Arial', 12, 'bold'),
            bg='#ef4444',
            fg='white',
            command=root.destroy,
            width=18,
            height=2
        )
        exit_btn.pack(pady=10)
        
        root.mainloop()

def main():
    """Main function"""
    tool = DataCheckerTool()
    tool.run()

if __name__ == "__main__":
    main()
