#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🚀 مشغل برنامج ناصر النهائي - النسخة المضمونة
LAUNCH NASSAR FINAL - Guaranteed Working Version

مشغل بسيط ومضمون لبرنامج ناصر للمحاسبة
Simple and guaranteed launcher for Nassar Accounting Program
"""

import os
import sys
import subprocess
import tkinter as tk
from tkinter import messagebox

def show_welcome():
    """عرض رسالة ترحيب"""
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    welcome_msg = """
🏢 مرحباً بك في برنامج ناصر النهائي للمحاسبة
Welcome to Nassar Final Accounting Program

✨ النسخة المضمونة العمل - Guaranteed Working Version

📋 الميزات المتوفرة:
• إدارة المخزون مع الباركود
• إدارة العملاء والموردين  
• نظام المبيعات والفواتير
• التقارير والإحصائيات
• واجهة ثنائية اللغة
• حماية كاملة للبيانات

🔒 مضمون عدم فقدان البيانات
🔒 Guaranteed No Data Loss

🚀 هل تريد تشغيل البرنامج الآن؟
Do you want to start the program now?
    """
    
    result = messagebox.askyesno(
        "برنامج ناصر النهائي - Nassar Final Program",
        welcome_msg
    )
    
    root.destroy()
    return result

def launch_program():
    """تشغيل البرنامج"""
    try:
        print("🚀 تشغيل برنامج ناصر النهائي...")
        print("🚀 Starting Nassar Final Program...")
        
        # البحث عن ملف البرنامج
        program_files = [
            "NASSAR_PROGRAM_FINAL_WORKING.py",
            r"C:\Users\<USER>\OneDrive\Desktop\NASSAR_PROGRAM_FINAL_WORKING.py",
            r"C:\Users\<USER>\OneDrive\Desktop\accounting program\NASSAR_PROGRAM_FINAL_WORKING.py"
        ]
        
        program_file = None
        for file_path in program_files:
            if os.path.exists(file_path):
                program_file = file_path
                print(f"✅ تم العثور على البرنامج: {file_path}")
                break
        
        if not program_file:
            print("❌ لم يتم العثور على ملف البرنامج")
            messagebox.showerror(
                "خطأ - Error",
                "لم يتم العثور على ملف البرنامج\nProgram file not found"
            )
            return False
        
        # تشغيل البرنامج
        subprocess.run([sys.executable, program_file])
        
        print("✅ تم إغلاق البرنامج بنجاح")
        print("✅ Program closed successfully")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل البرنامج: {e}")
        messagebox.showerror(
            "خطأ - Error",
            f"فشل في تشغيل البرنامج\nFailed to launch program:\n{str(e)}"
        )
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🏢 مشغل برنامج ناصر النهائي للمحاسبة")
    print("🏢 Nassar Final Accounting Program Launcher")
    print("=" * 60)
    print("🔒 النسخة المضمونة العمل - Guaranteed Working Version")
    print("=" * 60)
    
    # عرض رسالة الترحيب
    if show_welcome():
        # تشغيل البرنامج
        success = launch_program()
        
        if success:
            print("🎉 تم تشغيل البرنامج بنجاح!")
            print("🎉 Program launched successfully!")
        else:
            print("❌ فشل في تشغيل البرنامج")
            print("❌ Failed to launch program")
    else:
        print("❌ تم إلغاء تشغيل البرنامج")
        print("❌ Program launch cancelled")
    
    input("\nاضغط Enter للخروج / Press Enter to exit...")

if __name__ == "__main__":
    main()
