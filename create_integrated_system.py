#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Integrated System
إنشاء نظام متكامل

Create consistent integration between program parts for better interactivity and organized data storage
إنشاء ربط متناسق بين أجزاء البرنامج لتفاعل أفضل وتخزين منظم للبيانات
"""

import os
import shutil
from datetime import datetime

def create_integrated_system():
    """إنشاء نظام متكامل ومترابط"""
    try:
        print("🔗 إنشاء نظام متكامل ومترابط")
        print("🔗 Creating Integrated and Interactive System")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.integrated_system_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add integrated system methods
        integrated_methods = '''
    def __init__(self):
        """تهيئة النظام المتكامل"""
        super().__init__()
        
        # تهيئة قاعدة البيانات المتكاملة
        self.init_integrated_database()
        
        # تهيئة نظام الأحداث
        self.init_event_system()
        
        # تهيئة نظام التحديث التلقائي
        self.init_auto_update_system()
        
        # تهيئة نظام التخزين المنظم
        self.init_organized_storage()
        
        # تحميل البيانات المحفوظة
        self.load_all_data()
        
        print("✅ تم تهيئة النظام المتكامل")
    
    def init_integrated_database(self):
        """تهيئة قاعدة البيانات المتكاملة"""
        try:
            import sqlite3
            
            # إنشاء قاعدة البيانات الرئيسية
            self.db_path = "protech_integrated.db"
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()
            
            # إنشاء الجداول المترابطة
            self.create_integrated_tables()
            
            print("✅ تم تهيئة قاعدة البيانات المتكاملة")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            self.conn = None
            self.cursor = None
    
    def create_integrated_tables(self):
        """إنشاء الجداول المترابطة"""
        try:
            # جدول المنتجات المحسن
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    category TEXT DEFAULT 'غير مصنف',
                    barcode TEXT UNIQUE,
                    quantity INTEGER DEFAULT 0,
                    unit TEXT DEFAULT 'قطعة',
                    base_price REAL DEFAULT 0,
                    shop_owner_price REAL DEFAULT 0,
                    wholesale_price REAL DEFAULT 0,
                    retail_price REAL DEFAULT 0,
                    distributor_price REAL DEFAULT 0,
                    min_stock INTEGER DEFAULT 10,
                    supplier_id INTEGER,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            """)
            
            # جدول العملاء المحسن
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    customer_type TEXT DEFAULT 'تجزئة',
                    balance REAL DEFAULT 0,
                    credit_limit REAL DEFAULT 0,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول الموردين
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    contact_person TEXT,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول المبيعات المحسن
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    customer_id INTEGER,
                    customer_name TEXT,
                    customer_type TEXT,
                    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    subtotal REAL DEFAULT 0,
                    discount_amount REAL DEFAULT 0,
                    tax_amount REAL DEFAULT 0,
                    total_amount REAL NOT NULL,
                    paid_amount REAL DEFAULT 0,
                    balance_due REAL DEFAULT 0,
                    payment_status TEXT DEFAULT 'مكتمل',
                    notes TEXT,
                    created_by TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            """)
            
            # جدول تفاصيل المبيعات
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS sale_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sale_id INTEGER NOT NULL,
                    product_id INTEGER,
                    product_name TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    FOREIGN KEY (sale_id) REFERENCES sales (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # جدول حركات المخزون
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS inventory_movements (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER NOT NULL,
                    movement_type TEXT NOT NULL, -- 'in', 'out', 'adjustment'
                    quantity REAL NOT NULL,
                    reference_type TEXT, -- 'sale', 'purchase', 'adjustment'
                    reference_id INTEGER,
                    notes TEXT,
                    movement_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            """)
            
            # جدول سجل الأنشطة
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    action_type TEXT NOT NULL,
                    table_name TEXT,
                    record_id INTEGER,
                    old_values TEXT,
                    new_values TEXT,
                    user_name TEXT,
                    action_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # جدول الإعدادات
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    setting_key TEXT UNIQUE NOT NULL,
                    setting_value TEXT,
                    setting_type TEXT DEFAULT 'string',
                    description TEXT,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # إنشاء الفهارس لتحسين الأداء
            self.create_database_indexes()
            
            self.conn.commit()
            print("✅ تم إنشاء الجداول المترابطة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")
    
    def create_database_indexes(self):
        """إنشاء الفهارس لتحسين الأداء"""
        try:
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_products_name ON products(name)",
                "CREATE INDEX IF NOT EXISTS idx_products_barcode ON products(barcode)",
                "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category)",
                "CREATE INDEX IF NOT EXISTS idx_customers_name ON customers(name)",
                "CREATE INDEX IF NOT EXISTS idx_customers_phone ON customers(phone)",
                "CREATE INDEX IF NOT EXISTS idx_sales_date ON sales(sale_date)",
                "CREATE INDEX IF NOT EXISTS idx_sales_customer ON sales(customer_id)",
                "CREATE INDEX IF NOT EXISTS idx_sale_items_sale ON sale_items(sale_id)",
                "CREATE INDEX IF NOT EXISTS idx_sale_items_product ON sale_items(product_id)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_product ON inventory_movements(product_id)",
                "CREATE INDEX IF NOT EXISTS idx_inventory_date ON inventory_movements(movement_date)"
            ]
            
            for index_sql in indexes:
                self.cursor.execute(index_sql)
            
            print("✅ تم إنشاء الفهارس")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء الفهارس: {e}")
    
    def init_event_system(self):
        """تهيئة نظام الأحداث للتفاعل بين الأجزاء"""
        try:
            self.event_listeners = {}
            self.event_queue = []
            
            # تسجيل المستمعين للأحداث
            self.register_event_listeners()
            
            print("✅ تم تهيئة نظام الأحداث")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام الأحداث: {e}")
    
    def register_event_listeners(self):
        """تسجيل المستمعين للأحداث"""
        try:
            # أحداث المنتجات
            self.register_event('product_added', self.on_product_added)
            self.register_event('product_updated', self.on_product_updated)
            self.register_event('product_deleted', self.on_product_deleted)
            
            # أحداث العملاء
            self.register_event('customer_added', self.on_customer_added)
            self.register_event('customer_updated', self.on_customer_updated)
            
            # أحداث المبيعات
            self.register_event('sale_completed', self.on_sale_completed)
            self.register_event('sale_cancelled', self.on_sale_cancelled)
            
            # أحداث المخزون
            self.register_event('inventory_updated', self.on_inventory_updated)
            self.register_event('low_stock_alert', self.on_low_stock_alert)
            
            print("✅ تم تسجيل مستمعي الأحداث")
            
        except Exception as e:
            print(f"❌ خطأ في تسجيل الأحداث: {e}")
    
    def register_event(self, event_name, callback):
        """تسجيل مستمع حدث"""
        if event_name not in self.event_listeners:
            self.event_listeners[event_name] = []
        self.event_listeners[event_name].append(callback)
    
    def emit_event(self, event_name, data=None):
        """إطلاق حدث"""
        try:
            if event_name in self.event_listeners:
                for callback in self.event_listeners[event_name]:
                    try:
                        callback(data)
                    except Exception as e:
                        print(f"❌ خطأ في معالج الحدث {event_name}: {e}")
            
            # تسجيل الحدث في السجل
            self.log_activity(event_name, data)
            
        except Exception as e:
            print(f"❌ خطأ في إطلاق الحدث {event_name}: {e}")
    
    def init_auto_update_system(self):
        """تهيئة نظام التحديث التلقائي"""
        try:
            self.auto_update_enabled = True
            self.update_intervals = {
                'reports': 30000,  # 30 ثانية
                'inventory': 60000,  # دقيقة واحدة
                'customer_balances': 120000  # دقيقتان
            }
            
            # بدء التحديث التلقائي
            self.start_auto_updates()
            
            print("✅ تم تهيئة نظام التحديث التلقائي")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة التحديث التلقائي: {e}")
    
    def start_auto_updates(self):
        """بدء التحديث التلقائي"""
        try:
            if hasattr(self, 'root') and self.auto_update_enabled:
                # تحديث التقارير
                self.root.after(self.update_intervals['reports'], self.auto_update_reports)
                
                # تحديث المخزون
                self.root.after(self.update_intervals['inventory'], self.auto_update_inventory)
                
                # تحديث أرصدة العملاء
                self.root.after(self.update_intervals['customer_balances'], self.auto_update_customer_balances)
            
        except Exception as e:
            print(f"❌ خطأ في بدء التحديث التلقائي: {e}")
    
    def init_organized_storage(self):
        """تهيئة نظام التخزين المنظم"""
        try:
            # إنشاء مجلدات التخزين المنظم
            self.storage_paths = {
                'data': 'data',
                'backups': 'data/backups',
                'exports': 'data/exports',
                'imports': 'data/imports',
                'logs': 'data/logs',
                'reports': 'data/reports'
            }
            
            # إنشاء المجلدات
            for path in self.storage_paths.values():
                os.makedirs(path, exist_ok=True)
            
            # تهيئة نظام النسخ الاحتياطي التلقائي
            self.init_auto_backup()
            
            print("✅ تم تهيئة نظام التخزين المنظم")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة التخزين المنظم: {e}")
    
    def init_auto_backup(self):
        """تهيئة النسخ الاحتياطي التلقائي"""
        try:
            self.backup_enabled = True
            self.backup_interval = 3600000  # ساعة واحدة
            
            if hasattr(self, 'root') and self.backup_enabled:
                self.root.after(self.backup_interval, self.create_auto_backup)
            
            print("✅ تم تهيئة النسخ الاحتياطي التلقائي")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة النسخ الاحتياطي: {e}")
    
    def load_all_data(self):
        """تحميل جميع البيانات من قاعدة البيانات"""
        try:
            if self.conn:
                # تحميل المنتجات
                self.products = self.load_products_from_db()
                
                # تحميل العملاء
                self.customers = self.load_customers_from_db()
                
                # تحميل الموردين
                self.suppliers = self.load_suppliers_from_db()
                
                # تحميل المبيعات
                self.sales = self.load_sales_from_db()
                
                # تحميل الإعدادات
                self.settings = self.load_settings_from_db()
                
                print("✅ تم تحميل جميع البيانات")
            else:
                # تحميل من ملفات JSON كبديل
                self.load_data_from_json()
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            self.init_empty_data()
    
    def load_products_from_db(self):
        """تحميل المنتجات من قاعدة البيانات"""
        try:
            self.cursor.execute("SELECT * FROM products WHERE is_active = 1")
            rows = self.cursor.fetchall()
            
            products = []
            for row in rows:
                product = {
                    'id': row[0],
                    'name': row[1],
                    'category': row[2],
                    'barcode': row[3],
                    'quantity': row[4],
                    'unit': row[5],
                    'base_price': row[6],
                    'shop_owner_price': row[7],
                    'wholesale_price': row[8],
                    'retail_price': row[9],
                    'distributor_price': row[10],
                    'min_stock': row[11],
                    'supplier_id': row[12],
                    'notes': row[13],
                    'is_active': row[14],
                    'created_date': row[15],
                    'updated_date': row[16]
                }
                products.append(product)
            
            return products
            
        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات: {e}")
            return []
    
    def load_customers_from_db(self):
        """تحميل العملاء من قاعدة البيانات"""
        try:
            self.cursor.execute("SELECT * FROM customers WHERE is_active = 1")
            rows = self.cursor.fetchall()
            
            customers = []
            for row in rows:
                customer = {
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'email': row[3],
                    'address': row[4],
                    'customer_type': row[5],
                    'balance': row[6],
                    'credit_limit': row[7],
                    'notes': row[8],
                    'is_active': row[9],
                    'created_date': row[10],
                    'updated_date': row[11]
                }
                customers.append(customer)
            
            return customers
            
        except Exception as e:
            print(f"❌ خطأ في تحميل العملاء: {e}")
            return []

def apply_integrated_system():
    """تطبيق النظام المتكامل على ProTech"""
    try:
        print("\n🔗 تطبيق النظام المتكامل على ProTech")

        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")

        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()

        # Add integrated system import
        if "import sqlite3" not in content:
            import_section = content.find("import tkinter as tk")
            if import_section != -1:
                content = content[:import_section] + "import sqlite3\n" + content[import_section:]

        # Find the class definition
        class_match = content.find("class ")
        if class_match != -1:
            class_end = content.find(":", class_match)
            if class_end != -1:
                # Add integrated methods after class definition
                integrated_code = '''
    def __init__(self):
        """تهيئة النظام المتكامل"""
        # تهيئة الواجهة الأساسية
        self.root = tk.Tk()
        self.root.title("ProTech - نظام المحاسبة المتكامل")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # تهيئة قاعدة البيانات المتكاملة
        self.init_integrated_database()

        # تهيئة نظام الأحداث
        self.init_event_system()

        # تهيئة نظام التخزين المنظم
        self.init_organized_storage()

        # تحميل البيانات
        self.load_all_data()

        # إنشاء الواجهة
        self.create_interface()

        print("✅ تم تهيئة النظام المتكامل")

    def init_integrated_database(self):
        """تهيئة قاعدة البيانات المتكاملة"""
        try:
            self.db_path = "protech_integrated.db"
            self.conn = sqlite3.connect(self.db_path)
            self.cursor = self.conn.cursor()

            # إنشاء الجداول الأساسية
            self.create_basic_tables()

            print("✅ تم تهيئة قاعدة البيانات المتكاملة")

        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            self.conn = None
            self.cursor = None

    def create_basic_tables(self):
        """إنشاء الجداول الأساسية"""
        try:
            # جدول المنتجات
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    category TEXT DEFAULT 'غير مصنف',
                    barcode TEXT,
                    quantity INTEGER DEFAULT 0,
                    unit TEXT DEFAULT 'قطعة',
                    base_price REAL DEFAULT 0,
                    shop_owner_price REAL DEFAULT 0,
                    wholesale_price REAL DEFAULT 0,
                    retail_price REAL DEFAULT 0,
                    distributor_price REAL DEFAULT 0,
                    min_stock INTEGER DEFAULT 10,
                    notes TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول العملاء
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    phone TEXT,
                    email TEXT,
                    customer_type TEXT DEFAULT 'تجزئة',
                    balance REAL DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول المبيعات
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS sales (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_number TEXT UNIQUE NOT NULL,
                    customer_name TEXT,
                    customer_type TEXT,
                    sale_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    total_amount REAL NOT NULL,
                    paid_amount REAL DEFAULT 0,
                    balance_due REAL DEFAULT 0,
                    status TEXT DEFAULT 'مكتمل'
                )
            """)

            # جدول تفاصيل المبيعات
            self.cursor.execute("""
                CREATE TABLE IF NOT EXISTS sale_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sale_id INTEGER NOT NULL,
                    product_name TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    unit_price REAL NOT NULL,
                    total_price REAL NOT NULL,
                    FOREIGN KEY (sale_id) REFERENCES sales (id)
                )
            """)

            self.conn.commit()
            print("✅ تم إنشاء الجداول الأساسية")

        except Exception as e:
            print(f"❌ خطأ في إنشاء الجداول: {e}")

    def init_event_system(self):
        """تهيئة نظام الأحداث"""
        try:
            self.event_listeners = {}

            # تسجيل الأحداث الأساسية
            self.register_event('product_added', self.on_product_added)
            self.register_event('sale_completed', self.on_sale_completed)
            self.register_event('data_changed', self.on_data_changed)

            print("✅ تم تهيئة نظام الأحداث")

        except Exception as e:
            print(f"❌ خطأ في تهيئة نظام الأحداث: {e}")

    def register_event(self, event_name, callback):
        """تسجيل مستمع حدث"""
        if event_name not in self.event_listeners:
            self.event_listeners[event_name] = []
        self.event_listeners[event_name].append(callback)

    def emit_event(self, event_name, data=None):
        """إطلاق حدث"""
        try:
            if event_name in self.event_listeners:
                for callback in self.event_listeners[event_name]:
                    try:
                        callback(data)
                    except Exception as e:
                        print(f"❌ خطأ في معالج الحدث {event_name}: {e}")
        except Exception as e:
            print(f"❌ خطأ في إطلاق الحدث {event_name}: {e}")

    def init_organized_storage(self):
        """تهيئة نظام التخزين المنظم"""
        try:
            # إنشاء مجلدات التخزين
            self.storage_paths = {
                'data': 'data',
                'backups': 'data/backups',
                'exports': 'data/exports',
                'reports': 'data/reports'
            }

            for path in self.storage_paths.values():
                os.makedirs(path, exist_ok=True)

            print("✅ تم تهيئة نظام التخزين المنظم")

        except Exception as e:
            print(f"❌ خطأ في تهيئة التخزين المنظم: {e}")

    def load_all_data(self):
        """تحميل جميع البيانات"""
        try:
            if self.conn:
                self.products = self.load_products_from_db()
                self.customers = self.load_customers_from_db()
                self.sales = self.load_sales_from_db()
                self.suppliers = []
                print("✅ تم تحميل البيانات من قاعدة البيانات")
            else:
                self.load_data_from_json()
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            self.init_empty_data()

    def load_products_from_db(self):
        """تحميل المنتجات من قاعدة البيانات"""
        try:
            self.cursor.execute("SELECT * FROM products WHERE is_active = 1")
            rows = self.cursor.fetchall()

            products = []
            for row in rows:
                product = {
                    'id': row[0],
                    'name': row[1],
                    'category': row[2],
                    'barcode': row[3],
                    'quantity': row[4],
                    'unit': row[5],
                    'base_price': row[6],
                    'shop_owner_price': row[7],
                    'wholesale_price': row[8],
                    'retail_price': row[9],
                    'distributor_price': row[10],
                    'min_stock': row[11],
                    'notes': row[12],
                    'is_active': row[13]
                }
                products.append(product)

            return products
        except Exception as e:
            print(f"❌ خطأ في تحميل المنتجات: {e}")
            return []

    def load_customers_from_db(self):
        """تحميل العملاء من قاعدة البيانات"""
        try:
            self.cursor.execute("SELECT * FROM customers WHERE is_active = 1")
            rows = self.cursor.fetchall()

            customers = []
            for row in rows:
                customer = {
                    'id': row[0],
                    'name': row[1],
                    'phone': row[2],
                    'email': row[3],
                    'type': row[4],
                    'balance': row[5],
                    'is_active': row[6]
                }
                customers.append(customer)

            return customers
        except Exception as e:
            print(f"❌ خطأ في تحميل العملاء: {e}")
            return []

    def load_sales_from_db(self):
        """تحميل المبيعات من قاعدة البيانات"""
        try:
            self.cursor.execute("""
                SELECT s.*, GROUP_CONCAT(si.product_name || ':' || si.quantity || ':' || si.unit_price) as items
                FROM sales s
                LEFT JOIN sale_items si ON s.id = si.sale_id
                GROUP BY s.id
                ORDER BY s.sale_date DESC
            """)
            rows = self.cursor.fetchall()

            sales = []
            for row in rows:
                items = []
                if row[-1]:
                    items_str = row[-1].split(',')
                    for item_str in items_str:
                        parts = item_str.split(':')
                        if len(parts) >= 3:
                            items.append({
                                'name': parts[0],
                                'quantity': float(parts[1]),
                                'unit_price': float(parts[2])
                            })

                sale = {
                    'id': row[0],
                    'invoice_number': row[1],
                    'customer_name': row[2],
                    'customer_type': row[3],
                    'date': row[4],
                    'total': row[5],
                    'paid_amount': row[6],
                    'balance_due': row[7],
                    'status': row[8],
                    'items': items
                }
                sales.append(sale)

            return sales
        except Exception as e:
            print(f"❌ خطأ في تحميل المبيعات: {e}")
            return []

    def load_data_from_json(self):
        """تحميل البيانات من JSON"""
        try:
            import json
            json_file = "protech_simple_data.json"
            if os.path.exists(json_file):
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                self.suppliers = data.get('suppliers', [])

                print("✅ تم تحميل البيانات من JSON")
            else:
                self.init_empty_data()
        except Exception as e:
            print(f"❌ خطأ في تحميل JSON: {e}")
            self.init_empty_data()

    def init_empty_data(self):
        """تهيئة بيانات فارغة"""
        self.products = []
        self.customers = []
        self.sales = []
        self.suppliers = []
        print("⚠️ تم تهيئة بيانات فارغة")

    # معالجات الأحداث
    def on_product_added(self, product_data):
        """معالج إضافة منتج"""
        try:
            print(f"✅ تم إضافة المنتج: {product_data.get('name', 'غير محدد')}")
            self.refresh_displays()
        except Exception as e:
            print(f"❌ خطأ في معالج إضافة المنتج: {e}")

    def on_sale_completed(self, sale_data):
        """معالج إتمام مبيعة"""
        try:
            # تحديث المخزون
            for item in sale_data.get('items', []):
                self.update_product_quantity(item['name'], -item['quantity'])

            print(f"✅ تم إتمام المبيعة: {sale_data.get('invoice_number', 'غير محدد')}")
            self.refresh_displays()
        except Exception as e:
            print(f"❌ خطأ في معالج إتمام المبيعة: {e}")

    def on_data_changed(self, data):
        """معالج تغيير البيانات"""
        try:
            self.save_all_data()
            self.refresh_displays()
        except Exception as e:
            print(f"❌ خطأ في معالج تغيير البيانات: {e}")

    def update_product_quantity(self, product_name, quantity_change):
        """تحديث كمية المنتج"""
        try:
            for product in self.products:
                if product['name'] == product_name:
                    product['quantity'] += quantity_change

                    # تحديث في قاعدة البيانات
                    if self.conn:
                        self.cursor.execute(
                            "UPDATE products SET quantity = ? WHERE name = ?",
                            (product['quantity'], product_name)
                        )
                        self.conn.commit()

                    break
        except Exception as e:
            print(f"❌ خطأ في تحديث كمية المنتج: {e}")

    def refresh_displays(self):
        """تحديث العروض"""
        try:
            # تحديث التقارير إذا كانت مفتوحة
            if hasattr(self, 'current_page') and self.current_page == 'reports':
                self.refresh_reports()
        except Exception as e:
            print(f"❌ خطأ في تحديث العروض: {e}")

    def save_all_data(self):
        """حفظ جميع البيانات"""
        try:
            if self.conn:
                self.conn.commit()

            # حفظ في JSON كنسخة احتياطية
            self.save_data_to_json()

        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")

    def save_data_to_json(self):
        """حفظ البيانات في JSON"""
        try:
            import json

            data = {
                'products': self.products,
                'customers': self.customers,
                'sales': self.sales,
                'suppliers': self.suppliers,
                'last_updated': datetime.now().isoformat()
            }

            json_path = os.path.join(self.storage_paths['data'], 'protech_data_backup.json')
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            print(f"❌ خطأ في حفظ JSON: {e}")

    def close_system(self):
        """إغلاق النظام"""
        try:
            self.save_all_data()

            if self.conn:
                self.conn.close()

            print("✅ تم إغلاق النظام المتكامل")

        except Exception as e:
            print(f"❌ خطأ في إغلاق النظام: {e}")
'''

                # Insert the integrated code after class definition
                content = content[:class_end + 1] + integrated_code + content[class_end + 1:]

        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)

        print("✅ تم تطبيق النظام المتكامل")
        return True

    except Exception as e:
        print(f"❌ خطأ في تطبيق النظام المتكامل: {e}")
        return False

def main():
    """Main function"""
    print("🔗 إنشاء نظام متكامل ومترابط لـ ProTech")
    print("🔗 Creating Integrated and Interactive System for ProTech")
    print("="*70)

    success_count = 0
    total_steps = 2

    # Step 1: Create integrated system
    if create_integrated_system():
        success_count += 1
        print("✅ الخطوة 1: تم إنشاء النظام المتكامل")
    else:
        print("❌ الخطوة 1: فشل في إنشاء النظام المتكامل")

    # Step 2: Apply integrated system
    if apply_integrated_system():
        success_count += 1
        print("✅ الخطوة 2: تم تطبيق النظام المتكامل")
    else:
        print("❌ الخطوة 2: فشل في تطبيق النظام المتكامل")

    # Summary
    print("\n" + "="*70)
    print("📊 ملخص العملية:")
    print(f"✅ نجح: {success_count}/{total_steps} خطوات")

    if success_count == total_steps:
        print("\n🎉 تم إنشاء النظام المتكامل بنجاح!")

        print("\n🔗 الميزات الجديدة:")
        print("• 🗄️ قاعدة بيانات متكاملة (SQLite)")
        print("• 🔄 نظام أحداث تفاعلي")
        print("• 📁 تخزين منظم للبيانات")
        print("• 🔄 تحديث تلقائي للتقارير")
        print("• 💾 نسخ احتياطية تلقائية")
        print("• 📊 ربط متناسق بين الأجزاء")

        print("\n🎯 التفاعل بين الأجزاء:")
        print("• إضافة منتج → تحديث المخزون → تحديث التقارير")
        print("• إتمام مبيعة → تقليل المخزون → تحديث أرصدة العملاء")
        print("• تغيير البيانات → حفظ تلقائي → تحديث العروض")
        print("• تحذيرات المخزون المنخفض → إشعارات فورية")

        print("\n📁 التخزين المنظم:")
        print("• data/ - البيانات الرئيسية")
        print("• data/backups/ - النسخ الاحتياطية")
        print("• data/exports/ - الملفات المُصدرة")
        print("• data/reports/ - التقارير المحفوظة")

        print("\n🔄 التحديث التلقائي:")
        print("• التقارير: كل 30 ثانية")
        print("• المخزون: كل دقيقة")
        print("• أرصدة العملاء: كل دقيقتين")
        print("• النسخ الاحتياطية: كل ساعة")

        print("\n💡 الفوائد:")
        print("• تفاعل سلس بين جميع أجزاء البرنامج")
        print("• تخزين آمن ومنظم للمعلومات")
        print("• تحديث فوري للبيانات")
        print("• حماية من فقدان البيانات")
        print("• أداء محسن وسرعة في الاستجابة")

    else:
        print(f"\n⚠️ تم إنجاز {success_count} من {total_steps} خطوات فقط")
        print("قد تحتاج مراجعة إضافية")

    print("\n🔧 تم الانتهاء من إنشاء النظام المتكامل")

if __name__ == "__main__":
    main()
