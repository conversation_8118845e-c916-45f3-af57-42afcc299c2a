#!/usr/bin/env python3
"""
Fix ProTech Icon - إصلاح أيقونة ProTech
إضافة أيقونة آلة حاسبة بطريقة صحيحة

Fix ProTech calculator icon properly
إصلاح أيقونة آلة حاسبة ProTech بطريقة صحيحة
"""

def fix_protech_icon():
    """Fix ProTech calculator icon"""
    try:
        # Read the file
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Find the line with self.root.geometry and add icon code after it
        new_lines = []
        icon_added = False
        
        for line in lines:
            new_lines.append(line)
            
            # Add icon code after geometry setting
            if 'self.root.geometry(' in line and not icon_added:
                new_lines.append('\n')
                new_lines.append('        # Set calculator icon\n')
                new_lines.append('        try:\n')
                new_lines.append('            import os\n')
                new_lines.append('            if os.path.exists(r"C:\\Windows\\System32\\calc.exe"):\n')
                new_lines.append('                self.root.iconbitmap(r"C:\\Windows\\System32\\calc.exe")\n')
                new_lines.append('            else:\n')
                new_lines.append('                current_title = self.root.title()\n')
                new_lines.append('                self.root.title(f"🧮 {current_title}")\n')
                new_lines.append('        except:\n')
                new_lines.append('            pass\n')
                icon_added = True
                print('✅ تم إضافة كود أيقونة الآلة الحاسبة')

        # Write back the file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.writelines(new_lines)

        print('✅ تم إصلاح الملف بنجاح')
        return True
        
    except Exception as e:
        print(f'❌ خطأ في إصلاح الملف: {e}')
        return False

def test_file():
    """Test if the file compiles correctly"""
    try:
        import py_compile
        py_compile.compile('protech_simple_working.py', doraise=True)
        print('✅ الملف يعمل بدون أخطاء')
        return True
    except Exception as e:
        print(f'❌ خطأ في الملف: {e}')
        return False

def main():
    """Main function"""
    print("🔧 إصلاح أيقونة ProTech")
    print("🔧 Fixing ProTech Icon")
    print()
    
    # Fix the icon
    if fix_protech_icon():
        # Test the file
        if test_file():
            print("\n✅ تم إصلاح ProTech بنجاح!")
            print("✅ ProTech fixed successfully!")
            print("\nيمكنك الآن تشغيل البرنامج:")
            print("python protech_simple_working.py")
        else:
            print("\n❌ هناك أخطاء في الملف")
    else:
        print("\n❌ فشل في إصلاح الملف")

if __name__ == "__main__":
    main()
