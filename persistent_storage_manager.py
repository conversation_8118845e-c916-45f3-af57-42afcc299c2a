#!/usr/bin/env python3
"""
ProTech Persistent Storage Manager - مدير التخزين الدائم المنظم
نظام تخزين دائم ومنظم لنظام ProTech

Advanced persistent and organized storage system for ProTech
نظام تخزين متقدم دائم ومنظم لنظام ProTech
"""

import json
import os
import sqlite3
import shutil
import gzip
import pickle
import threading
import time
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import traceback

class PersistentStorageManager:
    """Advanced persistent storage manager with multiple storage backends"""
    
    def __init__(self, base_path: str = ".", app_name: str = "ProTech"):
        """Initialize persistent storage manager"""
        self.base_path = base_path
        self.app_name = app_name
        
        # Storage paths
        self.data_dir = os.path.join(base_path, "data")
        self.backup_dir = os.path.join(base_path, "backups")
        self.archive_dir = os.path.join(base_path, "archives")
        self.temp_dir = os.path.join(base_path, "temp")
        self.logs_dir = os.path.join(base_path, "logs")
        
        # Storage files
        self.json_file = os.path.join(self.data_dir, "protech_data.json")
        self.sqlite_file = os.path.join(self.data_dir, "protech_data.db")
        self.backup_json = os.path.join(self.backup_dir, "protech_backup.json")
        
        # Storage settings
        self.auto_backup_interval = 300  # 5 minutes
        self.max_backups = 50
        self.compression_enabled = True
        self.encryption_enabled = False
        
        # Thread safety
        self.storage_lock = threading.RLock()
        self.backup_lock = threading.Lock()
        
        # Performance tracking
        self.storage_stats = {
            'total_saves': 0,
            'total_loads': 0,
            'backup_count': 0,
            'last_save_time': None,
            'last_backup_time': None,
            'storage_size': 0,
            'compression_ratio': 0
        }
        
        # Initialize storage system
        self.setup_logging()
        self.create_directories()
        self.init_storage()
        self.start_background_tasks()
        
        print("✅ تم تهيئة نظام التخزين الدائم المنظم")
        print("✅ Persistent storage manager initialized")

    def setup_logging(self):
        """Setup logging for storage operations"""
        try:
            if not os.path.exists(self.logs_dir):
                os.makedirs(self.logs_dir)
            
            # Storage logger
            self.logger = logging.getLogger(f'{self.app_name}.Storage')
            self.logger.setLevel(logging.INFO)
            
            # File handler
            log_file = os.path.join(self.logs_dir, 'storage.log')
            handler = logging.FileHandler(log_file, encoding='utf-8')
            handler.setLevel(logging.INFO)
            
            # Formatter
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            
            self.logger.addHandler(handler)
            
        except Exception as e:
            print(f"خطأ في تهيئة نظام السجلات: {e}")

    def create_directories(self):
        """Create necessary directories for storage"""
        directories = [
            self.data_dir, self.backup_dir, self.archive_dir, 
            self.temp_dir, self.logs_dir
        ]
        
        for directory in directories:
            if not os.path.exists(directory):
                os.makedirs(directory)
                print(f"📁 تم إنشاء مجلد: {directory}")

    def init_storage(self):
        """Initialize storage backends"""
        try:
            # Initialize SQLite database
            self.init_sqlite()
            
            # Check for existing JSON data
            if os.path.exists(self.json_file):
                print("📄 تم العثور على ملف JSON موجود")
                self.migrate_json_to_sqlite()
            
            self.logger.info("Storage system initialized successfully")
            
        except Exception as e:
            print(f"❌ خطأ في تهيئة التخزين: {e}")
            self.logger.error(f"Storage initialization error: {e}")

    def init_sqlite(self):
        """Initialize SQLite database with optimized settings"""
        try:
            with self.storage_lock:
                conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
                
                # SQLite optimizations
                conn.execute("PRAGMA journal_mode=WAL")
                conn.execute("PRAGMA synchronous=NORMAL")
                conn.execute("PRAGMA cache_size=10000")
                conn.execute("PRAGMA temp_store=MEMORY")
                
                cursor = conn.cursor()
                
                # Create tables
                self.create_storage_tables(cursor)
                
                conn.commit()
                conn.close()
                
                print("✅ تم تهيئة قاعدة بيانات SQLite")
                
        except Exception as e:
            print(f"❌ خطأ في تهيئة SQLite: {e}")
            raise e

    def create_storage_tables(self, cursor):
        """Create storage tables"""
        
        # Main data table for flexible storage
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS app_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                data_type TEXT NOT NULL,
                data_key TEXT NOT NULL,
                data_value TEXT NOT NULL,
                metadata TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                version INTEGER DEFAULT 1,
                checksum TEXT,
                UNIQUE(data_type, data_key)
            )
        """)
        
        # Storage metadata table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS storage_metadata (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                key TEXT UNIQUE NOT NULL,
                value TEXT,
                description TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Backup history table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS backup_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                backup_name TEXT NOT NULL,
                backup_path TEXT NOT NULL,
                backup_size INTEGER,
                backup_type TEXT DEFAULT 'auto',
                compression_ratio REAL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'completed'
            )
        """)
        
        # Create indexes
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_app_data_type ON app_data(data_type)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_app_data_key ON app_data(data_key)")
        cursor.execute("CREATE INDEX IF NOT EXISTS idx_app_data_updated ON app_data(updated_at)")

    def save_data(self, data_type: str, data: Any, key: str = "default") -> bool:
        """Save data with organized structure"""
        try:
            with self.storage_lock:
                start_time = time.time()
                
                # Serialize data
                if isinstance(data, (dict, list)):
                    serialized_data = json.dumps(data, ensure_ascii=False, default=str)
                else:
                    serialized_data = str(data)
                
                # Calculate checksum
                checksum = hashlib.md5(serialized_data.encode()).hexdigest()
                
                # Prepare metadata
                metadata = {
                    'size': len(serialized_data),
                    'type': type(data).__name__,
                    'save_time': datetime.now().isoformat()
                }
                metadata_json = json.dumps(metadata)
                
                # Save to SQLite
                conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
                cursor = conn.cursor()
                
                cursor.execute("""
                    INSERT OR REPLACE INTO app_data 
                    (data_type, data_key, data_value, metadata, checksum, updated_at)
                    VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """, (data_type, key, serialized_data, metadata_json, checksum))
                
                conn.commit()
                conn.close()
                
                # Update statistics
                self.storage_stats['total_saves'] += 1
                self.storage_stats['last_save_time'] = datetime.now().isoformat()
                
                # Also save to JSON for compatibility
                self.save_to_json_backup(data_type, data, key)
                
                save_time = time.time() - start_time
                self.logger.info(f"Data saved: {data_type}.{key} in {save_time:.3f}s")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Save data error: {e}")
            print(f"❌ خطأ في حفظ البيانات: {e}")
            return False

    def load_data(self, data_type: str, key: str = "default", default_value: Any = None) -> Any:
        """Load data with fallback options"""
        try:
            with self.storage_lock:
                start_time = time.time()
                
                # Try SQLite first
                conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT data_value, metadata, checksum FROM app_data 
                    WHERE data_type = ? AND data_key = ?
                """, (data_type, key))
                
                result = cursor.fetchone()
                conn.close()
                
                if result:
                    data_value, metadata_json, stored_checksum = result
                    
                    # Verify checksum
                    calculated_checksum = hashlib.md5(data_value.encode()).hexdigest()
                    if calculated_checksum != stored_checksum:
                        self.logger.warning(f"Checksum mismatch for {data_type}.{key}")
                    
                    # Deserialize data
                    try:
                        data = json.loads(data_value)
                    except json.JSONDecodeError:
                        data = data_value
                    
                    # Update statistics
                    self.storage_stats['total_loads'] += 1
                    
                    load_time = time.time() - start_time
                    self.logger.info(f"Data loaded: {data_type}.{key} in {load_time:.3f}s")
                    
                    return data
                
                # Fallback to JSON if SQLite fails
                return self.load_from_json_backup(data_type, key, default_value)
                
        except Exception as e:
            self.logger.error(f"Load data error: {e}")
            print(f"❌ خطأ في تحميل البيانات: {e}")
            return default_value

    def save_to_json_backup(self, data_type: str, data: Any, key: str):
        """Save data to JSON backup for compatibility"""
        try:
            # Load existing JSON data
            json_data = {}
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
            
            # Update data
            if data_type not in json_data:
                json_data[data_type] = {}
            
            json_data[data_type][key] = data
            json_data['last_updated'] = datetime.now().isoformat()
            
            # Save to temporary file first
            temp_file = self.json_file + '.tmp'
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2, default=str)
            
            # Atomic move
            if os.path.exists(temp_file):
                shutil.move(temp_file, self.json_file)
                
        except Exception as e:
            self.logger.error(f"JSON backup save error: {e}")

    def load_from_json_backup(self, data_type: str, key: str, default_value: Any) -> Any:
        """Load data from JSON backup"""
        try:
            if os.path.exists(self.json_file):
                with open(self.json_file, 'r', encoding='utf-8') as f:
                    json_data = json.load(f)
                
                if data_type in json_data and key in json_data[data_type]:
                    return json_data[data_type][key]
            
            return default_value
            
        except Exception as e:
            self.logger.error(f"JSON backup load error: {e}")
            return default_value

    def migrate_json_to_sqlite(self):
        """Migrate existing JSON data to SQLite"""
        try:
            print("🔄 ترحيل البيانات من JSON إلى SQLite...")
            
            with open(self.json_file, 'r', encoding='utf-8') as f:
                json_data = json.load(f)
            
            migrated_count = 0
            
            for data_type, type_data in json_data.items():
                if data_type == 'last_updated':
                    continue
                
                if isinstance(type_data, dict):
                    for key, value in type_data.items():
                        if self.save_data(data_type, value, key):
                            migrated_count += 1
                else:
                    if self.save_data(data_type, type_data):
                        migrated_count += 1
            
            print(f"✅ تم ترحيل {migrated_count} عنصر من JSON إلى SQLite")
            self.logger.info(f"Migrated {migrated_count} items from JSON to SQLite")
            
        except Exception as e:
            print(f"❌ خطأ في ترحيل البيانات: {e}")
            self.logger.error(f"Migration error: {e}")

    def create_backup(self, backup_name: str = None, backup_type: str = "manual") -> str:
        """Create comprehensive backup"""
        try:
            with self.backup_lock:
                if backup_name is None:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    backup_name = f"protech_backup_{timestamp}"
                
                backup_path = os.path.join(self.backup_dir, f"{backup_name}.db")
                
                # Create SQLite backup
                with self.storage_lock:
                    if os.path.exists(self.sqlite_file):
                        shutil.copy2(self.sqlite_file, backup_path)
                
                # Create compressed backup
                if self.compression_enabled:
                    compressed_path = f"{backup_path}.gz"
                    with open(backup_path, 'rb') as f_in:
                        with gzip.open(compressed_path, 'wb') as f_out:
                            shutil.copyfileobj(f_in, f_out)
                    
                    # Calculate compression ratio
                    original_size = os.path.getsize(backup_path)
                    compressed_size = os.path.getsize(compressed_path)
                    compression_ratio = compressed_size / original_size if original_size > 0 else 1
                    
                    os.remove(backup_path)  # Remove uncompressed version
                    backup_path = compressed_path
                else:
                    compression_ratio = 1.0
                
                # Also backup JSON
                json_backup_path = os.path.join(self.backup_dir, f"{backup_name}.json")
                if os.path.exists(self.json_file):
                    shutil.copy2(self.json_file, json_backup_path)
                
                # Record backup in database
                self.record_backup(backup_name, backup_path, backup_type, compression_ratio)
                
                # Update statistics
                self.storage_stats['backup_count'] += 1
                self.storage_stats['last_backup_time'] = datetime.now().isoformat()
                self.storage_stats['compression_ratio'] = compression_ratio
                
                print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
                self.logger.info(f"Backup created: {backup_path}")
                
                return backup_path
                
        except Exception as e:
            print(f"❌ خطأ في إنشاء النسخة الاحتياطية: {e}")
            self.logger.error(f"Backup creation error: {e}")
            raise e

    def record_backup(self, backup_name: str, backup_path: str, backup_type: str, compression_ratio: float):
        """Record backup information in database"""
        try:
            backup_size = os.path.getsize(backup_path) if os.path.exists(backup_path) else 0
            
            conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute("""
                INSERT INTO backup_history 
                (backup_name, backup_path, backup_size, backup_type, compression_ratio)
                VALUES (?, ?, ?, ?, ?)
            """, (backup_name, backup_path, backup_size, backup_type, compression_ratio))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            self.logger.error(f"Record backup error: {e}")

    def cleanup_old_backups(self):
        """Clean up old backup files"""
        try:
            backup_files = []
            for file in os.listdir(self.backup_dir):
                if file.startswith('protech_backup_'):
                    file_path = os.path.join(self.backup_dir, file)
                    backup_files.append((file_path, os.path.getctime(file_path)))
            
            # Sort by creation time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)
            
            # Remove old backups
            if len(backup_files) > self.max_backups:
                for file_path, _ in backup_files[self.max_backups:]:
                    os.remove(file_path)
                    print(f"🗑️ تم حذف النسخة الاحتياطية القديمة: {os.path.basename(file_path)}")
                    
        except Exception as e:
            self.logger.error(f"Backup cleanup error: {e}")

    def start_background_tasks(self):
        """Start background maintenance tasks"""
        try:
            # Auto backup thread
            self.backup_thread = threading.Thread(target=self.auto_backup_worker, daemon=True)
            self.backup_thread.start()
            
            print("✅ تم تشغيل مهام الصيانة التلقائية")
            
        except Exception as e:
            print(f"خطأ في تشغيل المهام التلقائية: {e}")
            self.logger.error(f"Background tasks error: {e}")

    def auto_backup_worker(self):
        """Background worker for automatic backups"""
        while True:
            try:
                time.sleep(self.auto_backup_interval)
                self.create_backup(backup_type="auto")
                self.cleanup_old_backups()
            except Exception as e:
                self.logger.error(f"Auto backup error: {e}")

    def get_storage_stats(self) -> Dict[str, Any]:
        """Get comprehensive storage statistics"""
        try:
            stats = self.storage_stats.copy()
            
            # Add file sizes
            if os.path.exists(self.sqlite_file):
                stats['sqlite_size'] = os.path.getsize(self.sqlite_file)
            if os.path.exists(self.json_file):
                stats['json_size'] = os.path.getsize(self.json_file)
            
            # Add backup information
            backup_files = [f for f in os.listdir(self.backup_dir) if f.startswith('protech_backup_')]
            stats['backup_files_count'] = len(backup_files)
            
            if backup_files:
                total_backup_size = sum(
                    os.path.getsize(os.path.join(self.backup_dir, f)) 
                    for f in backup_files
                )
                stats['total_backup_size'] = total_backup_size
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Get storage stats error: {e}")
            return self.storage_stats.copy()

    def export_all_data(self, export_path: str = None) -> str:
        """Export all data to JSON file"""
        try:
            if export_path is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                export_path = os.path.join(self.backup_dir, f"protech_export_{timestamp}.json")
            
            # Get all data from SQLite
            conn = sqlite3.connect(self.sqlite_file, timeout=30.0)
            cursor = conn.cursor()
            
            cursor.execute("SELECT data_type, data_key, data_value FROM app_data")
            rows = cursor.fetchall()
            conn.close()
            
            # Organize data
            export_data = {}
            for data_type, data_key, data_value in rows:
                if data_type not in export_data:
                    export_data[data_type] = {}
                
                try:
                    export_data[data_type][data_key] = json.loads(data_value)
                except json.JSONDecodeError:
                    export_data[data_type][data_key] = data_value
            
            # Add metadata
            export_data['_metadata'] = {
                'export_time': datetime.now().isoformat(),
                'app_name': self.app_name,
                'version': '1.0',
                'total_records': len(rows)
            }
            
            # Write to file
            with open(export_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ تم تصدير البيانات إلى: {export_path}")
            self.logger.info(f"Data exported to: {export_path}")
            
            return export_path
            
        except Exception as e:
            print(f"❌ خطأ في تصدير البيانات: {e}")
            self.logger.error(f"Export data error: {e}")
            raise e

    def close(self):
        """Close storage manager and cleanup"""
        try:
            # Create final backup
            self.create_backup("final_backup", "manual")
            
            print("✅ تم إغلاق مدير التخزين الدائم")
            self.logger.info("Persistent storage manager closed")
            
        except Exception as e:
            self.logger.error(f"Storage close error: {e}")

# Example usage and testing
if __name__ == "__main__":
    print("🧪 اختبار مدير التخزين الدائم المنظم")
    print("🧪 Testing Persistent Storage Manager")
    
    try:
        # Initialize storage manager
        storage = PersistentStorageManager()
        
        # Test data operations
        test_data = {
            'suppliers': [
                {'name': 'مورد تجريبي', 'phone': '123456789'},
                {'name': 'Test Supplier', 'phone': '987654321'}
            ],
            'products': [
                {'name': 'منتج تجريبي', 'price': 100, 'stock': 50},
                {'name': 'Test Product', 'price': 200, 'stock': 30}
            ]
        }
        
        # Save test data
        for data_type, data in test_data.items():
            storage.save_data(data_type, data)
        
        # Load and verify data
        for data_type in test_data.keys():
            loaded_data = storage.load_data(data_type)
            print(f"✅ تم تحميل {data_type}: {len(loaded_data)} عنصر")
        
        # Test backup
        backup_path = storage.create_backup("test_backup")
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_path}")
        
        # Test export
        export_path = storage.export_all_data()
        print(f"✅ تم تصدير البيانات: {export_path}")
        
        # Show statistics
        stats = storage.get_storage_stats()
        print(f"\n📊 إحصائيات التخزين:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # Close storage
        storage.close()
        
        print("\n✅ اكتمل اختبار نظام التخزين بنجاح!")
        
    except Exception as e:
        print(f"\n❌ خطأ في اختبار نظام التخزين: {e}")
        traceback.print_exc()
