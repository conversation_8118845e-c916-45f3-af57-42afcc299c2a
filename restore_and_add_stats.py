#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Restore and Add Statistics
استعادة وإضافة الإحصائيات

Restore a clean backup and add simple statistics system
استعادة نسخة احتياطية نظيفة وإضافة نظام إحصائيات بسيط
"""

import os
import shutil
from datetime import datetime

def restore_and_add_statistics():
    """استعادة وإضافة نظام الإحصائيات"""
    try:
        print("🔄 استعادة نسخة نظيفة وإضافة نظام الإحصائيات")
        print("🔄 Restoring Clean Backup and Adding Statistics System")
        print("="*60)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        # Find a clean backup to restore from
        clean_backup = os.path.join(desktop_path, "protech_simple_working.py.simple_profits_20250620_092112")
        
        if not os.path.exists(clean_backup):
            print("❌ النسخة الاحتياطية النظيفة غير موجودة")
            return False
        
        # Create backup of current file
        current_backup = f"{protech_file}.before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        if os.path.exists(protech_file):
            shutil.copy2(protech_file, current_backup)
            print(f"💾 نسخة احتياطية للملف الحالي: {os.path.basename(current_backup)}")
        
        # Restore clean backup
        shutil.copy2(clean_backup, protech_file)
        print(f"✅ تم استعادة النسخة النظيفة")
        
        # Read the restored content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Add simple statistics method
        simple_stats_method = '''
    def create_advanced_statistics_page(self):
        """إنشاء صفحة الإحصائيات المتقدمة"""
        try:
            # Clear current page
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            
            self.current_page = 'advanced_statistics'
            
            # Main container
            main_container = tk.Frame(self.main_frame, bg='#f0f0f0')
            main_container.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Title
            title_label = tk.Label(main_container, text="📊 Business Statistics Dashboard", 
                                 font=("Arial", 16, "bold"), bg='#f0f0f0', fg='#2c3e50')
            title_label.pack(pady=(0, 20))
            
            # Get data
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            
            # Calculate basic metrics
            total_products = len(products)
            total_customers = len(customers)
            total_sales = len(sales)
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            
            exchange_rate = 89500
            avg_sale = total_revenue / total_sales if total_sales > 0 else 0
            
            # Stats cards frame
            cards_frame = tk.Frame(main_container, bg='#f0f0f0')
            cards_frame.pack(fill='x', pady=(0, 20))
            
            # Create 8 stat cards
            card_data = [
                ("Products", total_products, "#3498db"),
                ("Customers", total_customers, "#e74c3c"),
                ("Sales", total_sales, "#2ecc71"),
                ("Revenue LBP", f"{total_revenue:,.0f}", "#f39c12"),
                ("Revenue USD", f"${total_revenue/exchange_rate:,.2f}", "#9b59b6"),
                ("Inventory", f"{inventory_value:,.0f}", "#1abc9c"),
                ("Debts", f"{customer_debt:,.0f}", "#e67e22"),
                ("Avg Sale", f"{avg_sale:,.0f}", "#34495e")
            ]
            
            for i, (title, value, color) in enumerate(card_data):
                row = i // 4
                col = i % 4
                
                card_frame = tk.Frame(cards_frame, bg=color, relief='raised', bd=3)
                card_frame.grid(row=row, col=col, padx=5, pady=5, sticky='ew')
                
                title_label = tk.Label(card_frame, text=title, font=("Arial", 10, "bold"), 
                                     bg=color, fg='white')
                title_label.pack(pady=(10, 5))
                
                value_label = tk.Label(card_frame, text=str(value), font=("Arial", 12, "bold"), 
                                     bg=color, fg='white')
                value_label.pack(pady=(0, 10))
                
                cards_frame.grid_columnconfigure(col, weight=1)
            
            # Analysis section
            analysis_section = tk.Frame(main_container, bg='#ffffff', relief='ridge', bd=2)
            analysis_section.pack(fill='both', expand=True)
            
            # Analysis header
            header_frame = tk.Frame(analysis_section, bg='#34495e')
            header_frame.pack(fill='x')
            
            header_label = tk.Label(header_frame, text="📊 Business Analysis Report", 
                                  font=("Arial", 14, "bold"), bg='#34495e', fg='white')
            header_label.pack(side='left', padx=15, pady=10)
            
            # Export button
            export_btn = tk.Button(header_frame, text="Export Report", 
                                 command=self.export_business_report,
                                 bg='#e74c3c', fg='white', font=("Arial", 9, "bold"))
            export_btn.pack(side='right', padx=15, pady=10)
            
            # Analysis content
            content_frame = tk.Frame(analysis_section, bg='#ffffff')
            content_frame.pack(fill='both', expand=True, padx=15, pady=15)
            
            # Create analysis text
            analysis_text = self.create_analysis_text(total_products, total_customers, total_sales, 
                                                    total_revenue, inventory_value, customer_debt, exchange_rate)
            
            # Display analysis
            text_widget = tk.Text(content_frame, font=("Courier", 10), wrap='word', height=15)
            scrollbar = tk.Scrollbar(content_frame, orient="vertical", command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
            
            scrollbar.pack(side="right", fill="y")
            text_widget.pack(side="left", fill="both", expand=True)
            
            text_widget.insert(1.0, analysis_text)
            text_widget.config(state='disabled')
            
            # Store for export
            self.current_report_data = analysis_text
            
            print("✅ تم إنشاء صفحة الإحصائيات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء صفحة الإحصائيات: {e}")
    
    def create_analysis_text(self, products_count, customers_count, sales_count, revenue, inventory, debt, rate):
        """إنشاء نص التحليل"""
        try:
            text = "BUSINESS ANALYSIS REPORT\\n"
            text += "=" * 50 + "\\n\\n"
            
            text += "OVERVIEW:\\n"
            text += "-" * 20 + "\\n"
            text += f"Total Products:    {products_count:>10}\\n"
            text += f"Total Customers:   {customers_count:>10}\\n"
            text += f"Total Sales:       {sales_count:>10}\\n\\n"
            
            text += "FINANCIAL SUMMARY:\\n"
            text += "-" * 20 + "\\n"
            text += f"Revenue (LBP):     {revenue:>15,.0f}\\n"
            text += f"Revenue (USD):     ${revenue/rate:>14,.2f}\\n"
            text += f"Inventory Value:   {inventory:>15,.0f} LBP\\n"
            text += f"Customer Debts:    {debt:>15,.0f} LBP\\n\\n"
            
            # Business health
            net_worth = revenue + inventory
            net_worth_usd = net_worth / rate
            
            text += "BUSINESS HEALTH:\\n"
            text += "-" * 20 + "\\n"
            text += f"Net Worth (LBP):   {net_worth:>15,.0f}\\n"
            text += f"Net Worth (USD):   ${net_worth_usd:>14,.2f}\\n"
            
            if net_worth_usd >= 50000:
                status = "EXCELLENT"
            elif net_worth_usd >= 25000:
                status = "VERY GOOD"
            elif net_worth_usd >= 10000:
                status = "GOOD"
            else:
                status = "NEEDS IMPROVEMENT"
            
            text += f"Status:            {status:>15}\\n\\n"
            
            text += "RECOMMENDATIONS:\\n"
            text += "-" * 20 + "\\n"
            text += "• Monitor inventory levels regularly\\n"
            text += "• Follow up on customer payments\\n"
            text += "• Track sales performance trends\\n"
            text += "• Update pricing strategies\\n"
            text += "• Maintain customer relationships\\n"
            
            return text
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء نص التحليل: {e}")
            return "Error creating analysis"
    
    def export_business_report(self):
        """تصدير تقرير الأعمال"""
        try:
            if hasattr(self, 'current_report_data'):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"business_report_{timestamp}.txt"
                
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(self.current_report_data)
                
                print(f"✅ تم تصدير التقرير إلى {filename}")
                
                try:
                    import tkinter.messagebox as msgbox
                    msgbox.showinfo("Export Success", f"Report exported to {filename}")
                except:
                    pass
            else:
                print("❌ لا يوجد تقرير لتصديره")
                
        except Exception as e:
            print(f"❌ خطأ في تصدير التقرير: {e}")
'''
        
        # Add the statistics method before the last method
        last_method = content.rfind("\n    def ")
        if last_method != -1:
            # Find the end of the last method
            method_end = content.find("\nclass ", last_method)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + simple_stats_method + content[method_end:]
            print("✅ تم إضافة نظام الإحصائيات")
        
        # Update the statistics button
        if 'btn6 = tk.Button(sidebar, text="رصيد المحل"' in content:
            # Replace with simple button
            old_pattern = 'btn6 = tk.Button(sidebar, text="رصيد المحل", font=("Arial", 12), bg=\'#16a085\', fg=\'white\', width=18, height=2, command=self.show_store_balance_usd)'
            new_button = 'btn6 = tk.Button(sidebar, text="Statistics", font=("Arial", 12), bg=\'#16a085\', fg=\'white\', width=18, height=2, command=self.create_advanced_statistics_page)'
            
            content = content.replace(old_pattern, new_button)
            print("✅ تم تحديث زر الإحصائيات")
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم استعادة الملف وإضافة نظام الإحصائيات")
        
        # Test compilation
        import subprocess
        import sys
        
        print("🧪 اختبار التجميع...")
        result = subprocess.run([sys.executable, '-m', 'py_compile', protech_file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ اختبار التجميع: نجح")
            return True
        else:
            print("❌ اختبار التجميع: فشل")
            print(f"الخطأ: {result.stderr}")
            
            # Restore current backup
            if os.path.exists(current_backup):
                shutil.copy2(current_backup, protech_file)
                print("🔄 تم استعادة الملف الحالي")
            return False
        
    except Exception as e:
        print(f"❌ خطأ في استعادة وإضافة نظام الإحصائيات: {e}")
        return False

def main():
    """Main function"""
    print("🔄 استعادة وإضافة نظام الإحصائيات لـ ProTech")
    print("🔄 Restoring and Adding Statistics System for ProTech")
    print("="*70)
    
    if restore_and_add_statistics():
        print("\n🎉 تم استعادة الملف وإضافة نظام الإحصائيات بنجاح!")
        
        print("\n📊 النظام الجديد يشمل:")
        print("• 📈 صفحة إحصائيات أعمال متقدمة")
        print("• 🎯 8 بطاقات إحصائيات ملونة")
        print("• 📋 تقرير تحليل أعمال شامل")
        print("• 📊 تقييم صحة الأعمال")
        print("• 📤 تصدير التقرير")
        print("• 🔄 حسابات فورية ودقيقة")
        
        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Statistics' في الشريط الجانبي")
        print("3. استعرض بطاقات الإحصائيات الملونة")
        print("4. اقرأ تقرير تحليل الأعمال")
        print("5. انقر على 'Export Report' لتصدير التقرير")
        print("6. راجع التوصيات والحالة المالية")
        
        print("\n💡 البطاقات المعروضة:")
        print("• 🛍️ إجمالي المنتجات")
        print("• 👥 إجمالي العملاء")
        print("• 💰 إجمالي المبيعات")
        print("• 💵 الإيرادات بالليرة")
        print("• 💲 الإيرادات بالدولار")
        print("• 📦 قيمة المخزون")
        print("• 📋 ديون العملاء")
        print("• 📈 متوسط البيع")
        
        print("\n📊 التقرير يشمل:")
        print("• نظرة عامة على الأرقام")
        print("• الملخص المالي")
        print("• تقييم صحة الأعمال")
        print("• توصيات للتحسين")
        
    else:
        print("\n❌ فشل في استعادة وإضافة نظام الإحصائيات")
    
    print("\n🔧 تم الانتهاء من العملية")

if __name__ == "__main__":
    main()
