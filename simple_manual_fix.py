#!/usr/bin/env python3
"""
Simple Manual Startup Fix for ProTech
إصلاح بسيط للتشغيل اليدوي لـ ProTech

Simple and effective fix for manual startup issues
إصلاح بسيط وفعال لمشاكل التشغيل اليدوي
"""

import os
import re
import shutil
from datetime import datetime

def create_backup():
    """Create backup"""
    try:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f'protech_simple_working.py.manual_fix_{timestamp}'
        shutil.copy2('protech_simple_working.py', backup_name)
        print(f"✅ تم إنشاء نسخة احتياطية: {backup_name}")
        return backup_name
    except Exception as e:
        print(f"❌ خطأ في النسخة الاحتياطية: {e}")
        return None

def fix_manual_startup():
    """Fix manual startup issues simply and effectively"""
    try:
        with open('protech_simple_working.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. Remove problematic self assignments outside functions
        lines = content.split('\n')
        new_lines = []
        removed_count = 0
        
        for i, line in enumerate(lines):
            # Check for problematic patterns
            problematic = False
            patterns = ['self.suppliers = []', 'self.products = []', 'self.customers = []', 'self.sales = []', 'self.loading = False']
            
            for pattern in patterns:
                if pattern in line.strip():
                    # Check if we're inside a function
                    in_function = False
                    for j in range(i-1, max(0, i-10), -1):
                        prev_line = lines[j].strip()
                        if prev_line.startswith('def ') and '(self' in prev_line:
                            in_function = True
                            break
                        elif prev_line.startswith('class '):
                            break
                    
                    if not in_function:
                        problematic = True
                        removed_count += 1
                        print(f"🧹 إزالة السطر المشكل {i+1}: {line.strip()}")
                        break
            
            if not problematic:
                new_lines.append(line)
        
        content = '\n'.join(new_lines)
        
        # 2. Fix __init__ method to ensure proper initialization
        init_pattern = r'(def __init__\(self.*?\):.*?)(self\.root\.mainloop\(\)|if __name__|def\s+\w+)'
        
        def fix_init(match):
            init_content = match.group(1)
            next_part = match.group(2)
            
            # Ensure data initialization exists
            if 'self.suppliers = []' not in init_content:
                lines = init_content.split('\n')
                
                # Find where to insert
                insert_index = len(lines) - 1
                for i, line in enumerate(lines):
                    if 'self.root = tk.Tk()' in line:
                        insert_index = i + 1
                        break
                
                # Insert data initialization
                lines.insert(insert_index, '')
                lines.insert(insert_index + 1, '        # Initialize data structures')
                lines.insert(insert_index + 2, '        self.suppliers = []')
                lines.insert(insert_index + 3, '        self.products = []')
                lines.insert(insert_index + 4, '        self.customers = []')
                lines.insert(insert_index + 5, '        self.sales = []')
                lines.insert(insert_index + 6, '        self.loading = False')
                lines.insert(insert_index + 7, '')
                lines.insert(insert_index + 8, '        # Load data after UI is ready')
                lines.insert(insert_index + 9, '        self.root.after(100, self.load_existing_data)')
                
                init_content = '\n'.join(lines)
                print("✅ تم إصلاح دالة __init__")
            
            return init_content + next_part
        
        content = re.sub(init_pattern, fix_init, content, flags=re.DOTALL)
        
        # 3. Add simple data loading method
        load_method = '''
    def load_existing_data(self):
        """Load existing data simply and safely"""
        try:
            self.loading = True
            data_file = 'protech_simple_data.json'
            
            if os.path.exists(data_file):
                import json
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.suppliers = data.get('suppliers', [])
                self.products = data.get('products', [])
                self.customers = data.get('customers', [])
                self.sales = data.get('sales', [])
                
                print(f"📥 تم تحميل البيانات: {len(self.suppliers)} موردين، {len(self.products)} منتجات")
                
                # Update displays if they exist
                if hasattr(self, 'update_suppliers_display'):
                    self.update_suppliers_display()
                if hasattr(self, 'update_products_display'):
                    self.update_products_display()
                if hasattr(self, 'update_customers_display'):
                    self.update_customers_display()
            else:
                print("📝 لا يوجد ملف بيانات، سيتم البدء بملف فارغ")
                
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
        finally:
            self.loading = False
'''
        
        # Add the method if it doesn't exist
        if 'def load_existing_data(self):' not in content:
            if 'def save_data(self):' in content:
                content = content.replace('def save_data(self):', load_method + '\n    def save_data(self):')
            else:
                content = content.replace('if __name__ == "__main__":', load_method + '\nif __name__ == "__main__":')
            print("✅ تم إضافة دالة تحميل البيانات")
        
        # 4. Fix save_data to avoid permission errors
        save_pattern = r'def save_data\(self\):(.*?)(?=def\s+\w+|class\s+\w+|if __name__|$)'
        
        simple_save = '''def save_data(self):
        """Simple and safe data saving"""
        try:
            if getattr(self, 'loading', False):
                return True
            
            import json
            from datetime import datetime
            
            data = {
                'suppliers': getattr(self, 'suppliers', []),
                'products': getattr(self, 'products', []),
                'customers': getattr(self, 'customers', []),
                'sales': getattr(self, 'sales', []),
                'last_updated': datetime.now().isoformat()
            }
            
            # Simple direct save
            with open('protech_simple_data.json', 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2, default=str)
            
            print("✅ تم حفظ البيانات بنجاح")
            return True
            
        except PermissionError:
            # Try alternative save
            try:
                import os
                alt_file = os.path.expanduser("~/Documents/protech_simple_data.json")
                with open(alt_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print(f"✅ تم حفظ البيانات في: {alt_file}")
                return True
            except:
                print("❌ فشل في حفظ البيانات")
                return False
        except Exception as e:
            print(f"❌ خطأ في حفظ البيانات: {e}")
            return False

    '''
        
        # Replace save_data function
        content = re.sub(save_pattern, simple_save, content, flags=re.DOTALL)
        print("✅ تم إصلاح دالة حفظ البيانات")
        
        # 5. Remove problematic background save calls
        content = re.sub(r'threading\.Thread\(target=self\.save_data_background.*?\)\.start\(\)', 
                        '# Background save disabled to prevent permission errors', content)
        
        # Write the fixed file
        with open('protech_simple_working.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ تم إصلاح {removed_count} سطر مشكل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {e}")
        return False

def test_compilation():
    """Test if file compiles correctly"""
    try:
        import py_compile
        py_compile.compile('protech_simple_working.py', doraise=True)
        print("✅ الملف يعمل بدون أخطاء")
        return True
    except Exception as e:
        print(f"❌ خطأ في تركيب الملف: {e}")
        return False

def create_simple_launcher():
    """Create simple launcher"""
    launcher_content = '''#!/usr/bin/env python3
"""
Simple ProTech Launcher
مشغل ProTech البسيط
"""

import os
import sys
import subprocess

def launch_protech():
    """Launch ProTech simply"""
    try:
        print("🚀 تشغيل ProTech...")
        
        if not os.path.exists('protech_simple_working.py'):
            print("❌ ملف ProTech غير موجود!")
            input("اضغط Enter للخروج...")
            return
        
        # Launch ProTech
        subprocess.Popen([sys.executable, 'protech_simple_working.py'])
        print("✅ تم تشغيل ProTech بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في التشغيل: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    launch_protech()
'''
    
    with open('launch_protech_simple.py', 'w', encoding='utf-8') as f:
        f.write(launcher_content)
    
    print("✅ تم إنشاء مشغل بسيط: launch_protech_simple.py")

def main():
    """Main function"""
    print("🔧 إصلاح بسيط للتشغيل اليدوي لـ ProTech")
    print("🔧 Simple Manual Startup Fix for ProTech")
    print("="*50)
    
    try:
        # Step 1: Backup
        create_backup()
        
        # Step 2: Fix manual startup
        print("\n🔧 إصلاح التشغيل اليدوي...")
        if fix_manual_startup():
            print("✅ تم إصلاح التشغيل اليدوي")
        
        # Step 3: Test compilation
        print("\n🧪 اختبار الملف...")
        if test_compilation():
            print("✅ الملف جاهز للتشغيل")
        
        # Step 4: Create launcher
        print("\n🚀 إنشاء مشغل بسيط...")
        create_simple_launcher()
        
        print("\n" + "="*50)
        print("✅ تم إصلاح التشغيل اليدوي بنجاح!")
        print("✅ Manual startup fixed successfully!")
        print("="*50)
        
        print("\n🎯 الإصلاحات المطبقة:")
        print("• إزالة الأسطر المشكلة خارج الدوال")
        print("• إصلاح دالة __init__ للتهيئة الصحيحة")
        print("• إضافة تحميل آمن للبيانات")
        print("• إصلاح دالة حفظ البيانات")
        print("• تعطيل الحفظ التلقائي المشكل")
        print("• إنشاء مشغل بسيط")
        
        print("\n🚀 الآن يمكنك:")
        print("1. النقر المزدوج على protech_simple_working.py")
        print("2. استخدام launch_protech_simple.py")
        print("3. التشغيل من سطر الأوامر")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        return False

if __name__ == "__main__":
    main()
