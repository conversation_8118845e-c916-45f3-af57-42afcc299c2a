🚀 دليل البدء السريع - برنامج ناصر للمحاسبة
Quick Start Guide - Nassar Accounting Program

═══════════════════════════════════════════════════════════════

📋 خطوات التشغيل السريع / Quick Launch Steps:

1️⃣ اضغط مرتين على: START_NASSAR_PROGRAM.py
   Double-click: START_NASSAR_PROGRAM.py

2️⃣ اضغط "نعم" في نافذة الترحيب
   Click "Yes" in the welcome window

3️⃣ انتظر حتى يفتح البرنامج
   Wait for the program to open

4️⃣ ابدأ الاستخدام!
   Start using!

═══════════════════════════════════════════════════════════════

🎯 الوظائف الأساسية / Basic Functions:

📦 المخزون / Inventory:
   • إضافة منتجات جديدة
   • مسح الباركود
   • تتبع المخزون

👥 العملاء / Customers:
   • إضافة عملاء جدد
   • تتبع الأرصدة
   • أنواع العملاء المختلفة

💰 المبيعات / Sales:
   • إنشاء فواتير
   • حساب الأسعار التلقائي
   • تتبع المدفوعات

📊 التقارير / Reports:
   • تقارير المبيعات
   • تقارير المخزون
   • الأرباح والخسائر

═══════════════════════════════════════════════════════════════

🔧 حل المشاكل السريع / Quick Troubleshooting:

❌ البيانات لا تظهر / Data not showing:
   ➤ اذهب للمخزون → اضغط الزر الأحمر "إعادة تحميل البيانات"
   ➤ Go to Inventory → Click red "Force Reload Data" button

❌ البرنامج لا يفتح / Program won't open:
   ➤ انقر بالزر الأيمن → "Open with Python"
   ➤ Right-click → "Open with Python"

❌ خطأ في البيانات / Data error:
   ➤ استخدم أداة فحص البيانات في مجلد Tools
   ➤ Use Data Checker Tool in Tools folder

═══════════════════════════════════════════════════════════════

🛠️ الأدوات المساعدة / Utility Tools:

💾 النسخ الاحتياطي / Backup Tool:
   📁 04_Tools_Utilities/Data_Backup_Tool.py
   • إنشاء نسخ احتياطية
   • استعادة البيانات

🔍 فحص البيانات / Data Checker:
   📁 04_Tools_Utilities/Data_Checker_Tool.py
   • فحص سلامة البيانات
   • تقارير مفصلة

⚡ التشغيل السريع / Quick Launch:
   📁 03_Launchers/Quick_Launch_Nassar.py
   • تشغيل بدون رسائل ترحيب

═══════════════════════════════════════════════════════════════

📞 نصائح مهمة / Important Tips:

✅ احفظ نسخة احتياطية قبل إجراء تغييرات كبيرة
   Create backup before making major changes

✅ استخدم أزرار إعادة التحميل عند مواجهة مشاكل
   Use reload buttons when facing issues

✅ تحقق من التقارير بانتظام لمراقبة الأداء
   Check reports regularly to monitor performance

✅ استخدم أدوات الفحص للتأكد من سلامة البيانات
   Use checking tools to ensure data integrity

═══════════════════════════════════════════════════════════════

🎉 مرحباً بك في برنامج ناصر للمحاسبة!
Welcome to Nassar Accounting Program!

للمزيد من المعلومات، راجع ملف README في مجلد Documentation
For more information, check README file in Documentation folder
