#!/usr/bin/env python3
"""
ProTech Accounting System - Comprehensive System Test
اختبار شامل لنظام ProTech للمحاسبة

This script tests all major functionality of the ProTech Accounting System
"""

import requests
import json
import time
import sys
from datetime import datetime

class ProTechSystemTester:
    def __init__(self, base_url='http://localhost:5000'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = {
            'passed': 0,
            'failed': 0,
            'errors': []
        }
    
    def log(self, message, level='INFO'):
        """Log test messages"""
        timestamp = datetime.now().strftime('%H:%M:%S')
        print(f"[{timestamp}] {level}: {message}")
    
    def test_page(self, endpoint, expected_status=200, description=""):
        """Test a web page endpoint"""
        try:
            url = f"{self.base_url}{endpoint}"
            response = self.session.get(url, timeout=10)
            
            if response.status_code == expected_status:
                self.log(f"✅ {description or endpoint} - Status: {response.status_code}", "PASS")
                self.test_results['passed'] += 1
                return True
            else:
                self.log(f"❌ {description or endpoint} - Expected: {expected_status}, Got: {response.status_code}", "FAIL")
                self.test_results['failed'] += 1
                self.test_results['errors'].append(f"{endpoint}: Status {response.status_code}")
                return False
                
        except Exception as e:
            self.log(f"❌ {description or endpoint} - Error: {str(e)}", "ERROR")
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"{endpoint}: {str(e)}")
            return False
    
    def test_api(self, endpoint, method='GET', data=None, expected_status=200, description=""):
        """Test an API endpoint"""
        try:
            url = f"{self.base_url}/api{endpoint}"
            
            if method == 'GET':
                response = self.session.get(url, timeout=10)
            elif method == 'POST':
                response = self.session.post(url, json=data, timeout=10)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            if response.status_code == expected_status:
                try:
                    json_data = response.json()
                    if json_data.get('success', True):
                        self.log(f"✅ API {description or endpoint} - Status: {response.status_code}", "PASS")
                        self.test_results['passed'] += 1
                        return True, json_data
                    else:
                        self.log(f"❌ API {description or endpoint} - API returned success=false", "FAIL")
                        self.test_results['failed'] += 1
                        return False, json_data
                except json.JSONDecodeError:
                    self.log(f"❌ API {description or endpoint} - Invalid JSON response", "FAIL")
                    self.test_results['failed'] += 1
                    return False, None
            else:
                self.log(f"❌ API {description or endpoint} - Expected: {expected_status}, Got: {response.status_code}", "FAIL")
                self.test_results['failed'] += 1
                self.test_results['errors'].append(f"API {endpoint}: Status {response.status_code}")
                return False, None
                
        except Exception as e:
            self.log(f"❌ API {description or endpoint} - Error: {str(e)}", "ERROR")
            self.test_results['failed'] += 1
            self.test_results['errors'].append(f"API {endpoint}: {str(e)}")
            return False, None
    
    def run_all_tests(self):
        """Run comprehensive system tests"""
        self.log("🚀 Starting ProTech Accounting System Tests", "INFO")
        self.log("=" * 60, "INFO")
        
        # Test server availability
        self.log("📡 Testing server availability...", "INFO")
        if not self.test_page('/', description="Main server"):
            self.log("❌ Server is not responding. Please start the Flask application.", "ERROR")
            return False
        
        # Test main pages
        self.log("\n📄 Testing main pages...", "INFO")
        pages = [
            ('/', 'Dashboard'),
            ('/inventory', 'Inventory Management'),
            ('/customers', 'Customer Management'),
            ('/sales', 'Sales Management'),
            ('/suppliers', 'Supplier Management'),
            ('/reports', 'Reports & Analytics'),
            ('/test', 'System Test Page')
        ]
        
        for endpoint, description in pages:
            self.test_page(endpoint, description=description)
            time.sleep(0.5)  # Small delay between requests
        
        # Test API endpoints
        self.log("\n🔌 Testing API endpoints...", "INFO")
        apis = [
            ('/products', 'GET', None, 'Products API'),
            ('/low-stock', 'GET', None, 'Low Stock API'),
            ('/inventory-movements', 'GET', None, 'Inventory Movements API'),
            ('/dashboard-stats', 'GET', None, 'Dashboard Statistics API'),
            ('/search?q=laptop', 'GET', None, 'Global Search API')
        ]
        
        for endpoint, method, data, description in apis:
            success, response_data = self.test_api(endpoint, method, data, description=description)
            if success and response_data:
                if 'count' in response_data:
                    self.log(f"   📊 Data count: {response_data['count']}", "INFO")
                elif 'total_results' in response_data:
                    self.log(f"   🔍 Search results: {response_data['total_results']}", "INFO")
            time.sleep(0.5)
        
        # Test stock adjustment API
        self.log("\n⚙️ Testing interactive features...", "INFO")
        stock_data = {
            'product_id': 1,
            'new_quantity': 60,
            'reason': 'System Test',
            'notes': 'Automated test adjustment'
        }
        success, response_data = self.test_api('/adjust-stock', 'POST', stock_data, description='Stock Adjustment')
        
        # Test error handling
        self.log("\n🛡️ Testing error handling...", "INFO")
        self.test_page('/nonexistent-page', expected_status=404, description="404 Error Page")
        
        # Test API error handling
        invalid_stock_data = {
            'product_id': 999999,  # Non-existent product
            'new_quantity': -5,    # Invalid quantity
            'reason': ''           # Missing reason
        }
        self.test_api('/adjust-stock', 'POST', invalid_stock_data, expected_status=400, description='Invalid Stock Adjustment')
        
        # Performance tests
        self.log("\n⚡ Testing performance...", "INFO")
        start_time = time.time()
        self.test_page('/')
        load_time = (time.time() - start_time) * 1000
        
        if load_time < 1000:  # Less than 1 second
            self.log(f"✅ Page load time: {load_time:.2f}ms (Good)", "PASS")
            self.test_results['passed'] += 1
        elif load_time < 3000:  # Less than 3 seconds
            self.log(f"⚠️ Page load time: {load_time:.2f}ms (Acceptable)", "WARN")
        else:
            self.log(f"❌ Page load time: {load_time:.2f}ms (Slow)", "FAIL")
            self.test_results['failed'] += 1
        
        # Test concurrent requests
        self.log("\n🔄 Testing concurrent requests...", "INFO")
        import threading
        
        def concurrent_request():
            self.session.get(f"{self.base_url}/api/products")
        
        threads = []
        start_time = time.time()
        
        for i in range(5):
            thread = threading.Thread(target=concurrent_request)
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        concurrent_time = (time.time() - start_time) * 1000
        self.log(f"✅ 5 concurrent requests completed in {concurrent_time:.2f}ms", "PASS")
        
        # Generate test report
        self.generate_report()
        
        return self.test_results['failed'] == 0
    
    def generate_report(self):
        """Generate test report"""
        self.log("\n" + "=" * 60, "INFO")
        self.log("📊 TEST REPORT / تقرير الاختبار", "INFO")
        self.log("=" * 60, "INFO")
        
        total_tests = self.test_results['passed'] + self.test_results['failed']
        success_rate = (self.test_results['passed'] / total_tests * 100) if total_tests > 0 else 0
        
        self.log(f"✅ Tests Passed: {self.test_results['passed']}", "INFO")
        self.log(f"❌ Tests Failed: {self.test_results['failed']}", "INFO")
        self.log(f"📈 Success Rate: {success_rate:.1f}%", "INFO")
        
        if self.test_results['failed'] == 0:
            self.log("🎉 ALL TESTS PASSED! / جميع الاختبارات نجحت!", "PASS")
            self.log("✨ ProTech Accounting System is working perfectly!", "PASS")
        else:
            self.log("⚠️ SOME TESTS FAILED / بعض الاختبارات فشلت", "WARN")
            self.log("🔍 Error Details:", "INFO")
            for error in self.test_results['errors']:
                self.log(f"   • {error}", "ERROR")
        
        self.log("=" * 60, "INFO")
        
        # Save report to file
        report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(f"ProTech Accounting System Test Report\n")
                f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"{'='*60}\n\n")
                f.write(f"Tests Passed: {self.test_results['passed']}\n")
                f.write(f"Tests Failed: {self.test_results['failed']}\n")
                f.write(f"Success Rate: {success_rate:.1f}%\n\n")
                
                if self.test_results['errors']:
                    f.write("Errors:\n")
                    for error in self.test_results['errors']:
                        f.write(f"  - {error}\n")
                
            self.log(f"📄 Test report saved to: {report_file}", "INFO")
        except Exception as e:
            self.log(f"⚠️ Could not save report: {e}", "WARN")

def main():
    """Main function"""
    print("🧪 ProTech Accounting System - Comprehensive Test Suite")
    print("اختبار شامل لنظام ProTech للمحاسبة")
    print("=" * 60)
    
    # Check if server URL is provided
    base_url = 'http://localhost:5000'
    if len(sys.argv) > 1:
        base_url = sys.argv[1]
    
    print(f"🌐 Testing server: {base_url}")
    print("⏳ Starting tests...\n")
    
    # Create tester and run tests
    tester = ProTechSystemTester(base_url)
    success = tester.run_all_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)

if __name__ == '__main__':
    main()
