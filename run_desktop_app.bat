@echo off
title ProTech Accounting System - Desktop Application
color 0A

echo.
echo ============================================================
echo    🖥️ تطبيق ProTech لسطح المكتب
echo    🖥️ ProTech Desktop Application
echo ============================================================
echo.

echo [1/3] فحص Python / Checking Python...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python غير موجود / Python not found
    echo يرجى تثبيت Python أولاً / Please install Python first
    pause
    exit /b 1
)
echo ✅ Python موجود / Python found

echo.
echo [2/3] فحص المكتبات / Checking libraries...
python -c "import tkinter" >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ مكتبة tkinter غير موجودة / tkinter library not found
    pause
    exit /b 1
)
echo ✅ جميع المكتبات موجودة / All libraries found

echo.
echo [3/3] تشغيل التطبيق / Starting application...
echo.
echo ┌─────────────────────────────────────────────────────────┐
echo │  🖥️ تطبيق ProTech لسطح المكتب                         │
echo │  🖥️ ProTech Desktop Application                        │
echo │                                                         │
echo │  📋 المميزات / Features:                               │
echo │  ✅ واجهة سطح مكتب سهلة الاستخدام                     │
echo │  ✅ تحكم كامل في النظام                               │
echo │  ✅ فتح المتصفح تلقائياً                               │
echo │  ✅ مراقبة حالة النظام                                │
echo │  ✅ سجل نشاط مفصل                                     │
echo │                                                         │
echo │  🎯 التعليمات / Instructions:                          │
echo │  1. اضغط "تشغيل النظام" لبدء الخادم                    │
echo │  2. اضغط "فتح المتصفح" للوصول للتطبيق                │
echo │  3. استخدم النظام من خلال المتصفح                     │
echo │  4. اضغط "إيقاف النظام" عند الانتهاء                  │
echo └─────────────────────────────────────────────────────────┘
echo.

python protech_desktop.py

echo.
echo 🛑 تم إغلاق التطبيق / Application closed
pause
