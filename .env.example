# Database
DATABASE_URL="postgresql://username:password@localhost:5432/protech_accounting?schema=public"

# NextAuth.js
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key-here"

# JWT
JWT_SECRET="your-jwt-secret-here"

# Application Settings
APP_NAME="ProTech Accounting"
APP_VERSION="1.0.0"
APP_URL="http://localhost:3000"

# Email Configuration (Optional)
SMTP_HOST=""
SMTP_PORT=""
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# File Upload
MAX_FILE_SIZE="5242880" # 5MB in bytes
UPLOAD_DIR="./uploads"

# Barcode Settings
BARCODE_PREFIX="PT"
BARCODE_LENGTH="13"

# Default Settings
DEFAULT_CURRENCY="USD"
DEFAULT_TAX_RATE="0.15"
DEFAULT_LANGUAGE="en"

# Development
NODE_ENV="development"
