#!/usr/bin/env python3

print("🚀 بدء اختبار Flask / Starting Flask test...")

try:
    from flask import Flask
    print("✅ تم استيراد Flask بنجاح / Flask imported successfully")
    
    app = Flask(__name__)
    print("✅ تم إنشاء تطبيق Flask / Flask app created")
    
    @app.route('/')
    def home():
        return '''
        <html>
        <head>
            <title>ProTech Test</title>
            <meta charset="utf-8">
        </head>
        <body style="font-family: Arial; padding: 20px; background: #f0f8ff;">
            <h1 style="color: #2563eb;">🎉 نجح الاختبار! / Test Successful!</h1>
            <h2 style="color: #059669;">نظام ProTech للمحاسبة / ProTech Accounting System</h2>
            <div style="background: #dcfce7; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <p><strong>✅ حالة الخادم / Server Status:</strong> يعمل / Running</p>
                <p><strong>🐍 Python Flask:</strong> نشط / Active</p>
                <p><strong>🌐 URL:</strong> http://localhost:5000</p>
            </div>
            <div style="background: #fef3c7; padding: 15px; border-radius: 8px;">
                <p><strong>🚀 الخطوة التالية / Next Step:</strong></p>
                <p>الآن يمكن تشغيل التطبيق الكامل / Now we can run the full application</p>
            </div>
        </body>
        </html>
        '''
    
    print("✅ تم تعريف المسار / Route defined")
    print("🌐 بدء الخادم على / Starting server on: http://localhost:5000")
    print("=" * 50)
    
    app.run(host='0.0.0.0', port=5000, debug=True, use_reloader=False)
    
except Exception as e:
    print(f"❌ خطأ / Error: {e}")
    import traceback
    traceback.print_exc()
