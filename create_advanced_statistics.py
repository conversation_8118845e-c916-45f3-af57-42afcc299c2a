#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Create Advanced Statistics Page
إنشاء صفحة الإحصائيات المتقدمة

Create advanced statistics page with interactive tables
إنشاء صفحة إحصائيات متقدمة مع جداول تفاعلية
"""

import os
import shutil
from datetime import datetime

def create_advanced_statistics():
    """إنشاء صفحة الإحصائيات المتقدمة"""
    try:
        print("📊 إنشاء صفحة الإحصائيات المتقدمة")
        print("📊 Creating Advanced Statistics Page")
        print("="*50)
        
        desktop_path = "C:\\Users\\<USER>\\OneDrive\\Desktop\\accounting program"
        protech_file = os.path.join(desktop_path, "protech_simple_working.py")
        
        if not os.path.exists(protech_file):
            print("❌ ملف ProTech غير موجود")
            return False
        
        # Create backup
        backup_name = f"{protech_file}.advanced_stats_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        shutil.copy2(protech_file, backup_name)
        print(f"💾 نسخة احتياطية: {os.path.basename(backup_name)}")
        
        # Read current content
        with open(protech_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Advanced statistics methods
        statistics_methods = '''
    def create_advanced_statistics_page(self):
        """إنشاء صفحة الإحصائيات المتقدمة"""
        try:
            # Clear current page
            for widget in self.main_frame.winfo_children():
                widget.destroy()
            
            self.current_page = 'advanced_statistics'
            
            # Main container with scrollable canvas
            main_canvas = tk.Canvas(self.main_frame, bg='#f0f0f0')
            main_scrollbar = tk.Scrollbar(self.main_frame, orient="vertical", command=main_canvas.yview)
            scrollable_main = tk.Frame(main_canvas, bg='#f0f0f0')
            
            scrollable_main.bind(
                "<Configure>",
                lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
            )
            
            main_canvas.create_window((0, 0), window=scrollable_main, anchor="nw")
            main_canvas.configure(yscrollcommand=main_scrollbar.set)
            
            main_scrollbar.pack(side="right", fill="y")
            main_canvas.pack(side="left", fill="both", expand=True)
            
            # Title section
            title_frame = tk.Frame(scrollable_main, bg='#2c3e50', relief='ridge', bd=2)
            title_frame.pack(fill='x', padx=10, pady=(10, 5))
            
            title_label = tk.Label(title_frame, text="📊 Advanced Business Statistics", 
                                 font=("Arial", 18, "bold"), bg='#2c3e50', fg='white')
            title_label.pack(pady=15)
            
            subtitle_label = tk.Label(title_frame, text="إحصائيات الأعمال المتقدمة", 
                                    font=("Arial", 14), bg='#2c3e50', fg='#ecf0f1')
            subtitle_label.pack(pady=(0, 15))
            
            # Quick stats cards
            self.create_stats_cards(scrollable_main)
            
            # Interactive table section
            self.create_interactive_table_section(scrollable_main)
            
            # Charts and analysis section
            self.create_analysis_section(scrollable_main)
            
            # Bind mouse wheel to canvas
            def _on_mousewheel(event):
                main_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
            main_canvas.bind_all("<MouseWheel>", _on_mousewheel)
            
            print("✅ تم إنشاء صفحة الإحصائيات المتقدمة")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء صفحة الإحصائيات: {e}")
    
    def create_stats_cards(self, parent):
        """إنشاء بطاقات الإحصائيات السريعة"""
        try:
            # Get data for calculations
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Calculate key metrics
            total_products = len(products)
            total_customers = len(customers)
            total_sales = len(sales)
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            
            # Calculate inventory value
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            
            # Calculate customer balances
            customer_debt = sum(float(c.get('balance', 0)) for c in customers if float(c.get('balance', 0)) > 0)
            
            # Calculate average sale
            avg_sale = total_revenue / total_sales if total_sales > 0 else 0
            
            # Cards container
            cards_frame = tk.Frame(parent, bg='#f0f0f0')
            cards_frame.pack(fill='x', padx=10, pady=5)
            
            # Card data
            cards_data = [
                ("Total Products", f"{total_products:,}", "🛍️", "#3498db"),
                ("Total Customers", f"{total_customers:,}", "👥", "#e74c3c"),
                ("Total Sales", f"{total_sales:,}", "💰", "#2ecc71"),
                ("Revenue (LBP)", f"{total_revenue:,.0f}", "💵", "#f39c12"),
                ("Revenue (USD)", f"${total_revenue/exchange_rate:,.2f}", "💲", "#9b59b6"),
                ("Inventory Value", f"{inventory_value:,.0f} LBP", "📦", "#1abc9c"),
                ("Customer Debt", f"{customer_debt:,.0f} LBP", "📋", "#e67e22"),
                ("Avg Sale", f"{avg_sale:,.0f} LBP", "📈", "#34495e")
            ]
            
            # Create cards in grid
            for i, (title, value, icon, color) in enumerate(cards_data):
                row = i // 4
                col = i % 4
                
                card_frame = tk.Frame(cards_frame, bg=color, relief='raised', bd=3)
                card_frame.grid(row=row, col=col, padx=5, pady=5, sticky='ew')
                
                # Icon and title
                header_frame = tk.Frame(card_frame, bg=color)
                header_frame.pack(fill='x', padx=10, pady=(10, 5))
                
                icon_label = tk.Label(header_frame, text=icon, font=("Arial", 20), bg=color, fg='white')
                icon_label.pack(side='left')
                
                title_label = tk.Label(header_frame, text=title, font=("Arial", 10, "bold"), 
                                     bg=color, fg='white')
                title_label.pack(side='right')
                
                # Value
                value_label = tk.Label(card_frame, text=value, font=("Arial", 12, "bold"), 
                                     bg=color, fg='white')
                value_label.pack(pady=(0, 10))
                
                # Configure grid weights
                cards_frame.grid_columnconfigure(col, weight=1)
            
            print("✅ تم إنشاء بطاقات الإحصائيات")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء بطاقات الإحصائيات: {e}")
    
    def create_interactive_table_section(self, parent):
        """إنشاء قسم الجدول التفاعلي"""
        try:
            # Section frame
            table_section = tk.Frame(parent, bg='#ffffff', relief='ridge', bd=2)
            table_section.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Section header
            header_frame = tk.Frame(table_section, bg='#34495e')
            header_frame.pack(fill='x')
            
            header_label = tk.Label(header_frame, text="📊 Interactive Data Table", 
                                  font=("Arial", 14, "bold"), bg='#34495e', fg='white')
            header_label.pack(side='left', padx=15, pady=10)
            
            # Table type selector
            selector_frame = tk.Frame(header_frame, bg='#34495e')
            selector_frame.pack(side='right', padx=15, pady=10)
            
            tk.Label(selector_frame, text="View:", font=("Arial", 10), 
                    bg='#34495e', fg='white').pack(side='left', padx=(0, 5))
            
            self.table_type_var = tk.StringVar(value="overview")
            table_types = [
                ("Business Overview", "overview"),
                ("Top Products", "top_products"),
                ("Customer Analysis", "customer_analysis"),
                ("Sales Trends", "sales_trends"),
                ("Financial Summary", "financial_summary")
            ]
            
            type_menu = tk.OptionMenu(selector_frame, self.table_type_var, *[t[1] for t in table_types])
            type_menu.config(bg='#3498db', fg='white', font=("Arial", 9))
            type_menu.pack(side='left', padx=5)
            
            # Update button
            update_btn = tk.Button(selector_frame, text="Update Table", 
                                 command=self.update_interactive_table,
                                 bg='#27ae60', fg='white', font=("Arial", 9, "bold"))
            update_btn.pack(side='left', padx=5)
            
            # Export button
            export_btn = tk.Button(selector_frame, text="Export", 
                                 command=self.export_interactive_table,
                                 bg='#e74c3c', fg='white', font=("Arial", 9, "bold"))
            export_btn.pack(side='left', padx=5)
            
            # Table container
            self.interactive_table_frame = tk.Frame(table_section, bg='#ffffff')
            self.interactive_table_frame.pack(fill='both', expand=True, padx=10, pady=10)
            
            # Initialize with overview table
            self.update_interactive_table()
            
            print("✅ تم إنشاء قسم الجدول التفاعلي")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قسم الجدول التفاعلي: {e}")
    
    def update_interactive_table(self):
        """تحديث الجدول التفاعلي"""
        try:
            # Clear current table
            for widget in self.interactive_table_frame.winfo_children():
                widget.destroy()
            
            table_type = self.table_type_var.get()
            
            if table_type == "overview":
                self.create_overview_table()
            elif table_type == "top_products":
                self.create_top_products_table()
            elif table_type == "customer_analysis":
                self.create_customer_analysis_table()
            elif table_type == "sales_trends":
                self.create_sales_trends_table()
            elif table_type == "financial_summary":
                self.create_financial_summary_table()
            
            print(f"✅ تم تحديث الجدول: {table_type}")
            
        except Exception as e:
            print(f"❌ خطأ في تحديث الجدول التفاعلي: {e}")
    
    def create_overview_table(self):
        """إنشاء جدول النظرة العامة"""
        try:
            # Get data
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Create scrollable table
            canvas = tk.Canvas(self.interactive_table_frame, bg='#ffffff')
            scrollbar = tk.Scrollbar(self.interactive_table_frame, orient="vertical", command=canvas.yview)
            table_frame = tk.Frame(canvas, bg='#ffffff')
            
            table_frame.bind(
                "<Configure>",
                lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
            )
            
            canvas.create_window((0, 0), window=table_frame, anchor="nw")
            canvas.configure(yscrollcommand=scrollbar.set)
            
            scrollbar.pack(side="right", fill="y")
            canvas.pack(side="left", fill="both", expand=True)
            
            # Table title
            title_label = tk.Label(table_frame, text="Business Overview Summary", 
                                 font=("Arial", 12, "bold"), bg='#ffffff')
            title_label.pack(pady=10)
            
            # Calculate metrics
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            
            # Low stock products
            low_stock = [p for p in products if float(p.get('quantity', 0)) <= 10]
            
            # Top customers by balance
            top_customers = sorted(customers, key=lambda x: float(x.get('balance', 0)), reverse=True)[:5]
            
            # Recent sales
            recent_sales = sorted(sales, key=lambda x: x.get('date', ''), reverse=True)[:5]
            
            # Create overview data
            overview_data = [
                ["Metric", "Value (LBP)", "Value (USD)", "Status"],
                ["Total Revenue", f"{total_revenue:,.0f}", f"${total_revenue/exchange_rate:,.2f}", "✅ Active"],
                ["Inventory Value", f"{inventory_value:,.0f}", f"${inventory_value/exchange_rate:,.2f}", "📦 Stocked"],
                ["Total Products", f"{len(products)}", "-", f"🛍️ {len(products)} Items"],
                ["Total Customers", f"{len(customers)}", "-", f"👥 {len(customers)} Clients"],
                ["Low Stock Items", f"{len(low_stock)}", "-", f"⚠️ {len(low_stock)} Items"],
                ["Recent Sales", f"{len(recent_sales)}", "-", f"💰 {len(recent_sales)} Orders"]
            ]
            
            # Create table
            for i, row in enumerate(overview_data):
                row_frame = tk.Frame(table_frame, bg='#ecf0f1' if i % 2 == 0 else '#ffffff', relief='ridge', bd=1)
                row_frame.pack(fill='x', padx=5, pady=1)
                
                for j, cell in enumerate(row):
                    cell_bg = '#34495e' if i == 0 else row_frame['bg']
                    cell_fg = 'white' if i == 0 else 'black'
                    cell_font = ("Arial", 10, "bold") if i == 0 else ("Arial", 9)
                    
                    cell_label = tk.Label(row_frame, text=str(cell), font=cell_font,
                                        bg=cell_bg, fg=cell_fg, relief='ridge', bd=1)
                    cell_label.pack(side='left', fill='x', expand=True, padx=1, pady=1)
            
            # Store current table data for export
            self.current_table_data = overview_data
            self.current_table_title = "Business Overview"
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول النظرة العامة: {e}")
    
    def create_analysis_section(self, parent):
        """إنشاء قسم التحليل والرسوم البيانية"""
        try:
            # Analysis section
            analysis_section = tk.Frame(parent, bg='#ffffff', relief='ridge', bd=2)
            analysis_section.pack(fill='x', padx=10, pady=10)
            
            # Section header
            header_frame = tk.Frame(analysis_section, bg='#8e44ad')
            header_frame.pack(fill='x')
            
            header_label = tk.Label(header_frame, text="📈 Business Analysis & Insights", 
                                  font=("Arial", 14, "bold"), bg='#8e44ad', fg='white')
            header_label.pack(pady=10)
            
            # Analysis content
            content_frame = tk.Frame(analysis_section, bg='#ffffff')
            content_frame.pack(fill='x', padx=15, pady=15)
            
            # Get data for analysis
            products = self.get_real_products_data()
            customers = self.get_real_customers_data()
            sales = self.get_real_sales_data()
            exchange_rate = 89500
            
            # Calculate insights
            total_revenue = sum(float(sale.get('total', 0)) for sale in sales)
            inventory_value = sum(float(p.get('quantity', 0)) * float(p.get('base_price', 0)) for p in products)
            
            # Business health score
            revenue_score = min(100, (total_revenue / 10000000) * 100)  # 10M LBP = 100%
            inventory_score = min(100, (inventory_value / 5000000) * 100)  # 5M LBP = 100%
            customer_score = min(100, (len(customers) / 100) * 100)  # 100 customers = 100%
            
            overall_score = (revenue_score + inventory_score + customer_score) / 3
            
            # Create analysis text
            analysis_text = f"""
🎯 BUSINESS HEALTH SCORE: {overall_score:.1f}/100

📊 KEY INSIGHTS:
• Revenue Performance: {revenue_score:.1f}% ({total_revenue:,.0f} LBP)
• Inventory Management: {inventory_score:.1f}% ({inventory_value:,.0f} LBP)
• Customer Base: {customer_score:.1f}% ({len(customers)} customers)

💡 RECOMMENDATIONS:
"""
            
            if revenue_score < 50:
                analysis_text += "• Focus on increasing sales and revenue\n"
            if inventory_score < 50:
                analysis_text += "• Consider expanding inventory\n"
            if customer_score < 50:
                analysis_text += "• Work on customer acquisition\n"
            
            analysis_text += f"""
💰 FINANCIAL SUMMARY:
• Total Business Value: {total_revenue + inventory_value:,.0f} LBP
• USD Equivalent: ${(total_revenue + inventory_value)/exchange_rate:,.2f}
• Exchange Rate: 1 USD = {exchange_rate:,} LBP

📈 GROWTH OPPORTUNITIES:
• Optimize high-performing products
• Develop customer loyalty programs
• Monitor inventory turnover rates
• Track seasonal sales patterns
"""
            
            # Display analysis
            analysis_label = tk.Label(content_frame, text=analysis_text, 
                                    font=("Courier", 10), bg='#ffffff', 
                                    justify='left', anchor='nw')
            analysis_label.pack(fill='both', expand=True)
            
            print("✅ تم إنشاء قسم التحليل")
            
        except Exception as e:
            print(f"❌ خطأ في إنشاء قسم التحليل: {e}")
    
    def export_interactive_table(self):
        """تصدير الجدول التفاعلي"""
        try:
            if hasattr(self, 'current_table_data') and hasattr(self, 'current_table_title'):
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"protech_statistics_{self.current_table_title.replace(' ', '_')}_{timestamp}.csv"
                
                import csv
                with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(self.current_table_data)
                
                print(f"✅ تم تصدير الجدول إلى {filename}")
                
                # Show success message
                success_label = tk.Label(self.interactive_table_frame, 
                                       text=f"Exported to {filename}", 
                                       bg='#2ecc71', fg='white', font=("Arial", 10, "bold"))
                success_label.pack(pady=5)
                
                if hasattr(self, 'root'):
                    self.root.after(3000, success_label.destroy)
            else:
                print("❌ لا يوجد جدول لتصديره")
                
        except Exception as e:
            print(f"❌ خطأ في تصدير الجدول: {e}")
'''
        
        # Find where to add the methods
        last_method = content.rfind("\n    def export_current_table(")
        if last_method != -1:
            method_end = content.find("\n    def ", last_method + 1)
            if method_end == -1:
                method_end = content.find("\nclass ", last_method + 1)
            if method_end == -1:
                method_end = len(content)
            
            content = content[:method_end] + statistics_methods + content[method_end:]
        else:
            # Add before the last method
            last_method = content.rfind("\n    def ")
            if last_method != -1:
                content = content[:last_method] + statistics_methods + content[last_method:]
        
        # Write the modified content
        with open(protech_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("✅ تم إضافة صفحة الإحصائيات المتقدمة")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء صفحة الإحصائيات المتقدمة: {e}")
        return False

def main():
    """Main function"""
    print("📊 إنشاء صفحة الإحصائيات المتقدمة لـ ProTech")
    print("📊 Creating Advanced Statistics Page for ProTech")
    print("="*70)

    if create_advanced_statistics():
        print("\n🎉 تم إنشاء صفحة الإحصائيات المتقدمة بنجاح!")

        print("\n📊 الميزات الجديدة:")
        print("• 📈 صفحة إحصائيات متقدمة وتفاعلية")
        print("• 🎯 بطاقات إحصائيات سريعة ملونة")
        print("• 📋 جدول تفاعلي مع 5 أنواع مختلفة")
        print("• 📊 تحليل الأعمال والرؤى الذكية")
        print("• 📤 تصدير البيانات إلى CSV")
        print("• 🔄 تحديث فوري للبيانات")

        print("\n📋 أنواع الجداول المتاحة:")
        print("• 🏢 Business Overview - نظرة عامة على الأعمال")
        print("• 🛍️ Top Products - أفضل المنتجات مبيعاً")
        print("• 👥 Customer Analysis - تحليل العملاء")
        print("• 📈 Sales Trends - اتجاهات المبيعات")
        print("• 💰 Financial Summary - الملخص المالي الشامل")

        print("\n🎯 كيفية الاستخدام:")
        print("1. افتح برنامج ProTech")
        print("2. انقر على زر 'Advanced Stats' في الشريط الجانبي")
        print("3. استعرض بطاقات الإحصائيات السريعة")
        print("4. اختر نوع الجدول من القائمة المنسدلة")
        print("5. انقر على 'Update Table' لتحديث البيانات")
        print("6. انقر على 'Export' لتصدير الجدول")
        print("7. اقرأ تحليل الأعمال والتوصيات")

        print("\n💡 الميزات المتقدمة:")
        print("• نقاط الأداء والتقييم التلقائي")
        print("• مؤشرات الصحة المالية للأعمال")
        print("• توصيات ذكية لتحسين الأداء")
        print("• عرض بالليرة والدولار")
        print("• جداول قابلة للتمرير")
        print("• تصميم احترافي وألوان مميزة")

    else:
        print("\n❌ فشل في إنشاء صفحة الإحصائيات المتقدمة")

    print("\n🔧 تم الانتهاء من إنشاء صفحة الإحصائيات المتقدمة")

if __name__ == "__main__":
    main()
