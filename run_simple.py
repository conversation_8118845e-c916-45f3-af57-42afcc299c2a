#!/usr/bin/env python3
"""
Simple runner for ProTech Accounting System
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

print("🚀 Starting ProTech Accounting System...")
print("📍 Current directory:", os.getcwd())
print("🐍 Python version:", sys.version)

try:
    # Import and run the Flask app
    from app import app
    
    print("✅ Flask app imported successfully")
    print("🌐 Starting server on http://localhost:5000")
    print("📱 Access the application at: http://localhost:5000")
    print("🧪 Test page available at: http://localhost:5000/test")
    print("=" * 50)
    
    # Run the Flask application
    app.run(
        debug=True,
        host='0.0.0.0',
        port=5000,
        threaded=True,
        use_reloader=False  # Disable reloader to avoid issues
    )
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure Flask is installed: pip install flask")
    sys.exit(1)
    
except Exception as e:
    print(f"❌ Error starting server: {e}")
    sys.exit(1)
