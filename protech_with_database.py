#!/usr/bin/env python3
"""
ProTech with Advanced Database Integration
نظام ProTech مع تكامل قاعدة البيانات المتقدمة

Integration of ProTech application with advanced SQLite database manager
تكامل تطبيق ProTech مع مدير قاعدة البيانات SQLite المتقدم
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import os
from datetime import datetime
import threading
import time
import gc
import logging
from database_manager import ProTechDatabaseManager

class ProTechWithDatabase:
    """ProTech application with advanced database integration"""

    def __init__(self):
        print("🚀 تشغيل نظام ProTech مع قاعدة البيانات المتقدمة...")
        print("🚀 Starting ProTech with Advanced Database...")

        # Initialize timing
        self.start_time = time.time()
        self.startup_start_time = time.time()
        
        # Performance optimization flags
        self.loading = True
        self.cache_enabled = True
        self.auto_save_enabled = True
        self.last_save_time = time.time()

        # Initialize advanced database manager
        try:
            self.db_manager = ProTechDatabaseManager()
            print("✅ تم تهيئة مدير قاعدة البيانات المتقدم")
        except Exception as e:
            print(f"❌ خطأ في تهيئة قاعدة البيانات: {e}")
            messagebox.showerror("خطأ قاعدة البيانات", f"فشل في تهيئة قاعدة البيانات:\n{str(e)}")
            return

        # Performance statistics
        self.performance_stats = {
            'startup_time': time.time(),
            'database_operations': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'ui_operations': 0,
            'search_operations': 0
        }

        # Initialize main window
        self.root = tk.Tk()
        self.root.title("نظام ProTech مع قاعدة البيانات المتقدمة - ProTech with Advanced Database")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f8ff')
        self.root.state('zoomed')  # Start maximized

        # Setup window close handler
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # Create interface
        self.create_interface()

        # Load data from database
        self.load_data_from_database()

        # Start background tasks
        self.start_background_tasks()

        self.loading = False

        # Record startup time
        startup_duration = time.time() - self.startup_start_time
        self.performance_stats['startup_time'] = startup_duration

        print(f"✅ تم تحميل النظام مع قاعدة البيانات في {startup_duration:.2f} ثانية!")
        print(f"✅ System with database loaded in {startup_duration:.2f} seconds!")

    def create_interface(self):
        """Create enhanced interface with database features"""
        # Header with database status
        header_frame = tk.Frame(self.root, bg='#1e40af', height=100)
        header_frame.pack(fill='x')
        header_frame.pack_propagate(False)

        # Title
        title_label = tk.Label(
            header_frame,
            text="🏢 نظام ProTech مع قاعدة البيانات المتقدمة",
            font=('Arial', 22, 'bold'),
            fg='white',
            bg='#1e40af'
        )
        title_label.pack(side='left', padx=20, pady=20)

        # Database status
        self.db_status_label = tk.Label(
            header_frame,
            text="🗄️ قاعدة البيانات: متصلة",
            font=('Arial', 12, 'bold'),
            fg='#10b981',
            bg='#1e40af'
        )
        self.db_status_label.pack(side='right', padx=20, pady=20)

        # Main container
        main_frame = tk.Frame(self.root, bg='#f0f8ff')
        main_frame.pack(fill='both', expand=True, padx=10, pady=10)

        # Navigation panel with database features
        nav_frame = tk.Frame(main_frame, bg='#3b82f6', width=250)
        nav_frame.pack(side='left', fill='y', padx=(0, 10))
        nav_frame.pack_propagate(False)

        tk.Label(
            nav_frame,
            text="📋 القوائم الرئيسية\nMain Menus",
            font=('Arial', 14, 'bold'),
            fg='white',
            bg='#3b82f6'
        ).pack(pady=20)

        # Enhanced navigation buttons
        nav_buttons = [
            ("🏠 لوحة التحكم\nDashboard", self.show_dashboard),
            ("📦 المخزون\nInventory", self.show_inventory),
            ("👥 العملاء\nCustomers", self.show_customers),
            ("🏢 الموردين\nSuppliers", self.show_suppliers),
            ("💰 المبيعات\nSales", self.show_sales),
            ("📊 التقارير\nReports", self.show_reports),
            ("🗄️ قاعدة البيانات\nDatabase", self.show_database_management),
            ("⚙️ الإعدادات\nSettings", self.show_settings)
        ]

        for text, command in nav_buttons:
            btn = tk.Button(
                nav_frame,
                text=text,
                font=('Arial', 11, 'bold'),
                fg='white',
                bg='#3b82f6',
                activebackground='#1d4ed8',
                relief='flat',
                width=20,
                height=3,
                command=command,
                cursor='hand2'
            )
            btn.pack(pady=5, padx=10, fill='x')

        # Content area
        self.content_frame = tk.Frame(main_frame, bg='white', relief='ridge', bd=2)
        self.content_frame.pack(side='right', fill='both', expand=True)

        # Enhanced status bar with database info
        status_frame = tk.Frame(self.root, bg='#374151', height=35)
        status_frame.pack(fill='x', side='bottom')
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="جاهز / Ready",
            font=('Arial', 10),
            fg='white',
            bg='#374151'
        )
        self.status_label.pack(side='left', padx=10, pady=5)

        # Database stats in status bar
        self.db_stats_label = tk.Label(
            status_frame,
            text="قاعدة البيانات: جاهزة",
            font=('Arial', 10),
            fg='#10b981',
            bg='#374151'
        )
        self.db_stats_label.pack(side='right', padx=10, pady=5)

        # Show dashboard by default
        self.show_dashboard()

    def load_data_from_database(self):
        """Load data from database"""
        try:
            print("📥 تحميل البيانات من قاعدة البيانات...")
            
            # Load all data
            self.suppliers = self.db_manager.get_all_suppliers()
            self.products = self.db_manager.get_all_products()
            self.customers = self.db_manager.get_all_customers()
            self.sales = self.db_manager.get_all_sales()
            
            print(f"✅ تم تحميل {len(self.products)} منتج، {len(self.customers)} عميل، {len(self.suppliers)} مورد، {len(self.sales)} فاتورة")
            
            # Update status
            self.update_database_status()
            
        except Exception as e:
            print(f"❌ خطأ في تحميل البيانات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات من قاعدة البيانات:\n{str(e)}")

    def update_database_status(self):
        """Update database status in UI"""
        try:
            stats = self.db_manager.get_database_stats()
            
            # Update status labels
            db_size_mb = stats.get('database_size', 0) / (1024 * 1024)
            cache_hit_rate = stats.get('cache_stats', {}).get('cache_hit_rate', 0) * 100
            
            status_text = f"قاعدة البيانات: {db_size_mb:.1f}MB | معدل الذاكرة المؤقتة: {cache_hit_rate:.1f}%"
            self.db_stats_label.config(text=status_text)
            
        except Exception as e:
            print(f"خطأ في تحديث حالة قاعدة البيانات: {e}")

    def start_background_tasks(self):
        """Start background tasks for database maintenance"""
        try:
            # Status update thread
            self.status_update_thread = threading.Thread(target=self.status_update_worker, daemon=True)
            self.status_update_thread.start()
            
            print("✅ تم تشغيل مهام الخلفية")
            
        except Exception as e:
            print(f"خطأ في تشغيل المهام الخلفية: {e}")

    def status_update_worker(self):
        """Background worker to update status"""
        while True:
            try:
                time.sleep(30)  # Update every 30 seconds
                if hasattr(self, 'root') and self.root.winfo_exists():
                    self.root.after(0, self.update_database_status)
            except Exception as e:
                print(f"خطأ في تحديث الحالة: {e}")
                break

    def clear_content(self):
        """Clear content area"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def show_dashboard(self):
        """Show enhanced dashboard with database statistics"""
        self.clear_content()
        
        # Title
        tk.Label(
            self.content_frame,
            text="📊 لوحة التحكم المتقدمة / Advanced Dashboard",
            font=('Arial', 20, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Database statistics
        try:
            stats = self.db_manager.get_database_stats()
            
            # Main stats frame
            stats_frame = tk.Frame(self.content_frame, bg='white')
            stats_frame.pack(fill='x', padx=20, pady=10)

            # Calculate stats
            table_counts = stats.get('table_counts', {})
            total_products = table_counts.get('products', 0)
            total_customers = table_counts.get('customers', 0)
            total_suppliers = table_counts.get('suppliers', 0)
            total_sales = table_counts.get('sales', 0)

            # Create enhanced stat cards
            stat_cards = [
                ("📦", "المنتجات\nProducts", total_products, "#3b82f6"),
                ("👥", "العملاء\nCustomers", total_customers, "#10b981"),
                ("🏢", "الموردين\nSuppliers", total_suppliers, "#06b6d4"),
                ("🧾", "المبيعات\nSales", total_sales, "#f59e0b")
            ]

            for icon, title, value, color in stat_cards:
                card = tk.Frame(stats_frame, bg=color, relief='raised', bd=3)
                card.pack(side='left', fill='both', expand=True, padx=5)

                tk.Label(card, text=icon, font=('Arial', 28), fg='white', bg=color).pack(pady=8)
                tk.Label(card, text=title, font=('Arial', 11, 'bold'), fg='white', bg=color).pack()
                tk.Label(card, text=str(value), font=('Arial', 18, 'bold'), fg='white', bg=color).pack(pady=8)

            # Database performance stats
            perf_frame = tk.LabelFrame(self.content_frame, text="📈 إحصائيات الأداء / Performance Stats", 
                                     font=('Arial', 14, 'bold'), bg='white', fg='#1f2937')
            perf_frame.pack(fill='x', padx=20, pady=20)

            query_stats = stats.get('query_stats', {})
            cache_stats = stats.get('cache_stats', {})
            
            perf_info = [
                f"إجمالي الاستعلامات / Total Queries: {query_stats.get('total_queries', 0)}",
                f"الاستعلامات البطيئة / Slow Queries: {query_stats.get('slow_queries', 0)}",
                f"معدل نجاح الذاكرة المؤقتة / Cache Hit Rate: {cache_stats.get('cache_hit_rate', 0)*100:.1f}%",
                f"حجم قاعدة البيانات / Database Size: {stats.get('database_size', 0)/(1024*1024):.1f} MB",
                f"عدد النسخ الاحتياطية / Backup Count: {stats.get('backup_count', 0)}"
            ]

            for info in perf_info:
                tk.Label(perf_frame, text=info, font=('Arial', 11), bg='white', fg='#374151').pack(anchor='w', padx=10, pady=2)

        except Exception as e:
            tk.Label(
                self.content_frame,
                text=f"خطأ في تحميل الإحصائيات: {str(e)}",
                font=('Arial', 12),
                fg='red',
                bg='white'
            ).pack(pady=20)

    def show_inventory(self):
        """Show enhanced inventory management with database features"""
        self.clear_content()
        
        tk.Label(
            self.content_frame,
            text="📦 إدارة المخزون المتقدمة / Advanced Inventory Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Search frame
        search_frame = tk.Frame(self.content_frame, bg='white')
        search_frame.pack(fill='x', padx=20, pady=10)

        tk.Label(search_frame, text="🔍 البحث:", font=('Arial', 12, 'bold'), bg='white').pack(side='left', padx=5)
        
        self.search_var = tk.StringVar()
        search_entry = tk.Entry(search_frame, textvariable=self.search_var, font=('Arial', 12), width=30)
        search_entry.pack(side='left', padx=5)
        search_entry.bind('<KeyRelease>', self.on_search_products)

        # Refresh button
        refresh_btn = tk.Button(
            search_frame,
            text="🔄 تحديث",
            font=('Arial', 10, 'bold'),
            bg='#3b82f6',
            fg='white',
            command=self.refresh_products,
            cursor='hand2'
        )
        refresh_btn.pack(side='left', padx=10)

        # Products table with enhanced columns
        table_frame = tk.Frame(self.content_frame, bg='white')
        table_frame.pack(fill='both', expand=True, padx=20, pady=10)

        columns = ('ID', 'Barcode', 'Name', 'Category', 'Supplier', 'Stock', 'Min Stock', 'Price', 'Status')
        self.products_tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=20)

        # Configure columns
        column_widths = {'ID': 50, 'Barcode': 120, 'Name': 200, 'Category': 120, 'Supplier': 150, 
                        'Stock': 80, 'Min Stock': 80, 'Price': 100, 'Status': 80}
        
        for col in columns:
            self.products_tree.heading(col, text=col)
            self.products_tree.column(col, width=column_widths.get(col, 100), anchor='center')

        # Scrollbars
        v_scrollbar = ttk.Scrollbar(table_frame, orient='vertical', command=self.products_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_frame, orient='horizontal', command=self.products_tree.xview)
        self.products_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack scrollbars and tree
        self.products_tree.pack(side='left', fill='both', expand=True)
        v_scrollbar.pack(side='right', fill='y')
        h_scrollbar.pack(side='bottom', fill='x')

        # Load products data
        self.refresh_products()

    def on_search_products(self, event=None):
        """Handle product search"""
        try:
            search_term = self.search_var.get().strip()
            
            if search_term:
                # Use database search
                products = self.db_manager.search_products(search_term, limit=200)
                self.performance_stats['search_operations'] += 1
            else:
                # Show all products
                products = self.products
            
            # Update tree
            self.update_products_tree(products)
            
        except Exception as e:
            print(f"خطأ في البحث: {e}")

    def refresh_products(self):
        """Refresh products from database"""
        try:
            self.products = self.db_manager.get_all_products()
            self.update_products_tree(self.products)
            self.status_label.config(text=f"تم تحديث {len(self.products)} منتج")
            
        except Exception as e:
            print(f"خطأ في تحديث المنتجات: {e}")
            messagebox.showerror("خطأ", f"فشل في تحديث المنتجات:\n{str(e)}")

    def update_products_tree(self, products):
        """Update products tree with data"""
        try:
            # Clear existing items
            for item in self.products_tree.get_children():
                self.products_tree.delete(item)

            # Add products
            for product in products:
                stock = product.get('stock', 0)
                min_stock = product.get('min_stock', 0)
                status = "⚠️ منخفض" if stock <= min_stock else "✅ جيد"
                
                values = (
                    product.get('id', ''),
                    product.get('barcode', ''),
                    product.get('name', ''),
                    product.get('category', ''),
                    product.get('supplier_name', ''),
                    stock,
                    min_stock,
                    f"${product.get('base_price', 0):.2f}",
                    status
                )
                
                # Color coding for low stock
                item_id = self.products_tree.insert('', 'end', values=values)
                if stock <= min_stock:
                    self.products_tree.set(item_id, 'Status', '⚠️ منخفض')

        except Exception as e:
            print(f"خطأ في تحديث جدول المنتجات: {e}")

    def show_customers(self):
        """Show customer management"""
        self.clear_content()
        
        tk.Label(
            self.content_frame,
            text="👥 إدارة العملاء / Customer Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

    def show_suppliers(self):
        """Show supplier management"""
        self.clear_content()
        
        tk.Label(
            self.content_frame,
            text="🏢 إدارة الموردين / Supplier Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

    def show_sales(self):
        """Show sales management"""
        self.clear_content()
        
        tk.Label(
            self.content_frame,
            text="💰 إدارة المبيعات / Sales Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

    def show_reports(self):
        """Show reports with database analytics"""
        self.clear_content()
        
        tk.Label(
            self.content_frame,
            text="📊 التقارير المتقدمة / Advanced Reports",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

    def show_database_management(self):
        """Show database management interface"""
        self.clear_content()
        
        tk.Label(
            self.content_frame,
            text="🗄️ إدارة قاعدة البيانات / Database Management",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

        # Database operations frame
        ops_frame = tk.LabelFrame(self.content_frame, text="عمليات قاعدة البيانات", font=('Arial', 12, 'bold'), bg='white')
        ops_frame.pack(fill='x', padx=20, pady=10)

        # Database operation buttons
        db_buttons = [
            ("💾 إنشاء نسخة احتياطية", self.create_database_backup),
            ("📤 تصدير إلى JSON", self.export_database_to_json),
            ("📥 استيراد من JSON", self.import_database_from_json),
            ("🔧 تحسين قاعدة البيانات", self.optimize_database),
            ("📊 إحصائيات مفصلة", self.show_detailed_stats)
        ]

        for text, command in db_buttons:
            btn = tk.Button(
                ops_frame,
                text=text,
                font=('Arial', 11, 'bold'),
                bg='#3b82f6',
                fg='white',
                command=command,
                cursor='hand2',
                width=25,
                height=2
            )
            btn.pack(side='left', padx=5, pady=10)

    def show_settings(self):
        """Show settings"""
        self.clear_content()
        
        tk.Label(
            self.content_frame,
            text="⚙️ الإعدادات / Settings",
            font=('Arial', 18, 'bold'),
            fg='#1f2937',
            bg='white'
        ).pack(pady=20)

    def create_database_backup(self):
        """Create database backup"""
        try:
            backup_path = self.db_manager.create_backup()
            messagebox.showinfo("نجح", f"تم إنشاء نسخة احتياطية:\n{backup_path}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء النسخة الاحتياطية:\n{str(e)}")

    def export_database_to_json(self):
        """Export database to JSON"""
        try:
            json_path = self.db_manager.export_to_json()
            messagebox.showinfo("نجح", f"تم تصدير قاعدة البيانات إلى:\n{json_path}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير قاعدة البيانات:\n{str(e)}")

    def import_database_from_json(self):
        """Import database from JSON"""
        try:
            success = self.db_manager.import_from_json()
            if success:
                messagebox.showinfo("نجح", "تم استيراد البيانات من JSON بنجاح")
                self.load_data_from_database()  # Refresh data
            else:
                messagebox.showerror("خطأ", "فشل في استيراد البيانات من JSON")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في استيراد البيانات:\n{str(e)}")

    def optimize_database(self):
        """Optimize database"""
        try:
            self.db_manager.vacuum_database()
            self.db_manager.analyze_database()
            messagebox.showinfo("نجح", "تم تحسين قاعدة البيانات بنجاح")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحسين قاعدة البيانات:\n{str(e)}")

    def show_detailed_stats(self):
        """Show detailed database statistics"""
        try:
            stats = self.db_manager.get_database_stats()
            
            # Create stats window
            stats_window = tk.Toplevel(self.root)
            stats_window.title("إحصائيات قاعدة البيانات المفصلة")
            stats_window.geometry("600x500")
            stats_window.configure(bg='white')

            # Stats text
            text_widget = tk.Text(stats_window, font=('Arial', 11), bg='white', wrap='word')
            text_widget.pack(fill='both', expand=True, padx=20, pady=20)

            # Format stats
            stats_text = "📊 إحصائيات قاعدة البيانات المفصلة\n"
            stats_text += "=" * 50 + "\n\n"
            
            for key, value in stats.items():
                if isinstance(value, dict):
                    stats_text += f"{key}:\n"
                    for sub_key, sub_value in value.items():
                        stats_text += f"  {sub_key}: {sub_value}\n"
                else:
                    stats_text += f"{key}: {value}\n"
                stats_text += "\n"

            text_widget.insert('1.0', stats_text)
            text_widget.config(state='disabled')

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض الإحصائيات:\n{str(e)}")

    def on_closing(self):
        """Handle application closing"""
        try:
            print("🔄 إغلاق التطبيق...")
            
            # Close database manager
            if hasattr(self, 'db_manager'):
                self.db_manager.close()
            
            # Destroy window
            self.root.destroy()
            
            print("✅ تم إغلاق التطبيق بنجاح")
            
        except Exception as e:
            print(f"خطأ في إغلاق التطبيق: {e}")

    def run(self):
        """Run the application"""
        try:
            self.root.mainloop()
        except Exception as e:
            print(f"خطأ في تشغيل التطبيق: {e}")
            messagebox.showerror("خطأ", f"خطأ في تشغيل التطبيق:\n{str(e)}")

def main():
    """Main function"""
    print("🖥️ تشغيل نظام ProTech مع قاعدة البيانات المتقدمة...")
    print("🖥️ Starting ProTech with Advanced Database...")
    
    try:
        app = ProTechWithDatabase()
        app.run()
    except Exception as e:
        print(f"❌ خطأ: {e}")
        messagebox.showerror("خطأ / Error", f"فشل في تشغيل التطبيق:\n{str(e)}")

if __name__ == '__main__':
    main()
